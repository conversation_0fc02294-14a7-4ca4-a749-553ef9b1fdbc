import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { Repository } from 'typeorm';
import { AbstractProvider } from '../../core/service/abstract.provider';
import { BoardUserEntity } from '../entity/board-user.entity';
import { StatusEnum } from '../enum/boardUser-status.enum';

@Injectable()
export class BoardUserProvider extends AbstractProvider<BoardUserEntity> {
  constructor(
    @InjectRepository(BoardUserEntity)
    protected readonly repository: Repository<BoardUserEntity>,
    protected readonly logger: Logger,
  ) {
    super(repository, logger);
  }

  async getMember(userId: string, boardId: string): Promise<BoardUserEntity> {
    return this.getBy({ userId, boardId, status: StatusEnum.APPROVED });
  }

  async findAdmin(boardId: string) {
    return await this.repository.find({
      where: { boardId, isAdmin: true, status: StatusEnum.APPROVED },
    });
  }

  async isMember(userId: string, boardId: string): Promise<boolean> {
    return (
      (await this.repository.count({
        where: { userId, boardId, status: StatusEnum.APPROVED },
      })) > 0
    );
  }

  async isAdmin(userId: string, boardId: string): Promise<boolean> {
    return (
      (await this.repository.count({
        where: { userId, boardId, isAdmin: true },
      })) > 0
    );
  }

  async isOwner(userId: string, boardId: string): Promise<boolean> {
    return (
      (await this.repository.count({
        where: { userId, boardId, isOwner: true },
      })) > 0
    );
  }

  async isManager(userId: string, boardId: string): Promise<boolean> {
    return (
      (await this.repository.count({
        where: { userId, boardId, isOwner: true },
      })) > 0 ||
      (await this.repository.count({
        where: { userId, boardId, isAdmin: true },
      })) > 0
    );
  }
}
