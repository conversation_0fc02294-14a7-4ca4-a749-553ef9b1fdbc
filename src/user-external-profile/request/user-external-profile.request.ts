import { IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UserExternalProfileRequest {
  @IsOptional()
  @IsString()
  @ApiProperty()
  platform: string;

  @IsOptional()
  @IsString()
  @ApiProperty()
  username: string;

  @IsOptional()
  @IsString()
  @ApiProperty()
  externalId: string;

  @IsOptional()
  @ApiProperty()
  profileData: any;
}
