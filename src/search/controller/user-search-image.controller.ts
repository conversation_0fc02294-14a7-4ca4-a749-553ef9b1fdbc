import {
  Controller,
  Get,
  Post,
  Request,
  UsePipes,
  ValidationPipe,
  Res,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  Body,
  Param,
  Query,
} from '@nestjs/common/decorators/http/route-params.decorator';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiOkResponse,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiNotFoundResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import { UserProvider } from 'src/user/service/provider';
import { UserSearchImageDto } from '../dto/user-search-image.dto';
import { UserSearchImageRequest } from '../dto/user-search-image.request';
import { UserSearchImageRequestManager } from '../service/user-search-image.request-manager';
import { UserSearchImageResponseMapper } from '../service/user-search-image.response-mapper';
import { BaseFindResponseHeadersDto } from 'src/core/dto/base-find-response-headers.dto';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { UserSearchImageProvider } from '../service/user-search-image.provider';
import { UserSearchImageSearchRequest } from '../dto/user-search-image.search-request';
import { AuthOptional } from 'src/core/security/public-routes';

@ApiTags('search / user_image')
@Controller('search/user_images')
export class UserSearchImageController {
  constructor(
    private requestManager: UserSearchImageRequestManager,
    private responseMapper: UserSearchImageResponseMapper,
    private userProvider: UserProvider,
    private userSearchImageProvider: UserSearchImageProvider,
  ) {}

  @ApiOperation({
    operationId: 'user_search_image_list',
    summary: 'List user search images',
    description:
      'Retrieves a paginated list of user search images.\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of images per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- userId: UUID of the user\n' +
      '- imageCompletionId: UUID of the image completion\n',
  })
  @ApiOkResponse({
    type: UserSearchImageDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated list of user search images.',
  })
  @ApiQuery({
    type: UserSearchImageSearchRequest,
    description:
      'Query parameters for searching and paginating user search images.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - Filter parameters must be valid values.
      - \`userId\`: Must be a valid UUID.
      - \`imageCompletionId\`: Must be a valid UUID.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Get()
  @UsePipes(new ValidationPipe())
  async find(
    @Request() request,
    @Query() query: UserSearchImageSearchRequest,
    @Res() res: Response,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;

    const entities = await this.userSearchImageProvider.findBy(
      inputFilters,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    const totalCount = await this.userSearchImageProvider.countBy(inputFilters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultiple(entities));
  }

  @ApiOperation({
    operationId: 'user_search_image_get',
    summary: 'Get user search image by ID',
    description:
      'Retrieves a specific user search image by its unique identifier.\n\n' +
      'Requirements:\n' +
      '- User must be authenticated\n' +
      '- User must be the owner of the user search image' +
      '\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the user search image\n',
  })
  @ApiOkResponse({
    type: UserSearchImageDto,
    description: 'User search image details.',
  })
  @ApiParam({
    name: 'id',
    description: 'The unique identifier of the user search image',
    type: 'string',
    format: 'uuid',
  })
  @ApiNotFoundResponse({
    description: 'Not Found. The user search image could not be found.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Get(':id')
  @AuthOptional()
  async get(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<UserSearchImageDto> {
    const entity = await this.userSearchImageProvider.getBy({ id: id });

    return this.responseMapper.map(entity, false);
  }

  @ApiOperation({
    operationId: 'user_search_image_create',
    summary: 'Create a user search image',
    description:
      'Creates a new user search image for the current user.\n\n' +
      'Requirements:\n' +
      '- User must be authenticated\n' +
      '- Request body must contain valid image search parameters' +
      '\n\n' +
      'Optional Parameters:\n' +
      '- extension: File extension of the image\n' +
      '- imageCompletionId: UUID of the image completion to search\n',
  })
  @ApiBody({
    type: UserSearchImageRequest,
    description: 'User search image creation parameters.',
  })
  @ApiOkResponse({
    type: UserSearchImageDto,
    description: 'User search image created successfully.',
  })
  @ApiBadRequestResponse({ description: 'Bad Request. Invalid parameters.' })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Post()
  async create(
    @Body() requestBody: UserSearchImageRequest,
    @Request() request,
  ): Promise<UserSearchImageDto> {
    const user = await this.userProvider.get(request.user.id);

    const userImageSearch = await this.requestManager.create(requestBody, user);

    return this.responseMapper.map(userImageSearch, true);
  }
}
