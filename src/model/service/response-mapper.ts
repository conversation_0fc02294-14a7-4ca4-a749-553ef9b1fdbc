import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Logger } from 'nestjs-pino';
import { UserResponseMapper } from 'src/user/service/response-mapper';
import { ModelVersionDto } from '../dto/model-version.dto';
import { ModelDto } from '../dto/model.dto';
import { ModelEntity } from '../entity/model.entity';
import { AssetManager } from './asset.manager';
import { ModelVersionProvider } from './model-version.provider';
import { BookmarkProvider } from 'src/bookmark/service/bookmark.provider';
import { ModelOrganizationProvider } from './model-organization.provider';
import { ModelOrganizationResponseMapper } from './model-organization.response-mapper';

@Injectable()
export class ModelResponseMapper {
  private cdnHost: string;

  constructor(
    private assetManager: AssetManager,
    private configService: ConfigService,
    private userResponseMapper: UserResponseMapper,
    private modelVersionProvider: ModelVersionProvider,
    private logger: Logger,
    private bookmarkProvider: BookmarkProvider,
    private modelOrganizationProvider: ModelOrganizationProvider,
    private modelOrganizationResponseMapper: ModelOrganizationResponseMapper,
  ) {
    this.cdnHost = this.configService.get<string>('CDN_HOST');
  }

  async mapMultiple(
    entities: ModelEntity[],
    mapThumbnails = false,
    mapImages = false,
    userId: string = null,
    includeOrganizationAccess = false,
  ): Promise<any> {
    return Promise.all(
      entities.map((entity) =>
        this.map(
          entity,
          mapThumbnails,
          mapImages,
          userId,
          includeOrganizationAccess,
        ),
      ),
    );
  }

  async map(
    entity: ModelEntity,
    mapThumbnails = true,
    mapImages = true,
    userId: string = null,
    includeOrganizationAccess = false,
  ): Promise<ModelDto> {
    const dto = new ModelDto();

    dto.id = entity.id;

    if (entity.user) {
      dto.user = this.userResponseMapper.mapPublic(entity.user);
    }

    dto.name = entity.name;
    dto.username = entity?.user?.username;
    dto.type = entity.type;
    dto.class = entity.class;
    dto.description = entity.description;
    dto.website = entity.website;
    dto.status = entity.status;
    dto.progress = entity.progress;
    dto.privacy = entity.privacy;
    dto.usages = entity.usages;
    dto.imagesAvailable = entity.imagesAvailable;
    dto.systemVersions = entity.systemVersions;
    dto.version = entity.version;
    dto.likes = entity.likes;
    dto.comments = entity.comments;
    dto.settings = entity.settings;
    dto.isSponsored = entity.isSponsored;
    dto.isFeatured = entity.isFeatured;
    dto.isActive = entity.isActive;
    dto.createdAt = entity.createdAt;
    dto.webhookUrl = entity.webhookUrl;
    dto.trainingMode = entity.trainingMode;

    if (userId) {
      dto.isBookmarked = await this.bookmarkProvider.isBookmarked(
        entity.id,
        'model',
        userId,
      );
    }

    if (mapImages) {
      try {
        dto.images = await this.assetManager.generateSignedUrls(
          entity,
          'images',
        );
        // const images = await this.assetManager.getAssets(entity, 'images');
        // dto.images = images.map((image) => {
        //   return image.replace(entity.storagePath + '/images/', '');
        // });
      } catch (error) {
        this.logger.error('Error generating signed urls for images', {
          modelId: entity.id,
          error: error,
        });
      }
    }

    if (mapThumbnails) {
      // dto.thumbnailOptions = await this.assetManager.generateSignedUrls(
      //   entity,
      //   'thumbnails',
      // );

      if (entity.thumbnail) {
        // dto.thumbnail = await this.assetManager.generateThumbnailUrl(entity);
        dto.thumbnail = await this.generateThumbnailUrl(entity);
      }
    }

    const modelVersions = await this.modelVersionProvider.findByModelId(
      entity.id,
    );

    dto.versions = this.mapVersions(modelVersions);

    // Include organization access information if requested and user is authorized
    if (includeOrganizationAccess && userId && entity.userId === userId) {
      try {
        const modelOrganizations =
          await this.modelOrganizationProvider.findByModelId(entity.id);
        if (modelOrganizations.length > 0) {
          dto.organizationAccess =
            await this.modelOrganizationResponseMapper.mapMultiple(
              modelOrganizations,
            );
        }
      } catch (error) {
        this.logger.error('Error loading organization access for model', {
          modelId: entity.id,
          error: error,
        });
      }
    }

    return dto;
  }

  generateThumbnailUrl(entity: ModelEntity): string {
    return this.cdnHost + '/models/' + entity.thumbnail;
  }

  async mapMultipleInternal(entities: ModelEntity[]): Promise<any> {
    return Promise.all(entities.map((entity) => this.mapInternal(entity)));
  }

  async mapInternal(entity: ModelEntity): Promise<ModelDto> {
    const dto = await this.map(entity, false, false);

    dto.storageBucket = entity.storageBucket;
    dto.modelPath = entity.storagePath + '/model';
    dto.imagesPath = entity.storagePath + '/images';
    dto.thumbnailsPath = entity.storagePath + '/thumbnails';
    dto.thumbnail = entity.thumbnail;
    dto.thumbnailOptions = entity.thumbnailOptions;
    dto.generationData = entity.generationData;
    dto.userId = entity.user?.id;

    const modelVersions = await this.modelVersionProvider.findByModelId(
      entity.id,
    );

    dto.versions = this.mapVersions(modelVersions, false);

    return Promise.resolve(dto);
  }

  mapVersions(versions: any, isPublic = true): ModelVersionDto[] {
    if (!versions || !versions.length) {
      return [];
    }

    return versions.map((version) => {
      const dto = new ModelVersionDto();
      dto.id = version.id;
      dto.version = version.version;
      dto.systemVersions = version.systemVersionsData;
      dto.status = version.status;
      dto.createdAt = version.createdAt;
      dto.trainedAt = version.trainedAt;

      if (isPublic && dto.systemVersions?.length > 0) {
        dto.systemVersions = dto.systemVersions.map((systemVersion) => {
          delete systemVersion.prompt;

          return systemVersion;
        });
      }

      return dto;
    });
  }
}
