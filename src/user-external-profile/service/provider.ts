import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { FindManyOptions, FindOneOptions, Repository } from 'typeorm';
import { AbstractProvider } from '../../core/service/abstract.provider';
import { UserExternalProfileEntity } from '../entity/user-external-profile.entity';

@Injectable()
export class UserExternalProfileProvider extends AbstractProvider<UserExternalProfileEntity> {
  constructor(
    @InjectRepository(UserExternalProfileEntity)
    repository: Repository<UserExternalProfileEntity>,
    logger: Logger,
  ) {
    super(repository, logger);
  }

  prepareFindOneOptions(criteria: any): FindOneOptions {
    return {
      where: criteria,
      relations: {
        user: true,
      },
    };
  }

  prepareFindManyOptions(
    criteria: any,
    page: number,
    limit: number,
  ): FindManyOptions<UserExternalProfileEntity> {
    const parentOptions = super.prepareFindManyOptions(criteria, page, limit);

    return {
      ...parentOptions,
      relations: {
        user: true,
      },
    };
  }
}
