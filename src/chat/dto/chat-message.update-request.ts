import { ApiProperty } from '@nestjs/swagger';
import { ChatAttachedFileDto } from './chat-attached-file.dto';
import { IsArray, IsOptional } from 'class-validator';

export class ChatMessageUpdateRequest {
  @ApiProperty({
    description: 'Attached files associated with the message',
    type: [ChatAttachedFileDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  attachedFiles?: ChatAttachedFileDto[];
}
