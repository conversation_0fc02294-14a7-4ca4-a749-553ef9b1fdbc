import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1731348540496 implements MigrationInterface {
  name = 'Api1731348540496';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "allow_swapping" boolean NOT NULL DEFAULT false
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "allow_swapping"
        `);
  }
}
