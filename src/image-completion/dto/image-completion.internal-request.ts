import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON>Array,
  IsEnum,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';
import { PrivacyEnum, StatusEnum } from '../entity/image-completion.entity';
import { ImageCompletionRequest } from './image-completion.request';

export class ImageCompletionInternalRequest extends ImageCompletionRequest {
  @IsOptional()
  @IsEnum(StatusEnum, {
    message: 'Status must be a valid value',
  })
  @ApiProperty({ enum: StatusEnum })
  status?: StatusEnum;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  progress?: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  previewImage?: string;

  @IsOptional()
  @IsEnum(PrivacyEnum, {
    message: 'Privacy must be a valid value',
  })
  @ApiProperty({ enum: PrivacyEnum })
  privacy?: PrivacyEnum;

  @ApiProperty()
  @IsOptional()
  @IsString()
  promptSystem?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  baseModel?: string;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  likes?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  reports?: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  statusDetail?: string;

  @ApiProperty()
  @IsOptional()
  @IsArray()
  imagePaths?: string[];

  @ApiProperty()
  @IsOptional()
  hasWatermark?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsObject()
  generationSettings?: any;

  @ApiProperty()
  @IsOptional()
  @IsString()
  generationData?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  generatedByUnit?: string;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  generationSeconds?: number;

  @ApiProperty()
  @IsOptional()
  isNsfw?: boolean;

  @ApiProperty()
  @IsOptional()
  isUnsafe?: boolean;

  @ApiProperty()
  @IsOptional()
  isHot?: boolean;

  @ApiProperty()
  @IsOptional()
  isActive?: boolean;

  @ApiProperty()
  @IsOptional()
  isResized?: boolean;
}
