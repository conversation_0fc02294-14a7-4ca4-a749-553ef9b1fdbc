import {
  <PERSON>,
  Get,
  Param,
  ParseUUIDPipe,
  Query,
  Request,
  Res,
} from '@nestjs/common';
import {
  ApiOkResponse,
  ApiQuery,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import { BaseFindResponseHeadersDto } from 'src/core/dto/base-find-response-headers.dto';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { BoardDto } from '../dto/board.dto';
import { BoardSearchRequest } from '../dto/board.search-request';
import { PublicBoardDto } from '../dto/public.board.dto';
import { BoardUserProvider } from '../service/board-user.provider';
import { BoardProvider } from '../service/provider';
import { BoardRequestManager } from '../service/request-manager';
import { BoardResponseMapper } from '../service/response-mapper';

@ApiTags('board')
@Controller('boards')
export class ReadController {
  constructor(
    private boardProvider: BoardProvider,
    private boardResponseMapper: BoardResponseMapper,
    private boardRequestManager: BoardRequestManager,
    private boardUserProvider: BoardUserProvider,
  ) {}

  @Get()
  @ApiOperation({
    operationId: 'board_list',
    summary: 'List boards',
    description:
      'Retrieves a paginated list of boards for the authenticated user.\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of boards per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- search: Search term\n' +
      '- name: Filter by board name\n' +
      '- visibility: Filter by board visibility\n' +
      '- safety: Filter by board safety\n' +
      '- ownerId: Filter by board owner\n' +
      '- isMember: Filter by board membership\n' +
      '- isOwner: Filter by board ownership\n',
  })
  @ApiOkResponse({
    type: BoardDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated list of boards.',
  })
  @ApiQuery({
    type: BoardSearchRequest,
    description: 'Query parameters for searching and paginating boards.',
  })
  @ApiBadRequestResponse({
    description: `
      Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - Invalid query parameters.
      - Invalid filter values (e.g., visibility, safety).
      - Failed to analyze request.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async find(
    @Request() request,
    @Query() query: BoardSearchRequest,
    @Res() res: Response,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;
    const filters = await this.boardRequestManager.sanitizeSearchFilters(
      inputFilters,
      request.user.id,
    );

    const entities = await this.boardProvider.findBy(
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    );
    const totalCount = await this.boardProvider.countBy(filters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(
      await this.boardResponseMapper.mapMultiple(
        entities,
        true,
        request.user.id,
      ),
    );
  }

  @Get(':id')
  @ApiOperation({
    operationId: 'board_get',
    summary: 'Get board by ID',
    description:
      'Retrieves a board by its UUID. Returns public details if the user is not a manager.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the board',
  })
  @ApiOkResponse({
    type: PublicBoardDto,
    description: 'Returns the board with the specified ID.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the board to retrieve.',
    type: String,
  })
  @ApiBadRequestResponse({
    description: 'Bad Request. Invalid board ID format.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User does not have access to this board.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async get(
    @Request() req,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<PublicBoardDto> {
    const board = await this.boardProvider.get(id);

    if (!(await this.boardUserProvider.isManager(req.user.id, board.id))) {
      return await this.boardResponseMapper.mapPublic(board, req.user.id);
    }

    return await this.boardResponseMapper.map(board, false, req.user.id);
  }
}
