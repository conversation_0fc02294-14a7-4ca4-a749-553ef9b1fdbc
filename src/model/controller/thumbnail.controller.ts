import {
  Controller,
  Put,
  Param,
  Request,
  Body,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiBody,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiParam,
} from '@nestjs/swagger';
import { ModelProvider } from '../service/provider';
import { ModelDto } from '../dto/model.dto';
import { ModelRequestManager } from '../service/request-manager';
import { ModelResponseMapper } from '../service/response-mapper';
import { ModelThumbnailRequest } from '../dto/model-thumbnail.request';

@ApiTags('model')
@Controller('models')
export class ThumbnailController {
  constructor(
    // private assetManager: AssetManager,
    private provider: ModelProvider,
    private requestManager: ModelRequestManager,
    private responseMapper: ModelResponseMapper,
  ) {}

  // @ApiOkResponse({ type: [String] })
  // @ApiBadRequestResponse()
  // @Get(':id/thumbnails')
  // async getThumbnails(@Request() request, @Param() params): Promise<string[]> {
  //   return this.provider
  //     .getBy({ user: { id: request.user.id }, id: params.id })
  //     .then((entity) => {
  //       return this.assetManager.generateThumbnailOptionsUrls(entity);
  //     });
  // }

  @ApiOkResponse({ type: ModelDto })
  @Put(':id/thumbnail')
  @ApiOperation({
    operationId: 'model_update_thumbnail',
    summary: 'Update model thumbnail',
    description:
      'Updates the thumbnail of the specified model for the authenticated user.\n\n' +
      'Required Query Parameters:\n' +
      '- id: UUID of the model to update\n\n' +
      'Required Body Parameters:\n' +
      '- imageCompletionId: UUID of the image completion to use as thumbnail\n',
  })
  @ApiOkResponse({
    type: ModelDto,
    description: 'Model thumbnail updated successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - \`imageCompletionId\`: Must be a valid UUID.
      - Image completion does not exist.
      - Image completion is not part of the model.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description:
      'Forbidden. User does not have permission to update this model.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiParam({ name: 'id', description: 'UUID of the model to update.' })
  @ApiBody({
    type: ModelThumbnailRequest,
    description: 'Thumbnail update parameters.',
  })
  async updateThumbnail(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
    @Body() requestBody: ModelThumbnailRequest,
  ): Promise<ModelDto> {
    const model = await this.provider.getBy({
      user: { id: request.user.id },
      id: id,
    });

    await this.requestManager.updateThumbnail(
      model,
      requestBody.imageCompletionId,
    );

    return await this.responseMapper.map(model);
  }
}
