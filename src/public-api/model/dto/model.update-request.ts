import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional, Matches } from 'class-validator';
import { PrivacyEnum, SettingsEnum } from '../../../model/entity/model.entity';

export class ModelUpdateRequest {
  @IsOptional()
  @IsEnum(PrivacyEnum, {
    message: 'privacy must be a valid value',
  })
  @ApiProperty({ enum: PrivacyEnum })
  privacy: PrivacyEnum;

  @ApiProperty()
  @IsOptional()
  @Matches(/^[a-zA-Z0-9,()_\-:;\[\].äöüÄÖÜéÉèëÈË\? ]+$/u, {
    message:
      'type should only contain alphanumeric characters, commas, hyphens, underscores, parentheses, colons, semicolons, and question marks',
  })
  type: string;

  @ApiProperty()
  @IsOptional()
  description: string;

  @ApiProperty()
  @IsOptional()
  website: string;

  @ApiProperty({
    type: [String],
    enum: SettingsEnum,
    isArray: true,
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(SettingsEnum, {
    each: true,
    message: 'settings must be valid values',
  })
  settings: SettingsEnum[];

  @ApiProperty({ required: false })
  webhookUrl?: string;
}
