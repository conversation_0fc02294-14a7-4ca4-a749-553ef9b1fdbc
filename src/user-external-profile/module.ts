import { DynamicModule, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CreateController } from './controller/create.controller';
import { DeleteController } from './controller/delete.controller';
import { ReadController } from './controller/read.controller';
import { UpdateController } from './controller/update.controller';
import { UserExternalProfileResponseMapper } from './service/response-mapper';
import { UserExternalProfileProvider } from './service/provider';
import { UserExternalProfileRequestManager } from './service/request-manager';
import { UserExternalProfileManager } from './service/manager';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { UserExternalProfileEntity } from './entity/user-external-profile.entity';
import { jwtConfig } from '../auth/config/jwt.config';
import { UserModule } from '../user/user.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserExternalProfileEntity]),
    PassportModule,
    JwtModule.register(jwtConfig),
    UserModule,
  ],
  providers: [
    UserExternalProfileResponseMapper,
    UserExternalProfileProvider,
    UserExternalProfileRequestManager,
    UserExternalProfileManager,
  ],
})
export class UserExternalProfileModule {
  static register(enableControllers: boolean): DynamicModule {
    return {
      module: UserExternalProfileModule,
      controllers: enableControllers
        ? [CreateController, DeleteController, ReadController, UpdateController]
        : [],
    };
  }
}
