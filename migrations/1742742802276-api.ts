import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1742742802276 implements MigrationInterface {
  name = 'Api1742742802276';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "organization"
            ADD CONSTRAINT "UQ_21d67575fef801f33efb25f3d68" UNIQUE ("handle")
        `);
    await queryRunner.query(`
            CREATE INDEX "idx_organization_handle" ON "organization" ("handle")
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            DROP INDEX "public"."idx_organization_handle"
        `);
    await queryRunner.query(`
            ALTER TABLE "organization" DROP CONSTRAINT "UQ_21d67575fef801f33efb25f3d68"
        `);
  }
}
