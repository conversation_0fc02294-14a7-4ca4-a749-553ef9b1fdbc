import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ChatMessageEntity } from '../entity/chat-message.entity';
import { ChatRateLimitDto } from '../dto/chat-rate-limit.dto';

@Injectable()
export class ChatMessageManager {
  private defaultHourlyLimit = 50;

  constructor(
    @InjectRepository(ChatMessageEntity)
    private readonly messageRepository: Repository<ChatMessageEntity>,
  ) {}

  async create(message: ChatMessageEntity): Promise<ChatMessageEntity> {
    const savedMessage = await this.messageRepository.save(message);
    return savedMessage;
  }

  async update(message: ChatMessageEntity): Promise<ChatMessageEntity> {
    if (message.id) {
      await this.messageRepository.query(
        `
        DELETE FROM chat_message_relationship 
        WHERE chat_message_id = $1
      `,
        [message.id],
      );
    }

    return await this.messageRepository.save(message);
  }

  async deleteByConversationId(conversationId: string): Promise<void> {
    // Delete message relationships first to avoid foreign key constraint violation
    await this.messageRepository.query(
      `
      DELETE FROM chat_message_relationship 
      WHERE chat_message_id IN (
        SELECT id FROM chat_message WHERE conversation_id = $1
      )
    `,
      [conversationId],
    );

    // Then delete the messages
    await this.messageRepository.delete({ conversationId });
  }

  async getUserHourlyLimit(userId: string): Promise<number> {
    // We may adjust this based on user's plan
    return this.defaultHourlyLimit;
  }

  async countUserMessagesLastHour(userId: string): Promise<number> {
    const oneHourAgo = new Date();
    oneHourAgo.setHours(oneHourAgo.getHours() - 1);

    return this.messageRepository
      .createQueryBuilder('message')
      .innerJoin('message.conversation', 'conversation')
      .where('conversation.userId = :userId', { userId })
      .andWhere('message.role = :role', { role: 'user' })
      .andWhere('message.createdAt > :date', { date: oneHourAgo })
      .getCount();
  }

  async getMessageLimit(userId: string): Promise<boolean> {
    const messageCount = await this.countUserMessagesLastHour(userId);
    const userLimit = await this.getUserHourlyLimit(userId);

    return messageCount >= userLimit;
  }

  async getUserMessageUsage(userId: string): Promise<ChatRateLimitDto> {
    const messageCount = await this.countUserMessagesLastHour(userId);
    const limit = await this.getUserHourlyLimit(userId);
    const remaining = Math.max(0, limit - messageCount);

    const dto = new ChatRateLimitDto();
    dto.messageCount = messageCount;
    dto.limit = limit;
    dto.remaining = remaining;

    return dto;
  }
}
