import {
  Controller,
  Delete,
  HttpC<PERSON>,
  Param,
  Parse<PERSON><PERSON><PERSON>ip<PERSON>,
  Request,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiNoContentResponse,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtIntegrationAuthGuard } from 'src/auth/service/jwt-integration-auth.guard';
import { ModelManager } from 'src/model/service/manager';
import { ModelProvider } from 'src/model/service/provider';

@ApiTags('models')
@Controller('models')
@UseGuards(JwtIntegrationAuthGuard)
@ApiBearerAuth()
export class DeleteController {
  constructor(private provider: ModelProvider, private manager: ModelManager) {}

  @Delete(':id')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'models_delete',
    summary: 'Delete a model',
    description:
      'Permanently deletes a model.\n\n' +
      'Path Parameters:\n' +
      '- id: UUID of the model to delete',
  })
  @ApiParam({
    name: 'id',
    description: 'The unique identifier of the model to delete',
    type: 'string',
    format: 'uuid',
  })
  @ApiNoContentResponse({
    description: 'The model was successfully deleted.',
  })
  @ApiResponse({
    status: 400,
    description: `
      Bad Request. Possible reasons:
      - Invalid ID format
      - Model does not exist
      - Model is in use by active generations
    `,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden. User does not have permission to delete this model.',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found. The requested model could not be found.',
  })
  @ApiResponse({
    status: 409,
    description:
      'Conflict. The model cannot be deleted because it is being used by other resources.',
  })
  @ApiResponse({
    status: 500,
    description:
      'Internal Server Error. An unexpected error occurred while deleting the model.',
  })
  async delete(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<void> {
    const entity = await this.provider.getBy({
      user: { id: request.user.id },
      id: id,
    });

    await this.manager.delete(entity);
  }
}
