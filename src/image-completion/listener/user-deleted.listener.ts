import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CleanupAuditService } from 'src/core/service/cleanup-audit.service';
import { S3BatchDeleteService } from 'src/core/service/s3-batch-delete.service';
import { CleanupOperationTypeEnum } from 'src/core/enum/cleanup-status.enum';
import { UserDeletedEvent } from 'src/user/event/user-deleted.event';
import { ImageCompletionEntity } from '../entity/image-completion.entity';

@Injectable()
export class ImageCompletionDeletedListener {
  constructor(
    @InjectRepository(ImageCompletionEntity)
    private imageCompletionRepository: Repository<ImageCompletionEntity>,
    private cleanupAuditService: CleanupAuditService,
    private s3BatchDeleteService: S3BatchDeleteService,
  ) {}

  @OnEvent('user.deleted', { async: true })
  async handleUserDeletedEvent(event: UserDeletedEvent) {
    const operationId = this.cleanupAuditService.startOperation(
      CleanupOperationTypeEnum.IMAGE_COMPLETIONS,
      event.id,
      event.email,
      { username: event.username }
    );

    try {
      // Find all image completions for this user (including soft-deleted ones)
      const imageCompletions = await this.imageCompletionRepository.find({
        where: { userId: event.id },
        withDeleted: true,
      });

      let totalProcessed = 0;
      let totalDeleted = 0;
      let totalFailed = 0;

      if (imageCompletions.length > 0) {
        // Group images by storage bucket for efficient batch deletion
        const bucketGroups: Record<string, string[]> = {};

        for (const completion of imageCompletions) {
          totalProcessed++;

          const bucket = completion.storageBucket || 'letz-ai-images'; // Default bucket
          
          // Handle both storagePath and imagePaths (JSON array of paths)
          const imagesToDelete: string[] = [];
          
          if (completion.storagePath) {
            imagesToDelete.push(completion.storagePath);
          }
          
          if (completion.imagePaths && Array.isArray(completion.imagePaths)) {
            imagesToDelete.push(...completion.imagePaths);
          } else if (completion.imagePaths && typeof completion.imagePaths === 'object') {
            // Handle case where imagePaths might be an object with image URLs
            Object.values(completion.imagePaths).forEach((path) => {
              if (typeof path === 'string') {
                imagesToDelete.push(path);
              }
            });
          }

          // Also delete preview image if exists
          if (completion.previewImage) {
            imagesToDelete.push(completion.previewImage);
          }

          // Add valid paths to bucket groups
          for (const imagePath of imagesToDelete) {
            if (imagePath && this.s3BatchDeleteService.validateS3Path(bucket, imagePath)) {
              if (!bucketGroups[bucket]) {
                bucketGroups[bucket] = [];
              }
              bucketGroups[bucket].push(imagePath);
            }
          }
        }

        // Delete files from S3 in batches per bucket
        for (const [bucket, keys] of Object.entries(bucketGroups)) {
          if (keys.length > 0) {
            const deleteResult = await this.s3BatchDeleteService.deleteWithRetry(bucket, keys, 3);
            totalDeleted += deleteResult.deleted.length;
            totalFailed += deleteResult.failed.length;
          }
        }
      }

      // Soft delete the database records
      const dbResult = await this.imageCompletionRepository.softDelete({ userId: event.id });
      totalProcessed += dbResult.affected || 0;

      this.cleanupAuditService.completeOperation(
        operationId,
        totalProcessed,
        totalDeleted,
        totalFailed
      );

    } catch (error) {
      this.cleanupAuditService.failOperation(
        operationId,
        `Failed to cleanup image completions: ${error.message}`
      );
      throw error;
    }
  }
}
