import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ImageCompletionEntity } from '../../image-completion/entity/image-completion.entity';
import { OrganizationEntity } from '../../organization/entity/organization.entity';
import { UserEntity } from '../../user/entity/user.entity';
import { ImageEditImageCompletionEntity } from './image-edit-image-completion.entity';
import { ImageEditModelEntity } from './image-edit-model.entity';

export enum EditMode {
  IN = 'in',
  OUT = 'out',
  SKIN = 'skin',
  CONTEXT = 'context',
}

export enum StatusEnum {
  NEW = 'new',
  GENERATING = 'generating',
  READY = 'ready',
  SAVED = 'saved',
  FAILED = 'failed',
  INTERRUPTED = 'interrupted',
}

@Entity('image_edit')
export class ImageEditEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', nullable: true })
  @Index('idx_image_edit_original_image_completion_id')
  originalImageCompletionId: string;

  @ManyToOne(() => ImageCompletionEntity, { eager: true, nullable: true })
  originalImageCompletion: ImageCompletionEntity;

  @Column({ type: 'uuid', nullable: true })
  @Index('idx_image_edit_generated_image_completion_id')
  generatedImageCompletionId: string;

  @ManyToOne(() => ImageCompletionEntity, { nullable: false })
  generatedImageCompletion: ImageCompletionEntity;

  @OneToMany(
    () => ImageEditImageCompletionEntity,
    (imageEditImageCompletion) => imageEditImageCompletion.imageEdit,
    { cascade: true, eager: true },
  )
  imageCompletions: ImageEditImageCompletionEntity[];

  @Column({ type: 'uuid', nullable: false })
  @Index('idx_image_edit_user_id')
  userId: string;

  @ManyToOne(() => UserEntity)
  user: UserEntity;

  @Column({ type: 'uuid', nullable: true })
  @Index('idx_image_edit_organization_id')
  organizationId: string;

  @ManyToOne(() => OrganizationEntity)
  organization: OrganizationEntity;

  @OneToMany(
    () => ImageEditModelEntity,
    (imageEditModel) => imageEditModel.imageEdit,
    { cascade: true, eager: true },
  )
  models: ImageEditModelEntity[];

  @Column({ nullable: false, default: StatusEnum.NEW })
  status: string;

  @Column({ type: 'text', nullable: true })
  prompt: string;

  @Column({ type: 'text', nullable: true })
  promptSystem: string;

  @Column({ nullable: false, default: 3 })
  systemVersion?: number;

  @Column({ type: 'enum', enum: EditMode })
  mode: EditMode;

  @Column({ type: 'int', nullable: true })
  width?: number;

  @Column({ type: 'int', nullable: true })
  height?: number;

  @Column({ type: 'text', nullable: true })
  mask?: string;

  @Column({ type: 'text', nullable: true })
  webhookUrl?: string;

  @Column({ type: 'text', nullable: true })
  inputImageUrl?: string;

  @Column({ nullable: true })
  generationSeconds: number;

  @Column({ type: 'json', nullable: true, default: {} })
  settings?: any;

  @Column({ type: 'int', nullable: false })
  imageCompletionsCount: number;

  @Column({ default: false })
  hidePrompt: boolean;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  @Index('idx_image_edit_deleted_at')
  deletedAt?: Date;

  @CreateDateColumn()
  @Index('idx_image_edit_created_at')
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  hasImageCompletion(imageCompletionId: string): boolean {
    return this.imageCompletions.some(
      (imageEditImageCompletion) =>
        imageEditImageCompletion.imageCompletionId === imageCompletionId,
    );
  }
}
