import { Controller, Delete, Request, HttpCode } from '@nestjs/common';
import {
  ApiTags,
  ApiNoContentResponse,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { UserManager } from '../service/manager';
import { UserProvider } from '../service/provider';

@ApiTags('user')
@Controller('users')
export class DeleteController {
  constructor(
    private userManager: UserManager,
    private userProvider: UserProvider,
  ) {}

  @ApiOperation({
    operationId: 'user_delete_current',
    summary: 'Delete current user',
    description:
      'Deletes the account of the current authenticated user.\n\n' +
      'Requirements:\n' +
      '- User must be authenticated',
  })
  @ApiNoContentResponse({ description: 'User deleted successfully.' })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Delete('current')
  @HttpCode(204)
  async deleteCurrent(@Request() req): Promise<void> {
    const user = await this.userProvider.get(req.user.id);

    await this.userManager.delete(user);
  }
}
