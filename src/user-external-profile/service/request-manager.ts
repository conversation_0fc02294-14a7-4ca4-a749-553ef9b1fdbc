import { Injectable } from '@nestjs/common';
import { UserExternalProfileManager } from './manager';
import { UserExternalProfileRequest } from '../request/user-external-profile.request';
import { UserExternalProfileEntity } from '../entity/user-external-profile.entity';
import { UserEntity } from 'src/user/entity/user.entity';

@Injectable()
export class UserExternalProfileRequestManager {
  constructor(private manager: UserExternalProfileManager) {}

  create(
    request: UserExternalProfileRequest,
    user: UserEntity,
  ): Promise<UserExternalProfileEntity> {
    const entity = new UserExternalProfileEntity();

    entity.user = user;

    this.mapRequestData(entity, request);

    return this.manager.create(entity);
  }

  update(
    entity: UserExternalProfileEntity,
    request: UserExternalProfileRequest,
  ): Promise<UserExternalProfileEntity> {
    this.mapRequestData(entity, request);

    return this.manager.update(entity);
  }

  mapRequestData(
    entity: UserExternalProfileEntity,
    request: UserExternalProfileRequest,
  ): void {
    if (request.hasOwnProperty('platform')) {
      entity.platform = request.platform;
    }

    if (request.hasOwnProperty('username')) {
      entity.username = request.username;
    }
    if (request.hasOwnProperty('externalId')) {
      entity.externalId = request.externalId;
    }

    if (request.hasOwnProperty('profileData')) {
      entity.profileData = request.profileData;
    }
  }
}
