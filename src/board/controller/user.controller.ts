import {
  BadRequestException,
  Controller,
  Delete,
  ForbiddenException,
  Get,
  HttpCode,
  ParseUUIDPipe,
  Patch,
  Post,
  Put,
  Request,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  Body,
  Param,
  Query,
  Res,
} from '@nestjs/common/decorators/http/route-params.decorator';
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiQuery,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiUnauthorizedResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import { BaseFindResponseHeadersDto } from 'src/core/dto/base-find-response-headers.dto';
import { BaseSearchRequest } from 'src/core/dto/base.search-request';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { BoardUserCreateRequest } from '../dto/board-user.create-request';
import { BoardUserDto } from '../dto/board-user.dto';
import { BoardUserSearchRequest } from '../dto/board-user.search-request';
import { BoardUserUpdateRequest } from '../dto/board-user.update-request';
import { VisibilityEnum } from '../enum/board-visibility.enum';
import { StatusEnum } from '../enum/boardUser-status.enum';
import { BoardUserProvider } from '../service/board-user.provider';
import { BoardUserRequestManager } from '../service/board-user.request-manager';
import { BoardUserResponseMapper } from '../service/board-user.response-mapper';
import { BoardProvider } from '../service/provider';

@ApiTags('board')
@Controller('boards')
export class UserController {
  constructor(
    private boardProvider: BoardProvider,
    private boardUserRequestManager: BoardUserRequestManager,
    private boardUserProvider: BoardUserProvider,
    private boardUserResponseMapper: BoardUserResponseMapper,
  ) {}

  @Get(':boardId/users')
  @ApiOperation({
    operationId: 'board_user_list',
    summary: 'List users for a board',
    description:
      'Retrieves a paginated list of users for the specified board.\n\n' +
      'Required Parameters:\n' +
      '- boardId: UUID of the board\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of users per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- search: Search term\n' +
      '- userId: UUID of the user\n' +
      '- boardId: UUID of the board\n',
  })
  @ApiOkResponse({
    type: BoardUserDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated list of users for the board.',
  })
  @ApiQuery({
    type: BoardUserSearchRequest,
    description: 'Query parameters for searching and paginating users.',
  })
  @ApiParam({
    name: 'boardId',
    description: 'UUID of the board to retrieve users for.',
    type: String,
  })
  @ApiBadRequestResponse({
    description: `
      Bad Request. Possible reasons:\n
      - \`boardId\`: Must be a valid UUID.
      - \`userId\`: Must be a valid UUID.
      - Invalid query parameters.
      - Invalid body parameters (e.g., isAdmin, status).
      - Missing required fields.
      - Failed to analyze request.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User does not have access to this board.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @UsePipes(new ValidationPipe())
  async find(
    @Request() httpRequest,
    @Query() query: BoardUserSearchRequest,
    @Res() res: Response,
    @Param('boardId', new ParseUUIDPipe()) boardId: string,
  ): Promise<void> {
    const board = await this.boardProvider.get(boardId);

    if (
      board.visibility != VisibilityEnum.PUBLIC &&
      !this.boardUserProvider.isMember(httpRequest.user.id, board.id)
    ) {
      throw new ForbiddenException();
    }

    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;
    const filters = this.validateSearchFilters(
      inputFilters,
      httpRequest.user.id,
      boardId,
    );

    const entities = await this.boardUserProvider.findBy(
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    );
    const totalCount = await this.boardUserProvider.countBy(filters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.boardUserResponseMapper.mapMultiple(entities));
  }

  @Get('users/current/invites')
  @ApiOperation({
    operationId: 'board_user_current_invites',
    summary: "List current user's board invites",
    description:
      'Retrieves a paginated list of pending board invites for the authenticated user.\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of invites per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n',
  })
  @ApiOkResponse({
    type: BoardUserDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated list of pending invites for the user.',
  })
  @ApiQuery({
    type: BaseSearchRequest,
    description: 'Query parameters for searching and paginating invites.',
  })
  @ApiBadRequestResponse({
    description: 'Bad Request. Invalid query parameters.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @UsePipes(new ValidationPipe())
  async invitesUser(
    @Request() httpRequest,
    @Query() query: BaseSearchRequest,
    @Res() res: Response,
  ) {
    const userId = httpRequest.user.id;
    const { page, limit, sortBy, sortOrder } = query;
    const entities = await this.boardUserProvider.findBy(
      { status: StatusEnum.PENDING, userId },
      page,
      limit,
      sortBy,
      sortOrder,
    );
    const totalCount = await this.boardUserProvider.countBy({
      status: StatusEnum.PENDING,
      userId,
    });

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.boardUserResponseMapper.mapMultiple(entities));
  }

  @Get(':boardId/users/invites')
  @ApiOperation({
    operationId: 'board_user_invite_list',
    summary: 'List pending invites for a board',
    description:
      'Retrieves a paginated list of pending invites for the specified board. Only admins can access this endpoint.\n\n' +
      'Required Parameters:\n' +
      '- boardId: UUID of the board\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of invites per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- search: Search term\n' +
      '- userId: UUID of the user\n' +
      '- boardId: UUID of the board\n',
  })
  @ApiOkResponse({
    type: BoardUserDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated list of pending invites for the board.',
  })
  @ApiQuery({
    type: BoardUserSearchRequest,
    description: 'Query parameters for searching and paginating invites.',
  })
  @ApiParam({
    name: 'boardId',
    description: 'UUID of the board.',
    type: String,
  })
  @ApiBadRequestResponse({
    description: `
      Bad Request. Possible reasons:\n
      - \`boardId\`: Must be a valid UUID.
      - \`userId\`: Must be a valid UUID.
      - Invalid query parameters.
      - Invalid body parameters (e.g., isAdmin, status).
      - Missing required fields.
      - Failed to analyze request.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. Only admins can access this endpoint.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @UsePipes(new ValidationPipe())
  async invite(
    @Request() httpRequest,
    @Query() query: BoardUserSearchRequest,
    @Res() res: Response,
    @Param('boardId', new ParseUUIDPipe()) boardId: string,
  ) {
    const board = await this.boardProvider.get(boardId);
    const userId = httpRequest.user.id;

    const isAdmin = await this.boardUserProvider.isAdmin(userId, board.id);

    if (!isAdmin) {
      throw new ForbiddenException();
    }

    const { page, limit, sortBy, sortOrder } = query;
    const entities = await this.boardUserProvider.findBy(
      { status: StatusEnum.PENDING, boardId: boardId },
      page,
      limit,
      sortBy,
      sortOrder,
    );
    const totalCount = await this.boardUserProvider.countBy({
      status: StatusEnum.PENDING,
      boardId: boardId,
    });

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.boardUserResponseMapper.mapMultiple(entities));
  }

  @Post(':boardId/users')
  @ApiOperation({
    operationId: 'board_user_add',
    summary: 'Add user to board',
    description:
      'Adds a user to the specified board.\n\n' +
      'Required Query Parameters:\n' +
      '- boardId: UUID of the board\n' +
      'Required Body Parameters:\n' +
      '- userId: UUID of the user to add\n' +
      '- isAdmin: Whether the user is an admin\n',
  })
  @ApiNoContentResponse({ description: 'User added to board successfully.' })
  @ApiBadRequestResponse({
    description: `
      Bad Request. Possible reasons:\n
      - \`boardId\`: Must be a valid UUID.
      - \`userId\`: Must be a valid UUID.
      - Invalid query parameters.
      - Invalid body parameters (e.g., isAdmin, status).
      - Missing required fields.
      - Failed to analyze request.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User does not have permission to add users.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @HttpCode(204)
  async addUser(
    @Request() httpRequest,
    @Body() body: BoardUserCreateRequest,
    @Param('boardId', new ParseUUIDPipe()) boardId: string,
  ): Promise<void> {
    const board = await this.boardProvider.get(boardId);
    const user = httpRequest.user;

    await this.boardUserRequestManager.addUser(board, body, user);
  }

  @Get(':boardId/users/:userId')
  @ApiOperation({
    operationId: 'board_user_get',
    summary: 'Get user from board',
    description:
      'Retrieves a user from the specified board.\n\n' +
      'Required Parameters:\n' +
      '- boardId: UUID of the board\n' +
      '- userId: UUID of the user\n',
  })
  @ApiOkResponse({
    type: BoardUserDto,
    description: 'Returns the user from the board.',
  })
  @ApiBadRequestResponse({
    description: `
      Bad Request. Possible reasons:\n
      - \`boardId\`: Must be a valid UUID.
      - \`userId\`: Must be a valid UUID.
      - Invalid query parameters.
      - Invalid body parameters (e.g., isAdmin, status).
      - Missing required fields.
      - Failed to analyze request.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User does not have access to this board.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiParam({
    name: 'boardId',
    description: 'UUID of the board.',
    type: String,
  })
  @ApiParam({ name: 'userId', description: 'UUID of the user.', type: String })
  async getUser(
    @Request() httpRequest,
    @Param('boardId', new ParseUUIDPipe()) boardId: string,
    @Param('userId', new ParseUUIDPipe()) userId: string,
  ): Promise<BoardUserDto> {
    const board = await this.boardProvider.get(boardId);
    const currentUser = httpRequest.user;

    if (
      board.visibility != VisibilityEnum.PUBLIC &&
      !(await this.boardUserProvider.isMember(currentUser.id, board.id))
    ) {
      throw new ForbiddenException();
    }

    const boardUser = await this.boardUserProvider.getMember(userId, board.id);

    return await this.boardUserResponseMapper.map(boardUser);
  }

  @Patch(':boardId/users/:userId')
  @ApiOperation({
    operationId: 'board_user_update',
    summary: 'Update user in board',
    description:
      "Updates a user's role or status in the specified board.\n\n" +
      'Required Parameters:\n' +
      '- isAdmin: Whether the user is an admin\n',
  })
  @ApiNoContentResponse({ description: 'User updated successfully.' })
  @ApiBadRequestResponse({
    description: `
      Bad Request. Possible reasons:\n
      - \`boardId\`: Must be a valid UUID.
      - \`userId\`: Must be a valid UUID.
      - Invalid query parameters.
      - Invalid body parameters (e.g., isAdmin, status).
      - Missing required fields.
      - Failed to analyze request.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User does not have permission to update users.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiParam({
    name: 'boardId',
    description: 'UUID of the board.',
    type: String,
  })
  @ApiParam({ name: 'userId', description: 'UUID of the user.', type: String })
  async updateUser(
    @Request() httpRequest,
    @Body() body: BoardUserUpdateRequest,
    @Param('boardId', new ParseUUIDPipe()) boardId: string,
    @Param('userId', new ParseUUIDPipe()) userId: string,
  ): Promise<void> {
    const board = await this.boardProvider.get(boardId);

    if (
      !(await this.boardUserProvider.isManager(httpRequest.user.id, board.id))
    ) {
      throw new ForbiddenException();
    }

    if (
      !(await this.boardUserProvider.isOwner(httpRequest.user.id, board.id))
    ) {
      delete body.isAdmin;
    }

    await this.boardUserRequestManager.updateUser(board, userId, body);
  }

  @Delete(':boardId/users/:userId')
  @ApiOperation({
    operationId: 'board_user_remove',
    summary: 'Remove user from board',
    description:
      'Removes a user from the specified board.\n\n' +
      'Required Parameters:\n' +
      '- boardId: UUID of the board\n' +
      '- userId: UUID of the user to remove\n',
  })
  @ApiNoContentResponse({
    description: 'User removed from board successfully.',
  })
  @ApiBadRequestResponse({
    description: `
      Bad Request. Possible reasons:\n
      - \`boardId\`: Must be a valid UUID.
      - \`userId\`: Must be a valid UUID.
      - Invalid query parameters.
      - Invalid body parameters (e.g., isAdmin, status).
      - Missing required fields.
      - Failed to analyze request.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User does not have permission to remove users.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiParam({
    name: 'boardId',
    description: 'UUID of the board.',
    type: String,
  })
  @ApiParam({
    name: 'userId',
    description: 'UUID of the user to remove.',
    type: String,
  })
  async removeUser(
    @Request() httpRequest,
    @Param('boardId', new ParseUUIDPipe()) boardId: string,
    @Param('userId', new ParseUUIDPipe()) userId: string,
  ): Promise<void> {
    const board = await this.boardProvider.get(boardId);

    if (
      httpRequest.user.id != userId &&
      !(await this.boardUserProvider.isManager(httpRequest.user.id, board.id))
    ) {
      throw new ForbiddenException();
    }

    await this.boardUserRequestManager.removeUser(
      board,
      userId,
      httpRequest.user.id,
    );
  }

  @Put(':boardId/users/:boardUserId/approval')
  @ApiOperation({
    operationId: 'board_user_approval',
    summary: 'Approve board invite or membership',
    description:
      'Approves a pending invite or membership for a user in the specified board.\n\n' +
      'Required Parameters:\n' +
      '- boardId: UUID of the board\n' +
      '- boardUserId: UUID of the board user (invite/membership) to approve\n',
  })
  @ApiNoContentResponse({
    description: 'Invite or membership approved successfully.',
  })
  @ApiBadRequestResponse({
    description:
      'Bad Request. The invite is not pending or parameters are invalid.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User does not have permission to approve invites.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiParam({
    name: 'boardId',
    description: 'UUID of the board.',
    type: String,
  })
  @ApiParam({
    name: 'boardUserId',
    description: 'UUID of the board user to approve.',
    type: String,
  })
  async approval(
    @Request() httpRequest,
    @Param('boardId', new ParseUUIDPipe()) boardId: string,
    @Param('boardUserId', new ParseUUIDPipe()) boardUserId: string,
  ) {
    const board = await this.boardProvider.get(boardId);
    const boardUser = await this.boardUserProvider.get(boardUserId);
    const userId = httpRequest.user.id;

    if (boardUser.status != StatusEnum.PENDING) {
      throw new BadRequestException();
    }

    await this.boardUserRequestManager.approve(board, boardUser, userId);
  }

  @Delete(':boardId/users/:boardUserId/approval')
  @ApiOperation({
    operationId: 'board_user_approval_deny',
    summary: 'Deny board invite or membership',
    description:
      'Denies a pending invite or membership for a user in the specified board.\n\n' +
      'Required Parameters:\n' +
      '- boardId: UUID of the board\n' +
      '- boardUserId: UUID of the board user (invite/membership) to deny\n',
  })
  @ApiNoContentResponse({
    description: 'Invite or membership denied successfully.',
  })
  @ApiBadRequestResponse({
    description:
      'Bad Request. The invite is not pending or parameters are invalid.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User does not have permission to deny invites.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiParam({
    name: 'boardId',
    description: 'UUID of the board.',
    type: String,
  })
  @ApiParam({
    name: 'boardUserId',
    description: 'UUID of the board user to deny.',
    type: String,
  })
  async denied(
    @Request() httpRequest,
    @Param('boardId', new ParseUUIDPipe()) boardId: string,
    @Param('boardUserId', new ParseUUIDPipe()) boardUserId: string,
  ) {
    const board = await this.boardProvider.get(boardId);
    const boardUser = await this.boardUserProvider.get(boardUserId);
    const userId = httpRequest.user.id;

    if (boardUser.status != StatusEnum.PENDING) {
      throw new BadRequestException();
    }

    await this.boardUserRequestManager.deny(board, boardUser, userId);
  }

  validateSearchFilters(
    filters: BoardUserSearchRequest,
    currentUserId: string,
    boardId: string,
  ): any {
    filters.boardId = boardId;
    return filters;
  }
}
