import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1700311202155 implements MigrationInterface {
  name = 'Api1700311202155';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "user_credit_balance"
            ADD "expires_at" TIMESTAMP
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "user_credit_balance" DROP COLUMN "expires_at"
        `);
  }
}
