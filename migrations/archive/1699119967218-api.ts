import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1699119967218 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            INSERT INTO "credit_package"
            (id, name, price, expires_after_months, credit_types)
            VALUES 
            (uuid_generate_v4(), 'Just testing', 4.90, null, '{"IMAGE": 15, "MODEL": 1}'),
            (uuid_generate_v4(), 'Beginner', 9.90, 1, '{"IMAGE": 60, "MODEL": 5}'),
            (uuid_generate_v4(), 'Fun', 24.90, 1, '{"IMAGE": 180, "MODEL": 10}'),
            (uuid_generate_v4(), 'Pro', 74.90, 1, '{"IMAGE": 600, "MODEL": 20}')
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
