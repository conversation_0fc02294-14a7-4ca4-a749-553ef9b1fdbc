import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { ImageGeneratedEvent } from 'src/image-completion/event/image-generated.event';
import { UserManager } from 'src/user/service/manager';
import { UserProvider } from 'src/user/service/provider';
import { CreditTypeEnum } from '../entity/credit-type.enum';
import { TransactionTypeEnum } from '../entity/transaction-type.enum';
import { TransactionManager } from '../service/transaction.manager';

@Injectable()
export class ImageGeneratedListener {
  constructor(
    private userManager: UserManager,
    private userProvider: UserProvider,
    private transactionManager: TransactionManager,
  ) {}

  @OnEvent('image.generated', { async: true })
  async handleImageGeneratedEvent(event: ImageGeneratedEvent) {
    const user = await this.userProvider.get(event.userId);

    if (user.grants.imageGenerated === true) {
      return;
    }

    const amount = 60;

    await this.transactionManager.register(
      TransactionTypeEnum.TOP_UP,
      amount,
      CreditTypeEnum.IMAGE,
      'ImageCompletion|' + event.id,
      true,
      user.id,
    );

    user.grants.imageGenerated = true;

    this.userManager.update(user);
  }
}
