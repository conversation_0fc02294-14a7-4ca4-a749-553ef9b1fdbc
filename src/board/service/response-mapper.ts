import { Injectable } from '@nestjs/common';
import { ImageCompletionDto } from 'src/image-completion/dto/image-completion.dto';
import { ImageCompletionProvider } from 'src/image-completion/service/provider';
import { ImageCompletionResponseMapper } from 'src/image-completion/service/response-mapper';
import { UserResponseMapper } from '../../user/service/response-mapper';
import { BoardDto } from '../dto/board.dto';
import { PublicBoardDto } from '../dto/public.board.dto';
import { BoardEntity } from '../entity/board.entity';
import { BoardUserProvider } from './board-user.provider';

@Injectable()
export class BoardResponseMapper {
  constructor(
    private userResponseMapper: UserResponseMapper,
    private boardUserProvider: BoardUserProvider,
    private imageCompletionProvider: ImageCompletionProvider,
    private imageCompletionResponseMapper: ImageCompletionResponseMapper,
  ) {}

  async map(
    board: BoardEntity,
    includeLatestImages = false,
    currentUserId = null,
  ): Promise<BoardDto> {
    const dto = {
      id: board.id,
      name: board.name,
      description: board.description,
      challengeDeadline: board.challengeDeadline,
      owner: this.userResponseMapper.mapPublic(board.owner),
      isActive: board.isActive,
      members: board.members,
      visibility: board.visibility,
      safety: board.safety,
      createdAt: board.createdAt,
      updatedAt: board.updatedAt,
    } as BoardDto;

    if (board.thumbnail) {
      dto.thumbnail = this.getThumbnailUrls(board);
    }

    if (includeLatestImages) {
      dto.latestImages = await this.findBoardLatestImages(board);
    }

    if (currentUserId) {
      dto.isAdmin = await this.isAdmin(currentUserId, board);
      dto.isMember = dto.isAdmin || (await this.isMember(currentUserId, board));
    }

    return dto;
  }

  async mapMultiple(
    boardEntities: BoardEntity[],
    includeLatestImages = true,
    currentUserId = null,
  ): Promise<BoardDto[]> {
    return Promise.all(
      boardEntities.map((boardEntity) =>
        this.map(boardEntity, includeLatestImages, currentUserId),
      ),
    );
  }

  async mapPublic(
    board: BoardEntity,
    currentUserId = null,
  ): Promise<PublicBoardDto> {
    const dto = {
      id: board.id,
      name: board.name,
      description: board.description,
      owner: this.userResponseMapper.mapPublic(board.owner),
      visibility: board.visibility,
      safety: board.safety,
      members: board.members
    } as PublicBoardDto;

    if (currentUserId) {
      dto.isAdmin = await this.isAdmin(currentUserId, board);
      dto.isMember = dto.isAdmin || (await this.isMember(currentUserId, board));
    }

    if (board.thumbnail) {
      dto.thumbnail = this.getThumbnailUrls(board);
    }

    return dto;
  }

  async isAdmin(currentUserId: string, board: BoardEntity): Promise<boolean> {
    // if (board.boardUsers && board.boardUsers.length > 0) {
    //   return (await board.boardUsers).some(
    //     (member) =>
    //       member.id === currentUserId && (member.isOwner || member.isAdmin),
    //   );
    // }

    return await this.boardUserProvider.isManager(currentUserId, board.id);
  }

  async isMember(currentUserId: string, board: BoardEntity): Promise<boolean> {
    // if (board.boardUsers && board.boardUsers.length > 0) {
    //   return (await board.boardUsers).some(
    //     (member) => member.id === currentUserId,
    //   );
    // }

    return await this.boardUserProvider.isMember(currentUserId, board.id);
  }

  async mapMultiplePublic(
    boardEntities: BoardEntity[],
    currentUserId = null,
  ): Promise<PublicBoardDto[]> {
    return Promise.all(
      boardEntities.map((boardEntity) =>
        this.mapPublic(boardEntity, currentUserId),
      ),
    );
  }

  async findBoardLatestImages(
    board: BoardEntity,
    limit = 6,
  ): Promise<ImageCompletionDto[]> {
    const images = await this.imageCompletionProvider.findLatestImagesByBoard(
      board.id,
      limit,
    );

    return await this.imageCompletionResponseMapper.mapMultiple(images);
  }

  getThumbnailUrls(board: BoardEntity): { [key: string]: any } {
    return this.imageCompletionResponseMapper.generateImageVersions(
      board.thumbnail,
    );
  }
}
