import { ApiProperty } from '@nestjs/swagger';

export class UserNotificationChannelDto {
    @ApiProperty()
    id: string;
  
    @ApiProperty()
    userId: string;
  
    @ApiProperty()
    channel: string;
  
    @ApiProperty({ nullable: true })
    deviceType?: string | null;
  
    @ApiProperty({ nullable: true })
    externalId?: string | null;
  
    @ApiProperty({ nullable: true })
    token?: string | null;
  
    @ApiProperty({ nullable: true })
    expiresAt?: string | null;
  
    @ApiProperty({ type: 'object', nullable: true })
    details?: any[] | null;
  
    @ApiProperty()
    createdAt: string; 
  }
  