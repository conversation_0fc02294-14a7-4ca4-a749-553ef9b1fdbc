import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { UserProvider } from 'src/user/service/provider';
import { UserManager } from '../service/manager';
import {
  PrivacyEnum,
  StatusEnum,
} from 'src/image-completion/entity/image-completion.entity';
import { ImageCompletionProvider } from 'src/image-completion/service/provider';
import { ImageUnpublishedEvent } from 'src/image-completion/event/image-unpublished.event';

@Injectable()
export class ImageUnpublishedListener {
  constructor(
    private userManager: UserManager,
    private userProvider: UserProvider,
    private imageCompletionProvider: ImageCompletionProvider,
  ) {}

  @OnEvent('image.unpublished', { async: true })
  async handleImageUnpublishedEvent(event: ImageUnpublishedEvent) {
    const user = await this.userProvider.get(event.userId);

    const userImagesAvailable = await this.imageCompletionProvider.countBy({
      userId: user.id,
      privacy: PrivacyEnum.PUBLIC,
      status: StatusEnum.READY,
    })

    user.imagesAvailable = userImagesAvailable;

    this.userManager.update(user);
  }
}
