import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1746456832656 implements MigrationInterface {
  name = 'Api1746456832656';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "x_account" text
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "facebook_account" text
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "linkedin_account" text
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "tiktok_account" text
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "instagram_account" text
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "pinterest_account" text
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "github_account" text
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "github_account"
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "pinterest_account"
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "instagram_account"
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "tiktok_account"
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "linkedin_account"
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "facebook_account"
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "x_account"
        `);
  }
}
