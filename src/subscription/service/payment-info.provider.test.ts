import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Logger } from 'nestjs-pino';
import { PaymentInfoProvider } from './payment-info.provider';
import { SubscriptionEntity } from '../entity/subscription.entity';
import { SubscriptionStatusEnum } from '../entity/subscription-status.enum';

describe('PaymentInfoProvider', () => {
  let provider: PaymentInfoProvider;
  let repository: Repository<SubscriptionEntity>;
  let logger: Logger;

  const mockRepository = {
    createQueryBuilder: jest.fn(),
    findOne: jest.fn(),
  };

  const mockLogger = {
    error: jest.fn(),
    log: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PaymentInfoProvider,
        {
          provide: getRepositoryToken(SubscriptionEntity),
          useValue: mockRepository,
        },
        {
          provide: Logger,
          useValue: mockLogger,
        },
      ],
    }).compile();

    provider = module.get<PaymentInfoProvider>(PaymentInfoProvider);
    repository = module.get<Repository<SubscriptionEntity>>(
      getRepositoryToken(SubscriptionEntity),
    );
    logger = module.get<Logger>(Logger);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getPayments', () => {
    it('should return payments for a user', async () => {
      const userId = 'user-123';
      const mockPayments = [
        {
          id: 'payment-1',
          userId,
          price: 100,
          status: SubscriptionStatusEnum.ACTIVE,
          paidAt: new Date(),
        },
      ];

      const mockQueryBuilder = {
        createQueryBuilder: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([mockPayments, 1]),
      };

      mockRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      const result = await provider.getPayments(userId);

      expect(result.payments).toEqual(mockPayments);
      expect(result.total).toBe(1);
      expect(mockRepository.createQueryBuilder).toHaveBeenCalledWith('subscription');
    });

    it('should handle errors gracefully', async () => {
      const userId = 'user-123';
      const error = new Error('Database error');

      mockRepository.createQueryBuilder.mockImplementation(() => {
        throw error;
      });

      await expect(provider.getPayments(userId)).rejects.toThrow('Database error');
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to fetch payments',
        expect.objectContaining({
          userId,
          error: 'Database error',
        }),
      );
    });
  });

  describe('getPaymentById', () => {
    it('should return a specific payment', async () => {
      const userId = 'user-123';
      const paymentId = 'payment-123';
      const mockPayment = {
        id: paymentId,
        userId,
        price: 100,
        status: SubscriptionStatusEnum.ACTIVE,
      };

      mockRepository.findOne.mockResolvedValue(mockPayment);

      const result = await provider.getPaymentById(userId, paymentId);

      expect(result).toEqual(mockPayment);
      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { id: paymentId, userId },
        relations: ['creditPackage'],
      });
    });

    it('should return null if payment not found', async () => {
      const userId = 'user-123';
      const paymentId = 'payment-123';

      mockRepository.findOne.mockResolvedValue(null);

      const result = await provider.getPaymentById(userId, paymentId);

      expect(result).toBeNull();
    });
  });

  describe('getPaymentSummary', () => {
    it('should return payment summary', async () => {
      const userId = 'user-123';
      const mockSummary = {
        totalPaid: '500.00',
        totalPayments: '5',
        averagePayment: '100.00',
        lastPaymentDate: new Date(),
      };

      const mockQueryBuilder = {
        createQueryBuilder: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue(mockSummary),
      };

      mockRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      const result = await provider.getPaymentSummary(userId);

      expect(result.totalPaid).toBe(500);
      expect(result.totalPayments).toBe(5);
      expect(result.averagePayment).toBe(100);
      expect(result.lastPaymentDate).toEqual(mockSummary.lastPaymentDate);
    });
  });
});
