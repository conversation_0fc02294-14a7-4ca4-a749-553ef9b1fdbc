import { Controller, Post, Request } from '@nestjs/common';
import { Body } from '@nestjs/common/decorators/http/route-params.decorator';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { UserProvider } from '../../user/service/provider';
import { BoardDto } from '../dto/board.dto';
import { BoardRequest } from '../dto/board.request';
import { BoardRequestManager } from '../service/request-manager';
import { BoardResponseMapper } from '../service/response-mapper';

@ApiTags('board')
@Controller('boards')
export class CreateController {
  constructor(
    private responseMapper: BoardResponseMapper,
    private requestManager: BoardRequestManager,
    private userProvider: UserProvider,
  ) {}

  @Post()
  @ApiOperation({
    operationId: 'board_create',
    summary: 'Create a new board',
    description:
      'Creates a new board for the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- name: Name of the board\n' +
      '- description: Description of the board\n\n' +
      'Optional Parameters:\n' +
      '- visibility: Visibility of the board (PUBLIC, PRIVATE, HIDDEN)\n' +
      '- safety: Safety level of the board (NSFW, SFW)\n' +
      '- challengeDeadline: Deadline for the challenge\n\n' +
      'Returns the created board object.',
  })
  @ApiBody({
    type: BoardRequest,
    description:
      'Board creation parameters (name, description, visibility, etc).',
  })
  @ApiOkResponse({
    type: BoardDto,
    description: 'Board created successfully.',
  })
  @ApiBadRequestResponse({
    description: `
      Bad Request. Possible reasons:\n
      - \`name\`: Must be a non-empty string.
      - \`description\`: Must be a string.
      - \`visibility\`: Must be one of [PUBLIC, PRIVATE, HIDDEN].
      - \`safety\`: Must be one of [NSFW, SFW].
      - \`challengeDeadline\`: Must be a valid ISO date string.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiInternalServerErrorResponse({
    description:
      'Internal Server Error. Unexpected error occurred during board creation.',
  })
  async create(
    @Body() boardRequest: BoardRequest,
    @Request() req,
  ): Promise<BoardDto> {
    const owner = await this.userProvider.get(req.user.id);

    const board = await this.requestManager.create(boardRequest, owner);

    return this.responseMapper.map(board);
  }
}
