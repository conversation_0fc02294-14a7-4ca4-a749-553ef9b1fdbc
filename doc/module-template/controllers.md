# Controllers

Controllers handle HTTP requests and define the routes for the module.

## Create Controller

Handles POST requests to create new resources.

```typescript
import { Controller, Post, Body, Request } from '@nestjs/common';
import { ApiTags, ApiBody, ApiOkResponse, ApiBadRequestResponse } from '@nestjs/swagger';
import { NewModuleDto } from '../dto/new-module.dto';
import { NewModuleRequest } from '../dto/new-module.request';
import { NewModuleRequestManager } from '../service/new-module.request-manager';
import { NewModuleResponseMapper } from '../service/new-module.response-mapper';

@ApiTags('new-module')
@Controller('new-modules')
export class CreateController {
  constructor(
    private readonly requestManager: NewModuleRequestManager,
    private readonly responseMapper: NewModuleResponseMapper,
  ) {}

  @ApiBody({ type: NewModuleRequest })
  @ApiOkResponse({ type: NewModuleDto })
  @ApiBadRequestResponse()
  @Post()
  async create(
    @Body() requestBody: NewModuleRequest,
    @Request() request,
  ): Promise<NewModuleDto> {
    const user = request.user;
    const newEntity = await this.requestManager.create(requestBody, user);
    return this.responseMapper.map(newEntity);
  }
}
```

## Update Controller

Handles PUT requests to update existing resources.

```typescript
import { Controller, Put, Param, Body, Request, ParseUUIDPipe } from '@nestjs/common';
import { ApiTags, ApiBody, ApiOkResponse, ApiBadRequestResponse, ApiParam } from '@nestjs/swagger';
import { NewModuleDto } from '../dto/new-module.dto';
import { NewModuleRequest } from '../dto/new-module.request';
import { NewModuleRequestManager } from '../service/new-module.request-manager';
import { NewModuleResponseMapper } from '../service/new-module.response-mapper';

@ApiTags('new-module')
@Controller('new-modules')
export class UpdateController {
  constructor(
    private readonly requestManager: NewModuleRequestManager,
    private readonly responseMapper: NewModuleResponseMapper,
  ) {}

  @ApiParam({ name: 'id', description: 'ID of the resource to update' })
  @ApiBody({ type: NewModuleRequest })
  @ApiOkResponse({ type: NewModuleDto })
  @ApiBadRequestResponse()
  @Put(':id')
  async update(
    @Param('id', new ParseUUIDPipe()) id: string,
    @Body() requestBody: NewModuleRequest,
    @Request() request,
  ): Promise<NewModuleDto> {
    const user = request.user;
    const updatedEntity = await this.requestManager.update(id, requestBody, user);
    return this.responseMapper.map(updatedEntity);
  }
}
```

## Delete Controller

Handles DELETE requests to remove resources.

```typescript
import { Controller, Delete, Param, Request, HttpCode, ParseUUIDPipe } from '@nestjs/common';
import { ApiTags, ApiNoContentResponse, ApiBadRequestResponse, ApiParam } from '@nestjs/swagger';
import { NewModuleRequestManager } from '../service/new-module.request-manager';

@ApiTags('new-module')
@Controller('new-modules')
export class DeleteController {
  constructor(
    private readonly requestManager: NewModuleRequestManager,
  ) {}

  @ApiParam({ name: 'id', description: 'ID of the resource to delete' })
  @ApiNoContentResponse({ description: 'Resource deleted successfully' })
  @ApiBadRequestResponse()
  @Delete(':id')
  @HttpCode(204)
  async delete(
    @Param('id', new ParseUUIDPipe()) id: string,
    @Request() request,
  ): Promise<void> {
    const user = request.user;
    await this.requestManager.delete(id, user);
  }
}
```

## Read Controller

Handles GET requests to retrieve resources.

```typescript
import { Controller, Get, Param, Query, Request, Res, ParseUUIDPipe, UsePipes, ValidationPipe } from '@nestjs/common';
import { ApiTags, ApiOkResponse, ApiBadRequestResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { Response } from 'express';
import { NewModuleDto } from '../dto/new-module.dto';
import { NewModuleSearchRequest } from '../dto/new-module.search-request';
import { NewModuleProvider } from '../service/new-module.provider';
import { NewModuleResponseMapper } from '../service/new-module.response-mapper';
import { setPaginationHeaders } from 'src/core/utils/pagination';

@ApiTags('new-module')
@Controller('new-modules')
export class ReadController {
  constructor(
    private readonly provider: NewModuleProvider,
    private readonly responseMapper: NewModuleResponseMapper,
  ) {}

  @ApiOkResponse({ type: NewModuleDto, isArray: true })
  @ApiBadRequestResponse()
  @ApiQuery({ type: NewModuleSearchRequest })
  @Get()
  @UsePipes(new ValidationPipe())
  async find(
    @Query() query: NewModuleSearchRequest,
    @Res() res: Response,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...filters } = query;
    const entities = await this.provider.findBy(filters, page, limit, sortBy, sortOrder);
    const totalCount = await this.provider.countBy(filters);

    setPaginationHeaders(res, totalCount, page, limit);
    res.send(await this.responseMapper.mapMultiple(entities));
  }

  @ApiOkResponse({ type: NewModuleDto })
  @ApiBadRequestResponse()
  @ApiParam({ name: 'id', description: 'ID of the resource to retrieve' })
  @Get(':id')
  async get(
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<NewModuleDto> {
    const entity = await this.provider.get(id);
    return this.responseMapper.map(entity);
  }
}
```
