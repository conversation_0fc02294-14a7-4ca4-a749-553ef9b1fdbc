import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { IsNull, MoreThan, Repository } from 'typeorm';
import { UserEntity } from '../../user/entity/user.entity';
import { OrganizationUserProvider } from '../../organization/service/organization-user.provider';
import { CreditTypeEnum } from '../entity/credit-type.enum';
import { UserCreditBalanceEntity } from '../entity/user-credit-balance.entity';
import { UserCreditBalanceManager } from './user-credit-balance.manager';
import { UserCreditBalanceProvider } from './user-credit-balance.provider';

describe('UserCreditBalanceManager', () => {
  let service: UserCreditBalanceManager;
  let repository: Repository<UserCreditBalanceEntity>;
  let provider: UserCreditBalanceProvider;
  let organizationUserProvider: OrganizationUserProvider;
  let logger: Logger;

  const mockRepository = {
    findOne: jest.fn(),
    save: jest.fn(),
  };

  const mockLogger = {
    log: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserCreditBalanceManager,
        {
          provide: getRepositoryToken(UserCreditBalanceEntity),
          useValue: mockRepository,
        },
        {
          provide: UserCreditBalanceProvider,
          useValue: {
            getNextExpiring: jest.fn(),
          },
        },
        {
          provide: OrganizationUserProvider,
          useValue: {
            getUserOrganizations: jest.fn(),
            isMember: jest.fn(),
            isManager: jest.fn(),
          },
        },
        { provide: Logger, useValue: mockLogger },
      ],
    }).compile();

    service = module.get<UserCreditBalanceManager>(UserCreditBalanceManager);
    repository = module.get<Repository<UserCreditBalanceEntity>>(
      getRepositoryToken(UserCreditBalanceEntity),
    );
    provider = module.get<UserCreditBalanceProvider>(UserCreditBalanceProvider);
    organizationUserProvider = module.get<OrganizationUserProvider>(OrganizationUserProvider);
    logger = module.get<Logger>(Logger);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('deduct', () => {
    it('should deduct balance from the next expiring credit and save it', async () => {
      const user = { id: 'user1' } as UserEntity;
      const creditType = CreditTypeEnum.MODEL;
      const amount = 50;
      const existingBalance = { balance: 100, user } as UserCreditBalanceEntity;

      jest.spyOn(service, 'getNextExpiring').mockResolvedValue(existingBalance);
      mockRepository.save.mockResolvedValue(existingBalance);

      const result = await service.deduct(creditType, amount, user.id);

      expect(service.getNextExpiring).toHaveBeenCalledWith(creditType, user.id, undefined);
      expect(existingBalance.balance).toBe(50);
      expect(mockRepository.save).toHaveBeenCalledWith(existingBalance);
      expect(result.balance).toBe(50);
    });

    it('should throw an error if no credit balance is found', async () => {
      const user = { id: 'user1' } as UserEntity;
      const creditType = CreditTypeEnum.MODEL;
      const amount = 50;

      jest
        .spyOn(service, 'getNextExpiring')
        .mockRejectedValue(new Error('user_credit_balance.not_found'));

      await expect(service.deduct(creditType, amount, user.id)).rejects.toThrow(
        'user_credit_balance.not_found',
      );
    });
  });

  describe('increase', () => {
    it('should increase the balance and create a new entry if necessary', async () => {
      const user = { id: 'user1' } as UserEntity;
      const creditType = CreditTypeEnum.MODEL;
      const amount = 50;
      const expiresAt = new Date('2024-12-31');
      const newBalance = { balance: 0, user } as UserCreditBalanceEntity;

      jest.spyOn(service, 'getOrCreate').mockResolvedValue(newBalance);
      mockRepository.save.mockResolvedValue(newBalance);

      const result = await service.increase(
        creditType,
        amount,
        user.id,
        undefined,
        expiresAt,
      );

      expect(service.getOrCreate).toHaveBeenCalledWith(
        creditType,
        expiresAt,
        user.id,
        undefined,
      );
      expect(newBalance.balance).toBe(50);
      expect(mockRepository.save).toHaveBeenCalledWith(newBalance);
      expect(result.balance).toBe(50);
    });

    it('should increase balance of an existing entry', async () => {
      const user = { id: 'user1' } as UserEntity;
      const creditType = CreditTypeEnum.IMAGE;
      const amount = 30;
      const existingBalance = { balance: 70, user } as UserCreditBalanceEntity;

      jest.spyOn(service, 'getOrCreate').mockResolvedValue(existingBalance);
      mockRepository.save.mockResolvedValue(existingBalance);

      const result = await service.increase(creditType, amount, user.id);

      expect(service.getOrCreate).toHaveBeenCalledWith(
        creditType,
        undefined,
        user.id,
        undefined,
      );
      expect(existingBalance.balance).toBe(100);
      expect(mockRepository.save).toHaveBeenCalledWith(existingBalance);
      expect(result.balance).toBe(100);
    });
  });

  describe('getNextExpiring', () => {
    it('should return the next expiring balance', async () => {
      const user = { id: 'user1' } as UserEntity;
      const creditType = CreditTypeEnum.IMAGE;
      const existingBalance = {
        balance: 100,
        expiresAt: new Date('2024-12-31'),
      } as UserCreditBalanceEntity;

      jest.spyOn(provider, 'getNextExpiring').mockResolvedValue(existingBalance);

      const result = await service.getNextExpiring(creditType, user.id);

      expect(provider.getNextExpiring).toHaveBeenCalledWith(creditType, user.id);
      expect(result).toBe(existingBalance);
    });

    it('should throw an error if no expiring balance is found', async () => {
      const user = { id: 'user1' } as UserEntity;
      const creditType = CreditTypeEnum.MODEL;

      jest.spyOn(provider, 'getNextExpiring').mockRejectedValue(new Error('user_credit_balance.not_found'));

      await expect(service.getNextExpiring(creditType, user.id)).rejects.toThrow(
        'user_credit_balance.not_found',
      );
    });
  });

  describe('getOrCreate', () => {
    it('should create a new balance entry if not found', async () => {
      const user = { id: 'user1' } as UserEntity;
      const creditType = CreditTypeEnum.MODEL;
      const expiresAt = new Date('2024-12-31');
      const newBalance = {
        userId: user.id,
        organizationId: undefined,
        creditType,
        balance: 0,
        expiresAt,
      } as UserCreditBalanceEntity;

      mockRepository.findOne.mockResolvedValue(null);
      mockRepository.save.mockResolvedValue(newBalance);

      const result = await service.getOrCreate(creditType, expiresAt, user.id);

      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: {
          creditType,
          expiresAt,
          userId: user.id,
          organizationId: IsNull()
        },
      });
      expect(mockRepository.save).toHaveBeenCalledWith(expect.objectContaining({
        creditType,
        balance: 0,
        expiresAt,
        userId: user.id,
        organizationId: undefined,
      }));

      // Use toEqual to compare object structure, not reference
      expect(result).toEqual(newBalance);
    });

    it('should return an existing balance entry', async () => {
      const user = { id: 'user1' } as UserEntity;
      const creditType = CreditTypeEnum.MODEL;
      const existingBalance = {
        user,
        creditType,
        balance: 100,
      } as UserCreditBalanceEntity;

      mockRepository.findOne.mockResolvedValue(existingBalance);

      const result = await service.getOrCreate(creditType, undefined, user.id);

      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: {
          creditType,
          expiresAt: IsNull(),
          userId: user.id,
          organizationId: IsNull()
        },
      });
      expect(result).toBe(existingBalance);
    });
  });
});
