import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsInt,
  IsOptional,
  IsUUI<PERSON>,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { BaseSearchRequest } from '../../core/dto/base.search-request';
import { Transform } from 'class-transformer';

export class LikesRankingSearchRequest extends BaseSearchRequest {
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(50)
  @Transform((obj) => Number(obj.value))
  @ApiProperty({
    description: 'Number of items per page',
    required: false,
    default: 25,
  })
  limit?: number = 25;

  @IsOptional()
  @IsUUID()
  @ApiProperty({ required: false })
  userId?: string;

  // eslint-disable-next-line @typescript-eslint/no-inferrable-types
  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  day: boolean = false;

  // eslint-disable-next-line @typescript-eslint/no-inferrable-types
  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  week: boolean = false;

  // eslint-disable-next-line @typescript-eslint/no-inferrable-types
  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  month: boolean = false;

  // eslint-disable-next-line @typescript-eslint/no-inferrable-types
  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  year: boolean = false;
}
