import {
  BadRequestException,
  Controller,
  Get,
  HttpCode,
  NotFoundException,
  Param,
  Post,
  Req,
  Res,
  UnauthorizedException,
} from '@nestjs/common';
import { ApiOkResponse } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { Logger } from 'nestjs-pino';
import { Public } from 'src/core/security/public-routes';
import { UserAccessTokenDto } from '../dto/user-access-token.dto';
import { SamlConfigService } from '../service/saml-config.service';
import { SamlService } from '../service/saml.service';

@Controller()
export class SamlController {
  constructor(
    private readonly samlConfigService: SamlConfigService,
    private readonly samlService: SamlService,
    private readonly logger: Logger,
  ) {}

  @Get('saml/:name/login')
  @Public()
  async login(
    @Param('name') name: string,
    @Res() res: Response,
  ): Promise<void> {
    const idpConfig = await this.samlConfigService.getIdPConfig(name);

    if (!idpConfig) {
      throw new NotFoundException(`IdP configuration for "${name}" not found.`);
    }

    const loginUrl = await this.samlService.createLoginRequestUrl(idpConfig);
    res.redirect(loginUrl);
  }

  @Post('saml/acs')
  @ApiOkResponse({ type: UserAccessTokenDto })
  @Public()
  @HttpCode(200)
  async acs(@Req() req: Request): Promise<UserAccessTokenDto> {
    const samlResponse = req.body.SAMLResponse;
    if (!samlResponse) {
      throw new BadRequestException('SAMLResponse not found in request body');
    }

    try {
      return await this.samlService.processSamlResponse(samlResponse);
    } catch (e) {
      this.logger.error('saml.invalid_response', {
        body: req.body,
        error: e,
      });

      this.logger.warn('saml.auth_failed', { error: e.message });

      throw new UnauthorizedException('saml.auth_failed');
    }
  }

  @Get(['saml/:name/metadata', 'saml-metadata/:name.xml'])
  @Public()
  async getMetadata(
    @Param('name') name: string,
    @Res() res: Response,
  ): Promise<void> {
    const idpConfig = await this.samlConfigService.getIdPConfig(name);

    if (!idpConfig) {
      this.logger.log(`IdP configuration for "${name}" not found.`);
      throw new NotFoundException(`IdP configuration for "${name}" not found.`);
    }

    const metadataXML = this.samlConfigService.generateMetadataXML(idpConfig);

    // Set the response content type to XML
    res.type('application/xml');
    res.send(metadataXML);
  }
}
