import { Injectable, ExecutionContext } from '@nestjs/common';
import { JwtAuthGuard } from './jwt-auth.guard';
import { ApiKeyGuard } from './api-key.guard';
import { JwtIntegrationAuthGuard } from './jwt-integration-auth.guard';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class CombinedGuard {
  constructor(
    private readonly jwtAuthGuard: JwtAuthGuard,
    private readonly jwtIntegrationAuthGuard: JwtIntegrationAuthGuard,
    private readonly apiKeyGuard: ApiKeyGuard,
    private readonly configService: ConfigService,
  ) {}

  canActivate(context: ExecutionContext) {
    const request = context.switchToHttp().getRequest();

    const isPublicEnabled = this.configService.get<boolean>(
      'CONTROLLERS_ENABLE_PUBLIC',
    );


    if (isPublicEnabled) {
      return this.jwtIntegrationAuthGuard.canActivate(context);
    }

    const isPrivateEnabled = this.configService.get<boolean>(
      'CONTROLLERS_ENABLE_PRIVATE',
    );

    if (isPrivateEnabled) {
      if (request.path.startsWith('/internal')) {
        return this.apiKeyGuard.canActivate(context);
      }

      if (
        request.path.startsWith('/images') ||
        request.path.startsWith('/public')
      ) {
        return this.jwtIntegrationAuthGuard.canActivate(context);
      }

      return this.jwtAuthGuard.canActivate(context);
    }
  }
}
