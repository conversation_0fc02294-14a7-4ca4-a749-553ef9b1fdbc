import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1692020147225 implements MigrationInterface {
    name = 'Api1692020147225'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "image_completion"
            ADD "is_nsfw" boolean NOT NULL DEFAULT true
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "image_completion" DROP COLUMN "is_nsfw"
        `);
    }

}
