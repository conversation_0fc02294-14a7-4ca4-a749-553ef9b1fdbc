import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { Logger } from 'nestjs-pino';
import { ImageCompletionProvider } from 'src/image-completion/service/provider';
import { ImageCompletionResponseMapper } from 'src/image-completion/service/response-mapper';
import { ModelResponseMapper } from 'src/model/service/response-mapper';
import { ImageEditDto } from '../dto/image-edit.dto';
import { ImageEditInternalDto } from '../dto/image-edit.internal-dto';
import { ImageEditEntity } from '../entity/image-edit.entity';

@Injectable()
export class ImageEditResponseMapper {
  constructor(
    @Inject(forwardRef(() => ImageCompletionResponseMapper))
    private readonly imageCompletionResponseMapper: ImageCompletionResponseMapper,
    private readonly imageCompletionProvider: ImageCompletionProvider,
    private readonly modelResponseMapper: ModelResponseMapper,
    private readonly logger: Logger,
  ) {}

  async map(
    entity: ImageEditEntity,
    isInternal = false,
    allowNesting = true,
    includeImageEdits = true,
  ): Promise<ImageEditDto> {
    const dto = {
      id: entity.id,
      mode: entity.mode,
      imageCompletionsCount: entity.imageCompletionsCount,
      width: entity.width,
      height: entity.height,
      mask: entity.mask,
      inputImageUrl: entity.inputImageUrl,
      status: entity.status,
      settings: entity.settings,
      webhookUrl: entity.webhookUrl,
      hidePrompt: entity.hidePrompt,
    } as ImageEditDto;

    if (!entity.hidePrompt) {
      dto.prompt = entity.prompt;
    }

    if (entity.originalImageCompletionId) {
      try {
        const originalImageCompletion =
          entity.originalImageCompletion ??
          (await this.imageCompletionProvider.get(
            entity.originalImageCompletionId,
          ));

        dto.originalImageCompletion = isInternal
          ? await this.imageCompletionResponseMapper.mapInternal(
              originalImageCompletion,
            )
          : await this.imageCompletionResponseMapper.map(
              originalImageCompletion,
              true,
              true,
              null,
              allowNesting,
            );
      } catch (error) {
        this.logger.debug('Error mapping imageEdit.originalImageCompletion', {
          image_edit_id: entity.id,
          original_image_completion_id: entity.originalImageCompletionId,
          error: error.message,
        });
      }
    }

    if (entity.generatedImageCompletionId) {
      try {
        const generatedImageCompletion =
          entity.generatedImageCompletion ??
          (await this.imageCompletionProvider.get(
            entity.generatedImageCompletionId,
          ));

        dto.generatedImageCompletion = isInternal
          ? await this.imageCompletionResponseMapper.mapInternal(
              generatedImageCompletion,
            )
          : await this.imageCompletionResponseMapper.map(
              generatedImageCompletion,
              true,
              true,
              null,
              allowNesting,
            );
      } catch (error) {
        this.logger.debug('Error mapping imageEdit.generatedImageCompletion', {
          image_edit_id: entity.id,
          original_image_completion_id: entity.generatedImageCompletionId,
          error: error.message,
        });
      }
    }

    if (entity.imageCompletions) {
      dto.imageCompletionChoices = [];
      for (const imageCompletion of entity.imageCompletions) {
        try {
          const mappedCompletion = isInternal
            ? await this.imageCompletionResponseMapper.mapInternal(
                imageCompletion.imageCompletion,
              )
            : await this.imageCompletionResponseMapper.map(
                imageCompletion.imageCompletion,
                true,
                true,
                null,
                allowNesting,
              );

          if (!isInternal && mappedCompletion && includeImageEdits) {
            mappedCompletion.imageEdit = await this.map(
              entity,
              false,
              false,
              false, // Prevent further nesting of image edits
            );
          }

          dto.imageCompletionChoices.push(mappedCompletion);
        } catch (error) {
          this.logger.debug('Error mapping imageEdit.imageCompletionChoices', {
            image_edit_id: entity.id,
            image_completion_id: imageCompletion?.id,
            error: error.message,
          });
        }
      }
    }

    dto.models = [];
    if (entity.models?.length) {
      for (const imageEditModel of entity.models) {
        try {
          dto.models.push(
            await this.modelResponseMapper.mapInternal(imageEditModel.model),
          );
        } catch (error) {
          this.logger.debug('Error mapping image edit models', {
            image_edit_id: entity.id,
            image_edit_model_id: imageEditModel?.id,
            image_edit_model: imageEditModel,
            error: error.message,
          });
        }
      }
    }

    return dto;
  }

  async mapMultiple(entities: ImageEditEntity[]): Promise<ImageEditDto[]> {
    return Promise.all(entities.map((entity) => this.map(entity)));
  }

  async mapInternal(entity: ImageEditEntity): Promise<ImageEditInternalDto> {
    const dto = (await this.map(entity, true)) as ImageEditInternalDto;
    dto.systemVersion = entity.systemVersion;
    dto.promptSystem = entity.promptSystem;

    return dto;
  }

  async mapMultipleInternal(
    entities: ImageEditEntity[],
  ): Promise<ImageEditDto[]> {
    return Promise.all(entities.map((entity) => this.mapInternal(entity)));
  }
}
