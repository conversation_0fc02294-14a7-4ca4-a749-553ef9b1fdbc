import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1752072850654 implements MigrationInterface {
  name = 'Api1752072850654';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE "chat_message" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "content" character varying NOT NULL,
                "available_images" jsonb,
                "role" character varying NOT NULL,
                "conversation_id" uuid NOT NULL,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_3cc0d85193aade457d3077dd06b" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            CREATE TABLE "chat_conversation" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "title" character varying NOT NULL,
                "user_id" uuid,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_0c5b7697e69f674eb983b1e83cc" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            CREATE INDEX "IDX_3cb139d8e2ef1c42195f33af75" ON "chat_conversation" ("user_id")
        `);
    await queryRunner.query(`
            ALTER TABLE "chat_message"
            ADD CONSTRAINT "FK_aa14fc981646b7f4806f8b2b379" FOREIGN KEY ("conversation_id") REFERENCES "chat_conversation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "chat_message" DROP CONSTRAINT "FK_aa14fc981646b7f4806f8b2b379"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."IDX_3cb139d8e2ef1c42195f33af75"
        `);
    await queryRunner.query(`
            DROP TABLE "chat_conversation"
        `);
    await queryRunner.query(`
            DROP TABLE "chat_message"
        `);
  }
}
