import { UserEntity } from '../../user/entity/user.entity';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { ModelCommentEntity } from './model-comment.entity';

@Entity('model_comment_like')
export class ModelCommentLikeEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => ModelCommentEntity)
  modelComment: ModelCommentEntity;

  @Column({ type: 'uuid', nullable: false })
  @Index('idx_model_comment_like_model_comment_id')
  modelCommentId: string;

  @ManyToOne(() => UserEntity)
  user: UserEntity;

  @Column({ type: 'uuid', nullable: false })
  @Index('idx_model_comment_like_user_id')
  userId: string;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt?: Date;

  @CreateDateColumn()
  createdAt: Date;
}
