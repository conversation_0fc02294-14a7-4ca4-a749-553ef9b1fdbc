import { Injectable } from '@nestjs/common';
import { AbstractProvider } from 'src/core/service/abstract.provider';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, FindOneOptions, Repository } from 'typeorm';
import { Logger } from 'nestjs-pino';
import { ImageCompletionCommentLikeEntity } from '../entity/image-completion-comment-like.entity';

@Injectable()
export class ImageCompletionCommentLikeProvider extends AbstractProvider<ImageCompletionCommentLikeEntity> {
  constructor(
    @InjectRepository(ImageCompletionCommentLikeEntity)
    repository: Repository<ImageCompletionCommentLikeEntity>,
    logger: Logger,
  ) {
    super(repository, logger);
  }

  prepareFindOneOptions(
    criteria: any,
  ): FindOneOptions<ImageCompletionCommentLikeEntity> {
    return {
      where: criteria,
    };
  }

  prepareFindManyOptions(
    criteria: any,
    page: number,
    limit: number,
    sortBy?: string,
    sortOrder?: string,
  ): FindManyOptions<ImageCompletionCommentLikeEntity> {
    if (criteria.username) {
      criteria.user = {
        username: criteria.username,
      };

      delete criteria.username;
    }

    const parentOptions = super.prepareFindManyOptions(
      criteria,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    return {
      ...parentOptions,
      relations: {
        user: true,
      },
    };
  }
}
