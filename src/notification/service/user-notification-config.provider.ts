import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { AbstractProvider } from 'src/core/service/abstract.provider';
import { Repository } from 'typeorm';
import { UserNotificationConfigEntity } from '../entity/user-notification-config.entity';

@Injectable()
export class UserNotificationConfigProvider extends AbstractProvider<UserNotificationConfigEntity> {
  constructor(
    @InjectRepository(UserNotificationConfigEntity)
    repository: Repository<UserNotificationConfigEntity>,
    logger: Logger,
  ) {
    super(repository, logger);
  }
}
