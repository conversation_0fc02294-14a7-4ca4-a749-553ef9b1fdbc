import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

import { IsOptional, IsUUID } from 'class-validator';

export class UserSignupDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsUUID()
  organizationId?: string;
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  username: string;

  @ApiProperty()
  email: string;

  @ApiProperty()
  signupCode: string;

  @ApiProperty()
  isEmailValidated: boolean;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  isBot: boolean;
}
