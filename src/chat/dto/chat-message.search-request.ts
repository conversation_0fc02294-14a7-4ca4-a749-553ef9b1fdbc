import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsUUID } from 'class-validator';
import { BaseSearchRequest } from 'src/core/dto/base.search-request';

enum MessageRole {
  USER = 'user',
  ASSISTANT = 'assistant',
}

export class ChatMessageSearchRequest extends BaseSearchRequest {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsUUID()
  id?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsUUID()
  conversationId?: string;

  @ApiProperty({ required: false, enum: MessageRole })
  @IsOptional()
  @IsEnum(MessageRole)
  role?: MessageRole;
}
