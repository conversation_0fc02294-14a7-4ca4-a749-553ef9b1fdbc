import {
  <PERSON>,
  Get,
  Param,
  <PERSON>rseUUIDPipe,
  Query,
  <PERSON>s,
  UsePipes,
  ValidationPipe,
  Request,
  HttpCode,
  Put,
  Delete,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiQuery,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import { ModelCommentProvider } from '../service/model-comment.provider';
import { ModelCommentLikeProvider } from '../service/model-comment-like.provider';
import { ModelCommentLikeRequestManager } from '../service/model-comment-like.request-manager';
import { ModelCommentLikeResponseMapper } from '../service/model-comment-like.response-mapper';
import { ModelCommentLikeDto } from '../dto/model-comment-like.dto';
import { BaseFindResponseHeadersDto } from 'src/core/dto/base-find-response-headers.dto';
import { ModelCommentLikeSearchRequest } from '../dto/model-comment-like.search-request';
import { setPaginationHeaders } from 'src/core/utils/pagination';

@ApiTags('model_comment / likes')
@Controller('model_comment')
export class CommentLikeController {
  constructor(
    private modelCommentProvider: ModelCommentProvider,
    private modelCommentLikeProvider: ModelCommentLikeProvider,
    private requestManager: ModelCommentLikeRequestManager,
    private responseMapper: ModelCommentLikeResponseMapper,
  ) {}

  @Get(':id/likes')
  @ApiOperation({
    operationId: 'model_comment_like_list',
    summary: 'List likes for a model comment',
    description:
      'Retrieves a paginated list of likes for the specified model comment.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the model comment\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of likes per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- username: Username of the user\n' +
      '- userId: UUID of the user\n',
  })
  @ApiOkResponse({
    type: ModelCommentLikeDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated list of likes for the comment.',
  })
  @ApiQuery({
    type: ModelCommentLikeSearchRequest,
    description: 'Query parameters for searching and paginating likes.',
  })
  @ApiParam({ name: 'id', description: 'UUID of the model comment.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - Filter parameters must be valid values.
      - \`userId\`: Must be a valid UUID.
      - \`username\`: Must be a valid username.
      - Comment does not exist.
      - Invalid filter values (e.g., userId, username).
      - Failed to analyze request.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User does not have access to this comment.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @UsePipes(new ValidationPipe())
  async find(
    @Query() query: ModelCommentLikeSearchRequest,
    @Res() res: Response,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;
    const filters = this.validateSearchFilters(inputFilters, id);

    const entities = await this.modelCommentLikeProvider.findBy(
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    const totalCount = await this.modelCommentLikeProvider.countBy(filters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultiple(entities));
  }

  @Get('likes')
  @ApiOperation({
    operationId: 'model_comment_like_list_user',
    summary: 'List likes by current user',
    description:
      'Retrieves a paginated list of likes made by the authenticated user on model comments.\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of likes per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- username: Username of the user\n' +
      '- userId: UUID of the user\n',
  })
  @ApiOkResponse({
    type: ModelCommentLikeDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated list of likes by the user.',
  })
  @ApiQuery({
    type: ModelCommentLikeSearchRequest,
    description: 'Query parameters for searching and paginating likes.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - Filter parameters must be valid values.
      - \`userId\`: Must be a valid UUID.
      - \`username\`: Must be a valid username.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @UsePipes(new ValidationPipe())
  async findLikes(
    @Query() query: ModelCommentLikeSearchRequest,
    @Request() request,
    @Res() res: Response,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;

    delete inputFilters.username;
    inputFilters.userId = request.user.id;

    const entities = await this.modelCommentLikeProvider.findBy(
      inputFilters,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    const totalCount = await this.modelCommentLikeProvider.countBy(
      inputFilters,
    );

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultiple(entities, true));
  }

  @Put(':id/likes')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'model_comment_like',
    summary: 'Like a model comment',
    description:
      'Likes the specified model comment as the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the model comment to like\n',
  })
  @ApiNoContentResponse({ description: 'Comment liked successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - Comment does not exist.
      - User has already liked the comment.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiParam({ name: 'id', description: 'UUID of the model comment to like.' })
  async like(@Request() request, @Param('id', new ParseUUIDPipe()) id: string) {
    const entity = await this.modelCommentProvider.getBy({ id: id });
    await this.requestManager.like(entity, request.user.id);
  }

  @Delete(':id/likes')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'model_comment_unlike',
    summary: 'Unlike a model comment',
    description:
      'Removes the like from the specified model comment as the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the model comment to unlike\n',
  })
  @ApiNoContentResponse({ description: 'Comment unliked successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - Comment does not exist.
      - User has not liked the comment.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiParam({ name: 'id', description: 'UUID of the model comment to unlike.' })
  async unlike(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ) {
    const entity = await this.modelCommentProvider.getBy({ id: id });
    await this.requestManager.unlike(entity, request.user.id);
  }

  validateSearchFilters(
    filters: ModelCommentLikeSearchRequest,
    modelCommentId: string,
  ): any {
    return { ...filters, modelCommentId: modelCommentId };
  }
}
