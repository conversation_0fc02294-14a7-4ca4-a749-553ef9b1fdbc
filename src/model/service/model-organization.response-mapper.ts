import { Injectable } from '@nestjs/common';
import { OrganizationResponseMapper } from 'src/organization/service/response-mapper';
import { UserResponseMapper } from 'src/user/service/response-mapper';
import {
  ModelOrganizationDto,
  ModelOrganizationListDto,
} from '../dto/model-organization.dto';
import { ModelOrganizationEntity } from '../entity/model-organization.entity';

@Injectable()
export class ModelOrganizationResponseMapper {
  constructor(
    private organizationResponseMapper: OrganizationResponseMapper,
    private userResponseMapper: UserResponseMapper,
  ) {}

  /**
   * Map a single ModelOrganizationEntity to ModelOrganizationDto
   */
  async map(entity: ModelOrganizationEntity): Promise<ModelOrganizationDto> {
    const dto = new ModelOrganizationDto();

    dto.id = entity.id;
    dto.createdAt = entity.createdAt;
    dto.updatedAt = entity.updatedAt;

    // Map organization
    if (entity.organization) {
      dto.organization = await this.organizationResponseMapper.mapPublic(
        entity.organization,
      );
    }

    // Map shared by user
    if (entity.sharedByUser) {
      dto.sharedByUser = await this.userResponseMapper.mapPublic(
        entity.sharedByUser,
      );
    }

    return dto;
  }

  /**
   * Map multiple ModelOrganizationEntity to ModelOrganizationDto array
   */
  async mapMultiple(
    entities: ModelOrganizationEntity[],
  ): Promise<ModelOrganizationDto[]> {
    return Promise.all(entities.map((entity) => this.map(entity)));
  }

  /**
   * Map model organizations to a list DTO
   */
  async mapToListDto(
    modelId: string,
    entities: ModelOrganizationEntity[],
  ): Promise<ModelOrganizationListDto> {
    const dto = new ModelOrganizationListDto();

    dto.modelId = modelId;
    dto.organizations = await this.mapMultiple(entities);

    return dto;
  }
}
