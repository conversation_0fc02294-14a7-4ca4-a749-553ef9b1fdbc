import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1699307922534 implements MigrationInterface {
    name = 'Api1699307922534'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "subscription"
            ADD "stripe_checkout_session_id" character varying
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "subscription" DROP COLUMN "stripe_checkout_session_id"
        `);
    }

}
