import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsEnum, IsOptional } from 'class-validator';
import { BaseSearchRequest } from 'src/core/dto/base.search-request';
import { StatusEnum } from '../../../image-edit/entity/image-edit.entity';

export class ImageEditSearchRequest extends BaseSearchRequest {
  @IsOptional()
  @IsEnum(StatusEnum)
  @Transform(({ value }) => value.toLowerCase())
  @ApiProperty({ enum: StatusEnum })
  status?: StatusEnum;
}
