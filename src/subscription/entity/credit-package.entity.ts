import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { CreditTypeEnum } from './credit-type.enum';

@Entity('credit_package')
export class CreditPackageEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  stripeProductId?: string;

  @Column({ nullable: true })
  stripePriceId?: string;

  @Column()
  price: number;

  @Column({ default: 'eur' })
  currency: string;

  @Column({ nullable: true })
  seats?: number;

  @Column({ nullable: true })
  expiresAfterMonths?: number;

  @Column({ nullable: true })
  expiresAfterDays?: number;

  @Column({ default: 'user' })
  targetEntity: string;

  @Column({ default: true })
  isRecurring: boolean;

  @Column({ default: true })
  isVisible: boolean;

  @Column('json')
  creditTypes: { [key in CreditTypeEnum]: number };
}
