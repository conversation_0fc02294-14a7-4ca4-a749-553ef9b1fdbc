import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1753299475235 implements MigrationInterface {
  name = 'Api1753299475235';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "model"
            ADD "training_mode" character varying
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "model" DROP COLUMN "training_mode"
        `);
  }
}
