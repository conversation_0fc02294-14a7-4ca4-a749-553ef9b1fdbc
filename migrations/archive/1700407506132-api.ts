import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1700407506132 implements MigrationInterface {
  name = 'Api1700407506132';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "subscription"
            ADD "deleted_at" TIMESTAMP
        `);
    await queryRunner.query(`
            ALTER TABLE "transaction"
            ADD "deleted_at" TIMESTAMP
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "transaction" DROP COLUMN "deleted_at"
        `);
    await queryRunner.query(`
            ALTER TABLE "subscription" DROP COLUMN "deleted_at"
        `);
  }
}
