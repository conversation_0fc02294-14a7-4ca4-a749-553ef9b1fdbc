import {
  <PERSON>,
  Get,
  Param,
  ParseU<PERSON><PERSON><PERSON><PERSON>,
  Query,
  Request,
  Res,
  UnauthorizedException,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Response } from 'express';
import { JwtIntegrationAuthGuard } from 'src/auth/service/jwt-integration-auth.guard';
import { BaseFindResponseHeadersDto } from 'src/core/dto/base-find-response-headers.dto';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { PrivacyEnum } from 'src/model/entity/model.entity';
import { StatusEnum } from 'src/model/enum/status.enum';
import { ModelProvider } from 'src/model/service/provider';
import { UserEntity } from 'src/user/entity/user.entity';
import { OrganizationUserProvider } from 'src/organization/service/organization-user.provider';
import { ModelDto } from '../dto/model.dto';
import { ModelSearchRequest } from '../dto/model.search-request';
import { ModelResponseMapper } from '../service/response-mapper';

@ApiTags('models')
@Controller('models')
export class ReadController {
  constructor(
    private provider: ModelProvider,
    private responseMapper: ModelResponseMapper,
    private organizationUserProvider: OrganizationUserProvider,
  ) {}

  @ApiOperation({
    operationId: 'models_find',
    summary: 'Find models',
    description:
      'Retrieves a list of models based on search criteria.\n\n' +
      'Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Items per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort direction (ASC, DESC)\n' +
      '- search: General search term\n' +
      '- name: Filter by model name\n' +
      '- userId: Filter by user ID\n' +
      '- username: Filter by username\n' +
      '- class: Filter by model class\n' +
      '- type: Filter by model type\n' +
      '- description: Filter by model description\n' +
      '- privacy: Filter by privacy level (public, private, licensed)\n' +
      '- systemVersion: Filter by system version\n' +
      '- isActive: Filter by active status\n' +
      '- status: Filter by status (see Status Values)\n\n' +
      'Status Values:\n' +
      '- new: Initial state after creation\n' +
      '- pending: Waiting for training to start\n' +
      '- training: Currently being trained\n' +
      '- finished: Training completed\n' +
      '- available: Ready for public use\n' +
      '- failed: Training failed\n\n' +
      'Response Headers:\n' +
      '- X-Total-Count: Total count of items\n' +
      '- X-Current-Page: Current page of pagination\n' +
      '- X-Per-Page: Results per page\n' +
      '- X-Total-Pages: Total count of pages',
  })
  @ApiQuery({
    type: ModelSearchRequest,
    description: 'Search parameters for filtering models',
  })
  @ApiOkResponse({
    type: ModelDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'List of models matching the search criteria.',
  })
  @ApiResponse({
    status: 400,
    description: `
      Bad Request. Possible reasons:
      - Invalid query parameters
      - Invalid pagination values
      - Invalid sort parameters
      - Malformed filters
    `,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden. User does not have permission to access these models.',
  })
  @ApiResponse({
    status: 422,
    description:
      'Unprocessable Entity. The search request contains invalid data.',
  })
  @ApiResponse({
    status: 500,
    description:
      'Internal Server Error. An unexpected error occurred while retrieving models.',
  })
  @ApiBearerAuth()
  @Get()
  @UsePipes(new ValidationPipe())
  @UseGuards(JwtIntegrationAuthGuard)
  async find(
    @Request() request,
    @Query() query: ModelSearchRequest,
    @Res() res: Response,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;
    const filters = await this.validateSearchFilters(inputFilters, request.user);

    const entities = await this.provider.findBy(
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    );
    const totalCount = await this.provider.countBy(filters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultiple(entities));
  }

  @ApiOperation({
    operationId: 'models_get',
    summary: 'Get a model',
    description:
      'Retrieves detailed information about a specific model.\n\n' +
      'Path Parameters:\n' +
      '- id: UUID of the model to retrieve\n',
  })
  @ApiParam({
    name: 'id',
    description: 'The unique identifier of the model to retrieve',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponse({
    type: ModelDto,
    description: 'The requested model details.',
  })
  @ApiResponse({
    status: 400,
    description: `
      Bad Request. Possible reasons:
      - Invalid ID format
      - Model does not exist
    `,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden. User does not have permission to view this model.',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found. The requested model could not be found.',
  })
  @ApiResponse({
    status: 500,
    description:
      'Internal Server Error. An unexpected error occurred while retrieving the model.',
  })
  @ApiBearerAuth()
  @Get(':id')
  @UseGuards(JwtIntegrationAuthGuard)
  async get(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<ModelDto> {
    const entity = await this.provider.get(id);

    // Check if user can access this model
    const canAccess = await this.provider.isModelAccessibleToUser(id, request.user);
    if (!canAccess) {
      throw new UnauthorizedException(
        "You are not allowed to view this model.",
      );
    }

    return await this.responseMapper.map(entity);
  }

  async validateSearchFilters(
    filters: ModelSearchRequest,
    currentUser: UserEntity,
  ): Promise<any> {
    const hasUserFilter =
      (filters.hasOwnProperty('userId') && filters.userId !== currentUser.id) ||
      (filters.hasOwnProperty('username') &&
        filters.username !== currentUser.username);

    if (hasUserFilter) {
      // If the privacy filter is set to private and the user is not the owner, throw an exception
      if (
        filters.hasOwnProperty('privacy') &&
        filters.privacy !== PrivacyEnum.PUBLIC
      ) {
        throw new UnauthorizedException(
          "You are not allowed to view other users' private models.",
        );
      }

      // if an user filter is set, and it's different than the current user, then allow for only publicly available models
      filters.privacy = PrivacyEnum.PUBLIC;
      filters.status = StatusEnum.AVAILABLE;

      return filters;
    }

    // if privacy is set, then we can only show the current user's models
    if (
      filters.hasOwnProperty('privacy') &&
      PrivacyEnum.PUBLIC !== filters.privacy
    ) {
      filters.userId = currentUser.id;
    }

    // Get user's organization IDs for organization-based access
    const userOrganizations = await this.organizationUserProvider.getUserOrganizations(currentUser.id);
    const userOrgIds = userOrganizations.map(org => org.organizationId);

    // we show publicly available models, the current user's own models, and organization-shared models
    const baseFilters = [
      { ...filters, userId: currentUser.id },
      {
        ...filters,
        privacy: PrivacyEnum.PUBLIC,
        status: StatusEnum.AVAILABLE,
      },
    ];

    // Add organization-based access if user belongs to any organizations
    if (userOrgIds.length > 0) {
      baseFilters.push({
        ...filters,
        privacy: PrivacyEnum.PRIVATE,
        status: StatusEnum.AVAILABLE,
        // organizationIds will be handled separately in the provider
      });
    }

    return baseFilters;
  }
}
