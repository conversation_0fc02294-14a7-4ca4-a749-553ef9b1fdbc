import { DynamicModule, Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ImageCompletionModule } from 'src/image-completion/module';
import { ModelModule } from 'src/model/module';
import { jwtConfig } from '../auth/config/jwt.config';
import { UserModule } from '../user/user.module';
import { ReadController } from './controller/internal/read.controller';
import { StatisticsResponseMapper } from './service/response-mapper';
import { StatisticsProvider } from './service/provider';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    UserModule,
    AuthModule,
    ImageCompletionModule,
    ModelModule,
    PassportModule,
    JwtModule.register(jwtConfig),
  ],
  providers: [StatisticsResponseMapper, StatisticsProvider],
})
export class StatisticsModule {
  static register(enableControllers: boolean): DynamicModule {
    return {
      module: StatisticsModule,
      controllers: enableControllers ? [ReadController] : [],
    };
  }
}
