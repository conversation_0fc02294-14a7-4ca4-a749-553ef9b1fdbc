import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1720794596355 implements MigrationInterface {
  name = 'Api1720794596355';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "image_completion"
            ADD "is_unsafe" boolean NOT NULL DEFAULT false
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion"
            ALTER COLUMN "is_nsfw"
            SET DEFAULT false
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "image_completion"
            ALTER COLUMN "is_nsfw"
            SET DEFAULT true
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion" DROP COLUMN "is_unsafe"
        `);
  }
}
