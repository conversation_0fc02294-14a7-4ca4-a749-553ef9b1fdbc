import { Injectable } from '@nestjs/common';
import { ImageCompletionProvider } from 'src/image-completion/service/provider';
import { ImageCompletionResponseMapper } from 'src/image-completion/service/response-mapper';
import { ImageEditDto } from '../dto/image-edit.dto';
import { ImageEditEntity } from '../../../image-edit/entity/image-edit.entity';
import { ModelResponseMapper } from 'src/model/service/response-mapper';

@Injectable()
export class ImageEditResponseMapper {
  constructor(
    private readonly imageCompletionResponseMapper: ImageCompletionResponseMapper,
    private readonly imageCompletionProvider: ImageCompletionProvider,
    private readonly modelResponseMapper: ModelResponseMapper,
  ) {}

  async map(
    entity: ImageEditEntity,
    isInternal = false,
  ): Promise<ImageEditDto> {
    const dto = {
      id: entity.id,
      mode: entity.mode,
      imageCompletionsCount: entity.imageCompletionsCount,
      width: entity.width,
      height: entity.height,
      mask: entity.mask,
      status: entity.status,
      settings: entity.settings,
      inputImageUrl: entity.inputImageUrl,
      webhookUrl: entity.webhookUrl,
    } as ImageEditDto;

    if (entity.originalImageCompletionId) {
      const originalImageCompletion =
        entity.originalImageCompletion ??
        (await this.imageCompletionProvider.get(
          entity.originalImageCompletionId,
        ));

      dto.originalImageCompletion = isInternal
        ? await this.imageCompletionResponseMapper.mapInternal(
            originalImageCompletion,
          )
        : await this.imageCompletionResponseMapper.map(originalImageCompletion);
    }

    if (!entity.hidePrompt) {
      dto.prompt = entity.prompt;
    }

    if (entity.generatedImageCompletionId) {
      const generatedImageCompletion =
        entity.generatedImageCompletion ??
        (await this.imageCompletionProvider.get(
          entity.generatedImageCompletionId,
        ));

      dto.generatedImageCompletion = isInternal
        ? await this.imageCompletionResponseMapper.mapInternal(
            generatedImageCompletion,
          )
        : await this.imageCompletionResponseMapper.map(
            generatedImageCompletion,
          );
    }

    if (entity.imageCompletions) {
      dto.imageCompletionChoices = isInternal
        ? await this.imageCompletionResponseMapper.mapMultipleInternal(
            entity.imageCompletions.map(
              (imageCompletion) => imageCompletion.imageCompletion,
            ),
          )
        : await this.imageCompletionResponseMapper.mapMultiple(
            entity.imageCompletions.map(
              (imageCompletion) => imageCompletion.imageCompletion,
            ),
          );
    }

    dto.models = [];
    if (entity.models?.length) {
      for (const imageEditModel of entity.models) {
        try {
          dto.models.push(
            await this.modelResponseMapper.mapInternal(imageEditModel.model),
          );
        } catch (error) {
          console.error({
            message: 'Error mapping image models',
            image_edit_id: entity.id,
            image_edit_model_id: imageEditModel?.id,
            image_edit_model: imageEditModel,
            error: error.message,
          });
        }
      }
    }

    return dto;
  }

  async mapMultiple(entities: ImageEditEntity[]): Promise<ImageEditDto[]> {
    return Promise.all(entities.map((entity) => this.map(entity)));
  }
}
