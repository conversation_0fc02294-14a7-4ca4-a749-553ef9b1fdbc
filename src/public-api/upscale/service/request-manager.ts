import { BadRequestException, Injectable, UnauthorizedException } from '@nestjs/common';
import { ImageCompletionProvider } from 'src/image-completion/service/provider';
import { OrganizationUserProvider } from 'src/organization/service/organization-user.provider';
import { PaymentRequiredException } from 'src/subscription/exception/payment-required.exception';
import { UpscaleEntity } from 'src/upscale/entity/upscale.entity';
import { UpscaleManager } from 'src/upscale/service/manager';
import { UserEntity } from 'src/user/entity/user.entity';
import { UpscaleRequest } from '../dto/upscale.request';

@Injectable()
export class UpscaleRequestManager {
  constructor(
    private manager: UpscaleManager,
    private imageCompletionProvider: ImageCompletionProvider,
    private organizationUserProvider: OrganizationUserProvider,
  ) {}

  async create(
    request: UpscaleRequest,
    user: UserEntity,
  ): Promise<UpscaleEntity> {
    // Validate organization membership if organizationId is provided
    if (request.organizationId) {
      const isMember = await this.organizationUserProvider.isMember(
        user.id,
        request.organizationId,
      );
      if (!isMember) {
        throw new UnauthorizedException(
          'User is not a member of the specified organization',
        );
      }
    }

    const entity = new UpscaleEntity();

    if (request.imageId) {
      const imageCompletion = await this.imageCompletionProvider.get(
        request.imageId,
      );

      entity.imageCompletion = imageCompletion;
      entity.imageCompletionId = imageCompletion.id;
    }

    entity.user = user;
    entity.prompt = request.prompt;
    entity.imageUrl = request.imageUrl;
    entity.webhookUrl = request.webhookUrl;

    entity.generationSettings = {
      strength: request.strength,
    };

    // Set organizationId if provided for organization credit deduction
    if (request.organizationId) {
      entity.organizationId = request.organizationId;
    }

    try {
      await this.manager.create(entity);

      return entity;
    } catch (e) {
      if ('user_credit_balance.not_found' === e.message) {
        throw new PaymentRequiredException();
      }

      throw new BadRequestException(e.message);
    }
  }
}
