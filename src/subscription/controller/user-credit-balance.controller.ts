import { Controller, Get, Request } from '@nestjs/common';
import {
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiInternalServerErrorResponse,
  ApiBadRequestResponse,
} from '@nestjs/swagger';
import { Raw } from 'typeorm/find-options/operator/Raw';
import { UserCreditBalanceDto } from '../dto/user-credit-balance.dto';
import { UserCreditBalanceProvider } from '../service/user-credit-balance.provider';
import { UserCreditBalanceResponseMapper } from '../service/user-credit-balance.response-mapper';

@ApiTags('user-credit-balance')
@Controller('users/current/credit-balances')
export class UserCreditBalanceController {
  constructor(
    private responseMapper: UserCreditBalanceResponseMapper,
    private userCreditBalanceProvider: UserCreditBalanceProvider,
  ) {}

  @Get()
  @ApiOperation({
    operationId: 'user_credit_balance_list',
    summary: 'List current user credit balances',
    description:
      'Retrieves a list of active credit balances for the current authenticated user.',
  })
  @ApiOkResponse({
    type: UserCreditBalanceDto,
    isArray: true,
    description: 'List of active user credit balances.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - Invalid or unavailable parameters.
  `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async find(@Request() request): Promise<UserCreditBalanceDto[]> {
    const now = new Date();

    const entities = await this.userCreditBalanceProvider.findBy(
      {
        user: { id: request.user.id },
        expiresAt: Raw((alias) => `( ${alias} IS NULL OR ${alias} > :now )`, {
          now,
        }),
      },
      1,
      100,
    );

    return await this.responseMapper.mapMultiple(entities);
  }
}
