import { ApiProperty } from '@nestjs/swagger';

export class TopModelUserDto {
  @ApiProperty({ description: 'User ID' })
  userId: string;

  @ApiProperty({ description: 'Username' })
  username: string;

  @ApiProperty({ description: 'Email address' })
  email: string;

  @ApiProperty({ description: 'Total number of available models' })
  modelCount: number;

  @ApiProperty({ description: 'Number of public models' })
  publicModels: number;

  @ApiProperty({ description: 'Number of private models' })
  privateModels: number;
}
