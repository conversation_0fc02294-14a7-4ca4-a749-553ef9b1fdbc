import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';
import { Logger } from 'nestjs-pino';
import { Repository } from 'typeorm';
import Stripe from 'stripe';
import { CreditPackageEntity } from 'src/subscription/entity/credit-package.entity';
import { CreditPackageManager } from 'src/subscription/service/credit-package.manager';
import { CreditTypeEnum } from 'src/subscription/entity/credit-type.enum';
import { StripeService } from 'src/subscription/service/stripe.service';
import { UserCreditBalanceManager } from 'src/subscription/service/user-credit-balance.manager';
import { OrganizationSubscriptionEntity } from '../entity/organization-subscription.entity';
import { SubscriptionStatusEnum } from '../enum/subscription-status.enum';
import { OrganizationManager } from './manager';
import { OrganizationSubscriptionManager } from './organization-subscription.manager';
import { OrganizationSubscriptionProvider } from './organization-subscription.provider';
import { OrganizationUserProvider } from './organization-user.provider';

describe('OrganizationSubscriptionManager - Yearly Subscriptions', () => {
  let manager: OrganizationSubscriptionManager;
  let repository: Repository<OrganizationSubscriptionEntity>;
  let stripeService: StripeService;
  let userCreditBalanceManager: UserCreditBalanceManager;
  let logger: Logger;

  const mockRepository = {
    save: jest.fn(),
  };

  const mockStripe = {
    checkout: {
      sessions: {
        create: jest.fn(),
        retrieve: jest.fn(),
      },
    },
    subscriptions: {
      cancel: jest.fn(),
    },
  };

  const mockStripeService = {
    getOrCreateStripePriceId: jest.fn(),
  };

  const mockUserCreditBalanceManager = {
    increase: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockLogger = {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  };

  const mockProvider = {};
  const mockOrganizationUserProvider = {};
  const mockOrganizationManager = {
    updateSeats: jest.fn(),
  };
  const mockCreditPackageManager = {
    save: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrganizationSubscriptionManager,
        {
          provide: getRepositoryToken(OrganizationSubscriptionEntity),
          useValue: mockRepository,
        },
        {
          provide: 'Stripe',
          useValue: mockStripe,
        },
        {
          provide: StripeService,
          useValue: mockStripeService,
        },
        {
          provide: UserCreditBalanceManager,
          useValue: mockUserCreditBalanceManager,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: Logger,
          useValue: mockLogger,
        },
        {
          provide: OrganizationSubscriptionProvider,
          useValue: mockProvider,
        },
        {
          provide: OrganizationUserProvider,
          useValue: mockOrganizationUserProvider,
        },
        {
          provide: OrganizationManager,
          useValue: mockOrganizationManager,
        },
        {
          provide: CreditPackageManager,
          useValue: mockCreditPackageManager,
        },
      ],
    }).compile();

    manager = module.get<OrganizationSubscriptionManager>(OrganizationSubscriptionManager);
    repository = module.get<Repository<OrganizationSubscriptionEntity>>(
      getRepositoryToken(OrganizationSubscriptionEntity),
    );
    stripeService = module.get<StripeService>(StripeService);
    userCreditBalanceManager = module.get<UserCreditBalanceManager>(UserCreditBalanceManager);
    logger = module.get<Logger>(Logger);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('calculateCreditExpirationDate', () => {
    it('should return monthly expiration for yearly organization subscriptions', () => {
      const yearlySubscription = {
        creditPackage: { expiresAfterMonths: 12 },
        paidAt: new Date('2024-01-01'),
        createdAt: new Date('2024-01-01'),
      } as OrganizationSubscriptionEntity;

      const result = manager['calculateCreditExpirationDate'](yearlySubscription);
      const expected = new Date('2024-02-01');

      expect(result.getTime()).toBe(expected.getTime());
    });

    it('should return subscription expiration for monthly organization subscriptions', () => {
      const monthlySubscription = {
        creditPackage: { expiresAfterMonths: 1 },
        expiresAt: new Date('2024-02-01'),
      } as OrganizationSubscriptionEntity;

      const result = manager['calculateCreditExpirationDate'](monthlySubscription);
      const expected = new Date('2024-02-01');

      expect(result.getTime()).toBe(expected.getTime());
    });

    it('should use paidAt date when available for yearly subscriptions', () => {
      const yearlySubscription = {
        creditPackage: { expiresAfterMonths: 12 },
        paidAt: new Date('2024-02-01'),
        createdAt: new Date('2024-01-01'),
      } as OrganizationSubscriptionEntity;

      const result = manager['calculateCreditExpirationDate'](yearlySubscription);
      const expected = new Date('2024-03-01');

      expect(result.getTime()).toBe(expected.getTime());
    });
  });

  describe('activateSubscription - Yearly Subscriptions', () => {
    it('should use monthly expiration for yearly subscription credits', async () => {
      const yearlySubscription = {
        id: 'sub_123',
        organization: { id: 'org_123', seatsPurchased: 1 },
        status: SubscriptionStatusEnum.NEW,
        creditPackage: {
          id: 'pkg_123',
          name: 'Pro - Yearly',
          expiresAfterMonths: 12,
          seats: 5,
          creditTypes: {
            [CreditTypeEnum.IMAGE]: 36000,
            [CreditTypeEnum.MODEL]: 20,
          },
        },
        paidAt: new Date('2024-01-01'),
        createdAt: new Date('2024-01-01'),
      } as OrganizationSubscriptionEntity;

      await manager.activateSubscription(yearlySubscription);

      // Verify that credits are allocated with monthly expiration
      const expectedExpirationDate = new Date('2024-02-01');

      expect(mockUserCreditBalanceManager.increase).toHaveBeenCalledWith(
        CreditTypeEnum.IMAGE,
        36000,
        null,
        'org_123',
        expectedExpirationDate,
      );

      expect(mockUserCreditBalanceManager.increase).toHaveBeenCalledWith(
        CreditTypeEnum.MODEL,
        20,
        null,
        'org_123',
        expectedExpirationDate,
      );

      expect(mockRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          status: SubscriptionStatusEnum.ACTIVE,
        }),
      );

      expect(mockOrganizationManager.updateSeats).toHaveBeenCalledWith(
        yearlySubscription.organization,
        5,
      );
    });

    it('should not update seats if organization already has enough seats', async () => {
      const yearlySubscription = {
        id: 'sub_123',
        organization: { id: 'org_123', seatsPurchased: 10 },
        status: SubscriptionStatusEnum.NEW,
        creditPackage: {
          id: 'pkg_123',
          name: 'Pro - Yearly',
          expiresAfterMonths: 12,
          seats: 5,
          creditTypes: {
            [CreditTypeEnum.IMAGE]: 36000,
          },
        },
        paidAt: new Date('2024-01-01'),
        createdAt: new Date('2024-01-01'),
      } as OrganizationSubscriptionEntity;

      await manager.activateSubscription(yearlySubscription);

      expect(mockOrganizationManager.updateSeats).not.toHaveBeenCalled();
    });

    it('should not activate already active subscriptions', async () => {
      const activeSubscription = {
        status: SubscriptionStatusEnum.ACTIVE,
      } as OrganizationSubscriptionEntity;

      await manager.activateSubscription(activeSubscription);

      expect(mockUserCreditBalanceManager.increase).not.toHaveBeenCalled();
      expect(mockRepository.save).not.toHaveBeenCalled();
    });
  });

  describe('getOrCreateStripePriceId', () => {
    it('should use StripeService for price creation', async () => {
      const creditPackage = {
        id: 'pkg_123',
        name: 'Pro - Yearly',
        price: 7490,
        expiresAfterMonths: 12,
        stripePriceId: null,
      } as CreditPackageEntity;

      mockStripeService.getOrCreateStripePriceId.mockResolvedValue('price_yearly_123');

      const result = await manager['getOrCreateStripePriceId'](creditPackage);

      expect(mockStripeService.getOrCreateStripePriceId).toHaveBeenCalledWith(creditPackage);
      expect(result).toBe('price_yearly_123');
    });

    it('should return existing price ID if available', async () => {
      const creditPackage = {
        id: 'pkg_123',
        stripePriceId: 'existing_price_123',
      } as CreditPackageEntity;

      mockStripeService.getOrCreateStripePriceId.mockResolvedValue('existing_price_123');

      const result = await manager['getOrCreateStripePriceId'](creditPackage);

      expect(result).toBe('existing_price_123');
    });
  });

  describe('error handling', () => {
    it('should handle activation errors gracefully', async () => {
      const subscription = {
        id: 'sub_123',
        organization: { id: 'org_123' },
        status: SubscriptionStatusEnum.NEW,
        creditPackage: {
          creditTypes: { [CreditTypeEnum.IMAGE]: 1000 },
          expiresAfterMonths: 12,
        },
        paidAt: new Date(),
        createdAt: new Date(),
      } as OrganizationSubscriptionEntity;

      mockUserCreditBalanceManager.increase.mockRejectedValue(new Error('Database error'));

      await expect(manager.activateSubscription(subscription)).rejects.toThrow(
        'subscription.activation_failed',
      );

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to activate organization subscription',
        expect.objectContaining({
          error: expect.any(Error),
          organizationId: 'org_123',
        }),
      );
    });
  });
});
