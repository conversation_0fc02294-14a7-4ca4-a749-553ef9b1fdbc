import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1707056977639 implements MigrationInterface {
    name = 'Api1707056977639'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "model"
            ADD "prompt_v1" text
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "model" DROP COLUMN "prompt_v1"
        `);
    }

}
