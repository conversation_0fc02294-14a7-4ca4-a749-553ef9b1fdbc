import {
  Controller,
  ForbiddenException,
  ParseUUI<PERSON>ipe,
  Patch,
  Request,
} from '@nestjs/common';
import {
  Body,
  Param,
} from '@nestjs/common/decorators/http/route-params.decorator';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { OrganizationDto } from '../dto/organization.dto';
import { OrganizationRequest } from '../dto/organization.request';
import { OrganizationUserProvider } from '../service/organization-user.provider';
import { OrganizationProvider } from '../service/provider';
import { OrganizationRequestManager } from '../service/request-manager';
import { OrganizationResponseMapper } from '../service/response-mapper';

@ApiTags('organization')
@Controller('organizations')
export class UpdateController {
  constructor(
    private organizationProvider: OrganizationProvider,
    private organizationMapper: OrganizationResponseMapper,
    private organizationRequestManager: OrganizationRequestManager,
    private organizationUserProvider: OrganizationUserProvider,
  ) {}

  @ApiOperation({
    operationId: 'organization_update',
    summary: 'Update organization details',
    description:
      'Updates the details of an existing organization.\n\n' +
      'Requirements:\n' +
      '- User must be a manager of the organization\n' +
      '- Organization must exist' +
      '\n\n' +
      'Required Query Parameters:\n' +
      '- id: UUID of the organization to update\n\n' +
      'Optional Body Parameters:\n' +
      '- name: New name of the organization\n' +
      '- description: New description of the organization\n' +
      '- website: New website of the organization\n' +
      '- handle: New handle of the organization\n' +
      '- currency: New currency of the organization\n' +
      '- locale: New locale of the organization\n' +
      '- includeWatermarks: New watermark setting of the organization\n' +
      '- imageQueue: New image queue of the organization\n' +
      '- modelQueue: New model queue of the organization\n' +
      '- systemVersion: New system version of the organization\n\n' +
      'Returns the updated organization object.',
  })
  @ApiBody({
    type: OrganizationRequest,
    description: 'Organization update parameters.',
  })
  @ApiOkResponse({
    type: OrganizationDto,
    description: 'Organization updated successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - \`name\`: Must be a non-empty string.
      - \`handle\`: Must be a non-empty string.
      - \`description\`: Must be a string.
      - \`website\`: Must be a valid URL.
      - \`currency\`: Must be a valid currency code.
      - \`locale\`: Must be a valid locale.
      - \`includeWatermarks\`: Must be a valid boolean.
      - \`imageQueue\`: Must be a valid queue name.
      - \`modelQueue\`: Must be a valid queue name.
      - \`systemVersion\`: Must be a valid system version.
      - Invalid or unavailable parameters.
  `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User is not a manager of the organization.',
  })
  @ApiNotFoundResponse({
    description: 'Not Found. The organization could not be found.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. An unexpected error occurred.',
  })
  @Patch(':id')
  async update(
    @Body() organizationRequest: OrganizationRequest,
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<OrganizationDto> {
    const organization = await this.organizationProvider.get(id);

    if (
      !this.organizationUserProvider.isManager(request.user.id, organization.id)
    ) {
      throw new ForbiddenException();
    }

    await this.organizationRequestManager.update(
      organization,
      organizationRequest,
    );

    return await this.organizationMapper.map(organization);
  }
}
