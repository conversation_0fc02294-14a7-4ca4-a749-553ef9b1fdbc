import { DynamicModule, Module, forwardRef } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthModule } from 'src/auth/auth.module';
import { ImageCompletionModule } from 'src/image-completion/module';
import { ModelModule } from 'src/model/module';
import { NotificationModule } from 'src/notification/module';
import { OrganizationModule } from 'src/organization/organization.module';
import { SubscriptionModule } from 'src/subscription/module';
import { jwtConfig } from '../auth/config/jwt.config';
import { UserModule } from '../user/user.module';
import { CreateController } from './controller/create.controller';
import { DeleteController } from './controller/delete.controller';
import { ReadController as InternalReadController } from './controller/internal/read.controller';
import { UpdateController as InternalUpdateController } from './controller/internal/update.controller';
import { ReadController } from './controller/read.controller';
import { UpscaleEntity } from './entity/upscale.entity';
import { AssetManager } from './service/asset.manager';
import { UpscaleManager } from './service/manager';
import { UpscaleProvider } from './service/provider';
import { UpscaleRequestManager } from './service/request-manager';
import { UpscaleResponseMapper } from './service/response-mapper';

@Module({
  imports: [
    TypeOrmModule.forFeature([UpscaleEntity]),
    PassportModule,
    JwtModule.register(jwtConfig),
    forwardRef(() => AuthModule),
    forwardRef(() => ImageCompletionModule),
    forwardRef(() => ModelModule),
    forwardRef(() => NotificationModule),
    forwardRef(() => OrganizationModule),
    forwardRef(() => SubscriptionModule),
    forwardRef(() => UserModule),
  ],
  exports: [
    UpscaleProvider,
    UpscaleResponseMapper,
    UpscaleManager,
    AssetManager,
  ],
  providers: [
    UpscaleResponseMapper,
    UpscaleProvider,
    UpscaleRequestManager,
    UpscaleManager,
    AssetManager,
  ],
})
export class UpscaleModule {
  static register(enableControllers: boolean): DynamicModule {
    return {
      module: UpscaleModule,
      controllers: enableControllers
        ? [
            CreateController,
            DeleteController,
            ReadController,
            InternalReadController,
            InternalUpdateController,
          ]
        : [],
    };
  }
}
