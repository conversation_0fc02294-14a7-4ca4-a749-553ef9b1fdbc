import {
  <PERSON>,
  Get,
  Param,
  ParseUUI<PERSON>ipe,
  Query,
  Request,
  Res,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiBearerAuth,
  ApiResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import { JwtIntegrationAuthGuard } from 'src/auth/service/jwt-integration-auth.guard';
import { BaseFindResponseHeadersDto } from 'src/core/dto/base-find-response-headers.dto';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { UpscaleProvider } from 'src/upscale/service/provider';
import { UpscaleSearchRequest as PrivateUpscaleSearchRequest } from '../../../upscale/dto/upscale.search-request';
import { UpscaleDto } from '../dto/upscale.dto';
import { UpscaleSearchRequest } from '../dto/upscale.search-request';
import { UpscaleResponseMapper } from '../service/response-mapper';

@ApiTags('upscale')
@Controller('upscale')
@ApiBearerAuth()
export class ReadController {
  constructor(
    private provider: UpscaleProvider,
    private responseMapper: UpscaleResponseMapper,
  ) {}

  @ApiOperation({
    operationId: 'upscale_find',
    summary: 'Find upscales',
    description:
      'Retrieves a paginated list of upscales.\n\n' +
      'Query Parameters:\n' +
      '- page: Page number (default: 1)\n' +
      '- limit: Items per page (1-50, default: 10)\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: "ASC" or "DESC"\n' +
      '- userId: Filter by user ID\n' +
      '- username: Filter by username\n' +
      '- imageId: Filter by source image ID\n' +
      '- onlyFollowing: Show only upscales from followed users\n' +
      '- status: Filter by status (see Status Values)\n\n' +
      'Status Values:\n' +
      '- new: Initial state, waiting to be processed\n' +
      '- generating: Currently being upscaled\n' +
      '- ready: Upscale completed successfully\n' +
      '- failed: Upscale failed\n\n' +
      'Response Headers:\n' +
      '- X-Total-Count: Total count of items\n' +
      '- X-Current-Page: Current page of pagination\n' +
      '- X-Per-Page: Results per page\n' +
      '- X-Total-Pages: Total count of pages',
  })
  @ApiQuery({ type: UpscaleSearchRequest })
  @ApiOkResponse({
    type: UpscaleDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'List of upscales matching the search criteria.',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request. The request parameters are invalid.',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden. User does not have permission to access these upscales.',
  })
  @ApiResponse({
    status: 422,
    description: 'Unprocessable Entity. Invalid search parameters.',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. An unexpected error occurred.',
  })
  @Get()
  @UsePipes(new ValidationPipe())
  async find(
    @Request() request,
    @Query() query: UpscaleSearchRequest,
    @Res() res: Response,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;
    const filters = this.validateSearchFilters(inputFilters);

    const entities = await this.provider.findBy(
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    const totalCount = await this.provider.countBy(filters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultiple(entities));
  }

  @ApiOperation({
    operationId: 'upscale_get',
    summary: 'Get upscale details',
    description:
      'Retrieves details of a specific upscale.\n\n' +
      'Path Parameters:\n' +
      '- id: UUID of the upscale to retrieve',
  })
  @ApiParam({
    name: 'id',
    description: 'The unique identifier of the upscale to retrieve',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponse({
    type: UpscaleDto,
    description: 'The requested upscale details.',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request. The request parameters are invalid.',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden. User does not have permission to access this upscale.',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found. The requested upscale could not be found.',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. An unexpected error occurred.',
  })
  @Get(':id')
  @UseGuards(JwtIntegrationAuthGuard)
  async get(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<UpscaleDto> {
    return this.provider
      .getBy({ userId: request.user.id, id: id })
      .then((entity) => this.responseMapper.map(entity));
  }

  validateSearchFilters(filters: UpscaleSearchRequest): any {
    const criteria = new PrivateUpscaleSearchRequest();

    if (filters.imageId) {
      criteria.imageCompletionId = filters.imageId;

      delete filters.imageId;
    }

    return {
      ...criteria,
      ...filters,
    };
  }
}
