import {
  Controller,
  Delete,
  HttpC<PERSON>,
  Param,
  ParseUUI<PERSON>ipe,
  Request,
} from '@nestjs/common';
import {
  ApiNoContentResponse,
  ApiParam,
  ApiTags,
  ApiOperation,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { UserAuthTokenManager } from '../service/manager';
import { UserAuthTokenProvider } from '../service/user-auth-token.provider';

@ApiTags('user')
@Controller('auth/integration-tokens')
export class DeleteController {
  constructor(
    private provider: UserAuthTokenProvider,
    private manager: UserAuthTokenManager,
  ) {}

  @Delete(':id')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'integration_token_delete',
    summary: 'Delete integration token',
    description:
      'Deletes the specified integration token for the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the integration token to delete',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the integration token to delete.',
    type: String,
  })
  @ApiNoContentResponse({
    description: 'Integration token deleted successfully.',
  })
  @ApiBadRequestResponse({
    description:
      'Bad Request. Possible reasons:\n\n' + '- Invalid integration token ID.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description:
      'Forbidden. User does not have permission to delete this token.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async deleteAuth(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<void> {
    await this.provider
      .getBy({ user: { id: request.user.id }, id: id })
      .then((entity) => this.manager.delete(entity));
  }
}
