import { Injectable } from '@nestjs/common';
import { ChatConversationEntity } from '../entity/chat-conversation.entity';
import { ChatMessageEntity } from '../entity/chat-message.entity';
import { ChatConversationDto } from '../dto/chat-conversation.dto';
import { ChatMessageDto } from '../dto/chat-message.dto';

@Injectable()
export class ChatResponseMapper {
  async map(entity: ChatConversationEntity): Promise<ChatConversationDto> {
    const dto = new ChatConversationDto();
    dto.id = entity.id;
    dto.title = entity.title;
    dto.userId = entity.userId;
    dto.createdAt = entity.createdAt;
    dto.updatedAt = entity.updatedAt;

    return dto;
  }

  async mapMultiple(
    entities: ChatConversationEntity[],
  ): Promise<ChatConversationDto[]> {
    return Promise.all(entities.map((entity) => this.map(entity)));
  }

  async mapMessage(entity: ChatMessageEntity): Promise<ChatMessageDto> {
    const dto = new ChatMessageDto();
    dto.id = entity.id;
    dto.content = entity.content;
    dto.role = entity.role;
    dto.conversationId = entity.conversationId;

    if (entity.attachedFiles) {
      dto.attachedFiles = entity.attachedFiles.map((attachedFile) => ({
        type: attachedFile.type,
        id: attachedFile.referenceId,
      }));
    }

    dto.createdAt = entity.createdAt;

    return dto;
  }

  async mapMultipleMessages(
    entities: ChatMessageEntity[],
  ): Promise<ChatMessageDto[]> {
    return Promise.all(entities.map((entity) => this.mapMessage(entity)));
  }
}
