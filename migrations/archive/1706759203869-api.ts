import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1706759203869 implements MigrationInterface {
    name = 'Api1706759203869'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "is_verified" boolean NOT NULL DEFAULT FALSE
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "is_verified"
        `);
    }

}
