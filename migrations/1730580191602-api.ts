import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1730580191602 implements MigrationInterface {
    name = 'Api1730580191602'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "idp_config" (
                "id" SERIAL NOT NULL,
                "name" character varying NOT NULL,
                "entity_id" character varying NOT NULL,
                "acs_url" character varying NOT NULL,
                "sls_url" character varying NOT NULL,
                "certificate" text,
                "metadata_url" text,
                "single_sign_on_url" character varying,
                "organization_name" character varying,
                "organization_display_name" character varying,
                "organization_url" character varying,
                "contact_given_name" character varying,
                "contact_email" character varying,
                "response_mapping" json,
                "is_default" boolean NOT NULL DEFAULT false,
                CONSTRAINT "UQ_ead29a92c72e8aafe68c6a8239d" UNIQUE ("name"),
                CONSTRAINT "PK_7555e61fb217207c05ba35da4ba" PRIMARY KEY ("id")
            )
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DROP TABLE "idp_config"
        `);
    }

}
