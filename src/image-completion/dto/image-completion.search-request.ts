import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { BaseSearchRequest } from '../../core/dto/base.search-request';
import { PrivacyEnum, StatusEnum } from '../entity/image-completion.entity';

export class ImageCompletionSearchRequest extends BaseSearchRequest {
  @IsOptional()
  @IsUUID()
  @ApiProperty()
  userId: string;

  @IsOptional()
  @IsString()
  @ApiProperty()
  prompt: string;

  @IsOptional()
  @IsString()
  @ApiProperty()
  username: string;

  @IsOptional()
  @IsEnum(PrivacyEnum)
  @Transform(({ value }) => value.toLowerCase())
  @ApiProperty({ enum: PrivacyEnum })
  privacy?: PrivacyEnum;

  @IsOptional()
  @IsEnum(StatusEnum)
  @Transform(({ value }) => value.toLowerCase())
  @ApiProperty({ enum: StatusEnum })
  status?: StatusEnum;

  @IsOptional()
  @IsNumber()
  @ApiProperty()
  systemVersion: number;

  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  isHot: boolean;

  // eslint-disable-next-line @typescript-eslint/no-inferrable-types
  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  includeNsfw: boolean = false;

  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  onlyFollowing: boolean;

  @IsOptional()
  @IsArray()
  @ApiProperty()
  modelIds: string[];

  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  editedImages: boolean;

  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  day: boolean;

  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  week: boolean;

  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  month: boolean;

  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  year: boolean;
}
