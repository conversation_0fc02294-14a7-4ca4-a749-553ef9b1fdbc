import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1691757559168 implements MigrationInterface {
    name = 'Api1691757559168'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "image_completion"
            ADD "base_model" text
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "image_completion" DROP COLUMN "base_model"
        `);
    }

}
