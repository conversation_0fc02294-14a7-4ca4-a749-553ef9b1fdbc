import { Injectable } from '@nestjs/common';
import { ImageCompletionEntity } from 'src/image-completion/entity/image-completion.entity';
import { ImageCompletionProvider } from 'src/image-completion/service/provider';

@Injectable()
export class FeedProvider {
  constructor(private imageCompletionProvider: ImageCompletionProvider) {}

  async find(
    criteria: any,
    page: number,
    limit: number,
    sortBy?: string,
    sortOrder: 'ASC' | 'DESC' | 'RANDOM' = 'ASC',
  ): Promise<ImageCompletionEntity[]> {
    return await this.imageCompletionProvider.findBy(
      criteria,
      page,
      limit,
      sortBy,
      sortOrder,
    );
  }

  async count(criteria: any): Promise<number> {
    return await this.imageCompletionProvider.countBy(criteria);
  }
}
