import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsOptional, IsUUID } from 'class-validator';
import { BaseSearchRequest } from '../../core/dto/base.search-request';

export class UserFollowSearchRequest extends BaseSearchRequest {
  @IsOptional()
  @IsUUID()
  @ApiProperty()
  followerId?: string;

  @IsOptional()
  @IsUUID()
  @ApiProperty()
  followingId?: string;

  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  isPending?: boolean;

  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  isApproved?: boolean;
}
