import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1709642029269 implements MigrationInterface {
    name = 'Api1709642029269'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_account"
            ALTER COLUMN "system_version"
            SET DEFAULT '2'
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_account"
            ALTER COLUMN "system_version"
            SET DEFAULT '1'
        `);
    }

}
