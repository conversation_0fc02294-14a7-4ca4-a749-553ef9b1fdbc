import { ApiProperty } from '@nestjs/swagger';

export class UserDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  username: string;

  @ApiProperty()
  email: string;

  @ApiProperty()
  profilePicture: string;

  @ApiProperty()
  description: string;

  @ApiProperty()
  website: string;

  @ApiProperty()
  socialMediaAccounts: any;

  @ApiProperty()
  timezone: string;

  @ApiProperty()
  frontendTheme: string;

  @ApiProperty()
  imageQueue: string;

  @ApiProperty()
  currency: string;

  @ApiProperty()
  locale: string;

  @ApiProperty()
  birthday: string;

  @ApiProperty()
  imagesGenerated: number;

  @ApiProperty()
  imagesAvailable: number;

  @ApiProperty()
  modelsAvailable: number;

  @ApiProperty()
  followersCount: number;

  @ApiProperty()
  followingCount: number;

  @ApiProperty()
  systemVersion: number;

  @ApiProperty()
  isEmailValidated: boolean;

  @ApiProperty()
  isActive: boolean;

  @ApiProperty()
  isBlocked: boolean;

  @ApiProperty()
  isVerified: boolean;

  @ApiProperty()
  isBot: boolean;

  @ApiProperty()
  allowSwapping: boolean;

  @ApiProperty()
  includeWatermarks: boolean;

  @ApiProperty()
  hidePrompt: boolean;

  @ApiProperty()
  tutorialSteps: any;
}
