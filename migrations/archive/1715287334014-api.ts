import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1715287334014 implements MigrationInterface {
  name = 'Api1715287334014';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "subscription"
            ADD "renewed_at" TIMESTAMP
        `);
    await queryRunner.query(`
            ALTER TABLE "subscription"
            ADD "stripe_latest_invoice" character varying
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "subscription" DROP COLUMN "stripe_latest_invoice"
        `);
    await queryRunner.query(`
            ALTER TABLE "subscription" DROP COLUMN "renewed_at"
        `);
  }
}
