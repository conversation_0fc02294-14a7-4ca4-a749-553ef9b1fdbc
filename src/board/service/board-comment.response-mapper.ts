import { Injectable } from '@nestjs/common';
import { UserProvider } from 'src/user/service/provider';
import { UserResponseMapper } from 'src/user/service/response-mapper';
import { BoardCommentEntity } from '../entity/board-comment.entity';
import { BoardCommentDTO } from '../dto/board-comment.dto';

@Injectable()
export class BoardCommentResponseMapper {
  constructor(
    private userProvider: UserProvider,
    private userResponseMapper: UserResponseMapper,
  ) {}

  async mapMultiple(entities: BoardCommentEntity[]): Promise<any> {
    const dtos = [];

    for (const entity of entities) {
      dtos.push(await this.map(entity));
    }

    return dtos;
  }

  async map(entity: BoardCommentEntity): Promise<BoardCommentDTO> {
    const dto = new BoardCommentDTO();
    const user = await this.userProvider.get(entity.userId);

    dto.id = entity.id;
    dto.userId = entity.userId;
    dto.username = user.username;
    dto.userProfilePicture = this.userResponseMapper.mapProfilePicture(user);
    dto.comment = entity.comment;
    dto.createdAt = entity.createdAt;

    return dto;
  }
}
