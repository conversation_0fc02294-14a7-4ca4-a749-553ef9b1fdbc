import { DynamicModule, Module, forwardRef } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { AuthModule } from 'src/auth/auth.module';
import { jwtConfig } from 'src/auth/config/jwt.config';
import { ImageCompletionModule } from 'src/image-completion/module';
import { UpscaleModule } from 'src/upscale/module';
import { UserModule } from 'src/user/user.module';
import { CreateController } from './controller/create.controller';
import { ReadController } from './controller/read.controller';
import { DeleteController } from './controller/delete.controller';
import { UpscaleRequestManager } from './service/request-manager';
import { UpscaleResponseMapper } from './service/response-mapper';
import { OrganizationModule } from 'src/organization/organization.module';

@Module({})
export class PublicUpscaleModule {
  static register(enableControllers: boolean): DynamicModule {
    return {
      module: PublicUpscaleModule,
      providers: [UpscaleRequestManager, UpscaleResponseMapper],
      imports: [
        PassportModule,
        JwtModule.register(jwtConfig),
        forwardRef(() => AuthModule),
        forwardRef(() => UserModule),
        forwardRef(() => ImageCompletionModule),
        forwardRef(() => UpscaleModule),
        forwardRef(() => OrganizationModule),
      ],
      exports: [UpscaleResponseMapper],
      controllers: enableControllers
        ? [CreateController, ReadController, DeleteController]
        : [],
    };
  }
}
