import { DynamicModule, Module, forwardRef } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { AuthModule } from '../../auth/auth.module';
import { jwtConfig } from '../../auth/config/jwt.config';
import { UserImageModule } from '../../user-image/user-image.module';
import { UserModule } from '../../user/user.module';
import { CreateController } from './controller/create.controller';
import { DeleteController } from './controller/delete.controller';
import { ReadController } from './controller/read.controller';
import { OrganizationModule } from 'src/organization/organization.module';

@Module({})
export class PublicUserImageModule {
  static register(enableControllers: boolean): DynamicModule {
    return {
      module: PublicUserImageModule,
      imports: [
        PassportModule,
        JwtModule.register(jwtConfig),
        forwardRef(() => AuthModule),
        forwardRef(() => UserModule),
        forwardRef(() => UserImageModule),
        forwardRef(() => OrganizationModule),
      ],
      controllers: enableControllers
        ? [CreateController, ReadController, DeleteController]
        : [],
    };
  }
}
