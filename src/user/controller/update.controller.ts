import {
  Controller,
  HttpCode,
  Patch,
  Post,
  Put,
  Request,
} from '@nestjs/common';
import { Body } from '@nestjs/common/decorators/http/route-params.decorator';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { Public } from 'src/core/security/public-routes';
import { UserEmailValidationRequest } from '../dto/user-email-validation.request';
import { UserPasswordUpdateRequest } from '../dto/user-password.update.request';
import { UserDto } from '../dto/user.dto';
import { UserUpdateRequest } from '../dto/user.update.request';
import { UserProvider } from '../service/provider';
import { UserRequestManager } from '../service/request-manager';
import { UserResponseMapper } from '../service/response-mapper';

@ApiTags('user')
@Controller('users')
export class UpdateController {
  constructor(
    private userProvider: UserProvider,
    private userMapper: UserResponseMapper,
    private userRequestManager: UserRequestManager,
  ) {}

  @ApiOperation({
    operationId: 'user_update_current',
    summary: 'Update current user profile',
    description:
      'Updates the profile information of the current authenticated user.\n\n' +
      'Requirements:\n' +
      '- User must be authenticated' +
      'Optional Parameters:\n' +
      '- name: User name\n' +
      '- username: User username\n' +
      '- profilePicture: User profile picture\n' +
      '- description: User description\n' +
      '- website: User website\n' +
      '- socialMediaAccounts: User social media accounts\n' +
      '- birthday: User birthday\n' +
      '- timezone: User timezone\n' +
      '- currency: User currency\n' +
      '- imageQueue: User image queue\n' +
      '- systemVersion: User system version\n' +
      '- includeWatermarks: Whether to include watermarks\n' +
      '- hidePrompt: Whether to hide the prompt\n' +
      '- tutorialSteps: User tutorial steps\n' +
      '- isBot: Whether the user is a bot\n' +
      '- allowSwapping: Whether the user allows swapping\n' +
      '- locale: User locale\n\n' +
      'Returns the updated user object.',
  })
  @ApiBody({ type: UserUpdateRequest })
  @ApiOkResponse({
    type: UserDto,
    description: 'User profile updated successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`name\`: Must be a valid string.
      - \`username\`: Must be a valid string.
      - \`profilePicture\`: Must be a valid string.
      - \`description\`: Must be a valid string.
      - \`website\`: Must be a valid string.
      - \`socialMediaAccounts\`: Must be a valid object.
      - \`birthday\`: Must be a valid date.
      - \`timezone\`: Must be a valid string.
      - \`currency\`: Must be a valid string.
      - \`imageQueue\`: Must be a valid value from the \`QueueEnum\`.
      - \`systemVersion\`: Must be a valid number.
      - \`includeWatermarks\`: Must be a valid boolean.
      - \`hidePrompt\`: Must be a valid boolean.
      - \`tutorialSteps\`: Must be a valid object.
      - \`isBot\`: Must be a valid boolean.
      - \`allowSwapping\`: Must be a valid boolean.
      - \`locale\`: Must be a valid string.
      - Invalid or unavailable parameters.
  `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Patch('current')
  async updateCurrent(
    @Body() userUpdateRequest: UserUpdateRequest,
    @Request() req,
  ): Promise<UserDto> {
    const user = await this.userProvider.get(req.user.id);

    await this.userRequestManager.update(user, userUpdateRequest);

    return await this.userMapper.map(user);
  }

  @ApiOperation({
    operationId: 'user_update_password',
    summary: 'Update current user password',
    description:
      'Updates the password of the current authenticated user.\n\n' +
      'Requirements:\n' +
      '- User must be authenticated' +
      'Required Parameters:\n' +
      '- oldPassword: Current user password\n' +
      '- newPassword: New user password\n\n' +
      'Returns no content.',
  })
  @ApiBody({ type: UserPasswordUpdateRequest })
  @ApiNoContentResponse({ description: 'Password updated successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`oldPassword\`: Must be a valid string.
      - \`newPassword\`: Must be a valid string.
      - Invalid or unavailable parameters.
  `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @HttpCode(204)
  @Put('current/password')
  async updatePassword(
    @Body() userPasswordUpdateRequest: UserPasswordUpdateRequest,
    @Request() req,
  ) {
    const user = await this.userProvider.get(req.user.id);

    await this.userRequestManager.updatePassword(
      user,
      userPasswordUpdateRequest,
    );
  }

  @ApiOperation({
    operationId: 'user_email_validation_start',
    summary: 'Start email validation',
    description:
      'Initiates the email validation process for the current authenticated user.\n\n' +
      'Requirements:\n' +
      '- User must be authenticated',
  })
  @ApiNoContentResponse({ description: 'Email validation started.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - Invalid or unavailable parameters.
  `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @HttpCode(200)
  @Put('current/email-validation')
  async emailValidationStart(@Request() req) {
    const user = await this.userProvider.get(req.user.id);

    await this.userRequestManager.initiateEmailValidation(user);
  }

  @ApiOperation({
    operationId: 'user_email_validate',
    summary: 'Validate user email',
    description:
      'Validates the email address of a user using a validation code.\n\n' +
      'Requirements:\n' +
      '- Must provide a valid email validation code\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the user\n' +
      '- code: Email validation code\n',
  })
  @ApiBody({ type: UserEmailValidationRequest })
  @ApiNoContentResponse({ description: 'Email validated successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - \`code\`: Must be a valid email validation code.
      - Invalid or unavailable parameters.
  `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @HttpCode(204)
  @Public()
  @Post('current/email-validation')
  async validateEmail(@Body() request: UserEmailValidationRequest) {
    await this.userRequestManager.validateEmail(request);
  }
}
