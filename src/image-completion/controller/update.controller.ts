import {
  Body,
  Controller,
  HttpCode,
  Param,
  ParseUUIDPipe,
  Put,
  Request,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiNoContentResponse,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { UpscaleManager } from 'src/upscale/service/manager';
import { UpscaleProvider } from 'src/upscale/service/provider';
import { ImageCompletionPrivacyRequest } from '../dto/image-completion-privacy.request';
import { ImageCompletionProvider } from '../service/provider';
import { ImageCompletionRequestManager } from '../service/request-manager';
import { ImageCompletionPromptPrivacyRequest } from '../dto/image-completion-prompt-privacy.request';

@ApiTags('image_completions')
@Controller('image_completions')
export class UpdateController {
  constructor(
    private provider: ImageCompletionProvider,
    private upscaleProvider: UpscaleProvider,
    private requestManager: ImageCompletionRequestManager,
    private upscaleManager: UpscaleManager,
  ) {}

  @Put(':id/interruption')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'image_completion_interrupt',
    summary: 'Interrupt image completion',
    description:
      'Interrupts the specified image completion for the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the image completion to interrupt\n',
  })
  @ApiNoContentResponse({
    description: 'Image completion interrupted successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - Image completion does not exist.
      - Image completion is not in a valid state for the requested update.
      - Invalid or unavailable parameters.
      - Failed to analyze request body.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description:
      'Forbidden. User does not have permission to interrupt this image completion.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the image completion to interrupt.',
    type: String,
  })
  async imageCompletionActivation(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ) {
    const entity = await this.provider.getBy({
      user: { id: request.user.id },
      id: id,
    });

    await this.requestManager.interrupt(entity);
  }

  @Put(':id/privacy')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'image_completion_update_privacy',
    summary: 'Update image completion privacy',
    description:
      'Updates the privacy of the specified image completion for the authenticated user.\n\n' +
      'Required Query Parameters:\n' +
      '- id: UUID of the image completion to update\n' +
      'Required Body Parameters:\n' +
      '- privacy: New privacy level (public, private, organization)\n',
  })
  @ApiNoContentResponse({ description: 'Privacy updated successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - \`privacy\`: Must be a valid value from the \`PrivacyEnum\`.
      - Image completion does not exist.
      - Invalid or unavailable parameters.
      - Failed to analyze request body.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User does not have permission to update privacy.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the image completion to update.',
    type: String,
  })
  @ApiBody({
    type: ImageCompletionPrivacyRequest,
    description: 'Privacy update parameters.',
  })
  async imageCompletionPrivacy(
    @Body() requestBody: ImageCompletionPrivacyRequest,
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ) {
    const entity = await this.provider.getBy({
      user: { id: request.user.id },
      id: id,
    });

    await this.requestManager.updatePrivacy(entity, requestBody);
  }

  @Put(':id/upscale-selection/:upscaleId')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'image_completion_upscale_selection',
    summary: 'Select an upscale for image completion',
    description:
      'Marks the specified upscale as selected for the image completion.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the image completion\n' +
      '- upscaleId: UUID of the upscale to select\n',
  })
  @ApiNoContentResponse({ description: 'Upscale selected successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - \`upscaleId\`: Must be a valid UUID.
      - Image completion does not exist.
      - Upscale does not exist.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User does not have permission to select upscale.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the image completion.',
    type: String,
  })
  @ApiParam({
    name: 'upscaleId',
    description: 'UUID of the upscale to select.',
    type: String,
  })
  async imageCompletionUpscaleSelection(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
    @Param('upscaleId', new ParseUUIDPipe()) upscaleId: string,
  ) {
    await this.provider.getBy({
      user: { id: request.user.id },
      id: id,
    });

    const upscale = await this.upscaleProvider.get(upscaleId);

    await this.upscaleManager.markAsSelected(upscale);
  }

  @Put(':id/prompt-privacy')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'image_completion_update_prompt_privacy',
    summary: 'Update prompt privacy for image completion',
    description:
      'Updates the prompt privacy of the specified image completion for the authenticated user.\n\n' +
      'Required Query Parameters:\n' +
      '- id: UUID of the image completion to update\n' +
      'Required Body Parameters:\n' +
      '- hidePrompt: Boolean indicating if the prompt should be hidden\n',
  })
  @ApiNoContentResponse({ description: 'Prompt privacy updated successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - \`hidePrompt\`: Must be a valid boolean.
      - Image completion does not exist.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description:
      'Forbidden. User does not have permission to update prompt privacy.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the image completion to update.',
    type: String,
  })
  @ApiBody({
    type: ImageCompletionPromptPrivacyRequest,
    description: 'Prompt privacy update parameters.',
  })
  async imageCompletionPromptHidden(
    @Body() requestBody: ImageCompletionPromptPrivacyRequest,
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ) {
    const entity = await this.provider.getBy({
      user: { id: request.user.id },
      id: id,
    });

    await this.requestManager.updatePromptPrivacy(entity, requestBody);
  }
}
