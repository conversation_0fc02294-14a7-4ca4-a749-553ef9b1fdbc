import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1712755301780 implements MigrationInterface {
    name = 'Api1712755301780'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_auth_token"
            ADD "name" character varying
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_auth_token" DROP COLUMN "name"
        `);
    }

}
