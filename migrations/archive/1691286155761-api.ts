import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1691286155761 implements MigrationInterface {
    name = 'Api1691286155761'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_external_profile"
            ADD "deleted_at" TIMESTAMP
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_external_profile" DROP COLUMN "deleted_at"
        `);
    }

}
