import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1741833968658 implements MigrationInterface {
  name = 'Api1741833968658';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "organization" DROP CONSTRAINT "FK_44b825ff54ae787d79fb347c625"
        `);
    await queryRunner.query(`
            ALTER TABLE "organization" DROP COLUMN "owner_id"
        `);
    await queryRunner.query(`
            ALTER TABLE "image_edit"
            ADD "organization_id" uuid
        `);
    await queryRunner.query(`
            ALTER TABLE "upscale"
            ADD "organization_id" uuid
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion"
            ADD "organization_id" uuid
        `);
    await queryRunner.query(`
            ALTER TABLE "transaction"
            ADD "organization_id" uuid
        `);
    await queryRunner.query(`
            CREATE INDEX "idx_image_edit_organization_id" ON "image_edit" ("organization_id")
        `);
    await queryRunner.query(`
            CREATE INDEX "idx_upscale_organization_id" ON "upscale" ("organization_id")
        `);
    await queryRunner.query(`
            CREATE INDEX "idx_image_completion_organization_id" ON "image_completion" ("organization_id")
        `);
    await queryRunner.query(`
            CREATE INDEX "idx_transaction_organization_id" ON "transaction" ("organization_id")
        `);
    await queryRunner.query(`
            ALTER TABLE "image_edit"
            ADD CONSTRAINT "FK_f27b3fc2e4368848c769e7cc5c0" FOREIGN KEY ("organization_id") REFERENCES "organization"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "upscale"
            ADD CONSTRAINT "FK_dec1a6f5bccfb39b9c30eb25fd3" FOREIGN KEY ("organization_id") REFERENCES "organization"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion"
            ADD CONSTRAINT "FK_4f092a0b65217fcce687c550fc6" FOREIGN KEY ("organization_id") REFERENCES "organization"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "transaction"
            ADD CONSTRAINT "FK_15bd8509dd44ed40187eb9bc44d" FOREIGN KEY ("organization_id") REFERENCES "organization"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "transaction" DROP CONSTRAINT "FK_15bd8509dd44ed40187eb9bc44d"
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion" DROP CONSTRAINT "FK_4f092a0b65217fcce687c550fc6"
        `);
    await queryRunner.query(`
            ALTER TABLE "upscale" DROP CONSTRAINT "FK_dec1a6f5bccfb39b9c30eb25fd3"
        `);
    await queryRunner.query(`
            ALTER TABLE "image_edit_image_completion" DROP CONSTRAINT "FK_0e38bb7b48ae646612034cba015"
        `);
    await queryRunner.query(`
            ALTER TABLE "image_edit" DROP CONSTRAINT "FK_f27b3fc2e4368848c769e7cc5c0"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."idx_transaction_organization_id"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."idx_image_completion_organization_id"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."idx_upscale_organization_id"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."idx_image_edit_organization_id"
        `);
    await queryRunner.query(`
            ALTER TABLE "image_edit_image_completion" DROP CONSTRAINT "UQ_0e38bb7b48ae646612034cba015"
        `);
    await queryRunner.query(`
            ALTER TABLE "transaction" DROP COLUMN "organization_id"
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion" DROP COLUMN "organization_id"
        `);
    await queryRunner.query(`
            ALTER TABLE "upscale" DROP COLUMN "organization_id"
        `);
    await queryRunner.query(`
            ALTER TABLE "image_edit" DROP COLUMN "organization_id"
        `);
    await queryRunner.query(`
            ALTER TABLE "organization"
            ADD "owner_id" uuid NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "organization"
            ADD CONSTRAINT "FK_44b825ff54ae787d79fb347c625" FOREIGN KEY ("owner_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }
}
