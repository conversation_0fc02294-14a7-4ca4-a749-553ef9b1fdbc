import { BadRequestException, Injectable } from '@nestjs/common';
import {
  ImageCompletionEntity,
  StatusEnum as ImageCompletionStatusEnum,
} from 'src/image-completion/entity/image-completion.entity';
import { UserEntity } from '../../user/entity/user.entity';
import { BoardRequest } from '../dto/board.request';
import { BoardSearchRequest } from '../dto/board.search-request';
import { BoardEntity } from '../entity/board.entity';
import { BoardImageCompletionProvider } from './board-image-completion.provider';
import { BoardManager } from './manager';

@Injectable()
export class BoardRequestManager {
  constructor(
    private boardManager: BoardManager,
    private boardImageCompletionProvider: BoardImageCompletionProvider,
  ) {}

  async create(request: BoardRequest, owner: UserEntity): Promise<BoardEntity> {
    const board = new BoardEntity();
    board.owner = owner;
    board.ownerId = owner.id;

    this.mapRequestData(board, request);

    await this.boardManager.create(board, owner);

    return board;
  }

  async update(
    board: BoardEntity,
    boardRequest: BoardRequest,
  ): Promise<BoardEntity> {
    this.mapRequestData(board, boardRequest);

    return this.boardManager.update(board);
  }

  async updateThumbnail(
    board: BoardEntity,
    imageCompletion: ImageCompletionEntity,
  ) {
    if (imageCompletion.status !== ImageCompletionStatusEnum.READY) {
      throw new BadRequestException('image_completion_not_available');
    }

    // const boardImageCompletion =
    //   await this.boardImageCompletionProvider.findOneBy({
    //     boardId: board.id,
    //     imageCompletionId: imageCompletion.id,
    //   });

    // if (!boardImageCompletion) {
    //   throw new BadRequestException('image_completion_not_linked');
    // }

    board.thumbnail = imageCompletion.imagePaths[0];

    await this.boardManager.update(board);
  }

  mapRequestData(board: BoardEntity, request: BoardRequest): void {
    board.name = 'name' in request ? request.name : board.name;
    board.description =
      'description' in request ? request.description : board.description;
    board.visibility =
      'visibility' in request ? request.visibility : board.visibility;
    board.safety = 'safety' in request ? request.safety : board.safety;
    board.challengeDeadline =
      'challengeDeadline' in request
        ? new Date(request.challengeDeadline)
        : board.challengeDeadline;
  }

  sanitizeSearchFilters(
    inputFilters: BoardSearchRequest,
    currentUserId: string,
  ): any {
    return {
      memberId: currentUserId,
      ...inputFilters,
    };
  }
}
