import {
  Controller,
  Delete,
  Get,
  HttpCode,
  Param,
  ParseUUIDPipe,
  Put,
  Query,
  Request,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiQuery,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiUnauthorizedResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import { BaseFindResponseHeadersDto } from 'src/core/dto/base-find-response-headers.dto';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { ImageCompletionLikeDto } from '../dto/image-completion-like.dto';
import { ImageCompletionLikeSearchRequest } from '../dto/image-completion-like.search-request';
import { ImageCompletionLikeProvider } from '../service/like.provider';
import { ImageCompletionLikeRequestManager } from '../service/like.request-manager';
import { ImageCompletionLikeResponseMapper } from '../service/like.response-mapper';
import { ImageCompletionProvider } from '../service/provider';
import { AuthOptional, Public } from 'src/core/security/public-routes';
import { LikesRankDto } from '../dto/likes-rank.dto';
import { LikesRankingSearchRequest } from '../dto/likes-ranking.search.request';

@ApiTags('image_completions / likes')
@Controller('image_completions')
export class LikeController {
  constructor(
    private imageCompletionProvider: ImageCompletionProvider,
    private imageCompletionLikeProvider: ImageCompletionLikeProvider,
    private requestManager: ImageCompletionLikeRequestManager,
    private responseMapper: ImageCompletionLikeResponseMapper,
  ) {}

  @Get(':id/likes')
  @ApiOperation({
    operationId: 'image_completion_like_list',
    summary: 'List likes for an image completion',
    description:
      'Retrieves a paginated list of likes for the specified image completion.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the image completion\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of likes per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- userId: UUID of the user\n' +
      '- username: Username of the user\n' +
      '- modelIds[]: UUIDs of the models\n',
  })
  @ApiOkResponse({
    type: ImageCompletionLikeDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated list of likes for the image completion.',
  })
  @ApiQuery({
    type: ImageCompletionLikeSearchRequest,
    description: 'Query parameters for searching and paginating likes.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the image completion.',
    type: String,
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - Filter parameters must be valid values.
      - \`userId\`: Must be a valid UUID.
      - \`username\`: Must be a valid username.
      - \`modelIds\`: Must be a valid array of UUIDs.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @UsePipes(new ValidationPipe())
  async find(
    @Query() query: ImageCompletionLikeSearchRequest,
    @Res() res: Response,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;
    const filters = this.validateSearchFilters(inputFilters, id);

    const entities = await this.imageCompletionLikeProvider.findBy(
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    const totalCount = await this.imageCompletionLikeProvider.countBy(filters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultiple(entities));
  }

  @Get('likes')
  @ApiOperation({
    operationId: 'image_completion_like_list_user',
    summary: 'List likes by current user',
    description:
      'Retrieves a paginated list of likes made by the authenticated user on image completions.\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of likes per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- userId: UUID of the user\n' +
      '- username: Username of the user\n' +
      '- modelIds[]: UUIDs of the models\n',
  })
  @ApiOkResponse({
    type: ImageCompletionLikeDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated list of likes by the user.',
  })
  @ApiQuery({
    type: ImageCompletionLikeSearchRequest,
    description: 'Query parameters for searching and paginating likes.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - Filter parameters must be valid values.
      - \`userId\`: Must be a valid UUID.
      - \`username\`: Must be a valid username.
      - \`modelIds\`: Must be a valid array of UUIDs.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @UsePipes(new ValidationPipe())
  async findLikes(
    @Query() query: ImageCompletionLikeSearchRequest,
    @Request() request,
    @Res() res: Response,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;

    delete inputFilters.username;
    inputFilters.userId = request.user.id;

    const entities = await this.imageCompletionLikeProvider.findBy(
      inputFilters,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    const totalCount = await this.imageCompletionLikeProvider.countBy(
      inputFilters,
    );

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(
      await this.responseMapper.mapMultiple(entities, true, request.user.id),
    );
  }

  @Put(':id/likes')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'image_completion_like',
    summary: 'Like an image completion',
    description:
      'Likes the specified image completion as the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the image completion to like\n',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the image completion to like.',
    type: String,
  })
  @ApiNoContentResponse({ description: 'Image completion liked successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - Image completion does not exist.
      - User has already liked the image completion.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async like(@Request() request, @Param('id', new ParseUUIDPipe()) id: string) {
    const entity = await this.imageCompletionProvider.getBy({
      id: id,
    });

    await this.requestManager.like(entity, request.user.id);
  }

  @Delete(':id/likes')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'image_completion_unlike',
    summary: 'Unlike an image completion',
    description:
      'Removes the like from the specified image completion as the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the image completion to unlike\n',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the image completion to unlike.',
    type: String,
  })
  @ApiNoContentResponse({
    description: 'Image completion unliked successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - Image completion does not exist.
      - User has not liked the image completion.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async unlike(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ) {
    const entity = await this.imageCompletionProvider.getBy({
      id: id,
    });

    await this.requestManager.unlike(entity, request.user.id);
  }

  @Get('likes-ranking')
  @ApiOperation({
    operationId: 'image_completion_likes_ranking',
    summary: 'Get likes ranking',
    description:
      'Retrieves a paginated ranking of image completions by number of likes.\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of items per page\n' +
      '- day: Filter by items created today\n' +
      '- week: Filter by items created this week\n' +
      '- month: Filter by items created this month\n' +
      '- year: Filter by items created this year\n',
  })
  @ApiOkResponse({
    type: LikesRankDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated likes ranking.',
  })
  @ApiQuery({
    type: LikesRankingSearchRequest,
    description: 'Query parameters for likes ranking.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Filter parameters must be valid values.
      - \`day\`: Must be a valid boolean value.
      - \`week\`: Must be a valid boolean value.
      - \`month\`: Must be a valid boolean value.
      - \`year\`: Must be a valid boolean value.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @AuthOptional()
  @Public()
  async getLikesRanking(
    @Res() res: Response,
    @Query() query: LikesRankingSearchRequest,
  ): Promise<void> {
    const { dtos, totalCount } = await this.requestManager.generateLikesRanking(
      query,
    );

    setPaginationHeaders(res, totalCount, query.page, query.limit);

    res.send(dtos);
  }

  validateSearchFilters(
    filters: ImageCompletionLikeSearchRequest,
    imageCompletionId: string,
  ): any {
    return { ...filters, imageCompletionId: imageCompletionId };
  }
}
