import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1711090156659 implements MigrationInterface {
    name = 'Api1711090156659'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "image_completion"
            ADD "published_at" TIMESTAMP
        `);

        await queryRunner.query(`
            UPDATE image_completion SET published_at = NOW() WHERE published_at IS NULL AND privacy = 'public'
        `);

        await queryRunner.query(`
            UPDATE user_account 
            SET images_available = ( 
                SELECT COUNT(*) 
                FROM image_completion i 
                WHERE i.user_id = user_account.id 
                AND i.privacy = 'public' 
                AND i.deleted_at IS NULL
            )
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "image_completion" DROP COLUMN "published_at"
        `);
    }

}
