import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1706758336428 implements MigrationInterface {
    name = 'Api1706758336428'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "model"
            ADD "status_detail" text
        `);
        await queryRunner.query(`
            ALTER TABLE "model"
            ADD "settings" json
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "model" DROP COLUMN "settings"
        `);
        await queryRunner.query(`
            ALTER TABLE "model" DROP COLUMN "status_detail"
        `);
    }

}
