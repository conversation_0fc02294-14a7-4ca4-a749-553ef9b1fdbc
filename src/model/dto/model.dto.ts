import { ApiProperty } from '@nestjs/swagger';
import { PublicUserDto } from 'src/user/dto/public.user.dto';
import { ModelVersionDto } from './model-version.dto';
import { ModelOrganizationDto } from './model-organization.dto';

export class ModelDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  user?: PublicUserDto;

  @ApiProperty()
  userId?: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  username: string;

  @ApiProperty()
  type: string;

  @ApiProperty()
  class: string;

  @ApiProperty()
  description?: string;

  @ApiProperty()
  website?: string;

  @ApiProperty()
  status: string;

  @ApiProperty()
  progress: number;

  @ApiProperty()
  privacy: string;

  @ApiProperty()
  usages: number;

  @ApiProperty()
  imagesAvailable: number;

  @ApiProperty()
  likes: number;

  @ApiProperty()
  isBookmarked: boolean;

  @ApiProperty()
  comments: number;

  @ApiProperty()
  version: number;

  @ApiProperty()
  storageBucket?: string;

  @ApiProperty()
  storagePath?: string;

  @ApiProperty()
  modelPath?: string;

  @ApiProperty()
  imagesPath?: string;

  @ApiProperty()
  thumbnailsPath?: string;

  @ApiProperty()
  thumbnail?: string;

  @ApiProperty()
  thumbnailOptions?: any;

  @ApiProperty()
  settings?: any;

  @ApiProperty()
  generationData?: string;

  @ApiProperty()
  images?: any;

  @ApiProperty()
  isSponsored?: boolean;

  @ApiProperty()
  isFeatured?: boolean;

  @ApiProperty()
  isActive?: boolean;

  @ApiProperty()
  blockedAt?: Date;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  systemVersions: number[];

  @ApiProperty({ type: () => [ModelVersionDto] })
  versions?: ModelVersionDto[];

  @ApiProperty()
  webhookUrl?: string;

  @ApiProperty({
    description:
      'Organizations that have access to this model (only shown to authorized users)',
    type: [ModelOrganizationDto],
    required: false,
  })
  organizationAccess?: ModelOrganizationDto[];

  @ApiProperty()
  trainingMode?: string;
}
