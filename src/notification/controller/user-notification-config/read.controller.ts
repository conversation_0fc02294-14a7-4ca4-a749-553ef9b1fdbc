import { Controller, Get, Request } from '@nestjs/common';
import {
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiInternalServerErrorResponse,
  ApiBadRequestResponse,
} from '@nestjs/swagger';
import { UserNotificationConfigDto } from '../../dto/user-notification-config.dto';
import { UserNotificationConfigProvider } from '../../service/user-notification-config.provider';
import { UserNotificationConfigResponseMapper } from '../../service/user-notification-config.response-mapper';
import { BaseFindResponseHeadersDto } from 'src/core/dto/base-find-response-headers.dto';

@ApiTags('user')
@Controller('users/current/notification-config')
export class ReadController {
  constructor(
    private provider: UserNotificationConfigProvider,
    private responseMapper: UserNotificationConfigResponseMapper,
  ) {}

  @ApiOperation({
    operationId: 'user_notification_config_list',
    summary: 'List notification configurations',
    description:
      'Retrieves a list of notification configurations for the current user.',
  })
  @ApiOkResponse({
    type: UserNotificationConfigDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'List of notification configurations.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - Filter parameters must be valid values.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Get()
  async findCurrentNotification(
    @Request() request,
  ): Promise<UserNotificationConfigDto[]> {
    const filters = { user: { id: request.user.id } };

    const entities = await this.provider.findBy(filters, 1, 999);

    return this.responseMapper.mapMultiple(entities);
  }
}
