import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CleanupAuditService } from 'src/core/service/cleanup-audit.service';
import { S3BatchDeleteService } from 'src/core/service/s3-batch-delete.service';
import { CleanupOperationTypeEnum } from 'src/core/enum/cleanup-status.enum';
import { UserDeletedEvent } from 'src/user/event/user-deleted.event';
import { UserImageEntity } from '../entity/user-image.entity';

@Injectable()
export class UserImageDeletedListener {
  constructor(
    @InjectRepository(UserImageEntity)
    private userImageRepository: Repository<UserImageEntity>,
    private cleanupAuditService: CleanupAuditService,
    private s3BatchDeleteService: S3BatchDeleteService,
  ) {}

  @OnEvent('user.deleted', { async: true })
  async handleUserDeletedEvent(event: UserDeletedEvent) {
    const operationId = this.cleanupAuditService.startOperation(
      CleanupOperationTypeEnum.USER_IMAGES,
      event.id,
      event.email,
      { username: event.username }
    );

    try {
      // Find all user images (including soft-deleted ones)
      const userImages = await this.userImageRepository.find({
        where: { userId: event.id },
        withDeleted: true,
      });

      let totalProcessed = 0;
      let totalDeleted = 0;
      let totalFailed = 0;

      if (userImages.length > 0) {
        // Group images by storage bucket for efficient batch deletion
        const bucketGroups: Record<string, string[]> = {};

        for (const image of userImages) {
          totalProcessed++;

          // Use storageBucket and storagePath if available, otherwise construct from imagePath
          const bucket = image.storageBucket || 'letz-ai-images'; // Default bucket
          const key = image.storagePath || image.imagePath;

          if (key && this.s3BatchDeleteService.validateS3Path(bucket, key)) {
            if (!bucketGroups[bucket]) {
              bucketGroups[bucket] = [];
            }
            bucketGroups[bucket].push(key);
          }
        }

        // Delete files from S3 in batches per bucket
        for (const [bucket, keys] of Object.entries(bucketGroups)) {
          if (keys.length > 0) {
            const deleteResult = await this.s3BatchDeleteService.deleteWithRetry(bucket, keys, 3);
            totalDeleted += deleteResult.deleted.length;
            totalFailed += deleteResult.failed.length;
          }
        }
      }

      // Soft delete the database records
      const dbResult = await this.userImageRepository.softDelete({ userId: event.id });
      totalProcessed += dbResult.affected || 0;

      this.cleanupAuditService.completeOperation(
        operationId,
        totalProcessed,
        totalDeleted,
        totalFailed
      );

    } catch (error) {
      this.cleanupAuditService.failOperation(
        operationId,
        `Failed to cleanup user images: ${error.message}`
      );
      throw error;
    }
  }
}
