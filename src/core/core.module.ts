import { <PERSON>du<PERSON> } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>roller } from './controller/ping.controller';
import { AppConfigurationService } from './service/app-configuration.service';
import { LocalCacheService } from './service/local-cache.service';
import { Mailer } from './service/mailer.service';
import { OpenAIFactory } from './service/openai.factory';
import { BlockedEmailDomainsService } from './validation/blocked-email-domains.service';
import { IsEmailDomainAllowedConstraint } from './validation/is-email-domain-allowed.decorator';

@Module({
  controllers: [PingController],
  providers: [
    AppConfigurationService,
    LocalCacheService,
    Mailer,
    OpenAIFactory,
    BlockedEmailDomainsService,
    IsEmailDomainAllowedConstraint,
  ],
  exports: [
    AppConfigurationService,
    LocalCacheService,
    Mailer,
    'OPENAI',
    BlockedEmailDomainsService,
    IsEmailDomainAllowedConstraint,
  ],
})
export class CoreModule {}
