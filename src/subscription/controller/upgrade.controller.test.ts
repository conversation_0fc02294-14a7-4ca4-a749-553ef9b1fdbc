import { BadRequestException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { UserEntity } from 'src/user/entity/user.entity';
import { UserProvider } from '../../user/service/provider';
import { SubscriptionUpgradePreviewDto } from '../dto/subscription-upgrade-preview.dto';
import { SubscriptionUpgradeRequest } from '../dto/subscription-upgrade.request';
import { SubscriptionDto } from '../dto/subscription.dto';
import { SubscriptionEntity } from '../entity/subscription.entity';
import { SubscriptionResponseMapper } from '../service/response-mapper';
import { UpgradeManager } from '../service/upgrade.manager';
import { UpgradeController } from './upgrade.controller';

describe('UpgradeController', () => {
  let controller: UpgradeController;
  let upgradeManager: UpgradeManager;
  let userProvider: UserProvider;
  let responseMapper: SubscriptionResponseMapper;

  const mockUpgradeManager = {
    previewUpgrade: jest.fn(),
    executeUpgrade: jest.fn(),
    getUpgradeOptions: jest.fn(),
  };

  const mockUserProvider = {
    get: jest.fn(),
  };

  const mockResponseMapper = {
    map: jest.fn(),
  };

  const mockUser = {
    id: 'user_123',
    email: '<EMAIL>',
  } as UserEntity;

  const mockRequest = {
    user: {
      id: 'user_123',
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UpgradeController],
      providers: [
        {
          provide: UpgradeManager,
          useValue: mockUpgradeManager,
        },
        {
          provide: UserProvider,
          useValue: mockUserProvider,
        },
        {
          provide: SubscriptionResponseMapper,
          useValue: mockResponseMapper,
        },
      ],
    }).compile();

    controller = module.get<UpgradeController>(UpgradeController);
    upgradeManager = module.get<UpgradeManager>(UpgradeManager);
    userProvider = module.get<UserProvider>(UserProvider);
    responseMapper = module.get<SubscriptionResponseMapper>(SubscriptionResponseMapper);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('previewUpgrade', () => {
    it('should preview upgrade without voucher code', async () => {
      const targetCreditPackageId = 'pkg_yearly_123';
      const expectedPreview: SubscriptionUpgradePreviewDto = {
        currentSubscriptionId: 'sub_123',
        currentPackageName: 'Pro',
        currentPrice: 749,
        targetPackageName: 'Pro - Yearly',
        targetPrice: 7490,
        proratedAmount: 6741,
        creditedAmount: 374,
        netAmount: 6741,
        isYearlyUpgrade: true,
        nextBillingDate: new Date('2025-01-01'),
        yearlySavings: 1498,
        upgradeAllowed: true,
        finalAmount: 6741,
      };

      mockUserProvider.get.mockResolvedValue(mockUser);
      mockUpgradeManager.previewUpgrade.mockResolvedValue(expectedPreview);

      const result = await controller.previewUpgrade(
        mockRequest,
        targetCreditPackageId,
      );

      expect(userProvider.get).toHaveBeenCalledWith('user_123');
      expect(upgradeManager.previewUpgrade).toHaveBeenCalledWith(mockUser, {
        targetCreditPackageId,
        voucherCode: undefined,
      });
      expect(result).toEqual(expectedPreview);
    });

    it('should preview upgrade with voucher code', async () => {
      const targetCreditPackageId = 'pkg_yearly_123';
      const voucherCode = 'SAVE20';
      const expectedPreview: SubscriptionUpgradePreviewDto = {
        currentSubscriptionId: 'sub_123',
        currentPackageName: 'Pro',
        currentPrice: 749,
        targetPackageName: 'Pro - Yearly',
        targetPrice: 7490,
        proratedAmount: 6741,
        creditedAmount: 374,
        netAmount: 6741,
        isYearlyUpgrade: true,
        nextBillingDate: new Date('2025-01-01'),
        yearlySavings: 1498,
        upgradeAllowed: true,
        appliedVoucherCode: 'SAVE20',
        voucherDiscount: 2000,
        finalAmount: 4741,
      };

      mockUserProvider.get.mockResolvedValue(mockUser);
      mockUpgradeManager.previewUpgrade.mockResolvedValue(expectedPreview);

      const result = await controller.previewUpgrade(
        mockRequest,
        targetCreditPackageId,
        voucherCode,
      );

      expect(upgradeManager.previewUpgrade).toHaveBeenCalledWith(mockUser, {
        targetCreditPackageId,
        voucherCode,
      });
      expect(result).toEqual(expectedPreview);
    });

    it('should handle upgrade blocked scenarios', async () => {
      const targetCreditPackageId = 'pkg_downgrade_123';
      const expectedPreview: SubscriptionUpgradePreviewDto = {
        currentSubscriptionId: 'sub_123',
        currentPackageName: 'Pro',
        currentPrice: 749,
        targetPackageName: 'Beginner',
        targetPrice: 99,
        proratedAmount: 0,
        creditedAmount: 0,
        netAmount: 0,
        isYearlyUpgrade: false,
        nextBillingDate: new Date(),
        yearlySavings: 0,
        upgradeAllowed: false,
        upgradeBlockedReason: 'upgrade.downgrade_not_allowed',
        finalAmount: 0,
      };

      mockUserProvider.get.mockResolvedValue(mockUser);
      mockUpgradeManager.previewUpgrade.mockResolvedValue(expectedPreview);

      const result = await controller.previewUpgrade(
        mockRequest,
        targetCreditPackageId,
      );

      expect(result.upgradeAllowed).toBe(false);
      expect(result.upgradeBlockedReason).toBe('upgrade.downgrade_not_allowed');
    });

    it('should handle errors from upgrade manager', async () => {
      mockUserProvider.get.mockResolvedValue(mockUser);
      mockUpgradeManager.previewUpgrade.mockRejectedValue(
        new BadRequestException('upgrade.no_active_subscription'),
      );

      await expect(
        controller.previewUpgrade(mockRequest, 'pkg_invalid_123'),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('executeUpgrade', () => {
    it('should execute upgrade without voucher code', async () => {
      const upgradeRequest: SubscriptionUpgradeRequest = {
        targetCreditPackageId: 'pkg_yearly_123',
        confirmProration: true,
      };

      const mockUpgradedSubscription = {
        id: 'sub_new_123',
        userId: 'user_123',
        name: 'Pro - Yearly',
        price: 7490,
      } as SubscriptionEntity;

      const mockSubscriptionDto: SubscriptionDto = {
        id: 'sub_new_123',
        userId: 'user_123',
        name: 'Pro - Yearly',
        price: 7490,
        creditPackage: 'pkg_yearly_123',
        status: 'ACTIVE',
        createdAt: new Date(),
      };

      mockUserProvider.get.mockResolvedValue(mockUser);
      mockUpgradeManager.executeUpgrade.mockResolvedValue(mockUpgradedSubscription);
      mockResponseMapper.map.mockResolvedValue(mockSubscriptionDto);

      const result = await controller.executeUpgrade(upgradeRequest, mockRequest);

      expect(userProvider.get).toHaveBeenCalledWith('user_123');
      expect(upgradeManager.executeUpgrade).toHaveBeenCalledWith(mockUser, upgradeRequest);
      expect(responseMapper.map).toHaveBeenCalledWith(mockUpgradedSubscription);
      expect(result).toEqual(mockSubscriptionDto);
    });

    it('should execute upgrade with voucher code', async () => {
      const upgradeRequest: SubscriptionUpgradeRequest = {
        targetCreditPackageId: 'pkg_yearly_123',
        confirmProration: true,
        voucherCode: 'SAVE20',
      };

      const mockUpgradedSubscription = {
        id: 'sub_new_123',
        userId: 'user_123',
        name: 'Pro - Yearly',
        price: 7490,
      } as SubscriptionEntity;

      const mockSubscriptionDto: SubscriptionDto = {
        id: 'sub_new_123',
        userId: 'user_123',
        name: 'Pro - Yearly',
        price: 7490,
        creditPackage: 'pkg_yearly_123',
        status: 'ACTIVE',
        createdAt: new Date(),
      };

      mockUserProvider.get.mockResolvedValue(mockUser);
      mockUpgradeManager.executeUpgrade.mockResolvedValue(mockUpgradedSubscription);
      mockResponseMapper.map.mockResolvedValue(mockSubscriptionDto);

      const result = await controller.executeUpgrade(upgradeRequest, mockRequest);

      expect(upgradeManager.executeUpgrade).toHaveBeenCalledWith(mockUser, upgradeRequest);
      expect(result).toEqual(mockSubscriptionDto);
    });

    it('should handle voucher validation errors', async () => {
      const upgradeRequest: SubscriptionUpgradeRequest = {
        targetCreditPackageId: 'pkg_yearly_123',
        confirmProration: true,
        voucherCode: 'INVALID_VOUCHER',
      };

      mockUserProvider.get.mockResolvedValue(mockUser);
      mockUpgradeManager.executeUpgrade.mockRejectedValue(
        new BadRequestException('voucher.invalid_code'),
      );

      await expect(
        controller.executeUpgrade(upgradeRequest, mockRequest),
      ).rejects.toThrow(BadRequestException);
    });

    it('should handle proration confirmation required errors', async () => {
      const upgradeRequest: SubscriptionUpgradeRequest = {
        targetCreditPackageId: 'pkg_yearly_123',
        confirmProration: false, // Not confirmed
      };

      mockUserProvider.get.mockResolvedValue(mockUser);
      mockUpgradeManager.executeUpgrade.mockRejectedValue(
        new BadRequestException('upgrade.proration_confirmation_required'),
      );

      await expect(
        controller.executeUpgrade(upgradeRequest, mockRequest),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('getUpgradeOptions', () => {
    it('should return available upgrade options', async () => {
      const mockUpgradeOptions = [
        {
          id: 'pkg_yearly_123',
          name: 'Pro - Yearly',
          price: 7490,
          expiresAfterMonths: 12,
          isRecurring: true,
        },
        {
          id: 'pkg_enterprise_123',
          name: 'Enterprise',
          price: 1499,
          expiresAfterMonths: 1,
          isRecurring: true,
        },
      ];

      mockUserProvider.get.mockResolvedValue(mockUser);
      mockUpgradeManager.getUpgradeOptions.mockResolvedValue(mockUpgradeOptions);

      const result = await controller.getUpgradeOptions(mockRequest);

      expect(userProvider.get).toHaveBeenCalledWith('user_123');
      expect(upgradeManager.getUpgradeOptions).toHaveBeenCalledWith(mockUser);
      expect(result).toEqual(mockUpgradeOptions);
    });

    it('should return empty array when no upgrade options available', async () => {
      mockUserProvider.get.mockResolvedValue(mockUser);
      mockUpgradeManager.getUpgradeOptions.mockResolvedValue([]);

      const result = await controller.getUpgradeOptions(mockRequest);

      expect(result).toEqual([]);
    });

    it('should handle user not found errors', async () => {
      mockUserProvider.get.mockRejectedValue(
        new BadRequestException('user.not_found'),
      );

      await expect(controller.getUpgradeOptions(mockRequest)).rejects.toThrow(
        BadRequestException,
      );
    });
  });
});
