import { Test, TestingModule } from '@nestjs/testing';
import { S3Client, DeleteObjectsCommand, ListObjectsV2Command, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { S3BatchDeleteService } from './s3-batch-delete.service';

describe('S3BatchDeleteService', () => {
  let service: S3BatchDeleteService;
  let mockS3: any;

  beforeEach(async () => {
    mockS3 = {
      send: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        S3BatchDeleteService,
        {
          provide: 'S3',
          useValue: mockS3,
        },
      ],
    }).compile();

    service = module.get<S3BatchDeleteService>(S3BatchDeleteService);
    
    // Mock the logger to avoid console output during tests
    jest.spyOn(service['logger'], 'log').mockImplementation();
    jest.spyOn(service['logger'], 'error').mockImplementation();
    jest.spyOn(service['logger'], 'warn').mockImplementation();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('batchDelete', () => {
    it('should handle empty keys array', async () => {
      const result = await service.batchDelete('test-bucket', []);
      
      expect(result.deleted).toEqual([]);
      expect(result.failed).toEqual([]);
      expect(mockS3.send).not.toHaveBeenCalled();
    });

    it('should delete files successfully', async () => {
      const keys = ['file1.jpg', 'file2.jpg', 'file3.jpg'];
      
      mockS3.send.mockResolvedValueOnce({
        Deleted: [
          { Key: 'file1.jpg' },
          { Key: 'file2.jpg' },
          { Key: 'file3.jpg' },
        ],
        Errors: [],
      });

      const result = await service.batchDelete('test-bucket', keys);

      expect(result.deleted).toEqual(['file1.jpg', 'file2.jpg', 'file3.jpg']);
      expect(result.failed).toEqual([]);
      expect(mockS3.send).toHaveBeenCalledWith(
        expect.any(DeleteObjectsCommand)
      );
      expect(service['logger'].log).toHaveBeenCalledWith(
        'Batch delete completed',
        expect.objectContaining({
          bucket: 'test-bucket',
          totalKeys: 3,
          deleted: 3,
          failed: 0,
        })
      );
    });

    it('should handle partial failures', async () => {
      const keys = ['file1.jpg', 'file2.jpg', 'file3.jpg'];
      
      mockS3.send.mockResolvedValueOnce({
        Deleted: [{ Key: 'file1.jpg' }, { Key: 'file2.jpg' }],
        Errors: [
          {
            Key: 'file3.jpg',
            Code: 'AccessDenied',
            Message: 'Access denied to delete object',
          },
        ],
      });

      const result = await service.batchDelete('test-bucket', keys);

      expect(result.deleted).toEqual(['file1.jpg', 'file2.jpg']);
      expect(result.failed).toEqual([
        {
          key: 'file3.jpg',
          error: 'AccessDenied: Access denied to delete object',
        },
      ]);
    });

    it('should handle batch sizes larger than MAX_BATCH_SIZE', async () => {
      // Create 1500 keys to test batching
      const keys = Array.from({ length: 1500 }, (_, i) => `file${i}.jpg`);
      
      // Mock two successful batch responses
      mockS3.send
        .mockResolvedValueOnce({
          Deleted: Array.from({ length: 1000 }, (_, i) => ({ Key: `file${i}.jpg` })),
          Errors: [],
        })
        .mockResolvedValueOnce({
          Deleted: Array.from({ length: 500 }, (_, i) => ({ Key: `file${i + 1000}.jpg` })),
          Errors: [],
        });

      const result = await service.batchDelete('test-bucket', keys);

      expect(result.deleted).toHaveLength(1500);
      expect(result.failed).toHaveLength(0);
      expect(mockS3.send).toHaveBeenCalledTimes(2);
    });

    it('should handle batch operation failures', async () => {
      const keys = ['file1.jpg', 'file2.jpg'];
      
      mockS3.send.mockRejectedValueOnce(new Error('Network error'));

      const result = await service.batchDelete('test-bucket', keys);

      expect(result.deleted).toEqual([]);
      expect(result.failed).toEqual([
        { key: 'file1.jpg', error: 'Network error' },
        { key: 'file2.jpg', error: 'Network error' },
      ]);
      expect(service['logger'].error).toHaveBeenCalledWith(
        'Batch delete failed',
        expect.objectContaining({
          bucket: 'test-bucket',
          batchSize: 2,
          error: 'Network error',
        })
      );
    });
  });

  describe('deleteFolder', () => {
    it('should list and delete all objects in a folder', async () => {
      const prefix = 'user123/images/';
      
      // Mock listing objects
      mockS3.send
        .mockResolvedValueOnce({
          Contents: [
            { Key: 'user123/images/file1.jpg' },
            { Key: 'user123/images/file2.jpg' },
          ],
          NextContinuationToken: undefined,
        })
        // Mock deleting objects
        .mockResolvedValueOnce({
          Deleted: [
            { Key: 'user123/images/file1.jpg' },
            { Key: 'user123/images/file2.jpg' },
          ],
          Errors: [],
        });

      const result = await service.deleteFolder('test-bucket', prefix);

      expect(result.deleted).toEqual(['user123/images/file1.jpg', 'user123/images/file2.jpg']);
      expect(result.failed).toEqual([]);
      
      // Should call list first, then delete
      expect(mockS3.send).toHaveBeenCalledWith(expect.any(ListObjectsV2Command));
      expect(mockS3.send).toHaveBeenCalledWith(expect.any(DeleteObjectsCommand));
    });

    it('should handle pagination when listing objects', async () => {
      const prefix = 'user123/models/';
      
      // Mock paginated listing
      mockS3.send
        .mockResolvedValueOnce({
          Contents: [{ Key: 'user123/models/file1.bin' }],
          NextContinuationToken: 'token123',
        })
        .mockResolvedValueOnce({
          Contents: [{ Key: 'user123/models/file2.bin' }],
          NextContinuationToken: undefined,
        })
        // Mock deleting objects
        .mockResolvedValueOnce({
          Deleted: [
            { Key: 'user123/models/file1.bin' },
            { Key: 'user123/models/file2.bin' },
          ],
          Errors: [],
        });

      const result = await service.deleteFolder('test-bucket', prefix);

      expect(result.deleted).toEqual(['user123/models/file1.bin', 'user123/models/file2.bin']);
      expect(mockS3.send).toHaveBeenCalledTimes(3); // 2 list calls, 1 delete call
    });

    it('should handle empty folders', async () => {
      const prefix = 'user123/empty/';
      
      mockS3.send.mockResolvedValueOnce({
        Contents: undefined,
        NextContinuationToken: undefined,
      });

      const result = await service.deleteFolder('test-bucket', prefix);

      expect(result.deleted).toEqual([]);
      expect(result.failed).toEqual([]);
      expect(mockS3.send).toHaveBeenCalledTimes(1); // Only list call
    });
  });

  describe('deleteObject', () => {
    it('should delete a single object successfully', async () => {
      mockS3.send.mockResolvedValueOnce({});

      const result = await service.deleteObject('test-bucket', 'file.jpg');

      expect(result).toBe(true);
      expect(mockS3.send).toHaveBeenCalledWith(expect.any(DeleteObjectCommand));
      expect(service['logger'].log).toHaveBeenCalledWith(
        'Object deleted successfully',
        { bucket: 'test-bucket', key: 'file.jpg' }
      );
    });

    it('should handle single object deletion failure', async () => {
      mockS3.send.mockRejectedValueOnce(new Error('Access denied'));

      const result = await service.deleteObject('test-bucket', 'file.jpg');

      expect(result).toBe(false);
      expect(service['logger'].error).toHaveBeenCalledWith(
        'Failed to delete object',
        expect.objectContaining({
          bucket: 'test-bucket',
          key: 'file.jpg',
          error: 'Access denied',
        })
      );
    });
  });

  describe('deleteWithRetry', () => {
    it('should succeed on first attempt', async () => {
      const keys = ['file1.jpg', 'file2.jpg'];
      
      mockS3.send.mockResolvedValueOnce({
        Deleted: [{ Key: 'file1.jpg' }, { Key: 'file2.jpg' }],
        Errors: [],
      });

      const result = await service.deleteWithRetry('test-bucket', keys, 3, 100);

      expect(result.deleted).toEqual(['file1.jpg', 'file2.jpg']);
      expect(result.failed).toEqual([]);
      expect(mockS3.send).toHaveBeenCalledTimes(1);
    });

    it('should retry failed operations', async () => {
      const keys = ['file1.jpg', 'file2.jpg'];
      
      // First attempt: partial failure
      mockS3.send
        .mockResolvedValueOnce({
          Deleted: [{ Key: 'file1.jpg' }],
          Errors: [{ Key: 'file2.jpg', Code: 'Temporary', Message: 'Try again' }],
        })
        // Second attempt: success
        .mockResolvedValueOnce({
          Deleted: [{ Key: 'file2.jpg' }],
          Errors: [],
        });

      const result = await service.deleteWithRetry('test-bucket', keys, 3, 10);

      expect(result.deleted).toEqual(['file1.jpg', 'file2.jpg']);
      expect(result.failed).toEqual([]);
      expect(mockS3.send).toHaveBeenCalledTimes(2);
    });

    it('should give up after max retries', async () => {
      const keys = ['file1.jpg'];
      
      // All attempts fail
      mockS3.send
        .mockResolvedValueOnce({
          Deleted: [],
          Errors: [{ Key: 'file1.jpg', Code: 'PermanentError', Message: 'Cannot delete' }],
        })
        .mockResolvedValueOnce({
          Deleted: [],
          Errors: [{ Key: 'file1.jpg', Code: 'PermanentError', Message: 'Cannot delete' }],
        })
        .mockResolvedValueOnce({
          Deleted: [],
          Errors: [{ Key: 'file1.jpg', Code: 'PermanentError', Message: 'Cannot delete' }],
        });

      const result = await service.deleteWithRetry('test-bucket', keys, 3, 10);

      expect(result.deleted).toEqual([]);
      expect(result.failed).toEqual([
        { key: 'file1.jpg', error: 'PermanentError: Cannot delete' },
      ]);
      expect(mockS3.send).toHaveBeenCalledTimes(3);
    });
  });

  describe('validateS3Path', () => {
    it('should validate correct S3 paths', () => {
      expect(service.validateS3Path('my-bucket', 'path/to/file.jpg')).toBe(true);
      expect(service.validateS3Path('bucket123', 'file.txt')).toBe(true);
    });

    it('should reject invalid S3 paths', () => {
      expect(service.validateS3Path('', 'file.jpg')).toBe(false);
      expect(service.validateS3Path('bucket', '')).toBe(false);
      expect(service.validateS3Path('bucket', null as any)).toBe(false);
    });
  });
});
