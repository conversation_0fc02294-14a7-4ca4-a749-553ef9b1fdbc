import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1737340856160 implements MigrationInterface {
    name = 'Api1737340856160'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "image_completion"
            ADD "original_image_completion_id" uuid
        `);
        await queryRunner.query(`
            CREATE INDEX "idx_image_completion_original_image_completion_id" ON "image_completion" ("original_image_completion_id")
        `);
        await queryRunner.query(`
            ALTER TABLE "image_completion"
            ADD CONSTRAINT "FK_b2c9823ad4fc3641d61fdcdf9c6" FOREIGN KEY ("original_image_completion_id") REFERENCES "image_completion"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "image_completion" DROP CONSTRAINT "FK_b2c9823ad4fc3641d61fdcdf9c6"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."idx_image_completion_original_image_completion_id"
        `);
        await queryRunner.query(`
            ALTER TABLE "image_completion" DROP COLUMN "original_image_completion_id"
        `);
    }

}
