import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { AbstractProvider } from 'src/core/service/abstract.provider';
import { LocalCacheService } from 'src/core/service/local-cache.service';
import {
  FindManyOptions,
  FindOneOptions,
  In,
  Repository,
  SelectQueryBuilder,
} from 'typeorm';
import { ImageCompletionLikeEntity } from '../entity/image-completion-like.entity';

@Injectable()
export class ImageCompletionLikeProvider extends AbstractProvider<ImageCompletionLikeEntity> {
  constructor(
    @InjectRepository(ImageCompletionLikeEntity)
    repository: Repository<ImageCompletionLikeEntity>,
    logger: Logger,
  ) {
    super(repository, logger);
  }

  async findBy(
    criteria: any,
    page: number,
    limit: number,
    sortBy = 'createdAt',
    sortOrder: 'ASC' | 'DESC' | 'RANDOM' = 'ASC',
  ): Promise<ImageCompletionLikeEntity[]> {
    const queryBuilder = this.prepareQueryBuilder(criteria, true);

    queryBuilder.offset((page - 1) * limit).limit(limit);

    if (sortOrder === 'RANDOM') {
      queryBuilder.orderBy('RANDOM()');
    } else {
      queryBuilder.orderBy(`imageCompletionLike.${sortBy}`, sortOrder);
    }

    return await queryBuilder.getMany();
  }

  async countBy(criteria: any): Promise<number> {
    const cacheKey = `count:${JSON.stringify(criteria)}`;

    if (LocalCacheService.isEnabled()) {
      const cachedCount = LocalCacheService.getCache(cacheKey);
      if (cachedCount !== null) {
        return cachedCount;
      }
    }

    const queryBuilder = this.prepareQueryBuilder(criteria, false);
    const count = await queryBuilder
      .select('COUNT(DISTINCT imageCompletionLike.id)', 'count')
      .getRawOne()
      .then((result) => Number(result.count));

    if (LocalCacheService.isEnabled()) {
      LocalCacheService.setCache(cacheKey, count);
    }

    return count;
  }

  async findRanking(
    start: string,
    end: string,
    page: number,
    limit: number,
    username: string = null,
  ) {
    let query = this.prepareRankingQuery(start, end, username);

    query += `
      ORDER BY COUNT(image_completion_like.id) DESC
      LIMIT ${limit}
      OFFSET ${(page - 1) * limit};
    `;
    return await this.repository.query(query);
  }

  async countRanking(start: string, end: string, userId: string = null) {
    const query =
      `SELECT COUNT(*) AS totalCount FROM ( ` +
      this.prepareRankingQuery(start, end, userId) +
      `) x`;

    const result = await this.repository.query(query);

    return result.totalCount;
  }

  prepareRankingQuery(start: string, end: string, userId: string = null) {
    let userFilter = '';

    if (userId) {
      userFilter = `AND image_completion.user_id = '${userId}'`;
    }

    return `
      SELECT image_completion.user_id AS user_id,
      COUNT(image_completion_like.id) AS likes_count,
      RANK() OVER (ORDER BY COUNT(*) DESC) AS rank
      FROM image_completion_like
      LEFT JOIN image_completion ON image_completion_like.image_completion_id = image_completion.id
      WHERE image_completion_like.created_at BETWEEN '${start}' AND '${end}'
      AND image_completion.privacy = 'public'
      ${userFilter}
      GROUP BY image_completion.user_id
    `;
  }

  prepareFindOneOptions(criteria: any): FindOneOptions {
    return {
      where: criteria,
      relations: {
        imageCompletion: true,
      },
    };
  }

  prepareFindManyOptions(
    criteria: any,
    page: number,
    limit: number,
    sortBy?: string,
    sortOrder = 'ASC',
  ): FindManyOptions<ImageCompletionLikeEntity> {
    if (criteria.username) {
      criteria.user = {
        username: criteria.username,
      };

      delete criteria.username;
    }

    if (criteria.modelIds) {
      criteria.imageCompletion = {
        models: {
          modelId: In(criteria.modelIds),
        },
      };

      delete criteria.modelIds;
    }

    const parentOptions = super.prepareFindManyOptions(
      criteria,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    return {
      ...parentOptions,
      relations: {
        imageCompletion: {
          user: true,
          models: true,
        },
        user: true,
      },
    };
  }

  prepareQueryBuilder(
    criteria: any,
    includeRelations = true,
  ): SelectQueryBuilder<ImageCompletionLikeEntity> {
    const where = { ...criteria };
    const queryBuilder = this.repository.createQueryBuilder(
      'imageCompletionLike',
    );

    // Add necessary joins based on criteria
    if (where.modelIds?.length > 0) {
      queryBuilder
        .innerJoin('imageCompletionLike.imageCompletion', 'imageCompletion')
        .innerJoin('imageCompletion.models', 'imageCompletionModel')
        .andWhere('imageCompletionModel.modelId IN (:...modelIds)', {
          modelIds: where.modelIds,
        });
      delete where.modelIds;
    }

    if (where.boardIds?.length > 0) {
      if (
        !queryBuilder.expressionMap.joinAttributes.some(
          (join) => join.alias.name === 'imageCompletion',
        )
      ) {
        queryBuilder.innerJoin(
          'imageCompletionLike.imageCompletion',
          'imageCompletion',
        );
      }
      queryBuilder
        .innerJoin('imageCompletion.boards', 'imageCompletionBoard')
        .andWhere('imageCompletionBoard.boardId IN (:...boardIds)', {
          boardIds: where.boardIds,
        });
      delete where.boardIds;
    }

    // Add remaining simple conditions
    Object.keys(where).forEach((key) => {
      queryBuilder.andWhere(`imageCompletionLike.${key} = :${key}`, {
        [key]: where[key],
      });
    });

    // Include all relations only when needed
    if (includeRelations) {
      if (
        !queryBuilder.expressionMap.joinAttributes.some(
          (join) => join.alias.name === 'imageCompletion',
        )
      ) {
        queryBuilder.leftJoinAndSelect(
          'imageCompletionLike.imageCompletion',
          'imageCompletion',
        );
      } else {
        // If we already have the join, add the select part
        queryBuilder.addSelect('imageCompletion');
      }
      queryBuilder
        .leftJoinAndSelect('imageCompletionLike.user', 'user')
        .leftJoinAndSelect('imageCompletion.user', 'owner');
    }

    return queryBuilder;
  }
}
