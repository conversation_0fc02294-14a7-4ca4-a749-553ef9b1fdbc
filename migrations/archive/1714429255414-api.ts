import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1714429255414 implements MigrationInterface {
    name = 'Api1714429255414'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "user_follow" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "follower_id" uuid,
                "following_id" uuid,
                "is_approved" boolean,
                "deleted_at" TIMESTAMP,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_9dcfbeea350dbb23069bea9d7eb" PRIMARY KEY ("id")
            )
        `);

        // await queryRunner.query(`
        //     ALTER TABLE "model" DROP COLUMN "is_hot"
        // `);

	await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "followers_count" integer DEFAULT '0'
        `);
        await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "following_count" integer DEFAULT '0'
        `);
        await queryRunner.query(`
            ALTER TABLE "user_follow"
            ADD CONSTRAINT "FK_afe3fdfb98cd47cd28108fa4846" FOREIGN KEY ("follower_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "user_follow"
            ADD CONSTRAINT "FK_3759ce2cf656e35680e1be81548" FOREIGN KEY ("following_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_follow" DROP CONSTRAINT "FK_3759ce2cf656e35680e1be81548"
        `);
        await queryRunner.query(`
            ALTER TABLE "user_follow" DROP CONSTRAINT "FK_afe3fdfb98cd47cd28108fa4846"
        `);
        await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "following_count"
        `);
        await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "followers_count"
        `);
        await queryRunner.query(`
            ALTER TABLE "model"
            ADD "is_hot" boolean NOT NULL DEFAULT false
        `);
        await queryRunner.query(`
            DROP TABLE "user_follow"
        `);
    }

}
