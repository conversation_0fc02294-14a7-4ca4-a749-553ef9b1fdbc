import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1741717158076 implements MigrationInterface {
  name = 'Api1741717158076';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TYPE "public"."organization_invite_status_enum" AS ENUM('pending', 'rejected', 'accepted')
        `);
    await queryRunner.query(`
            CREATE TABLE "organization_invite" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "organization_id" uuid NOT NULL,
                "user_id" uuid,
                "is_admin" boolean NOT NULL DEFAULT false,
                "email" character varying,
                "status" "public"."organization_invite_status_enum" NOT NULL DEFAULT 'pending',
                "invited_by" uuid,
                "invited_at" TIMESTAMP,
                "accepted_at" TIMESTAMP,
                "deleted_at" TIMESTAMP,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_b36f8c9832b1a4b21118585f8b7" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            CREATE INDEX "IDX_026f7b81efdb07af5e1fafb496" ON "organization_invite" ("organization_id")
        `);
    await queryRunner.query(`
            CREATE INDEX "IDX_7bd5f45961360aaf50feaf807e" ON "organization_invite" ("user_id")
        `);
    await queryRunner.query(`
            ALTER TABLE "organization"
            ADD "members" integer NOT NULL DEFAULT '1'
        `);
    await queryRunner.query(`
            ALTER TABLE "organization"
            ADD "seats_purchased" integer NOT NULL DEFAULT '1'
        `);

    await queryRunner.query(`
            ALTER TABLE "organization_invite"
            ADD CONSTRAINT "FK_026f7b81efdb07af5e1fafb4969" FOREIGN KEY ("organization_id") REFERENCES "organization"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "organization_invite"
            ADD CONSTRAINT "FK_7bd5f45961360aaf50feaf807e0" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "organization_invite" DROP CONSTRAINT "FK_7bd5f45961360aaf50feaf807e0"
        `);
    await queryRunner.query(`
            ALTER TABLE "organization_invite" DROP CONSTRAINT "FK_026f7b81efdb07af5e1fafb4969"
        `);

    await queryRunner.query(`
            ALTER TABLE "organization" DROP COLUMN "seats_purchased"
        `);
    await queryRunner.query(`
            ALTER TABLE "organization" DROP COLUMN "members"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."IDX_7bd5f45961360aaf50feaf807e"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."IDX_026f7b81efdb07af5e1fafb496"
        `);
    await queryRunner.query(`
            DROP TABLE "organization_invite"
        `);
    await queryRunner.query(`
            DROP TYPE "public"."organization_invite_status_enum"
        `);
  }
}
