import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddContextMode1749921000000 implements MigrationInterface {
  name = 'AddContextMode1749921000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TYPE "public"."image_edit_mode_enum"
            ADD VALUE 'context'
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Note: PostgreSQL doesn't support removing enum values directly
    // This would require recreating the enum type and updating the column
    await queryRunner.query(`
            ALTER TYPE "public"."image_edit_mode_enum"
            RENAME TO "image_edit_mode_enum_old"
        `);
    await queryRunner.query(`
            CREATE TYPE "public"."image_edit_mode_enum" AS ENUM('in', 'out', 'skin')
        `);
    await queryRunner.query(`
            ALTER TABLE "image_edit"
            ALTER COLUMN "mode" TYPE "public"."image_edit_mode_enum" USING "mode"::"text"::"public"."image_edit_mode_enum"
        `);
    await queryRunner.query(`
            DROP TYPE "public"."image_edit_mode_enum_old"
        `);
  }
}
