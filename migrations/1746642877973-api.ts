import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1746642877973 implements MigrationInterface {
  name = 'Api1746642877973';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "transaction"
            ALTER COLUMN "user_id" DROP NOT NULL
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "transaction"
            ALTER COLUMN "user_id"
            SET NOT NULL
        `);
  }
}
