import {
  <PERSON>,
  Get,
  Param,
  ParseUUIDPipe,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import {
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiInternalServerErrorResponse,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/service/jwt-auth.guard';
import { UserImageDto } from '../dto/user-image.dto';
import { UserImageProvider } from '../service/user-image.provider';
import { UserImageResponseMapper } from '../service/user-image.response-mapper';
import { BaseSearchRequest } from 'src/core/dto/base.search-request';

@ApiTags('user-images')
@Controller('user-images')
@UseGuards(JwtAuthGuard)
export class ReadController {
  constructor(
    private readonly userImageProvider: UserImageProvider,
    private readonly responseMapper: UserImageResponseMapper,
  ) {}

  @Get()
  @ApiOperation({
    operationId: 'user_image_list',
    summary: 'List user images',
    description:
      'Retrieves a paginated list of images uploaded by the authenticated user.\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of images per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n',
  })
  @ApiOkResponse({
    type: [UserImageDto],
    description: 'Paginated list of user images.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 100)',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by (default: createdAt)',
  })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    type: String,
    description: 'Sort order ("asc" or "desc", default: "DESC")',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiBadRequestResponse({
    description: 'Bad Request. Invalid query parameters.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async getUserImages(
    @Request() request,
    @Query() query: BaseSearchRequest,
  ): Promise<UserImageDto[]> {
    const { page, limit, sortBy, sortOrder } = query;
    const images = await this.userImageProvider.findBy(
      { userId: request.user.id },
      page,
      limit,
      sortBy,
      sortOrder,
    );

    return this.responseMapper.mapMany(images);
  }

  @Get(':id')
  @ApiOperation({
    operationId: 'user_image_get',
    summary: 'Get a specific user image',
    description:
      'Retrieves a specific image by its ID for the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- id: Image ID (UUID)\n',
  })
  @ApiParam({
    name: 'id',
    description: 'Image ID (UUID)',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponse({ type: UserImageDto, description: 'User image details.' })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiBadRequestResponse({ description: 'Bad Request. Invalid image ID.' })
  @ApiNotFoundResponse({
    description: 'Not Found. The image could not be found.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async getUserImage(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<UserImageDto> {
    const image = await this.userImageProvider.getBy({
      id,
      userId: request.user.id,
    });

    return this.responseMapper.map(image);
  }
}
