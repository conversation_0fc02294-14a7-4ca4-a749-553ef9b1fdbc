import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1696540723475 implements MigrationInterface {
  name = 'Api1696540723475';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "image_completion"
            ADD "regenerated_from_id" uuid
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion"
            ADD CONSTRAINT "FK_2267553fa16f349cbde19402451" FOREIGN KEY ("regenerated_from_id") REFERENCES "image_completion"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "image_completion" DROP COLUMN "regenerated_from_id"
        `);
  }
}
