import { Injectable } from '@nestjs/common';
import { <PERSON>gger } from 'nestjs-pino';
import { SubscriptionStatusEnum } from '../entity/subscription-status.enum';
import { EmailManager } from './email.manager';
import { SubscriptionManager } from './manager';
import { SubscriptionProvider } from './provider';

@Injectable()
export class DunningService {
  constructor(
    private readonly subscriptionProvider: SubscriptionProvider,
    private readonly subscriptionManager: SubscriptionManager,
    private readonly emailManager: EmailManager,
    private readonly logger: Logger,
  ) {}

  /**
   * Runs daily at 3 AM to process dunning management for failed payments
   * TODO: Add @Cron(CronExpression.EVERY_DAY_AT_3AM) when @nestjs/schedule is installed
   */
  async processDunningManagement(): Promise<void> {
    this.logger.log('dunning.process_started');

    try {
      await this.processGracePeriods();
      await this.sendDunningEmails();
      await this.processExpiredGracePeriods();

      this.logger.log('dunning.process_completed');
    } catch (error) {
      this.logger.error('dunning.process_error', {
        error: error.message || error,
      });
    }
  }

  /**
   * Process subscriptions in grace period
   */
  private async processGracePeriods(): Promise<void> {
    this.logger.log('dunning.grace_periods_started');

    const pastDueSubscriptions = await this.subscriptionProvider.findBy(
      {
        status: SubscriptionStatusEnum.PAST_DUE,
        creditPackage: {
          expiresAfterMonths: 12,
        },
      },
      1,
      1000,
    );

    let processedCount = 0;
    let inGracePeriodCount = 0;

    for (const subscription of pastDueSubscriptions) {
      try {
        const gracePeriodInfo =
          await this.subscriptionManager.handleGracePeriod(subscription);

        if (gracePeriodInfo.inGracePeriod) {
          inGracePeriodCount++;

          this.logger.log('dunning.subscription_in_grace_period', {
            subscriptionId: subscription.id,
            userId: subscription.userId,
            daysRemaining: gracePeriodInfo.daysRemaining,
          });
        }

        processedCount++;
      } catch (error) {
        this.logger.error('dunning.grace_period_processing_failed', {
          subscriptionId: subscription.id,
          error: error.message || error,
        });
      }
    }

    this.logger.log('dunning.grace_periods_completed', {
      totalProcessed: processedCount,
      inGracePeriodCount,
    });
  }

  /**
   * Send dunning emails to users with failed payments
   */
  private async sendDunningEmails(): Promise<void> {
    this.logger.log('dunning.emails_started');

    const pastDueSubscriptions = await this.subscriptionProvider.findBy(
      {
        status: SubscriptionStatusEnum.PAST_DUE,
        creditPackage: {
          expiresAfterMonths: 12,
        },
      },
      1,
      1000,
    );

    let emailsSent = 0;
    let emailsFailed = 0;

    for (const subscription of pastDueSubscriptions) {
      try {
        const gracePeriodInfo =
          await this.subscriptionManager.handleGracePeriod(subscription);

        if (gracePeriodInfo.inGracePeriod) {
          // Send dunning email based on days remaining
          const emailType = this.getDunningEmailType(
            gracePeriodInfo.daysRemaining || 0,
          );

          if (emailType) {
            await this.sendDunningEmail(
              subscription,
              emailType,
              gracePeriodInfo,
            );
            emailsSent++;
          }
        }
      } catch (error) {
        this.logger.error('dunning.email_failed', {
          subscriptionId: subscription.id,
          error: error.message || error,
        });
        emailsFailed++;
      }
    }

    this.logger.log('dunning.emails_completed', {
      emailsSent,
      emailsFailed,
    });
  }

  /**
   * Process subscriptions that have exceeded grace period
   */
  private async processExpiredGracePeriods(): Promise<void> {
    this.logger.log('dunning.expired_grace_periods_started');

    try {
      await this.subscriptionManager.processExpiredGracePeriods();
      this.logger.log('dunning.expired_grace_periods_completed');
    } catch (error) {
      this.logger.error('dunning.expired_grace_periods_error', {
        error: error.message || error,
      });
    }
  }

  /**
   * Determine which dunning email to send based on days remaining
   */
  private getDunningEmailType(daysRemaining: number): string | null {
    if (daysRemaining === 7) {
      return 'payment_failed_initial';
    } else if (daysRemaining === 3) {
      return 'payment_failed_reminder';
    } else if (daysRemaining === 1) {
      return 'payment_failed_final_warning';
    }

    return null; // Don't send email on other days
  }

  /**
   * Send dunning email to user
   */
  private async sendDunningEmail(
    subscription: any,
    emailType: string,
    gracePeriodInfo: any,
  ): Promise<void> {
    this.logger.log('dunning.sending_email', {
      subscriptionId: subscription.id,
      userId: subscription.userId,
      emailType,
      daysRemaining: gracePeriodInfo.daysRemaining,
    });

    try {
      switch (emailType) {
        case 'payment_failed_initial':
          await this.emailManager.sendPaymentFailedInitialEmail(
            subscription,
            gracePeriodInfo,
          );
          break;
        case 'payment_failed_reminder':
          await this.emailManager.sendPaymentFailedReminderEmail(
            subscription,
            gracePeriodInfo,
          );
          break;
        case 'payment_failed_final_warning':
          await this.emailManager.sendPaymentFailedFinalWarningEmail(
            subscription,
            gracePeriodInfo,
          );
          break;
        default:
          this.logger.warn('dunning.unknown_email_type', {
            emailType,
            subscriptionId: subscription.id,
          });
      }

      this.logger.log('dunning.email_sent', {
        subscriptionId: subscription.id,
        userId: subscription.userId,
        emailType,
      });
    } catch (error) {
      this.logger.error('dunning.email_send_failed', {
        subscriptionId: subscription.id,
        userId: subscription.userId,
        emailType,
        error: error.message || error,
      });
      throw error;
    }
  }

  /**
   * Manual method to retry payment for a specific subscription
   */
  async retryPaymentManual(subscriptionId: string): Promise<{
    success: boolean;
    error?: string;
    gracePeriodInfo?: any;
  }> {
    const subscription = await this.subscriptionProvider.get(subscriptionId);

    if (!subscription) {
      throw new Error(`Subscription not found: ${subscriptionId}`);
    }

    if (subscription.status !== SubscriptionStatusEnum.PAST_DUE) {
      throw new Error(`Subscription is not past due: ${subscriptionId}`);
    }

    const retryResult = await this.subscriptionManager.retryYearlyPayment(
      subscription,
    );
    const gracePeriodInfo = await this.subscriptionManager.handleGracePeriod(
      subscription,
    );

    return {
      ...retryResult,
      gracePeriodInfo,
    };
  }

  /**
   * Get dunning statistics
   */
  async getDunningStatistics(): Promise<{
    totalPastDue: number;
    inGracePeriod: number;
    expiredGracePeriod: number;
    averageDaysRemaining: number;
  }> {
    const pastDueSubscriptions = await this.subscriptionProvider.findBy(
      {
        status: SubscriptionStatusEnum.PAST_DUE,
        creditPackage: {
          expiresAfterMonths: 12,
        },
      },
      1,
      10000,
    );

    let inGracePeriodCount = 0;
    let expiredGracePeriodCount = 0;
    let totalDaysRemaining = 0;

    for (const subscription of pastDueSubscriptions) {
      const gracePeriodInfo = await this.subscriptionManager.handleGracePeriod(
        subscription,
      );

      if (gracePeriodInfo.inGracePeriod) {
        inGracePeriodCount++;
        totalDaysRemaining += gracePeriodInfo.daysRemaining || 0;
      } else {
        expiredGracePeriodCount++;
      }
    }

    const averageDaysRemaining =
      inGracePeriodCount > 0
        ? Math.round(totalDaysRemaining / inGracePeriodCount)
        : 0;

    return {
      totalPastDue: pastDueSubscriptions.length,
      inGracePeriod: inGracePeriodCount,
      expiredGracePeriod: expiredGracePeriodCount,
      averageDaysRemaining,
    };
  }
}
