import {
  ArgumentMetadata,
  Injectable,
  ValidationPipe,
  ValidationPipeOptions,
} from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { validateSync } from 'class-validator';

@Injectable()
export class CustomValidationPipe extends ValidationPipe {
  constructor(options: ValidationPipeOptions) {
    super(options);
  }

  async transform(value: any, metadata: ArgumentMetadata) {
    const { metatype } = metadata;

    // Skip transformation for primitive types
    if (!metatype || !this.toValidate(metadata)) {
      return value;
    }

    // Convert to instance of the DTO/class
    const instance = plainToInstance(metatype, value, this.transformOptions);

    // Handle per-property transformation
    Object.keys(value).forEach((key) => {
      const propertyType = Reflect.getMetadata(
        'design:type',
        metatype.prototype,
        key,
      );

      // Skip implicit conversion for booleans
      if (propertyType === Boolean && typeof value[key] === 'string') {
        instance[key] = this.toBoolean(value[key]);
      }
    });

    // Validate the instance
    const errors = validateSync(instance, this.validatorOptions);
    if (errors.length > 0) {
      throw this.createExceptionFactory()(errors);
    }

    return instance;
  }

  protected toValidate(metadata: ArgumentMetadata): boolean {
    const { metatype } = metadata;
    const types: Function[] = [String, Boolean, Number, Array, Object];
    return !types.includes(metatype);
  }

  private toBoolean(value): boolean {
    if (value === undefined || value === '') {
      return undefined;
    }

    return (
      typeof value === 'string' &&
      (value.toLowerCase() === 'true' || value === '1')
    );
  }
}
