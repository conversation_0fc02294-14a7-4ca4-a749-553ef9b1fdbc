import {
  Controller,
  Delete,
  ForbiddenException,
  HttpCode,
  Param,
  ParseUUIDPipe,
  Request,
} from '@nestjs/common';
import {
  ApiNoContentResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiResponse,
  ApiInternalServerErrorResponse,
  ApiBadRequestResponse,
} from '@nestjs/swagger';
import { OrganizationManager } from '../service/manager';
import { OrganizationUserProvider } from '../service/organization-user.provider';
import { OrganizationProvider } from '../service/provider';

@ApiTags('organization')
@Controller('organizations')
export class DeleteController {
  constructor(
    private organizationProvider: OrganizationProvider,
    private organizationUserProvider: OrganizationUserProvider,
    private organizationManager: OrganizationManager,
  ) {}

  @ApiOperation({
    operationId: 'organization_delete',
    summary: 'Delete an organization',
    description:
      'Deletes the specified organization.\n\n' +
      'Requirements:\n' +
      '- User must be the owner of the organization\n' +
      '- Organization must exist' +
      'Required Parameters:\n' +
      '- id: UUID of the organization to delete\n',
  })
  @ApiNoContentResponse({ description: 'Organization deleted successfully.' })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User is not the owner of the organization.',
  })
  @ApiNotFoundResponse({
    description: 'Not Found. The organization could not be found.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. An unexpected error occurred.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - Invalid or unavailable parameters.
      - Organization does not exist.
    `,
  })
  @ApiResponse({
    status: 409,
    description:
      'Conflict. Organization cannot be deleted due to existing dependencies.',
  })
  @Delete(':id')
  @HttpCode(204)
  async delete(
    @Request() req,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<void> {
    const organization = await this.organizationProvider.get(id);

    if (!this.organizationUserProvider.isOwner(req.user.id, organization.id)) {
      throw new ForbiddenException();
    }

    await this.organizationManager.delete(organization);
  }
}
