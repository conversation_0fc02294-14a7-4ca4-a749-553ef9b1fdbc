import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsUUID } from 'class-validator';
import { BaseSearchRequest } from 'src/core/dto/base.search-request';

export class BoardCommentSearchRequest extends BaseSearchRequest {
  @IsOptional()
  @IsUUID()
  @ApiProperty()
  userId: string;

  @IsOptional()
  @IsUUID()
  @ApiProperty()
  boardId: string;

  @IsOptional()
  @IsString()
  @ApiProperty()
  username: string;
}
