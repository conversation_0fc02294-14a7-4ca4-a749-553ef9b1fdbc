import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SignupCodeEntity } from '../entity/signup-code.entity';
import { UserEntity } from '../entity/user.entity';
import { SignupCodeCreatedEvent } from '../event/signup-code-created.event';
import { SignupCodeUsedEvent } from '../event/signup-code-used.event';
import { Notifier } from 'src/notification/service/notifier';
import { SignupCodeUsedNotification } from '../notification/signup-code-used.notification';

@Injectable()
export class SignupCodeManager {
  constructor(
    @InjectRepository(SignupCodeEntity)
    private repository: Repository<SignupCodeEntity>,
    private eventEmitter: EventEmitter2,
    private notifier: Notifier,
  ) {}

  async create(signupCode: SignupCodeEntity): Promise<SignupCodeEntity> {
    await this.repository.save(signupCode);

    this.eventEmitter.emit(
      'signup_code.created',
      new SignupCodeCreatedEvent({
        id: signupCode.id,
      }),
    );

    return signupCode;
  }

  async update(signupCode: SignupCodeEntity): Promise<SignupCodeEntity> {
    return await this.repository.save(signupCode);
  }

  async delete(signupCode: SignupCodeEntity): Promise<void> {
    await this.repository.softDelete(signupCode.id);
  }

  async registerUsage(signupCode: SignupCodeEntity, user: UserEntity) {
    // Check if this usage reaches the limit
    const reachedLimit =
      signupCode.limit && signupCode.usages === signupCode.limit;

    // Increment usage counter
    signupCode.usages++;

    // If limit is reached, set REFERER_FREE_IMAGE to 0 to prevent referrer from getting credits
    if (reachedLimit && signupCode.features) {
      signupCode.features.REFERER_FREE_IMAGE = 0;
    }

    await this.update(signupCode);

    await this.notifier.dispatch(
      new SignupCodeUsedNotification(signupCode.userId, {
        userId: user.id,
        username: user.username,
      }),
    );

    this.eventEmitter.emit(
      'signup_code.used',
      new SignupCodeUsedEvent({
        id: signupCode.id,
        userId: user.id,
      }),
    );
  }
}
