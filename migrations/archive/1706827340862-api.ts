import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1706827340862 implements MigrationInterface {
    name = 'Api1706827340862'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            UPDATE user_account
            SET is_verified = TRUE
            WHERE id IN (
                SELECT user_id
                FROM subscription
                Where status = 'active'
                AND expires_at > CURRENT_TIMESTAMP
                AND deleted_at IS NULL
            )        
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
    }

}
