import { Body, Controller, Post, Request, UseGuards } from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiBearerAuth,
  ApiResponse,
} from '@nestjs/swagger';
import { JwtIntegrationAuthGuard } from '../../../auth/service/jwt-integration-auth.guard';
import { UserProvider } from '../../../user/service/provider';
import { UserImageRequestManager } from '../../../user-image/service/user-image.request-manager';
import { UserImageResponseMapper } from '../../../user-image/service/user-image.response-mapper';
import { UserImageDto } from '../dto/user-image.dto';
import { UserImageRequest } from '../dto/user-image.request';

@ApiTags('user-images')
@Controller('user-images')
@ApiBearerAuth()
export class CreateController {
  constructor(
    private readonly userProvider: UserProvider,
    private readonly requestManager: UserImageRequestManager,
    private readonly responseMapper: UserImageResponseMapper,
  ) {}

  @ApiOperation({
    operationId: 'user_image_create',
    summary: 'Create a new user image',
    description: `Creates a new image entry and generates an upload URL.

The response includes an 'uploadUrl' property which is a pre-signed S3 URL that can be used to upload the image file.

To upload the image:
1. Make a PUT request to the uploadUrl with the image file as the request body
2. Set the Content-Type header to match the image type (e.g., 'image/jpeg', 'image/png')
3. No authentication is required for this PUT request as the URL is pre-signed

Example using curl:
\`\`\`
curl -X PUT -H "Content-Type: image/jpeg" --data-binary "@/path/to/image.jpg" "https://pre-signed-url-from-response"
\`\`\`

Example using JavaScript fetch:
\`\`\`javascript
const response = await fetch(uploadUrl, {
  method: 'PUT',
  headers: {
    'Content-Type': 'image/jpeg'
  },
  body: imageFile // File or Blob object
});
\`\`\`

After successful upload, the image will be accessible via the 'imageUrl' property in the response.`,
  })
  @ApiBody({ type: UserImageRequest })
  @ApiOkResponse({
    type: UserImageDto,
    description: 'The user image was successfully created',
  })
  @ApiBadRequestResponse({
    description: 'Invalid request parameters',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. The user is not authenticated',
  })
  @ApiResponse({
    status: 402,
    description: 'Payment Required. Insufficient credit balance.',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden. User does not have permission to create images',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found. The requested resource could not be found.',
  })
  @ApiResponse({
    status: 422,
    description:
      'Unprocessable Entity. The request payload contains invalid data.',
  })
  @ApiResponse({
    status: 429,
    description: 'Too Many Requests. Please try again later.',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. An unexpected error occurred.',
  })
  @Post()
  @UseGuards(JwtIntegrationAuthGuard)
  async create(
    @Body() userImageRequest: UserImageRequest,
    @Request() request,
  ): Promise<UserImageDto> {
    const user = await this.userProvider.get(request.user.id);

    const userImage = await this.requestManager.create(userImageRequest, user);

    return this.responseMapper.map(userImage, true);
  }
}
