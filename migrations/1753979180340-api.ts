import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1753979180340 implements MigrationInterface {
  name = 'Api1753979180340';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE "chat_message_relationship" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "chat_message_id" uuid NOT NULL,
                "type" character varying NOT NULL,
                "reference_id" character varying NOT NULL,
                CONSTRAINT "PK_09893308725b64ff6ee514fabe0" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            CREATE INDEX "idx_chat_message_relationship_chat_message_id" ON "chat_message_relationship" ("chat_message_id")
        `);
    await queryRunner.query(`
            CREATE INDEX "chat_message_relationship_reference_id" ON "chat_message_relationship" ("reference_id")
        `);
    await queryRunner.query(`
            ALTER TABLE "chat_message_relationship"
            ADD CONSTRAINT "FK_242d322a618cb3fed931fdf14fc" FOREIGN KEY ("chat_message_id") REFERENCES "chat_message"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "chat_message_relationship" DROP CONSTRAINT "FK_242d322a618cb3fed931fdf14fc"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."chat_message_relationship_reference_id"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."idx_chat_message_relationship_chat_message_id"
        `);
    await queryRunner.query(`
            DROP TABLE "chat_message_relationship"
        `);
  }
}
