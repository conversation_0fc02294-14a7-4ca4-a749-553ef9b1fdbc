import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1716501155252 implements MigrationInterface {
  name = 'Api1716501155252';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "birthday" date
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "birthday"
        `);
  }
}
