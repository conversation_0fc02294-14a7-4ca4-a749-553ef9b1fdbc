import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1723034479841 implements MigrationInterface {
  name = 'Api1723034479841';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            UPDATE "user_account"
            SET images_available = ( 
              SELECT COUNT(*)
              FROM image_completion ic
              WHERE ic.deleted_at IS NULL
              AND ic.user_id = user_account.id
              AND ic.privacy = 'public'
              AND ic.status = 'ready'
            ),
            models_available = ( 
              SELECT COUNT(*)
              FROM model m
              WHERE m.deleted_at IS NULL
              AND m.user_id = user_account.id
              AND m.privacy = 'public'
              AND m.status = 'available'
            )
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
