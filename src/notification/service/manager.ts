import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NotificationEntity } from '../entity/notification.entity';
import { Injectable } from '@nestjs/common';

@Injectable()
export class NotificationManager {
  constructor(
    @InjectRepository(NotificationEntity)
    private repository: Repository<NotificationEntity>,
  ) {}

  async create(entity: NotificationEntity): Promise<NotificationEntity> {
    await this.repository.save(entity);

    return entity;
  }

  async update(entity: NotificationEntity): Promise<NotificationEntity> {
    return await this.repository.save(entity);
  }

  async delete(entity: NotificationEntity): Promise<void> {
    await this.repository.softDelete(entity.id);
  }

  async markAsRead(entity: NotificationEntity) {
    entity.readAt = new Date();

    await this.update(entity);
  }

  async markAsUnread(entity: NotificationEntity) {
    entity.readAt = null;

    await this.update(entity);
  }
}
