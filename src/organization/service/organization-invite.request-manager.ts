import { BadRequestException, Injectable } from '@nestjs/common';
import { OrganizationInviteManager } from './organization-invite.manager';
import { OrganizationInviteEntity } from '../entity/organization-invite.entity';
import { OrganizationEntity } from '../entity/organization.entity';
import { UserEntity } from 'src/user/entity/user.entity';
import { Notifier } from 'src/notification/service/notifier';
import { OrganizationInvitedNotification } from '../notification/organization-invite.notification';
import { UserResponseMapper } from 'src/user/service/response-mapper';
import { Mailer } from 'src/core/service/mailer.service';
import * as path from 'path';
import { AppConfigurationService } from 'src/core/service/app-configuration.service';
import { Logger } from 'nestjs-pino';
import { OrganizationUserEntity } from '../entity/organization-user.entity';
import { OrganizationUserProvider } from './organization-user.provider';
import { OrganizationInviteRequest } from '../dto/organization-invite.request';
import { UserProvider } from 'src/user/service/provider';
import { OrganizationInviteProvider } from './organization-invite.provider';
import { StatusEnum } from '../enum/organization-user.enum';

@Injectable()
export class OrganizationInviteRequestManager {
  constructor(
    private manager: OrganizationInviteManager,
    private notifier: Notifier,
    private userResponseMapper: UserResponseMapper,
    private mailer: Mailer,
    private appConfig: AppConfigurationService,
    private logger: Logger,
    private organizationUserProvider: OrganizationUserProvider,
    private userProvider: UserProvider,
    private organizationInviteProvider: OrganizationInviteProvider,
  ) {}

  async invite(
    organization: OrganizationEntity,
    request: OrganizationInviteRequest,
    invitedBy: UserEntity = null,
  ): Promise<void> {
    if (request.email) {
      const isUser =
        (await this.userProvider.countBy({ email: request.email })) > 0;
      if (isUser) {
        request.userId = (
          await this.userProvider.findOneBy({
            email: request.email,
          })
        ).id;
      }
    }
    if (request.userId) {
      if (
        await this.organizationUserProvider.findOneBy({
          userId: request.userId,
          organizationId: organization.id,
        })
      ) {
        throw new BadRequestException('organization_user.already_member');
      }

      if (
        await this.organizationInviteProvider.findOneBy({
          userId: request.userId,
          organizationId: organization.id,
          status: StatusEnum.PENDING,
        })
      ) {
        throw new BadRequestException('organization_user.already_invited');
      }
    }

    if (organization.members + 1 > organization.seatsPurchased) {
      throw new BadRequestException('organization_user.not_enough_seats');
    }

    const organizationInvite = new OrganizationInviteEntity();

    organizationInvite.organizationId = organization.id;
    organizationInvite.organization = organization;

    organizationInvite.invitedAt = new Date();
    organizationInvite.invitedBy = invitedBy.id;

    if (request.userId) {
      organizationInvite.userId = request.userId;
      organizationInvite.email = (
        await this.userProvider.get(request.userId)
      ).email;

      await this.notifier.dispatch(
        new OrganizationInvitedNotification(organizationInvite.userId, {
          invitedById: invitedBy.id,
          invitedByUsername: invitedBy.username,
          invitedByThumbnail:
            this.userResponseMapper.mapProfilePicture(invitedBy),
          invitedAt: new Date(),
          organizationId: organization.id,
          organizationName: organization.name,
        }),
      );

      try {
        await this.sendOrganizationIsUserInviteEmail(organizationInvite);
      } catch (error) {
        this.logger.error('Error sending organization invite email', {
          organizationInviteId: organizationInvite.id,
          error: error,
        });
      }
    } else {
      organizationInvite.email = request.email;

      try {
        await this.sendOrganizationNotUserInviteEmail(organizationInvite);
      } catch (error) {
        this.logger.error('Error sending organization invite email', {
          organizationInviteId: organizationInvite.id,
          error: error,
        });
      }
    }

    await this.manager.create(organizationInvite, organization);
  }

  async accept(
    organization: OrganizationEntity,
    organizationInvite: OrganizationInviteEntity,
  ) {
    const organizationUser = new OrganizationUserEntity();
    organizationUser.organizationId = organization.id;
    organizationUser.organization = organization;
    organizationUser.userId = organizationInvite.userId;

    await this.manager.accept(organizationUser, organizationInvite);
  }

  async reject(
    organizationInvite: OrganizationInviteEntity,
    organization: OrganizationEntity,
  ) {
    await this.manager.reject(organizationInvite, organization);
  }

  async sendOrganizationNotUserInviteEmail(
    organizationInvite: OrganizationInviteEntity,
  ) {
    const subject = `You have been invited to an organization!`;

    const replacements = {
      subject: subject,
      username:
        organizationInvite.user.username || organizationInvite.user.name,
    };

    const template = path.join(
      this.appConfig.templateDir,
      'email',
      'organization',
      'invite-not-user.html',
    );

    try {
      await this.mailer.send(
        template,
        replacements,
        subject,
        organizationInvite.email,
      );
    } catch (error) {
      this.logger.error('Error sending model finished email', {
        organizationInviteId: organizationInvite.id,
        error: error,
      });
    }
  }

  async sendOrganizationIsUserInviteEmail(
    organizationInvite: OrganizationInviteEntity,
  ) {
    const subject = `You have been invited to an organization!`;

    const replacements = {
      subject: subject,
      username:
        organizationInvite.user.username || organizationInvite.user.name,
      organizationName: organizationInvite.organization.name,
      inviteLink: `${this.appConfig.asString(
        'FRONTEND_URL',
      )}/organization/invites`,
    };

    const template = path.join(
      this.appConfig.templateDir,
      'email',
      'organization',
      'invite-is-user.html',
    );

    try {
      await this.mailer.send(
        template,
        replacements,
        subject,
        organizationInvite.email,
      );
    } catch (error) {
      this.logger.error('Error sending model finished email', {
        organizationInviteId: organizationInvite.id,
        error: error,
      });
    }
  }
}
