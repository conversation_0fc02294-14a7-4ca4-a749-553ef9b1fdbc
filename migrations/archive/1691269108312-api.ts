import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1691269108312 implements MigrationInterface {
  name = 'Api1691269108312';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "username" character varying
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD CONSTRAINT "UQ_3c4d4fae641bf9048ad324ee0d9" UNIQUE ("username")
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "profile_picture" text
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "timezone" character varying
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "frontend_theme" character varying
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "currency" character varying
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "last_login" TIMESTAMP
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "blocked_at" TIMESTAMP
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "is_active" boolean NOT NULL DEFAULT true
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "email_validated_at" TIMESTAMP
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "email_validated_at"
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "is_active"
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "blocked_at"
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "last_login"
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "currency"
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "frontend_theme"
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "timezone"
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "profile_picture"
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account" DROP CONSTRAINT "UQ_3c4d4fae641bf9048ad324ee0d9"
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "username"
        `);
  }
}
