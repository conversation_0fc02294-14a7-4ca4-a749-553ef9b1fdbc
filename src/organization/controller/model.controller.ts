import {
  <PERSON>,
  Get,
  Param,
  <PERSON><PERSON><PERSON><PERSON><PERSON>ip<PERSON>,
  Request,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/auth/service/jwt-auth.guard';
import { ModelDto } from 'src/model/dto/model.dto';
import { ModelOrganizationManager } from 'src/model/service/model-organization.manager';
import { ModelResponseMapper } from 'src/model/service/response-mapper';

@ApiTags('organization / model')
@Controller('organizations')
export class OrganizationModelController {
  constructor(
    private modelOrganizationManager: ModelOrganizationManager,
    private modelResponseMapper: ModelResponseMapper,
  ) {}

  @ApiOperation({
    operationId: 'organizations_list_models',
    summary: 'List models available to an organization',
    description:
      'Retrieves a list of models that are available to the specified organization.\n\n' +
      'This includes:\n' +
      '- Models owned by organization members\n' +
      '- Models shared with the organization by external users\n\n' +
      'Requirements:\n' +
      '- User must be a member of the organization\n' +
      'Required Parameters:\n' +
      '- organizationId: UUID of the organization\n',
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The unique identifier of the organization',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponse({
    type: [ModelDto],
    description: 'List of models available to the organization.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`organizationId\`: Must be a valid UUID.
    `,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden. The user is not a member of the organization.',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found. The organization could not be found.',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. An unexpected error occurred.',
  })
  @ApiBearerAuth()
  @Get(':organizationId/models')
  @UseGuards(JwtAuthGuard)
  async listModels(
    @Param('organizationId', new ParseUUIDPipe()) organizationId: string,
    @Request() req,
  ): Promise<ModelDto[]> {
    const models = await this.modelOrganizationManager.getModelsForOrganization(
      organizationId,
      req.user,
    );

    return await this.modelResponseMapper.mapMultiple(
      models.map((share) => share.model),
      true,
      false,
      req.user.id,
    );
  }
}
