import { ForbiddenException, Injectable } from '@nestjs/common';
import { OrganizationUserUpdateRequest } from '../dto/organization-user.update-request';
import { OrganizationEntity } from '../entity/organization.entity';
import { OrganizationUserManager } from './organization-user.manager';
import { OrganizationUserProvider } from './organization-user.provider';

@Injectable()
export class OrganizationUserRequestManager {
  constructor(
    private manager: OrganizationUserManager,
    private provider: OrganizationUserProvider,
  ) {}

  async updateUser(
    organization: OrganizationEntity,
    userId: string,
    request: OrganizationUserUpdateRequest,
  ): Promise<void> {
    const organizationUser = await this.provider.getBy({
      organizationId: organization.id,
      userId: userId,
    });

    if ('isAdmin' in request) {
      organizationUser.isAdmin = request.isAdmin;
    }

    await this.manager.update(organizationUser);
  }

  async removeUser(
    organization: OrganizationEntity,
    userId: string,
    adminId: string,
  ): Promise<void> {
    const organizationUser = await this.provider.getBy({
      organizationId: organization.id,
      userId,
    });

    if (
      (organizationUser.isAdmin || organizationUser.isOwner) &&
      !(await this.provider.isOwner(adminId, organization.id))
    ) {
      throw new ForbiddenException();
    }

    if (organizationUser.isOwner) {
      throw new ForbiddenException('Cannot remove the Owner of a organization');
    }

    await this.manager.delete(organization, organizationUser);
  }
}
