import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1698206545240 implements MigrationInterface {
  name = 'Api1698206545240';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "image_completion"
            ADD "generated_by_unit" character varying
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "image_completion" DROP COLUMN "generated_by_unit"
        `);
  }
}
