import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsOptional } from 'class-validator';

export class OrganizationDashboardSearchRequest {
  // eslint-disable-next-line @typescript-eslint/no-inferrable-types
  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  day: boolean = false;

  // eslint-disable-next-line @typescript-eslint/no-inferrable-types
  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  week: boolean = false;

  // eslint-disable-next-line @typescript-eslint/no-inferrable-types
  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  month: boolean = false;

  // eslint-disable-next-line @typescript-eslint/no-inferrable-types
  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  year: boolean = false;
}
