import { SQSClient, SendMessageCommand } from '@aws-sdk/client-sqs';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { ModelManager } from 'src/model/service/manager';
import { ModelVersionProvider } from 'src/model/service/model-version.provider';
import { ModelProvider } from 'src/model/service/provider';
// import { Notifier } from 'src/notification/service/notifier';
import { CreditTypeEnum } from 'src/subscription/entity/credit-type.enum';
import { TransactionTypeEnum } from 'src/subscription/entity/transaction-type.enum';
import { TransactionManager } from 'src/subscription/service/transaction.manager';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import {
  ModelEntity,
  PrivacyEnum as ModelPrivacyEnum,
} from '../../model/entity/model.entity';
import { ImageCompletionModelEntity } from '../entity/image-completion-model.entity';
import {
  ImageCompletionEntity,
  PrivacyEnum,
  QueueEnum,
  StatusEnum,
} from '../entity/image-completion.entity';
import { ImageDeletedEvent } from '../event/image-deleted.event';
import { ImageGeneratedEvent } from '../event/image-generated.event';
import { ImageNotAllowedEvent } from '../event/image-not-allowed.event';
import { ImagePublishedEvent } from '../event/image-published.event';
import { ImageUnpublishedEvent } from '../event/image-unpublished.event';
import { AssetManager } from './asset.manager';
import { PromptManager } from './prompt.manager';
import { ImageCompletionResponseMapper } from './response-mapper';
import axios from 'axios';

@Injectable()
export class ImageCompletionManager {
  private MAX_BILLED_SECONDS = 35;

  constructor(
    @InjectRepository(ImageCompletionEntity)
    private repository: Repository<ImageCompletionEntity>,
    private modelProvider: ModelProvider,
    private modelVersionProvider: ModelVersionProvider,
    private modelManager: ModelManager,
    private configService: ConfigService,
    private assetManager: AssetManager,
    private transactionManager: TransactionManager,
    private responseMapper: ImageCompletionResponseMapper,
    private promptManager: PromptManager,
    private eventEmitter: EventEmitter2,
    // private notifier: Notifier,
    @Inject('SQS') private sqs: SQSClient,
    private logger: Logger,
  ) {}

  async create(entity: ImageCompletionEntity): Promise<ImageCompletionEntity> {
    entity.id = uuidv4();
    entity.promptSystem = await this.generatePromptSystem(entity);
    entity.generationSeconds = 1;

    this.assetManager.generateStoragePath(entity);

    await this.save(entity);

    if (!entity.user.isBot) {
      await this.registerTransaction(entity);
    }

    // if (QueueEnum.SLOW !== entity.queue) {
    //   await this.registerTransaction(entity);
    // }

    await this.writeToQueue(entity);

    return entity;
  }

  async registerTransaction(entity: ImageCompletionEntity) {
    const billedSeconds = Math.min(
      this.MAX_BILLED_SECONDS,
      entity.generationSeconds,
    );

    await this.transactionManager.register(
      TransactionTypeEnum.SPENDING,
      billedSeconds,
      CreditTypeEnum.IMAGE,
      entity.id,
      true,
      entity.organizationId ? null : entity.userId,
      entity.organizationId,
    );
  }

  getQueueUrl(image: ImageCompletionEntity): string {
    // if (QueueEnum.PRIORITY === (image.queue as QueueEnum)) {
    //   throw new BadRequestException('Priority queue is not available');
    // }

    // if (QueueEnum.CUSTOM === (image.queue as QueueEnum)) {
    //   const { user } = image;

    //   if (!user.imageQueue) {
    //     throw new BadRequestException('Custom queue is not available');
    //   }

    //   return this.configService.get<string>(
    //     'IMAGE_COMPLETION_SQS_QUEUE_URL_' + user.imageQueue.toUpperCase(),
    //   );
    // }

    if (QueueEnum.FAST === (image.queue as QueueEnum)) {
      return this.configService.get<string>(
        'IMAGE_COMPLETION_SQS_QUEUE_URL_FAST',
      );
    }

    return this.configService.get<string>(
      'IMAGE_COMPLETION_SQS_QUEUE_URL_V' + image.systemVersion,
    );
  }

  async writeToQueue(entity: ImageCompletionEntity) {
    const queueUrl = this.getQueueUrl(entity);

    const sendMessageCommand = new SendMessageCommand({
      QueueUrl: queueUrl,
      MessageBody: JSON.stringify(
        await this.responseMapper.mapInternal(entity),
      ),
    });

    await this.sqs.send(sendMessageCommand);
  }

  async enqueueResizing(entity: ImageCompletionEntity) {
    const queueUrl = this.configService.get<string>(
      'IMAGE_RESIZER_SQS_QUEUE_URL',
    );

    const messageBody = {
      id: entity.id,
      userId: entity.userId,
      bucket: entity.storageBucket,
      key: entity.imagePaths[0],
    };

    const sendMessageCommand = new SendMessageCommand({
      QueueUrl: queueUrl,
      MessageBody: JSON.stringify(messageBody),
    });

    await this.sqs.send(sendMessageCommand);
  }

  async generatePromptSystem(entity: ImageCompletionEntity): Promise<string> {
    let promptSystem = entity.prompt;

    const models = await entity.models;

    if (entity.models?.length) {
      for (const imageCompletionModel of models) {
        try {
          const model = await this.modelProvider.get(
            imageCompletionModel.modelId,
          );

          // Replace '@+<model name>' with model.prompt and model.type (optional)
          const placeholder = '@+' + model.name;
          const modelVersion = await this.modelVersionProvider.getModelVersion(
            model.id,
            model.version,
          );

          const systemVersion = modelVersion.systemVersionsData.find(
            (systemVersion) =>
              systemVersion.systemVersion === entity.systemVersion,
          );

          const replacement =
            `${systemVersion.prompt}, ` + (model.type ? `${model.type}, ` : '');

          promptSystem = promptSystem.replace(
            new RegExp(placeholder, 'ig'),
            replacement,
          );
        } catch (e) {
          this.logger.error('Error generating prompt system', {
            imageCompletion: entity,
            error: e.message,
          });
        }
      }
    }

    promptSystem += promptSystem.endsWith(', ') ? '' : ', ';

    return promptSystem;
  }

  async update(entity: ImageCompletionEntity): Promise<ImageCompletionEntity> {
    this.logger.log('image-completion.update', entity);

    if (entity.status === StatusEnum.READY) {
      for (const imageCompletionModel of entity.models) {
        if (!imageCompletionModel.markedAsUsed) {
          imageCompletionModel.markedAsUsed = true;

          const model = imageCompletionModel.model;
          model.usages++;
          this.modelManager.update(model);
        }
      }

      // await this.notifier.dispatch(
      //   new ImageGeneratedNotification(entity.userId, {
      //     id: entity.id,
      //     userId: entity.userId,
      //     generatedAt: new Date(),
      //   }),
      // );

      this.eventEmitter.emit(
        'image.generated',
        new ImageGeneratedEvent({
          id: entity.id,
          userId: entity.userId,
        }),
      );

      await this.enqueueResizing(entity);

      if (entity.webhookUrl) {
        try {
          const imageDto = await this.responseMapper.map(entity);
          await axios.post(entity.webhookUrl, imageDto, {
            headers: {
              'Content-Type': 'application/json',
            },
            timeout: 5000, // 5 second timeout
          });

          this.logger.log('Webhook notification sent successfully', {
            imageId: entity.id,
            webhookUrl: entity.webhookUrl,
          });
        } catch (error) {
          this.logger.error('Failed to send webhook notification', {
            imageId: entity.id,
            webhookUrl: entity.webhookUrl,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }
    }

    if (null === entity.publishedAt && entity.privacy === PrivacyEnum.PUBLIC) {
      entity.publishedAt = new Date();
      await this.save(entity);

      // await this.notifier.dispatch(
      //   new ImagePublishedNotification(entity.userId, {
      //     id: entity.id,
      //     userId: entity.userId,
      //     publishedAt: entity.publishedAt,
      //   }),
      // );

      this.eventEmitter.emit(
        'image.published',
        new ImagePublishedEvent({
          id: entity.id,
          userId: entity.userId,
        }),
      );
    } else if (
      null !== entity.publishedAt &&
      entity.privacy === PrivacyEnum.PRIVATE
    ) {
      entity.publishedAt = null;
      await this.save(entity);

      // await this.notifier.dispatch(
      //   new ImageUnpublishedNotification(entity.userId, {
      //     id: entity.id,
      //     userId: entity.userId,
      //     unpublishedAt: new Date(),
      //   }),
      // );

      this.eventEmitter.emit(
        'image.unpublished',
        new ImageUnpublishedEvent({
          id: entity.id,
          userId: entity.userId,
        }),
      );
    }

    if (entity.status === StatusEnum.NOT_ALLOWED) {
      this.eventEmitter.emit(
        'image.not_allowed',
        new ImageNotAllowedEvent({
          id: entity.id,
          userId: entity.userId,
        }),
      );
    }

    await this.save(entity);

    if (!entity.user.isBot && entity.isBilled) {
      await this.registerTransaction(entity);
    }

    return entity;
  }

  async save(entity: ImageCompletionEntity) {
    await this.repository.save(entity);

    if (!entity.storagePath) {
      this.assetManager.generateStoragePath(entity);
    }

    await this.repository.save(entity);
  }

  async delete(entity: ImageCompletionEntity): Promise<void> {
    await this.repository.softDelete(entity.id);

    this.eventEmitter.emit(
      'image.deleted',
      new ImageDeletedEvent({
        id: entity.id,
        userId: entity.userId,
      }),
    );
  }

  async unlinkRelatedImages(imageId: string): Promise<void> {
    await this.repository
      .createQueryBuilder()
      .update(ImageCompletionEntity)
      .set({ originalImageCompletionId: null })
      .where('originalImageCompletionId = :imageId', { imageId })
      .execute();
  }

  async generateImageCompletionModels(entity: ImageCompletionEntity) {
    if (!entity.prompt) {
      return;
    }

    const models = await this.promptManager.extractModelsFromPrompt(
      entity.prompt,
      entity.user,
    );

    if (models.length === 0) {
      return;
    }

    entity.models = models.map((model) => {
      const imageCompletionModel = new ImageCompletionModelEntity();
      imageCompletionModel.model = model;
      imageCompletionModel.modelId = model.id;

      return imageCompletionModel;
    });

    entity.hasPrivateModel = this.containsPrivateModel(models);
  }

  containsPrivateModel(models: ModelEntity[]): boolean {
    const privacyLevels = models.map((model) => model.privacy);

    return (
      privacyLevels.includes(ModelPrivacyEnum.LICENSED) ||
      privacyLevels.includes(ModelPrivacyEnum.PRIVATE)
    );
  }
}
