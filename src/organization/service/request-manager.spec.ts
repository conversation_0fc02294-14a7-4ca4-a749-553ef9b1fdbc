import { Test, TestingModule } from '@nestjs/testing';
import { ImageCompletionProvider } from '../../image-completion/service/provider';
import { UserEntity } from '../../user/entity/user.entity';
import { OrganizationRequest } from '../dto/organization.request';
import { OrganizationEntity } from '../entity/organization.entity';
import { OrganizationManager } from './manager';
import { OrganizationRequestManager } from './request-manager';

describe('OrganizationRequestManager', () => {
  let requestManager: OrganizationRequestManager;
  let managerMock: Partial<Record<keyof OrganizationManager, jest.Mock>>;
  let imageCompletionProviderMock: jest.Mock;

  beforeEach(async () => {
    managerMock = {
      create: jest.fn(),
      update: jest.fn(),
    };

    imageCompletionProviderMock = jest.fn(); // Mock do ImageCompletionProvider

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrganizationRequestManager,
        {
          provide: OrganizationManager,
          useValue: managerMock,
        },
        {
          provide: ImageCompletionProvider,
          useValue: imageCompletionProviderMock, // Mock do ImageCompletionProvider
        },
      ],
    }).compile();

    requestManager = module.get<OrganizationRequestManager>(
      OrganizationRequestManager,
    );
  });

  it('should be defined', () => {
    expect(requestManager).toBeDefined();
  });

  describe('create', () => {
    it('should create a new organization', async () => {
      const organizationData: OrganizationRequest = {
        name: 'Test Org',
        handle: 'test-org',
        description: 'Test Desc',
        website: 'https://test.org',
        currency: 'USD',
        locale: 'en-US',
        includeWatermarks: true,
        imageQueue: 'default',
        modelQueue: 'default',
        systemVersion: 3,
      };

      const owner = new UserEntity();
      owner.id = 'user-123';

      const createdOrganization = new OrganizationEntity();
      managerMock.create.mockResolvedValue(createdOrganization);

      const result = await requestManager.create(organizationData, owner);

      expect(managerMock.create).toHaveBeenCalledWith(
        expect.objectContaining({
          name: organizationData.name,
          handle: organizationData.handle,
          description: organizationData.description,
          website: organizationData.website,
          includeWatermarks: organizationData.includeWatermarks,
        }),
        owner,
      );
      expect(result).toEqual(expect.objectContaining({
        name: organizationData.name,
        handle: organizationData.handle,
        description: organizationData.description,
        website: organizationData.website,
        includeWatermarks: organizationData.includeWatermarks,
      }));
    });
  });

  describe('update', () => {
    it('should update an existing organization', async () => {
      const organization = new OrganizationEntity();
      organization.id = 'org-1';

      const updateData: OrganizationRequest = {
        name: 'Updated Org',
        handle: 'updated-org',
        description: 'Updated Desc',
        website: 'https://updated.org',
        currency: 'EUR',
        locale: 'fr-FR',
        includeWatermarks: false,
        imageQueue: 'updated-queue',
        modelQueue: 'updated-model',
        systemVersion: 3,
      };

      const updatedOrganization = new OrganizationEntity();
      managerMock.update.mockResolvedValue(updatedOrganization);

      const result = await requestManager.update(organization, updateData);

      expect(managerMock.update).toHaveBeenCalledWith(organization);
      expect(result).toBe(updatedOrganization);
    });
  });
});
