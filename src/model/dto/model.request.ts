import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsOptional,
  IsString,
  IsUrl,
  Matches,
  MaxLength,
} from 'class-validator';
import { PrivacyEnum, SettingsEnum } from '../entity/model.entity';

export class ModelRequest {
  @IsOptional()
  @IsString()
  @Matches(/^[a-zA-Z0-9_.]+$/, {
    message:
      'Name must contain only alphanumeric characters, underscores, and dots',
  })
  @MaxLength(255)
  @ApiProperty()
  name: string;

  @IsOptional()
  @IsEnum(PrivacyEnum, {
    message: 'privacy must be a valid value',
  })
  @ApiProperty({ enum: PrivacyEnum })
  privacy: PrivacyEnum;

  @ApiProperty()
  @IsOptional()
  @Matches(/^[a-zA-Z0-9,()_\-:;\[\].äöüÄÖÜéÉèëÈË\? ]+$/u, {
    message:
      'type should only contain alphanumeric characters, commas, hyphens, underscores, parentheses, colons, semicolons, and question marks',
  })
  type: string;

  @ApiProperty()
  @IsOptional()
  @Matches(/^[a-zA-Z0-9, \-_()]*$/, {
    message:
      'class should only contain alphanumeric characters, commas, hyphens, underscores, and parentheses',
  })
  class: string;

  @ApiProperty()
  @IsOptional()
  description: string;

  @ApiProperty()
  @IsOptional()
  trainingMode?: string;

  @ApiProperty()
  @IsOptional()
  website: string;

  @ApiProperty({
    type: [String],
    enum: SettingsEnum,
    isArray: true,
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(SettingsEnum, {
    each: true,
    message: 'settings must be valid values',
  })
  settings: SettingsEnum[];

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsUrl()
  webhookUrl?: string;
}
