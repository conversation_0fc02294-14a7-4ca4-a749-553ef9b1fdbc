# DTOs

DTOs define the structure of data sent and received by the API.

## new-module.dto.ts

Defines the structure of the data returned to the client.

```typescript
import { ApiProperty } from '@nestjs/swagger';

export class NewModuleDto {
  @ApiProperty({ description: 'Unique identifier for the resource' })
  id: string;

  @ApiProperty({ description: 'Name of the resource' })
  name: string;

  @ApiProperty({ description: 'Description of the resource' })
  description: string;

  @ApiProperty({ description: 'Creation date of the resource' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update date of the resource' })
  updatedAt: Date;
}
```

## new-module.request.ts

Defines the structure of the data required to create or update a resource.

```typescript
import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional } from 'class-validator';

export class NewModuleRequest {
  @ApiProperty({ description: 'Name of the resource' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: 'Description of the resource', required: false })
  @IsString()
  @IsOptional()
  description?: string;
}
```

## new-module.search-request.ts

Defines the structure of the search request parameters.

```typescript
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsInt, Min } from 'class-validator';

export class NewModuleSearchRequest {
  @ApiProperty({ description: 'Name of the resource to search for', required: false })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ description: 'Page number for pagination', required: false, default: 1 })
  @IsInt()
  @Min(1)
  @IsOptional()
  page?: number = 1;

  @ApiProperty({ description: 'Number of items per page', required: false, default: 10 })
  @IsInt()
  @Min(1)
  @IsOptional()
  limit?: number = 10;

  @ApiProperty({ description: 'Field to sort by', required: false, default: 'createdAt' })
  @IsString()
  @IsOptional()
  sortBy?: string = 'createdAt';

  @ApiProperty({ description: 'Sort order', required: false, default: 'ASC' })
  @IsString()
  @IsOptional()
  sortOrder?: 'ASC' | 'DESC' = 'ASC';
}
```
