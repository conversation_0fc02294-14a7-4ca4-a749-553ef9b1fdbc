import { Injectable } from '@nestjs/common';
import { UserFollowDto } from '../dto/user-follow.dto';
import { UserFollowEntity } from '../entity/user-follow.entity';
import { UserResponseMapper } from './response-mapper';

@Injectable()
export class UserFollowResponseMapper {
  constructor(private userResponseMapper: UserResponseMapper) {}

  map(userFollow: UserFollowEntity): UserFollowDto {
    const userFollowDto = new UserFollowDto();

    userFollowDto.id = userFollow.id;
    userFollowDto.followerId = userFollow.followerId;
    userFollowDto.follower = this.userResponseMapper.mapPublic(
      userFollow.follower,
    );
    userFollowDto.followingId = userFollow.following.id;
    userFollowDto.following = this.userResponseMapper.mapPublic(
      userFollow.following,
    );
    userFollowDto.isApproved = userFollow.isApproved;
    userFollowDto.createdAt = userFollow.createdAt;
    userFollowDto.updatedAt = userFollow.updatedAt;

    return userFollowDto;
  }

  mapMultiple(userFollows: UserFollowEntity[]): UserFollowDto[] {
    return userFollows.map((userFollow) => this.map(userFollow));
  }
}
