import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ImageEditImageCompletionEntity } from '../entity/image-edit-image-completion.entity';
import { FindOneOptions, Repository } from 'typeorm';
import { AbstractProvider } from 'src/core/service/abstract.provider';
import { Logger } from 'nestjs-pino';

@Injectable()
export class ImageEditImageCompletionProvider extends AbstractProvider<ImageEditImageCompletionEntity> {
  constructor(
    @InjectRepository(ImageEditImageCompletionEntity)
    repository: Repository<ImageEditImageCompletionEntity>,
    logger: Logger,
  ) {
    super(repository, logger);
  }

  prepareFindOneOptions(
    criteria: any,
  ): FindOneOptions<ImageEditImageCompletionEntity> {
    return {
      where: criteria,
      relations: {
        imageCompletion: true,
        imageEdit: true,
      },
    };
  }
}
