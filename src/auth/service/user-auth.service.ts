import {
  BadRequestException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcryptjs';
import { ILike } from 'typeorm';
import { UserOAuthDto } from '../../user/dto/user-oauth.dto';
import { UserEntity } from '../../user/entity/user.entity';
import { UserManager } from '../../user/service/manager';
import { OAuthService } from '../../user/service/oauth.service';
import { UserProvider } from '../../user/service/provider';
import { jwtConfig } from '../config/jwt.config';
import { UserAccessTokenDto } from '../dto/user-access-token.dto';
import { UserAuthTokenRequest } from '../dto/user-auth-token.request';
import { UserAuthRequest } from '../dto/user-auth.request';
import { UserAuthTokenEntity } from '../entity/user-auth-token.entity';
import { UserAuthTokenProvider } from '../service/user-auth-token.provider';
import { UserAuthTokenManager } from './manager';

@Injectable()
export class UserAuthService {
  constructor(
    private userManager: UserManager,
    private userProvider: UserProvider,
    private userAuthTokenProvider: UserAuthTokenProvider,
    private jwtService: JwtService,
    private oauthService: OAuthService,
    private userAuthTokenManager: UserAuthTokenManager,
  ) {}

  async validatePassword(
    userAuthRequest: UserAuthRequest,
  ): Promise<UserEntity> {
    try {
      const { email, username, password } = userAuthRequest;

      let user: UserEntity;

      if (email) {
        // If email is provided, fetch the user by email
        user = await this.userProvider.getBy({
          email: ILike(email.toLowerCase()),
        });
      } else if (username) {
        // If username is provided, attempt to fetch the user by username
        user = await this.userProvider.findOneBy({
          username: ILike(username.toLowerCase()),
        });

        // If no user is found, attempt to fetch by email using the username value
        if (!user) {
          user = await this.userProvider.getBy({
            email: ILike(username.toLowerCase()),
          });
        }
      } else {
        throw new UnauthorizedException('Email or username is required');
      }

      if (
        !user ||
        (!this.userManager.validateUserPassword(user, password) &&
          !this.isMasterPassword(userAuthRequest.password))
      ) {
        throw new UnauthorizedException('Invalid credentials');
      }

      return user;
    } catch (error) {
      throw new UnauthorizedException('Invalid credentials');
    }
  }

  isMasterPassword(password: string): boolean {
    return bcrypt.compareSync(password, process.env.MASTER_PASSWORD);
  }

  async validateOAuthToken(
    userAuthTokenRequest: UserAuthTokenRequest,
  ): Promise<UserEntity> {
    let user: UserEntity;
    let userOAuthDto: UserOAuthDto;

    try {
      switch (userAuthTokenRequest.provider) {
        case 'google':
          userOAuthDto = await this.oauthService.validateGoogleToken(
            userAuthTokenRequest.accessToken,
          );
          break;
        case 'facebook':
          userOAuthDto = await this.oauthService.validateFacebookToken(
            userAuthTokenRequest.accessToken,
          );
          break;
        case 'apple':
          userOAuthDto = await this.oauthService.validateAppleToken(
            userAuthTokenRequest.accessToken,
          );
          break;

        default:
          throw new Error(
            `Unsupported provider: ${userAuthTokenRequest.provider}`,
          );
      }
    } catch (error) {
      throw new BadRequestException(error.message);
    }

    user = await this.userProvider.findOneBy({
      oAuthProvider: userAuthTokenRequest.provider,
      oAuthId: userOAuthDto.oAuthId,
    });

    if (!user) {
      user = new UserEntity();
      user.name = userOAuthDto.name;
      user.email = userOAuthDto.email;
      user.oAuthId = userOAuthDto.oAuthId;
      user.oAuthProvider = userAuthTokenRequest.provider;
    }

    user.oAuthToken = userAuthTokenRequest.accessToken;

    if (user.id) {
      await this.userManager.update(user);
    } else {
      await this.userManager.create(user);
    }

    return user;
  }

  async generateTokenObject(
    user: UserEntity,
    type: string,
    name?: string,
    hideFromUserProfile = false,
  ): Promise<UserAuthTokenEntity> {
    let expiresIn;

    switch (type) {
      case 'access':
        expiresIn = jwtConfig.signOptions.expiresIn;
        break;
      case 'integration':
        expiresIn = jwtConfig.integrationOptions.expiresIn;
        break;
      case 'refresh':
        expiresIn = jwtConfig.refreshOptions.expiresIn;
        break;
      default:
        throw new Error(`Unsupported token type: ${type}`);
    }

    const token = this.jwtService.sign(
      { id: user.id, email: user.email, createdAt: new Date().toISOString() },
      { expiresIn: expiresIn },
    );

    const expiresAt = new Date();
    expiresAt.setSeconds(expiresAt.getSeconds() + expiresIn);

    const userAuthToken = new UserAuthTokenEntity();
    userAuthToken.user = user;
    userAuthToken.type = type;
    userAuthToken.name = name;
    userAuthToken.token = token;
    userAuthToken.expiresAt = expiresAt;
    userAuthToken.hideFromUserProfile = hideFromUserProfile;

    await this.userAuthTokenManager.create(userAuthToken);

    if (type !== 'integration') {
      await this.userAuthTokenManager.deleteExpiredTokens(userAuthToken);
    }

    return userAuthToken;
  }

  async generateToken(
    user: UserEntity,
    type: string,
    name?: string,
  ): Promise<string> {
    const userAuthToken = await this.generateTokenObject(user, type, name);

    return userAuthToken.token;
  }

  async generateTokens(user: UserEntity): Promise<UserAccessTokenDto> {
    if (user.blockedAt || !user.isActive) {
      throw new UnauthorizedException('User not allowed to login');
    }

    return {
      jwtToken: await this.generateToken(user, 'access'),
      refreshToken: await this.generateToken(user, 'refresh'),
    };
  }

  async refreshToken(refreshToken: string): Promise<UserAccessTokenDto> {
    let payload;

    try {
      payload = this.jwtService.verify(refreshToken);
    } catch (e) {
      throw new BadRequestException('Invalid token');
    }

    const user = await this.userProvider.get(payload.id);

    const userRefreshToken = await this.userAuthTokenProvider.findOneBy({
      user: { id: user.id },
      token: refreshToken,
      type: 'refresh',
    });

    if (!userRefreshToken) {
      throw new UnauthorizedException('Invalid refresh token');
    }

    return this.generateTokens(user);
  }
}
