import {
  Controller,
  Delete,
  HttpCode,
  Param,
  ParseUUIDPipe,
  Request,
} from '@nestjs/common';
import {
  ApiNoContentResponse,
  ApiParam,
  ApiTags,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiNotFoundResponse,
  ApiInternalServerErrorResponse,
  ApiBadRequestResponse,
} from '@nestjs/swagger';
import { SignupCodeManager } from '../../service/signup-code.manager';
import { SignupCodeProvider } from '../../service/signup-code.provider';

@ApiTags('signup-code')
@Controller('signup_codes')
export class DeleteController {
  constructor(
    private provider: SignupCodeProvider,
    private manager: SignupCodeManager,
  ) {}

  @ApiOperation({
    operationId: 'signup_code_delete',
    summary: 'Delete a signup code',
    description:
      'Deletes a specific signup code belonging to the current user.\n\n' +
      'Requirements:\n' +
      '- User must be authenticated\n' +
      '- Signup code must exist and belong to the user' +
      'Required Parameters:\n' +
      '- id: UUID of the signup code\n',
  })
  @ApiNoContentResponse({ description: 'Signup code deleted successfully.' })
  @ApiParam({
    name: 'id',
    description: 'The unique identifier of the signup code',
    type: 'string',
    format: 'uuid',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiNotFoundResponse({
    description: 'Not Found. The signup code could not be found.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - Signup code does not exist.
  `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Delete(':id')
  @HttpCode(204)
  async get(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<void> {
    await this.provider
      .getBy({ user: { id: request.user.id }, id: id })
      .then((entity) => this.manager.delete(entity));
  }
}
