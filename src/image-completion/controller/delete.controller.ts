import {
  Controller,
  Delete,
  HttpCode,
  Param,
  ParseUUIDPipe,
  Request,
} from '@nestjs/common';
import {
  ApiNoContentResponse,
  ApiParam,
  ApiTags,
  ApiOperation,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { ImageCompletionManager } from '../service/manager';
import { ImageCompletionProvider } from '../service/provider';

@ApiTags('image_completion')
@Controller('image_completions')
export class DeleteController {
  constructor(
    private provider: ImageCompletionProvider,
    private manager: ImageCompletionManager,
  ) {}

  @Delete(':id')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'image_completion_delete',
    summary: 'Delete an image completion',
    description:
      'Deletes the specified image completion for the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the image completion to delete\n',
  })
  @ApiNoContentResponse({
    description: 'Image completion deleted successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - Image completion does not exist.
      - Image completion is not in a valid state for the requested update.
      - Invalid or unavailable parameters.
      - Failed to analyze request body.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description:
      'Forbidden. User does not have permission to delete this image completion.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the image completion to delete.',
    type: String,
  })
  async get(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<void> {
    const entity = await this.provider.getBy({
      user: { id: request.user.id },
      id: id,
    });
    await this.manager.delete(entity);
  }
}
