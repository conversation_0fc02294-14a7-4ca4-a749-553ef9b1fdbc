import {
  Controller,
  Delete,
  HttpCode,
  Param,
  ParseUUIDPipe,
  Request,
} from '@nestjs/common';
import { UpscaleManager } from '../service/manager';
import { UpscaleProvider } from '../service/provider';
import {
  ApiNoContentResponse,
  ApiParam,
  ApiTags,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiNotFoundResponse,
  ApiInternalServerErrorResponse,
  ApiBadRequestResponse,
} from '@nestjs/swagger';

@ApiTags('upscale')
@Controller('upscales')
export class DeleteController {
  constructor(
    private provider: UpscaleProvider,
    private manager: UpscaleManager,
  ) {}

  @ApiOperation({
    operationId: 'upscale_delete',
    summary: 'Delete an upscale',
    description:
      'Deletes a specific upscale belonging to the current user.\n\n' +
      'Requirements:\n' +
      '- User must be authenticated\n' +
      '- Upscale must exist and belong to the user' +
      '\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the upscale\n',
  })
  @ApiNoContentResponse({ description: 'Upscale deleted successfully.' })
  @ApiParam({
    name: 'id',
    description: 'The unique identifier of the upscale',
    type: 'string',
    format: 'uuid',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiNotFoundResponse({
    description: 'Not Found. The upscale could not be found.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - Upscale does not exist.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Delete(':id')
  @HttpCode(204)
  async get(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<void> {
    await this.provider
      .getBy({ user: { id: request.user.id }, id: id })
      .then((entity) => this.manager.delete(entity));
  }
}
