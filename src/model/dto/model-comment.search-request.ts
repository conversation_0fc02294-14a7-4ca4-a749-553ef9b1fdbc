import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsUUID } from 'class-validator';
import { BaseSearchRequest } from 'src/core/dto/base.search-request';

export class ModelCommentSearchRequest extends BaseSearchRequest {
  @IsOptional()
  @IsUUID()
  @ApiProperty()
  userId: string;

  @IsOptional()
  @IsUUID()
  @ApiProperty()
  modelId: string;

  @IsOptional()
  @IsString()
  @ApiProperty()
  username: string;
}
