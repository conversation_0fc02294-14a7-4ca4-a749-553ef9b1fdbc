import {
  Controller,
  Get,
  Param,
  Query,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiOkResponse,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiOperation,
  ApiBadRequestResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { BaseFindResponseHeadersDto } from '../../../core/dto/base-find-response-headers.dto';
import { ModelDto } from '../../dto/model.dto';
import { ModelInternalSearchRequest } from '../../dto/model.internal-search-request';
import { ModelProvider } from '../../service/provider';
import { ModelResponseMapper } from '../../service/response-mapper';

@ApiTags('model / internal')
@Controller('internal/models')
export class ReadController {
  constructor(
    private provider: ModelProvider,
    private responseMapper: ModelResponseMapper,
  ) {}

  @Get()
  @ApiOperation({
    operationId: 'internal_model_list',
    summary: 'List internal models',
    description:
      'Retrieves a paginated list of models for internal use.\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of models per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n',
  })
  @ApiOkResponse({
    type: ModelDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated list of models.',
  })
  @ApiQuery({
    type: ModelInternalSearchRequest,
    description: 'Query parameters for searching and paginating models.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - Filter parameters must be valid values.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @UsePipes(new ValidationPipe())
  async find(
    @Query() query: ModelInternalSearchRequest,
    @Res() res: Response,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;

    const entities = await this.provider.findBy(
      inputFilters,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    const totalCount = await this.provider.countBy(inputFilters);
    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultipleInternal(entities));
  }

  @Get(':id')
  @ApiOperation({
    operationId: 'internal_model_get',
    summary: 'Get internal model by ID',
    description:
      'Retrieves a specific model by its UUID for internal use.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the model\n',
  })
  @ApiOkResponse({
    type: ModelDto,
    description: 'Returns the model with the specified ID.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the model to retrieve.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - Model does not exist.
    `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async get(@Param() params): Promise<ModelDto> {
    return this.provider
      .get(params.id)
      .then((entity) => this.responseMapper.mapInternal(entity));
  }
}
