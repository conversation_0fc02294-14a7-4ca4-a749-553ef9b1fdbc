name: 🐛 Bug Report
description: Report a bug that affects the API functionality
title: "[BUG] "
labels: [bug]
body:
  - type: textarea
    id: description
    attributes:
      label: Description
      description: Describe the bug and what behavior you expected.
      placeholder: "When I call the /foo endpoint with X payload, I get a 500 error. I expected Y."
    validations:
      required: true

  - type: textarea
    id: steps
    attributes:
      label: Steps to Reproduce
      description: Detail the steps to reproduce the bug (with curl or Postman example if possible).
      placeholder: |
        1. Send a POST request to /foo with payload { "bar": 123 }
        2. Observe 500 error
      render: shell
    validations:
      required: true

  - type: textarea
    id: context
    attributes:
      label: Additional Context
      description: Anything else we should know? Logs, environment details, etc.
      placeholder: |
        - Request ID
        - Relevant logs (from frontend/backend)
        - Environment (dev/staging/prod)

  - type: dropdown
    id: impact
    attributes:
      label: Impact Level
      description: How severe is the bug?
      options:
        - 🔴 High (Blocking users or breaking critical features)
        - 🟠 Medium (Affects some users or workflows)
        - 🟢 Low (Minor issue or visual glitch)

