import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1716145880537 implements MigrationInterface {
  name = 'Api1716145880537';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE INDEX "idx_notification_user_id" ON "notification" ("user_id")
        `);
    await queryRunner.query(`
            CREATE INDEX "idx_notification_type" ON "notification" ("type")
        `);
    await queryRunner.query(`
            CREATE INDEX "idx_user_notification_config_user_id" ON "user_notification_config" ("user_id")
        `);
    await queryRunner.query(`
            CREATE INDEX "idx_user_notification_config_channel" ON "user_notification_config" ("channel")
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            DROP INDEX "public"."idx_user_notification_config_channel"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."idx_user_notification_config_user_id"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."idx_notification_type"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."idx_notification_user_id"
        `);
  }
}
