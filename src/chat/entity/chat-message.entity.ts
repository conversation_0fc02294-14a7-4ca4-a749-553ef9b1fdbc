import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Date<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { ChatConversationEntity } from './chat-conversation.entity';
import { ChatMessageRelationshipEntity } from './chat-message-relationship.entity';

@Entity('chat_message')
export class ChatMessageEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  content: string;

  @Column({ type: 'jsonb', nullable: true })
  availableImages: any;

  @OneToMany(
    () => ChatMessageRelationshipEntity,
    (chatMessageRelationship) => chatMessageRelationship.chatMessage,
    { cascade: true, eager: true },
  )
  attachedFiles: ChatMessageRelationshipEntity[];

  @Column()
  role: string;

  @Column({ name: 'conversation_id' })
  conversationId: string;

  @ManyToOne(
    () => ChatConversationEntity,
    (conversation) => conversation.messages,
  )
  @JoinColumn({ name: 'conversation_id' })
  conversation: ChatConversationEntity;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;
}
