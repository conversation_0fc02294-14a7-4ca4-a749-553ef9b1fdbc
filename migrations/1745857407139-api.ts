import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1745857407139 implements MigrationInterface {
  name = 'Api1745857407139';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "image_edit"
            ADD "webhook_url" text
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "image_edit" DROP COLUMN "webhook_url"
        `);
  }
}
