import { Injectable } from '@nestjs/common';
import { ChatConversationEntity } from '../entity/chat-conversation.entity';
import { ChatConversationRequest } from '../dto/chat-conversation.request';
import { ChatConversationManager } from './chat-conversation.manager';

@Injectable()
export class ChatConversationRequestManager {
  constructor(private readonly manager: ChatConversationManager) {}

  async create(
    request: ChatConversationRequest,
    user: any,
  ): Promise<ChatConversationEntity> {
    const conversation = new ChatConversationEntity();
    conversation.title = request.title;
    conversation.userId = user.id;

    return await this.manager.create(conversation);
  }

  async delete(conversation: ChatConversationEntity): Promise<void> {
    await this.manager.delete(conversation);
  }
}
