import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1701019595094 implements MigrationInterface {
    name = 'Api1701019595094'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "credit_package"
            ADD "is_visible" boolean NOT NULL DEFAULT true
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "credit_package" DROP COLUMN "is_visible"
        `);
    }

}
