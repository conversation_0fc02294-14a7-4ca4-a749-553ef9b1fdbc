import {
  Controller,
  ForbiddenException,
  Get,
  Param,
  ParseUUI<PERSON>ipe,
  Query,
  Request,
  Res,
  UnauthorizedException,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiOkResponse,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiInternalServerErrorResponse,
  ApiResponse,
  ApiBadRequestResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import { BaseFindResponseHeadersDto } from 'src/core/dto/base-find-response-headers.dto';
import { Public } from 'src/core/security/public-routes';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { OrganizationDto } from '../dto/organization.dto';
import { OrganizationSearchRequest } from '../dto/organization.search-request';
import { PublicOrganizationDto } from '../dto/public.organization.dto';
import { OrganizationUserProvider } from '../service/organization-user.provider';
import { OrganizationProvider } from '../service/provider';
import { OrganizationResponseMapper } from '../service/response-mapper';
import { ImageCompletionProvider } from 'src/image-completion/service/provider';
import { OrganizationDashboardDto } from '../dto/organization-dashboard.dto';
import { OrganizationDashboardSearchRequest } from '../dto/organization-dashboard.search';
import { OrganizationDashboardSeatsDto } from '../dto/organization-dashboard-seats.dto';
import { BaseSearchRequest } from 'src/core/dto/base.search-request';

@ApiTags('organization')
@Controller('organizations')
export class ReadController {
  constructor(
    private organizationProvider: OrganizationProvider,
    private organizationMapper: OrganizationResponseMapper,
    private organizationUserProvider: OrganizationUserProvider,
    private imageCompletionProvider: ImageCompletionProvider,
  ) {}

  @ApiOperation({
    operationId: 'organization_list',
    summary: 'List organizations for current user',
    description:
      'Retrieves a paginated list of organizations the current user is a member of. \n\n' +
      'Requirements:\n' +
      '- User must be authenticated' +
      '\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of organizations per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- name: Filter by organization name\n',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - \`name\`: Must be a valid string.
      - Filter parameters must be valid values.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiOkResponse({
    type: OrganizationDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated list of organizations.',
  })
  @ApiQuery({
    type: OrganizationSearchRequest,
    description: 'Query parameters for searching and paginating organizations.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Get()
  @UsePipes(new ValidationPipe())
  async find(
    @Request() request,
    @Query() query: OrganizationSearchRequest,
    @Res() res: Response,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...filters } = query;

    const entities = await this.organizationProvider.findBy(
      { ...filters, userId: request.user.id },
      page,
      limit,
      sortBy,
      sortOrder,
    );
    const totalCount = await this.organizationProvider.countBy(filters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(
      await this.organizationMapper.mapMultiple(entities, request.user.id),
    );
  }

  @ApiOperation({
    operationId: 'organization_dashboard',
    summary: 'Get organization dashboard',
    description:
      'Retrieves dashboard statistics for the specified organization.\n\n' +
      'Requirements:\n' +
      '- User must be a member of the organization' +
      '\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the organization' +
      '\n\n' +
      'Optional Query Parameters:\n' +
      '- day: Show statistics for today\n' +
      '- week: Show statistics for the last week\n' +
      '- month: Show statistics for the last month\n' +
      '- year: Show statistics for the last year\n',
  })
  @ApiOkResponse({
    type: OrganizationDashboardDto,
    description: 'Organization dashboard statistics.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User is not a member of the organization.',
  })
  @ApiNotFoundResponse({
    description: 'Not Found. The organization could not be found.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - \`day\`: Must be a valid boolean value.
      - \`week\`: Must be a valid boolean value.
      - \`month\`: Must be a valid boolean value.
      - \`year\`: Must be a valid boolean value.
      - Invalid query parameters.
    `,
  })
  @Get(':id/dashboard')
  @UsePipes(new ValidationPipe())
  async findDashboard(
    @Request() request,
    @Res() res: Response,
    @Query() query: OrganizationDashboardSearchRequest,
    @Param('id', new ParseUUIDPipe()) id: string,
  ) {
    if (!(await this.organizationUserProvider.isMember(request.user.id, id))) {
      throw new ForbiddenException('Not a member');
    }
    const imagesGenerated = await this.imageCompletionProvider.countBy({
      ...query,
      organizationId: id,
    });
    res.send(
      await this.organizationMapper.mapDashboard({
        imagesGenerated: imagesGenerated,
      }),
    );
  }

  @ApiOperation({
    operationId: 'organization_dashboard_seats',
    summary: 'Get organization dashboard seats',
    description:
      'Retrieves seat usage statistics for the specified organization.\n\n' +
      'Requirements:\n' +
      '- User must be a member of the organization' +
      '\n\n' +
      'Required Parameters:\n' +
      '- organizationId: UUID of the organization\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of seats per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n',
  })
  @ApiOkResponse({
    type: OrganizationDashboardSeatsDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'List of seat usage statistics.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`organizationId\`: Must be a valid UUID.
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User is not a member of the organization.',
  })
  @ApiNotFoundResponse({
    description: 'Not Found. The organization could not be found.',
  })
  @Get(':organizationId/seats')
  @UsePipes(new ValidationPipe())
  async findDashboardSeats(
    @Request() request,
    @Res() res: Response,
    @Query() query: BaseSearchRequest,
    @Param('organizationId', new ParseUUIDPipe()) id: string,
  ) {
    if (!(await this.organizationUserProvider.isMember(request.user.id, id))) {
      throw new ForbiddenException('Not a member');
    }
    const { page, limit } = query;
    const entities =
      await this.imageCompletionProvider.findOrganizationDashboardSeats(
        page,
        limit,
        id,
      );
    const totalCount =
      await this.imageCompletionProvider.countOrganizationDashboardSeats(id);
    setPaginationHeaders(res, totalCount, page, limit);
    res.send(await this.organizationMapper.mapSeatDashboardMultiple(entities));
  }

  @ApiOperation({
    operationId: 'organization_get',
    summary: 'Get organization by ID',
    description:
      'Retrieves the details of the specified organization.\n\n' +
      'Requirements:\n' +
      '- User must be a member of the organization' +
      '\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the organization',
  })
  @ApiOkResponse({
    type: OrganizationDto,
    description: 'Organization details.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User is not a member of the organization.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
    `,
  })
  @ApiNotFoundResponse({
    description: 'Not Found. The organization could not be found.',
  })
  @Get(':id([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})')
  async get(
    @Request() req,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<OrganizationDto> {
    const organization = await this.organizationProvider.get(id);

    if (!this.organizationUserProvider.isMember(req.user.id, organization.id)) {
      throw new UnauthorizedException();
    }

    return await this.organizationMapper.map(organization, req.user.id);
  }

  @ApiOperation({
    operationId: 'organization_get_by_handle',
    summary: 'Get public organization by handle',
    description:
      'Retrieves the public details of an organization by its handle.' +
      '\n\n' +
      'Required Parameters:\n' +
      '- handle: Handle of the organization',
  })
  @ApiOkResponse({
    type: PublicOrganizationDto,
    description: 'Public organization details.',
  })
  @ApiParam({ name: 'handle', description: 'Organization handle' })
  @ApiResponse({
    status: 404,
    description: 'Not Found. The organization could not be found.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`handle\`: Must be a valid string.
    `,
  })
  @Public()
  @Get(':handle')
  async getByHandle(
    @Request() req,
    @Param('handle') handle: string,
  ): Promise<PublicOrganizationDto> {
    const organization = await this.organizationProvider.getBy({
      handle,
    });

    if (!this.organizationUserProvider.isMember(req.user.id, organization.id)) {
      throw new UnauthorizedException();
    }

    return await this.organizationMapper.map(organization, req.user.id);
  }
}
