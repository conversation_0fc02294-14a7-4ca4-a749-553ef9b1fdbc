import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  ArrayMaxSize,
  ArrayMinSize,
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUrl,
  IsUUID,
  Matches,
  MaxLength,
} from 'class-validator';
import {
  ClassEnum,
  PrivacyEnum,
  SettingsEnum,
} from '../../../model/entity/model.entity';

export class ModelCreateRequest {
  @IsNotEmpty()
  @IsString()
  @Matches(/^[a-zA-Z0-9_.]+$/, {
    message:
      'Name must contain only alphanumeric characters, underscores, and dots',
  })
  @MaxLength(255)
  @ApiProperty()
  name: string;

  @IsOptional()
  @IsEnum(PrivacyEnum, {
    message: 'privacy must be a valid value',
  })
  @ApiProperty({ enum: PrivacyEnum })
  privacy: PrivacyEnum;

  @ApiProperty()
  @IsOptional()
  @Transform(({ value }) => (value === '' ? null : value))
  @Matches(/^[a-zA-Z0-9,()_\-:;\[\].äöüÄÖÜéÉèëÈË\? ]+$/u, {
    message:
      'type should only contain alphanumeric characters, commas, hyphens, underscores, parentheses, colons, semicolons, and question marks',
  })
  type: string;

  @IsNotEmpty()
  @Transform(({ value }) =>
    typeof value === 'string' ? value.toLowerCase() : value,
  )
  @IsEnum(ClassEnum, {
    message: 'class must be a valid value',
  })
  @ApiProperty({ enum: ClassEnum })
  class: ClassEnum;

  @ApiProperty()
  @IsOptional()
  description: string;

  @ApiProperty()
  @IsOptional()
  website: string;

  @ApiProperty({
    type: [String],
    enum: SettingsEnum,
    isArray: true,
    required: false,
    enumName: 'SettingsEnum',
    example: [SettingsEnum.NO_ADULT_CONTENT, SettingsEnum.NO_VIOLENCE], // Replace with actual enum values
  })
  @IsOptional()
  @IsArray()
  @IsEnum(SettingsEnum, {
    each: true,
    message: 'settings must be valid values',
  })
  settings: SettingsEnum[];

  @ApiProperty({ type: [String], required: false, minItems: 1, maxItems: 50 })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(50)
  @IsUrl(
    {},
    { each: true, message: 'Each training data URL must be a valid URL' },
  )
  trainingDataUrls?: string[];

  @ApiProperty()
  @IsOptional()
  @IsUrl()
  webhookUrl?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  trainingMode?: string;

  @ApiProperty({
    description:
      'Organization ID to deduct credits from. If provided, credits will be deducted from the organization account instead of the user account. User must be a member of the organization.',
    required: false,
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'organizationId must be a valid UUID' })
  organizationId?: string;
}
