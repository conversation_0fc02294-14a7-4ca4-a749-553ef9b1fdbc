import { ApiProperty } from '@nestjs/swagger';
import {
    IsBoolean,
    IsIn,
    IsInt,
    IsOptional,
    IsString,
    IsUrl,
    IsUUID,
    <PERSON>,
    Min
} from 'class-validator';

export enum ModeEnum {
  DEFAULT = 'default',
  SIGMA = 'sigma',
}

export class ImageRequest {
  @ApiProperty()
  @IsString()
  @IsOptional()
  prompt: string;

  @ApiProperty()
  @IsInt()
  @Min(480)
  @Max(2160)
  width?: number = 1600;

  @ApiProperty()
  @IsInt()
  @Min(480)
  @Max(2160)
  height?: number = 1600;

  @ApiProperty()
  @IsInt()
  @Min(1)
  @Max(6)
  quality?: number = 2;

  @ApiProperty()
  @IsInt()
  @Min(1)
  @Max(6)
  creativity?: number = 2;

  @ApiProperty()
  @IsOptional()
  hasWatermark?: boolean = true;

  @IsOptional()
  @ApiProperty()
  @IsIn([2, 3])
  systemVersion: number;

  // eslint-disable-next-line @typescript-eslint/no-inferrable-types
  @ApiProperty()
  @IsBoolean()
  hideFromUserProfile: boolean = false;

  @IsOptional()
  @ApiProperty({ enum: ModeEnum })
  mode?: ModeEnum = ModeEnum.DEFAULT;

  @ApiProperty()
  @IsOptional()
  @IsUrl()
  webhookUrl?: string;

  @ApiProperty({
    description: 'Organization ID to deduct credits from. If provided, credits will be deducted from the organization account instead of the user account. User must be a member of the organization.',
    required: false,
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID(4, { message: 'organizationId must be a valid UUID' })
  organizationId?: string;
}
