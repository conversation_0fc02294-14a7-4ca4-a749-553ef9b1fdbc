import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsBoolean,
  IsEnum,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { BaseSearchRequest } from '../../core/dto/base.search-request';
import { StatusEnum } from '../entity/upscale.entity';

export class UpscaleSearchRequest extends BaseSearchRequest {
  @IsOptional()
  @IsUUID()
  @ApiProperty()
  userId: string;

  @IsOptional()
  @IsString()
  @ApiProperty()
  username: string;

  @IsOptional()
  @IsUUID()
  @ApiProperty()
  imageCompletionId: string;

  @IsOptional()
  @IsEnum(StatusEnum)
  @Transform(({ value }) => value.toLowerCase())
  @ApiProperty({ enum: StatusEnum })
  status?: StatusEnum;

  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  onlyFollowing: boolean;
}
