on:
  push:
    branches:
      - develop
    tags:
      - dev-*

name: Deploy to QA

permissions:
  id-token: write
  contents: read

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          role-to-assume: arn:aws:iam::046587876437:role/github-actions-iam-user-role
          role-session-name: GitHub_to_AWS_via_FederatedOIDC
          aws-region: 'eu-north-1'

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REPOSITORY_URI: 046587876437.dkr.ecr.eu-north-1.amazonaws.com/letzai-api-dev
          IMAGE_TAG: latest
        run: |
          docker build -t $ECR_REPOSITORY_URI:$IMAGE_TAG .
          docker push $ECR_REPOSITORY_URI:$IMAGE_TAG

      - name: Deploy new image to ECS Service
        env:
          CLUSTER: letzai-dev-services
          REGION: eu-north-1
        run: |
          aws ecs update-service --cluster $CLUSTER --service letzai-api --force-new-deployment --region $REGION
          aws ecs update-service --cluster $CLUSTER --service letzai-private-api --force-new-deployment --region $REGION
