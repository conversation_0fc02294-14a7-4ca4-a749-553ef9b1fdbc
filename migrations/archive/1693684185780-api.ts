import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1693684185780 implements MigrationInterface {
    name = 'Api1693684185780'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "model"
            ADD "generation_data" json
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "model" DROP COLUMN "generation_data"
        `);
    }

}
