import {
  DeleteObjectCommand,
  DeleteObjectsCommand,
  ListObjectsV2Command,
  S3Client,
} from '@aws-sdk/client-s3';
import { Inject, Injectable, Logger } from '@nestjs/common';

export interface S3DeleteResult {
  deleted: string[];
  failed: Array<{ key: string; error: string }>;
}

export interface S3Object {
  Key: string;
  VersionId?: string;
}

@Injectable()
export class S3BatchDeleteService {
  // AWS S3 allows max 1000 objects per batch delete request
  private readonly MAX_BATCH_SIZE = 1000;
  
  private readonly logger = new Logger(S3BatchDeleteService.name);
  
  constructor(
    @Inject('S3') private s3: S3Client,
  ) {}

  /**
   * Delete multiple objects in batches
   */
  async batchDelete(bucket: string, keys: string[]): Promise<S3DeleteResult> {
    if (keys.length === 0) {
      return { deleted: [], failed: [] };
    }

    const result: S3DeleteResult = { deleted: [], failed: [] };
    
    // Process in batches of MAX_BATCH_SIZE
    for (let i = 0; i < keys.length; i += this.MAX_BATCH_SIZE) {
      const batch = keys.slice(i, i + this.MAX_BATCH_SIZE);
      
      try {
        const batchResult = await this.deleteBatch(bucket, batch);
        result.deleted.push(...batchResult.deleted);
        result.failed.push(...batchResult.failed);
      } catch (error) {
        this.logger.error('Batch delete failed', {
          bucket,
          batchSize: batch.length,
          error: error.message,
        });
        
        // Add all keys in this batch to failed list
        result.failed.push(...batch.map(key => ({
          key,
          error: error.message,
        })));
      }
    }

    this.logger.log('Batch delete completed', {
      bucket,
      totalKeys: keys.length,
      deleted: result.deleted.length,
      failed: result.failed.length,
    });

    return result;
  }

  /**
   * Delete all objects in a folder/prefix
   */
  async deleteFolder(bucket: string, prefix: string): Promise<S3DeleteResult> {
    const allKeys: string[] = [];
    let continuationToken: string | undefined;

    // List all objects with the given prefix
    do {
      const listCommand = new ListObjectsV2Command({
        Bucket: bucket,
        Prefix: prefix,
        ContinuationToken: continuationToken,
        MaxKeys: 1000,
      });

      const response = await this.s3.send(listCommand);
      
      if (response.Contents) {
        allKeys.push(...response.Contents.map(obj => obj.Key!));
      }

      continuationToken = response.NextContinuationToken;
    } while (continuationToken);

    this.logger.log('Found objects to delete', {
      bucket,
      prefix,
      objectCount: allKeys.length,
    });

    if (allKeys.length === 0) {
      return { deleted: [], failed: [] };
    }

    return this.batchDelete(bucket, allKeys);
  }

  /**
   * Delete a single object
   */
  async deleteObject(bucket: string, key: string): Promise<boolean> {
    try {
      const command = new DeleteObjectCommand({
        Bucket: bucket,
        Key: key,
      });

      await this.s3.send(command);
      
      this.logger.log('Object deleted successfully', { bucket, key });
      return true;
    } catch (error) {
      this.logger.error('Failed to delete object', {
        bucket,
        key,
        error: error.message,
      });
      return false;
    }
  }

  /**
   * Delete objects with retry mechanism
   */
  async deleteWithRetry(
    bucket: string,
    keys: string[],
    maxRetries = 3,
    retryDelayMs = 1000
  ): Promise<S3DeleteResult> {
    let attempt = 0;
    let keysToRetry = [...keys];
    const result: S3DeleteResult = { deleted: [], failed: [] };

    while (attempt < maxRetries && keysToRetry.length > 0) {
      attempt++;
      
      this.logger.log('Attempting batch delete', {
        bucket,
        attempt,
        maxRetries,
        keysToRetry: keysToRetry.length,
      });

      const batchResult = await this.batchDelete(bucket, keysToRetry);
      
      // Add successful deletions to result
      result.deleted.push(...batchResult.deleted);
      
      // Update keys to retry (only the failed ones)
      keysToRetry = batchResult.failed.map(f => f.key);
      
      if (keysToRetry.length === 0) {
        // All objects deleted successfully
        break;
      }
      
      if (attempt < maxRetries) {
        // Wait before retry
        await this.sleep(retryDelayMs * attempt); // Exponential backoff
      } else {
        // Final attempt failed, add remaining failures to result
        result.failed.push(...batchResult.failed);
      }
    }

    return result;
  }

  /**
   * Delete a batch of objects using S3's batch delete API
   */
  private async deleteBatch(bucket: string, keys: string[]): Promise<S3DeleteResult> {
    const objects: S3Object[] = keys.map(key => ({ Key: key }));
    
    const command = new DeleteObjectsCommand({
      Bucket: bucket,
      Delete: {
        Objects: objects,
        Quiet: false, // Return information about deleted and failed objects
      },
    });

    const response = await this.s3.send(command);
    
    const deleted = (response.Deleted || []).map(obj => obj.Key!);
    const failed = (response.Errors || []).map(err => ({
      key: err.Key!,
      error: `${err.Code}: ${err.Message}`,
    }));

    return { deleted, failed };
  }

  /**
   * Helper method to sleep for specified milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Validate that an S3 bucket and key are properly formatted
   */
  validateS3Path(bucket: string, key: string): boolean {
    if (!bucket || bucket.length === 0) {
      this.logger.warn('Invalid S3 bucket name', { bucket });
      return false;
    }

    if (!key || key.length === 0) {
      this.logger.warn('Invalid S3 key', { key });
      return false;
    }

    return true;
  }
}
