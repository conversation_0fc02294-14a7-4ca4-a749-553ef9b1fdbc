import { Injectable } from '@nestjs/common';
import { UserAccessTokenDto } from '../dto/user-access-token.dto';
import { UserAuthTokenEntity } from '../entity/user-auth-token.entity';
import { IntegrationTokenDto } from '../dto/integration-token.dto';

@Injectable()
export class UserAuthTokenResponseMapper {
  mapAccessToken(jwtToken: string, refreshToken: string): UserAccessTokenDto {
    const userAccessTokenDto = new UserAccessTokenDto();

    userAccessTokenDto.jwtToken = jwtToken;
    userAccessTokenDto.refreshToken = refreshToken;

    return userAccessTokenDto;
  }

  mapMultiple(entities: UserAuthTokenEntity[]): IntegrationTokenDto[] {
    return entities.map((entity) => this.map(entity));
  }

  map(entity: UserAuthTokenEntity): IntegrationTokenDto {
    const dto = new IntegrationTokenDto();

    dto.id = entity.id;
    dto.name = entity.name;
    dto.jwtToken = entity.token;
    dto.expiresAt = entity.expiresAt;
    dto.createdAt = entity.createdAt;

    return dto;
  }
}
