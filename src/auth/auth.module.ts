import { DynamicModule, forwardRef, Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CoreModule } from 'src/core/core.module';
import { OrganizationModule } from 'src/organization/organization.module';
import { UserModule } from 'src/user/user.module';
import { OAuthService } from '../user/service/oauth.service';
import { jwtConfig } from './config/jwt.config';
import { AuthController } from './controller/auth.controller';
import { CreateController } from './controller/create.controller';
import { DeleteController } from './controller/delete.controller';
import { ReadController } from './controller/read.controller';
import { SamlController } from './controller/saml.controller';
import { IdpConfigEntity } from './entity/idp-config.entity';
import { OrganizationAuthTokenEntity } from './entity/organization-auth-token.entity';
import { UserAuthTokenEntity } from './entity/user-auth-token.entity';
import { JwtAuthGuard } from './service/jwt-auth.guard';
import { JwtIntegrationAuthGuard } from './service/jwt-integration-auth.guard';
import { JwtStrategy } from './service/jwt.strategy';
import { UserAuthTokenManager } from './service/manager';
import { OrganizationAuthTokenProvider } from './service/organization-auth-token.provider';
import { UserAuthTokenRequestManager } from './service/request-manager';
import { UserAuthTokenResponseMapper } from './service/response-mapper';
import { SamlConfigService } from './service/saml-config.service';
import { SamlService } from './service/saml.service';
import { UserAuthTokenProvider } from './service/user-auth-token.provider';
import { UserAuthService } from './service/user-auth.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      IdpConfigEntity,
      UserAuthTokenEntity,
      OrganizationAuthTokenEntity,
    ]),
    PassportModule,
    CoreModule,
    forwardRef(() => UserModule),
    forwardRef(() => OrganizationModule),
    JwtModule.register(jwtConfig),
  ],
  providers: [
    JwtAuthGuard,
    JwtIntegrationAuthGuard,
    JwtStrategy,
    OAuthService,
    UserAuthService,
    UserAuthTokenProvider,
    OrganizationAuthTokenProvider,
    UserAuthTokenResponseMapper,
    UserAuthTokenRequestManager,
    UserAuthTokenManager,
    SamlConfigService,
    SamlService,
  ],
  exports: [
    UserAuthTokenProvider,
    OrganizationAuthTokenProvider,
    JwtAuthGuard,
    JwtIntegrationAuthGuard,
    UserAuthService,
  ],
})
export class AuthModule {
  static register(enableControllers: boolean): DynamicModule {
    return {
      module: AuthModule,
      controllers: enableControllers
        ? [
            AuthController,
            CreateController,
            DeleteController,
            ReadController,
            SamlController,
          ]
        : [],
    };
  }
}
