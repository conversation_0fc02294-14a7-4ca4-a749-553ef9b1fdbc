import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1743185139327 implements MigrationInterface {
  name = 'Api1743185139327';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "user_auth_token"
            ADD "hide_from_user_profile" boolean NOT NULL DEFAULT false
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "user_auth_token" DROP COLUMN "hide_from_user_profile"
        `);
  }
}
