import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { OrganizationEntity } from '../../organization/entity/organization.entity';
import { UserEntity } from '../../user/entity/user.entity';
import { TransactionTypeEnum } from './transaction-type.enum';

@Entity('transaction')
export class TransactionEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => UserEntity)
  user: UserEntity;

  @Column({ type: 'uuid', nullable: true })
  @Index('idx_transaction_user_id')
  userId: string;

  @Column({ type: 'uuid', nullable: true })
  @Index('idx_transaction_organization_id')
  organizationId: string;

  @ManyToOne(() => OrganizationEntity)
  organization: OrganizationEntity;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column()
  amount: number;

  @Column()
  @Index('idx_transaction_type')
  type: TransactionTypeEnum;

  @Column({ nullable: true })
  reference?: string;

  @Column({ type: 'json', nullable: true })
  context?: any;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt?: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
