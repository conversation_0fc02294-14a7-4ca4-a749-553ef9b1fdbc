import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1723034479840 implements MigrationInterface {
  name = 'Api1723034479840';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "upscale"
            ADD "is_selected" boolean NOT NULL DEFAULT false
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "upscale" DROP COLUMN "is_selected"
        `);
  }
}
