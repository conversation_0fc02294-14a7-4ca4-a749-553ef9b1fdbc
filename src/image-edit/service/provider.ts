import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { AbstractProvider } from 'src/core/service/abstract.provider';
import { EntityNotFoundError, Repository, SelectQueryBuilder } from 'typeorm';
import { FindOneOptions } from 'typeorm/find-options/FindOneOptions';
import { ImageEditEntity } from '../entity/image-edit.entity';

@Injectable()
export class ImageEditProvider extends AbstractProvider<ImageEditEntity> {
  constructor(
    @InjectRepository(ImageEditEntity)
    repository: Repository<ImageEditEntity>,
    logger: Logger,
  ) {
    super(repository, logger);
  }

  async get(id: string): Promise<ImageEditEntity> {
    const imageEdit = await this.createBaseQueryBuilder()
      .where('imageEdit.id = :id', { id })
      .getOne();

    if (!imageEdit) {
      throw new EntityNotFoundError(this.repository.target, id);
    }

    return imageEdit;
  }

  async getBy(criteria: any): Promise<ImageEditEntity> {
    // Use our custom query builder instead of the base class method
    // to properly handle JSON fields
    const queryBuilder = this.prepareQueryBuilder(criteria);
    const imageEdit = await queryBuilder.getOne();

    if (!imageEdit) {
      throw new EntityNotFoundError(this.repository.target, criteria);
    }

    return imageEdit;
  }

  prepareFindOneOptions(criteria: any): FindOneOptions<ImageEditEntity> {
    // Override the base class method to handle JSON fields properly
    // However, since we override getBy above, this might not be used much
    // but it's good to have for consistency
    const processedCriteria = { ...criteria };

    // Handle inputImageUrl JSON field
    if (processedCriteria.inputImageUrl !== undefined) {
      // For FindOneOptions, we can't use custom SQL, so we'll need to
      // handle this in the overridden getBy method instead
      // This is a fallback that won't work perfectly for JSON fields
    }

    return { where: processedCriteria };
  }

  async findBy(
    criteria: any,
    page: number,
    limit: number,
    sortBy = 'createdAt',
    sortOrder: 'ASC' | 'DESC' = 'ASC',
  ): Promise<ImageEditEntity[]> {
    const queryBuilder = this.prepareQueryBuilder(criteria);

    queryBuilder
      .offset((page - 1) * limit)
      .limit(limit)
      .orderBy(`imageEdit.${sortBy}`, sortOrder);

    return await queryBuilder.getMany();
  }

  async countBy(criteria: any): Promise<number> {
    // Override to use our custom query builder for JSON field handling
    const queryBuilder = this.prepareQueryBuilder(criteria);
    return await queryBuilder.getCount();
  }

  prepareQueryBuilder(criteria: any): SelectQueryBuilder<ImageEditEntity> {
    const where = { ...criteria };
    const queryBuilder = this.createBaseQueryBuilder();

    Object.keys(where).forEach((key) => {
      // Handle JSON fields that require special comparison
      if (key === 'inputImageUrl') {
        // For JSON array fields, we need to use JSON operators
        if (Array.isArray(where[key])) {
          queryBuilder.andWhere(`imageEdit.${key} = :${key}::json`, {
            [key]: JSON.stringify(where[key]),
          });
        } else if (where[key] === null || where[key] === undefined) {
          queryBuilder.andWhere(`imageEdit.${key} IS NULL`);
        } else {
          // If it's a single value, treat it as searching for an array containing that value
          queryBuilder.andWhere(`imageEdit.${key} @> :${key}::json`, {
            [key]: JSON.stringify([where[key]]),
          });
        }
      } else {
        // Handle regular fields with standard equality
        queryBuilder.andWhere(`imageEdit.${key} = :${key}`, {
          [key]: where[key],
        });
      }
    });

    return queryBuilder;
  }

  createBaseQueryBuilder(): SelectQueryBuilder<ImageEditEntity> {
    return this.repository
      .createQueryBuilder('imageEdit')
      .innerJoinAndSelect('imageEdit.user', 'user')
      .leftJoinAndSelect(
        'imageEdit.originalImageCompletion',
        'originalImageCompletion',
      )
      .leftJoinAndSelect(
        'originalImageCompletion.user',
        'originalImageCompletionUser',
      )
      .leftJoinAndSelect(
        'imageEdit.generatedImageCompletion',
        'generatedImageCompletion',
      )
      .leftJoinAndSelect(
        'generatedImageCompletion.user',
        'generatedImageCompletionUser',
      )
      .leftJoinAndSelect('imageEdit.imageCompletions', 'imageCompletions')
      .leftJoinAndSelect(
        'imageCompletions.imageCompletion',
        'imageCompletionChoice',
      )
      .leftJoinAndSelect(
        'imageCompletionChoice.user',
        'imageCompletionChoiceUser',
      )
      .leftJoinAndSelect(
        'imageCompletionChoice.models',
        'imageCompletionChoiceModels',
      )
      .leftJoinAndSelect(
        'imageCompletionChoiceModels.model',
        'imageCompletionChoiceModel',
      )
      .leftJoinAndSelect('imageEdit.models', 'imageEditModels')
      .leftJoinAndSelect('imageEditModels.model', 'model');
  }
}
