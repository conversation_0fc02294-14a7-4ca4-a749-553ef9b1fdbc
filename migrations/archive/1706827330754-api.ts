import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1706827330754 implements MigrationInterface {
    name = 'Api1706827330754'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "image_completion"
            ADD "status_detail" text
        `);
        await queryRunner.query(`
            ALTER TABLE "user_account"
            ALTER COLUMN "is_verified"
            SET DEFAULT false
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_account"
            ALTER COLUMN "is_verified"
            SET DEFAULT true
        `);
        await queryRunner.query(`
            ALTER TABLE "image_completion" DROP COLUMN "status_detail"
        `);
    }

}
