import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON><PERSON><PERSON>y,
  IsBoolean,
  IsEnum,
  IsInt,
  IsOptional,
  IsString,
  IsUrl,
  IsUUI<PERSON>,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { EditMode } from '../entity/image-edit.entity';

export class ImageEditRequest {
  @ApiProperty({ description: 'Original image completion ID', required: false })
  @IsOptional()
  @IsUUID()
  originalImageCompletionId: string;

  @ApiProperty()
  @IsUUID()
  @IsOptional()
  organizationId?: string;

  @ApiProperty({
    description: 'Edit mode, either "in", "out", "skin", or "context"',
  })
  @IsOptional()
  @IsEnum(EditMode)
  mode: EditMode;

  @ApiProperty({ description: 'Width of the edited image', required: false })
  @IsInt()
  @IsOptional()
  width?: number;

  @ApiProperty({ description: 'Height of the edited image', required: false })
  @IsInt()
  @IsOptional()
  height?: number;

  @ApiProperty({
    type: 'number',
    description: 'Number of image completions to generate',
    required: false,
  })
  @IsInt()
  @IsOptional()
  @Min(1)
  @Max(5)
  imageCompletionsCount? = 3;

  @ApiProperty({ default: false })
  @IsBoolean()
  hidePrompt = false;

  @ApiProperty({ description: 'Prompt', required: false })
  @IsString()
  @IsOptional()
  prompt?: string;

  @ApiProperty({ description: 'Inpainting mask', required: false })
  @IsString()
  @IsOptional()
  mask?: string;

  @ApiProperty({
    description: 'Input image URLs',
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @IsUrl({}, { each: true })
  inputImageUrl?: string[];

  @ApiProperty({ type: 'object', required: false })
  @IsOptional()
  settings?: any;

  @ApiProperty()
  @IsOptional()
  @IsUrl()
  webhookUrl?: string;
}
