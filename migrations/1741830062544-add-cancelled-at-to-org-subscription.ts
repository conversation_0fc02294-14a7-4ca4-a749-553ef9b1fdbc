import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCancelledAtToOrgSubscription1741830062544
  implements MigrationInterface
{
  name = 'AddCancelledAtToOrgSubscription1741830062544';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "organization_subscription"
            ADD "cancelled_at" TIMESTAMP
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "organization_subscription"
            DROP COLUMN "cancelled_at"
        `);
  }
}
