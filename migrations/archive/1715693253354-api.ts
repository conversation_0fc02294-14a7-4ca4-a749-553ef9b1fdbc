import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1715693253354 implements MigrationInterface {
    name = 'Api1715693253354'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_auth_token" DROP CONSTRAINT "FK_e4dbc40e1c38f1497e0de9d1670"
        `);
        await queryRunner.query(`
            ALTER TABLE "user_auth_token"
            ALTER COLUMN "user_id"
            SET NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "model_version" DROP CONSTRAINT "FK_959afe36ff1a069b7cfbdb65ece"
        `);
        await queryRunner.query(`
            ALTER TABLE "model_version"
            ALTER COLUMN "model_id"
            SET NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription" DROP CONSTRAINT "FK_940d49a105d50bbd616be540013"
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription" DROP CONSTRAINT "FK_7dd09a6b2a5612c4c04485711d0"
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription"
            ALTER COLUMN "user_id"
            SET NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription"
            ALTER COLUMN "credit_package_id"
            SET NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "transaction" DROP CONSTRAINT "FK_b4a3d92d5dde30f3ab5c34c5862"
        `);
        await queryRunner.query(`
            ALTER TABLE "transaction"
            ALTER COLUMN "user_id"
            SET NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "user_credit_balance" DROP CONSTRAINT "FK_a73c0a8540dbc42c293caa0f25b"
        `);
        await queryRunner.query(`
            ALTER TABLE "user_credit_balance"
            ALTER COLUMN "user_id"
            SET NOT NULL
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_e4dbc40e1c38f1497e0de9d167" ON "user_auth_token" ("user_id")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_959afe36ff1a069b7cfbdb65ec" ON "model_version" ("model_id")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_71526a7bd80ed755ce8f78d4da" ON "model" ("user_id")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_940d49a105d50bbd616be54001" ON "subscription" ("user_id")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_7dd09a6b2a5612c4c04485711d" ON "subscription" ("credit_package_id")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_b4a3d92d5dde30f3ab5c34c586" ON "transaction" ("user_id")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_a73c0a8540dbc42c293caa0f25" ON "user_credit_balance" ("user_id")
        `);
        await queryRunner.query(`
            ALTER TABLE "user_auth_token"
            ADD CONSTRAINT "FK_e4dbc40e1c38f1497e0de9d1670" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "model_version"
            ADD CONSTRAINT "FK_959afe36ff1a069b7cfbdb65ece" FOREIGN KEY ("model_id") REFERENCES "model"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription"
            ADD CONSTRAINT "FK_940d49a105d50bbd616be540013" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription"
            ADD CONSTRAINT "FK_7dd09a6b2a5612c4c04485711d0" FOREIGN KEY ("credit_package_id") REFERENCES "credit_package"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "transaction"
            ADD CONSTRAINT "FK_b4a3d92d5dde30f3ab5c34c5862" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "user_credit_balance"
            ADD CONSTRAINT "FK_a73c0a8540dbc42c293caa0f25b" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_credit_balance" DROP CONSTRAINT "FK_a73c0a8540dbc42c293caa0f25b"
        `);
        await queryRunner.query(`
            ALTER TABLE "transaction" DROP CONSTRAINT "FK_b4a3d92d5dde30f3ab5c34c5862"
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription" DROP CONSTRAINT "FK_7dd09a6b2a5612c4c04485711d0"
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription" DROP CONSTRAINT "FK_940d49a105d50bbd616be540013"
        `);
        await queryRunner.query(`
            ALTER TABLE "model_version" DROP CONSTRAINT "FK_959afe36ff1a069b7cfbdb65ece"
        `);
        await queryRunner.query(`
            ALTER TABLE "user_auth_token" DROP CONSTRAINT "FK_e4dbc40e1c38f1497e0de9d1670"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_a73c0a8540dbc42c293caa0f25"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_b4a3d92d5dde30f3ab5c34c586"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_7dd09a6b2a5612c4c04485711d"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_940d49a105d50bbd616be54001"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_71526a7bd80ed755ce8f78d4da"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_959afe36ff1a069b7cfbdb65ec"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_e4dbc40e1c38f1497e0de9d167"
        `);
        await queryRunner.query(`
            ALTER TABLE "user_credit_balance"
            ALTER COLUMN "user_id" DROP NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "user_credit_balance"
            ADD CONSTRAINT "FK_a73c0a8540dbc42c293caa0f25b" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "transaction"
            ALTER COLUMN "user_id" DROP NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "transaction"
            ADD CONSTRAINT "FK_b4a3d92d5dde30f3ab5c34c5862" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription"
            ALTER COLUMN "credit_package_id" DROP NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription"
            ALTER COLUMN "user_id" DROP NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription"
            ADD CONSTRAINT "FK_7dd09a6b2a5612c4c04485711d0" FOREIGN KEY ("credit_package_id") REFERENCES "credit_package"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription"
            ADD CONSTRAINT "FK_940d49a105d50bbd616be540013" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "model_version"
            ALTER COLUMN "model_id" DROP NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "model_version"
            ADD CONSTRAINT "FK_959afe36ff1a069b7cfbdb65ece" FOREIGN KEY ("model_id") REFERENCES "model"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "user_auth_token"
            ALTER COLUMN "user_id" DROP NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "user_auth_token"
            ADD CONSTRAINT "FK_e4dbc40e1c38f1497e0de9d1670" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    }

}
