import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  Index,
  Unique,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
} from 'typeorm';

import { UserEntity } from '../../user/entity/user.entity';
import { NotificationChannel } from '../enum/notification-channel.enum';

@Entity('user_notification_config')
export class UserNotificationConfigEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  @Index('idx_user_notification_config_user_id')
  userId: string;

  @ManyToOne(() => UserEntity, { eager: false })
  user: UserEntity;

  @Column({
    type: 'enum',
    enum: NotificationChannel,
  })
  @Index('idx_user_notification_config_channel')
  channel: NotificationChannel;

  @Column({ nullable: true })
  externalId?: string;

  @Column('json', { nullable: true })
  details?: any;

  @Column({ default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt?: Date;
}
