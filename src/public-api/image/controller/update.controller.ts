import {
  Body,
  Controller,
  HttpCode,
  Param,
  ParseUUIDPipe,
  Put,
  Request,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiNoContentResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
} from '@nestjs/swagger';
import { ImageCompletionProvider } from 'src/image-completion/service/provider';
import { ImageRequestManager } from '../service/request-manager';
import { ImagePrivacyRequest } from '../dto/image.privacy-request';
import { ImagePromptPrivacyRequest } from '../dto/image.prompt-privacy-request';

@ApiTags('images')
@Controller('images')
@ApiBearerAuth()
export class UpdateController {
  constructor(
    private provider: ImageCompletionProvider,
    private requestManager: ImageRequestManager,
  ) {}

  @Put(':id/interruption')
  @ApiOperation({
    operationId: 'images_interrupt',
    summary: 'Interrupt image generation',
    description:
      'Stops the ongoing generation process for a specific image.\n\n' +
      'Path Parameters:\n' +
      '- id: UUID of the image to interrupt\n\n' +
      'The endpoint will:\n' +
      '- Stop the generation process\n' +
      '- Update image status to interrupted\n\n' +
      'Note: Only works for images in generating status.',
  })
  @ApiParam({
    name: 'id',
    description: 'The unique identifier of the image to interrupt',
    type: 'string',
    format: 'uuid',
  })
  @ApiNoContentResponse({
    description: 'The image generation was successfully interrupted.',
  })
  @ApiResponse({
    status: 400,
    description: `
      Bad Request. Possible reasons:
      - Image is not in a processing state
      - Image does not exist
      - Image belongs to another user
    `,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found. The requested image could not be found.',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. An unexpected error occurred.',
  })
  @HttpCode(204)
  async imageCompletionActivation(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ) {
    const entity = await this.provider.getBy({
      user: { id: request.user.id },
      id: id,
    });

    await this.requestManager.interrupt(entity);
  }

  @Put(':id/privacy')
  @ApiOperation({
    operationId: 'images_privacy_change',
    summary: 'Update image privacy',
    description:
      'Changes the privacy settings of an image.\n\n' +
      'Path Parameters:\n' +
      '- id: UUID of the image\n\n' +
      'Request Body:\n' +
      '- privacy: New privacy level (public, private, organization)\n\n' +
      'Note: Only works for images in ready status.',
  })
  @ApiParam({
    name: 'id',
    description: 'The unique identifier of the image to update',
    type: 'string',
    format: 'uuid',
  })
  @ApiBody({ type: ImagePrivacyRequest })
  @ApiNoContentResponse({
    description: 'The image privacy was successfully updated.',
  })
  @ApiResponse({
    status: 400,
    description: `
      Bad Request. Possible reasons:
      - Image is not in READY state
      - Image contains NSFW content
      - Image uses private models
      - Invalid privacy value
    `,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found. The requested image could not be found.',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. An unexpected error occurred.',
  })
  @HttpCode(204)
  async imageCompletionPrivacy(
    @Body() requestBody: ImagePrivacyRequest,
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ) {
    const entity = await this.provider.getBy({
      user: { id: request.user.id },
      id: id,
    });
    await this.requestManager.updatePrivacy(entity, requestBody);
  }

  @ApiNoContentResponse()
  @Put(':id/prompt-privacy')
  @ApiOperation({
    operationId: 'images_prompt_privacy_change',
    summary: 'Update image prompt privacy',
    description:
      'Changes the prompt privacy settings of an image.\n\n' +
      'Path Parameters:\n' +
      '- id: UUID of the image\n\n' +
      'Request Body:\n' +
      '- hidden: Boolean indicating if the prompt should be hidden\n\n' +
      'Note: Only works for images owned by the user.',
  })
  @ApiParam({
    name: 'id',
    description: 'The unique identifier of the image to update prompt privacy',
    type: 'string',
    format: 'uuid',
  })
  @ApiBody({ type: ImagePromptPrivacyRequest })
  @ApiNoContentResponse({
    description: 'The image prompt privacy was successfully updated.',
  })
  @ApiResponse({
    status: 400,
    description: `
      Bad Request. Possible reasons:
      - Invalid prompt privacy value
    `,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found. The requested image could not be found.',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. An unexpected error occurred.',
  })
  @HttpCode(204)
  async imageCompletionPromptHidden(
    @Body() requestBody: ImagePromptPrivacyRequest,
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ) {
    const entity = await this.provider.getBy({
      user: { id: request.user.id },
      id: id,
    });

    await this.requestManager.updatePromptPrivacy(entity, requestBody);
  }
}
