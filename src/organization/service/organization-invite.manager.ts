import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { OrganizationInviteEntity } from '../entity/organization-invite.entity';
import { Repository } from 'typeorm';
import { OrganizationEntity } from '../entity/organization.entity';
import { OrganizationUserEntity } from '../entity/organization-user.entity';
import { StatusEnum } from '../enum/organization-user.enum';
import { OrganizationUserManager } from './organization-user.manager';
import { OrganizationManager } from './manager';

@Injectable()
export class OrganizationInviteManager {
  constructor(
    @InjectRepository(OrganizationInviteEntity)
    private repository: Repository<OrganizationInviteEntity>,
    private organizationUserManager: OrganizationUserManager,
    private organizationManager: OrganizationManager,
  ) {}

  async create(
    organizationInvite: OrganizationInviteEntity,
    organization: OrganizationEntity,
  ): Promise<void> {
    organization.members++;
    await this.organizationManager.update(organization);

    await this.repository.save(organizationInvite);
  }

  async update(
    organizationInvite: OrganizationInviteEntity,
  ): Promise<OrganizationInviteEntity> {
    return await this.repository.save(organizationInvite);
  }

  async accept(
    organizationUser: OrganizationUserEntity,
    organizationInvite: OrganizationInviteEntity,
  ): Promise<void> {
    organizationInvite.status = StatusEnum.ACCEPTED;
    organizationInvite.acceptedAt = new Date();

    await this.update(organizationInvite);

    await this.organizationUserManager.create(organizationUser);
  }

  async reject(
    organizationInvite: OrganizationInviteEntity,
    organization: OrganizationEntity,
  ): Promise<void> {
    organizationInvite.status = StatusEnum.REJECTED;

    organization.members--;
    await this.organizationManager.update(organization);

    await this.update(organizationInvite);
  }

  async delete(
    organization: OrganizationEntity,
    organizationInvite: OrganizationInviteEntity,
  ): Promise<void> {
    await this.repository.softDelete(organizationInvite.id);

    organization.members--;

    await this.organizationManager.update(organization);
  }
}
