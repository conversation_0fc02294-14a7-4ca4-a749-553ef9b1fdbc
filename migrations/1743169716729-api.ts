import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1743169716729 implements MigrationInterface {
  name = 'Api1743169716729';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "image_completion"
            ADD "hide_from_user_profile" boolean NOT NULL DEFAULT false
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "image_completion" DROP COLUMN "hide_from_user_profile"
        `);
  }
}
