import { ApiProperty } from '@nestjs/swagger';
import { PublicUserDto } from 'src/user/dto/public.user.dto';
import { StatusEnum } from '../enum/boardUser-status.enum';
import { PublicBoardDto } from './public.board.dto';

export class BoardUserDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  board: PublicBoardDto;

  @ApiProperty()
  isAdmin: boolean;

  @ApiProperty()
  status: StatusEnum;

  @ApiProperty()
  invitedBy?: PublicUserDto;

  @ApiProperty()
  invitedAt?: Date;

  @ApiProperty()
  approvedBy?: string;

  @ApiProperty()
  approvedAt?: Date;

  @ApiProperty()
  user: PublicUserDto;

  @ApiProperty()
  createdAt?: Date;
}
