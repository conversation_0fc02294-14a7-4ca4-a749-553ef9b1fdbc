// import { faker } from '@faker-js/faker';
import {
  Controller,
  Get,
  Query,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiOkResponse,
  ApiQuery,
  ApiTags,
  ApiOperation,
  ApiBadRequestResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import { BaseFindResponseHeadersDto } from 'src/core/dto/base-find-response-headers.dto';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { InternalUserStatsDto } from '../dto/internal-user-stats.dto';
import { InternalUserStatsSearchRequest } from '../dto/internal-user-stats.search-request';
import { ModelProvider } from 'src/model/service/provider';
import { TopModelUserDto } from '../dto/top-model-users.dto';
import { TopModelUsersResponseMapper } from '../service/top-model-users.response-mapper';

@ApiTags('internal / stats / users')
@Controller('internal/stats/users')
export class UserStatsController {
  constructor(
    private modelProvider: ModelProvider,
    private topModelUsersResponseMapper: TopModelUsersResponseMapper,
  ) {}

  @ApiOkResponse({
    type: InternalUserStatsDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
  })
  @ApiQuery({ type: InternalUserStatsSearchRequest })
  @Get()
  @UsePipes(new ValidationPipe())
  async find(
    @Query() query: InternalUserStatsSearchRequest,
    @Res() res: Response,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;

    // const entities = await this.provider.findBy(
    //   inputFilters,
    //   page,
    //   limit,
    //   sortBy,
    //   sortOrder,
    // );

    // const totalCount = await this.provider.countBy(inputFilters);

    // const page = 1;
    // const limit = 10;

    const totalCount = limit * 3;
    setPaginationHeaders(res, totalCount, page, limit);

    const dtos = this.generateRandomInternalUserStatsDtos(limit);

    res.send(dtos);

    // res.send(await this.responseMapper.mapMultipleInternal(entities));
  }

  private generateRandomInternalUserStatsDto(): InternalUserStatsDto {
    const dto = new InternalUserStatsDto();
    // dto.userId = faker.datatype.uuid();
    // dto.username = faker.internet.userName();
    // dto.images = faker.datatype.number({ min: 0, max: 100 });
    // dto.modelsUsed = faker.datatype.number({ min: 0, max: 100 });
    // dto.modelsCreated = faker.datatype.number({ min: 0, max: 50 });
    // dto.modelsPrivate = faker.datatype.number({ min: 0, max: 50 });
    // dto.modelsPublic = faker.datatype.number({ min: 0, max: 50 });
    // dto.modelsAverageImages = faker.datatype.float({ min: 0, max: 10 });
    return dto;
  }

  private generateRandomInternalUserStatsDtos(
    count: number,
  ): InternalUserStatsDto[] {
    const dtos: InternalUserStatsDto[] = [];
    for (let i = 0; i < count; i++) {
      dtos.push(this.generateRandomInternalUserStatsDto());
    }
    return dtos;
  }

  @ApiOperation({
    operationId: 'internal_top_model_users',
    summary: 'List top model users',
    description:
      'Retrieves a paginated list of users with the most popular models.\n\n' +
      'Optional Query Parameters:\n' +
      '- limit: Maximum number of users to return (default: 10)\n',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`limit\`: Must be a positive integer between 1 and 100.
    `,
  })
  @ApiOkResponse({
    type: TopModelUserDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated list of top model users.',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Maximum number of users to return.',
  })
  @Get('top-model')
  @UsePipes(new ValidationPipe())
  async findTopModelUsers(
    @Query('limit') limit = 10,
    @Res() res: Response,
  ): Promise<void> {
    const topUsers = await this.modelProvider.findTopModelUsers(limit);
    const totalCount = topUsers.length;

    setPaginationHeaders(res, totalCount, 1, limit);

    const dtos = this.topModelUsersResponseMapper.mapMultiple(topUsers);
    res.send(dtos);
  }
}
