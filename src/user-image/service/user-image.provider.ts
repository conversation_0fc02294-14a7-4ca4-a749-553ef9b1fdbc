import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindOneOptions, Repository } from 'typeorm';
import { Logger } from 'nestjs-pino';
import { AbstractProvider } from '../../core/service/abstract.provider';
import { UserImageEntity } from '../entity/user-image.entity';

@Injectable()
export class UserImageProvider extends AbstractProvider<UserImageEntity> {
  constructor(
    @InjectRepository(UserImageEntity)
    protected repository: Repository<UserImageEntity>,
    protected logger: Logger,
  ) {
    super(repository, logger);
  }

  prepareFindOneOptions(criteria: any): FindOneOptions<UserImageEntity> {
    return {
      where: criteria,
      relations: {
        user: true,
      },
    };
  }
}
