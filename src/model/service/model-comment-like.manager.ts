import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ModelCommentLikeEntity } from '../entity/model-comment-like.entity';
import { Repository } from 'typeorm';
import { ModelCommentManager } from './model-comment.manager';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Notifier } from 'src/notification/service/notifier';
import { UserResponseMapper } from 'src/user/service/response-mapper';
import { UserProvider } from 'src/user/service/provider';
import { ModelResponseMapper } from './response-mapper';
import { ModelCommentEntity } from '../entity/model-comment.entity';
import { ModelCommentLikedNotification } from '../notification/model-comment-like.notification';
import { ModelCommentLikedEvent } from '../event/model-comment-like.event';
import { ModelProvider } from './provider';
import { ModelCommentProvider } from './model-comment.provider';

@Injectable()
export class ModelCommentLikeManager {
  constructor(
    @InjectRepository(ModelCommentLikeEntity)
    private repository: Repository<ModelCommentLikeEntity>,
    private modelCommentManager: ModelCommentManager,
    private eventEmitter: EventEmitter2,
    private notifier: Notifier,
    private userResponseMapper: UserResponseMapper,
    private modelResponseMapper: ModelResponseMapper,
    private userProvider: UserProvider,
    private modelProvider: ModelProvider,
    private modelCommentProvider: ModelCommentProvider,
  ) {}

  async like(modelComment: ModelCommentEntity, userId: string): Promise<void> {
    const modelCommentLike = new ModelCommentLikeEntity();
    modelCommentLike.modelComment = modelComment;
    modelCommentLike.userId = userId;
    await this.repository.save(modelCommentLike);

    modelComment.likes++;

    await this.modelCommentManager.save(modelComment);

    const modelEntity = await this.modelProvider.getBy({
      id: modelComment.modelId,
    });

    const user = await this.userProvider.get(userId);

    if (modelComment.userId != modelCommentLike.userId) {
      await this.notifier.dispatch(
        new ModelCommentLikedNotification(modelComment.userId, {
          id: modelEntity.id,
          name: modelEntity.name,
          thumbnail: this.modelResponseMapper.generateThumbnailUrl(modelEntity),
          userId: modelComment.userId,
          likedById: modelCommentLike.userId,
          likedByUsername: user.username,
          likedByThumbnail: this.userResponseMapper.mapProfilePicture(user),
          likedAt: modelCommentLike.createdAt,
        }),
      );
    }

    await this.eventEmitter.emit(
      'model.comment_liked',
      new ModelCommentLikedEvent({
        id: modelCommentLike.id,
        modelCommentId: modelComment.id,
        userId: userId,
        ownerId: modelComment.id,
      }),
    );
  }

  async unlike(modelCommentLike: ModelCommentLikeEntity): Promise<void> {
    const modelComment = await this.modelCommentProvider.get(
      modelCommentLike.modelCommentId,
    );

    await this.repository.softRemove(modelCommentLike);

    modelComment.likes--;

    await this.modelCommentManager.save(modelComment);
  }
}
