import {
  <PERSON>,
  Get,
  Param,
  <PERSON>rseU<PERSON><PERSON>ipe,
  Query,
  Request,
  Res,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiBearerAuth,
  ApiResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import { JwtIntegrationAuthGuard } from 'src/auth/service/jwt-integration-auth.guard';
import { BaseFindResponseHeadersDto } from 'src/core/dto/base-find-response-headers.dto';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { ImageCompletionProvider } from 'src/image-completion/service/provider';
import { ImageDto } from '../dto/image.dto';
import { ImageSearchRequest } from '../dto/image.search-request';
import { ImageRequestManager } from '../service/request-manager';
import { ImageResponseMapper } from '../service/response-mapper';

@ApiTags('images')
@Controller('images')
@ApiBearerAuth()
export class ReadController {
  constructor(
    private provider: ImageCompletionProvider,
    private responseMapper: ImageResponseMapper,
    private requestManager: ImageRequestManager,
  ) {}

  @Get()
  @UsePipes(new ValidationPipe())
  @ApiOperation({
    operationId: 'images_find',
    summary: 'Find and filter images',
    description:
      'Retrieves a paginated list of images.\n\n' +
      'Query Parameters:\n' +
      '- page: Page number (default: 1)\n' +
      '- limit: Items per page (1-50, default: 10)\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: "ASC" or "DESC"\n' +
      '- userId: Filter by user ID\n' +
      '- username: Filter by username\n' +
      '- privacy: Filter by privacy level\n' +
      '- systemVersion: Filter by system version\n' +
      '- isHot: Filter by hot images\n' +
      '- includeNsfw: Include NSFW content (default: false)\n' +
      '- onlyFollowing: Show only images from followed users\n' +
      '- modelIds: Filter by model IDs\n' +
      '- status: Filter by status (see Status Values)\n\n' +
      'Status Values:\n' +
      '- new: Initial state, waiting to be processed\n' +
      '- generating: Currently being generated\n' +
      '- ready: Generation completed successfully\n' +
      '- hidden: Image has been hidden from public view\n' +
      '- failed: Generation failed\n' +
      '- interrupted: Generation was interrupted\n' +
      '- not_allowed: Generation was rejected\n\n' +
      'Response Headers:\n' +
      '- X-Total-Count: Total count of items\n' +
      '- X-Current-Page: Current page of pagination\n' +
      '- X-Per-Page: Results per page\n' +
      '- X-Total-Pages: Total count of pages',
  })
  @ApiOkResponse({
    type: ImageDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Successfully retrieved the list of images',
  })
  @ApiQuery({
    type: ImageSearchRequest,
    description: 'Search parameters for filtering and pagination',
  })
  @ApiResponse({
    status: 400,
    description: `
      Bad Request. Possible reasons:
      - \`page\`: Must be a positive integer.
      - \`limit\`: Must be a positive integer between 1 and 100.
      - \`sortBy\`: Must be one of the allowed sort fields.
      - \`sortOrder\`: Must be either 'ASC' or 'DESC'.
      - Invalid filter parameters provided.
      - Malformed query parameters.
    `,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. Authentication token is missing or invalid',
  })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden. User does not have permission to access these images',
  })
  @ApiResponse({
    status: 500,
    description:
      'Internal Server Error. An unexpected error occurred while retrieving images',
  })
  async find(
    @Request() request,
    @Query() query: ImageSearchRequest,
    @Res() res: Response,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;
    const filters = await this.requestManager.sanitizeSearchFilters(
      inputFilters,
      request.user,
    );

    const entities = await this.provider.findBy(
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    const totalCount = await this.provider.countBy(filters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultiple(entities, request.user?.id));
  }

  @Get(':id')
  @UseGuards(JwtIntegrationAuthGuard)
  @ApiOperation({
    operationId: 'images_get',
    summary: 'Get detailed image information',
    description:
      'Retrieves information about a specific image by ID.\n\n' +
      'Path Parameters:\n' +
      '- id: UUID of the image',
  })
  @ApiOkResponse({
    type: ImageDto,
    description: 'Successfully retrieved the image details',
  })
  @ApiParam({
    name: 'id',
    description: 'The unique identifier of the image',
    type: 'string',
    format: 'uuid',
  })
  @ApiResponse({
    status: 400,
    description: `
      Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID format.
      - Invalid image identifier provided.
    `,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. Authentication token is missing or invalid',
  })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden. User does not have permission to access this image',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found. The requested image does not exist',
  })
  @ApiResponse({
    status: 500,
    description:
      'Internal Server Error. An unexpected error occurred while retrieving the image',
  })
  async get(@Param('id', new ParseUUIDPipe()) id: string): Promise<ImageDto> {
    const imageCompletion = await this.provider.get(id);

    return await this.responseMapper.map(imageCompletion);
  }
}
