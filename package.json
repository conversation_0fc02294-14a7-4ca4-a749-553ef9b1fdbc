{"name": "api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "postbuild": "cp -r templates/ dist/templates/; cp -R public dist/public/; cp -R resources dist/resources/", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node ./node_modules/typeorm/cli", "typeorm:migration:run": "npm run typeorm migration:run -- -d ./ormconfig-migrations.ts", "typeorm:migration:generate": "npm run -s typeorm -- -d ./ormconfig-migrations.ts migration:generate -p ./migrations/api", "typeorm:migration:create": "npm run typeorm -- migration:create ./migrations/$npm_config_name", "typeorm:migration:revert": "npm run typeorm -- -d ./ormconfig-migrations.ts migration:revert", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org letzai --project dev-private-api ./dist && sentry-cli sourcemaps upload --org letzai --project dev-private-api ./dist"}, "dependencies": {"@aws-sdk/client-s3": "^3.385.0", "@aws-sdk/client-ses": "^3.395.0", "@aws-sdk/client-sqs": "^3.386.0", "@aws-sdk/s3-request-presigner": "^3.385.0", "@faker-js/faker": "^8.1.0", "@nestjs/common": "^9.0.0", "@nestjs/config": "^2.3.4", "@nestjs/core": "^9.0.0", "@nestjs/cqrs": "^10.2.7", "@nestjs/event-emitter": "^2.0.3", "@nestjs/jwt": "^10.1.0", "@nestjs/passport": "^9.0.0", "@nestjs/platform-express": "^9.0.0", "@nestjs/serve-static": "^4.0.2", "@nestjs/swagger": "^6.0.5", "@nestjs/typeorm": "^9.0.1", "@sentry/cli": "^2.36.1", "@sentry/nestjs": "^8.30.0", "@sentry/profiling-node": "^8.30.0", "api": "^5.0.1", "axios": "^1.4.0", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "date-fns": "^4.1.0", "express-basic-auth": "^1.2.1", "handlebars": "^4.7.8", "joi": "^17.9.2", "jwks-rsa": "^3.0.1", "lru-cache": "^11.0.2", "luxon": "^3.4.4", "nestjs-pino": "^3.3.0", "nodemailer": "^6.9.4", "openai": "^4.91.1", "passport": "^0.6.0", "passport-jwt": "^4.0.0", "passport-local": "^1.0.0", "passport-saml": "^3.2.4", "pg": "^8.7.3", "pino-http": "^8.4.0", "pino-pretty": "^10.2.0", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "saml2-js": "^4.0.2", "sharp": "^0.34.1", "stripe": "^17.0.0", "typeorm": "^0.3.7", "typeorm-naming-strategies": "^4.1.0", "xml2js": "^0.6.2"}, "devDependencies": {"@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@types/express": "^4.17.13", "@types/jest": "^28.1.4", "@types/node": "^16.0.0", "@types/passport-jwt": "^3.0.6", "@types/passport-local": "^1.0.34", "@types/sharp": "^0.31.1", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^28.1.2", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "^28.0.8", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "^4.0.0", "typescript": "^4.9.5"}}