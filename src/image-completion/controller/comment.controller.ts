import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Param,
  ParseUUIDPipe,
  Post,
  Query,
  Request,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiQuery,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import { BaseFindResponseHeadersDto } from 'src/core/dto/base-find-response-headers.dto';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { ImageCompletionCommentDto } from '../dto/image-completion-comment.dto';
import { ImageCompletionCommentSearchRequest } from '../dto/image-completion-comment.search-request';
import { ImageCompletionCommentProvider } from '../service/comment.provider';
import { ImageCompletionCommentRequestManager } from '../service/comment.request-manager';
import { ImageCompletionCommentResponseMapper } from '../service/comment.response-mapper';
import { ImageCompletionProvider } from '../service/provider';
import { ImageCompletionCommentRequest } from '../dto/image-completion-comment.request';

@ApiTags('image_completions / comments')
@Controller('image_completions')
export class CommentController {
  constructor(
    private imageCompletionProvider: ImageCompletionProvider,
    private imageCompletionCommentProvider: ImageCompletionCommentProvider,
    private requestManager: ImageCompletionCommentRequestManager,
    private responseMapper: ImageCompletionCommentResponseMapper,
  ) {}

  @Get(':id/comments')
  @ApiOperation({
    operationId: 'image_completion_comment_list',
    summary: 'List comments for an image completion',
    description:
      'Retrieves a paginated list of comments for the specified image completion.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the image completion\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of comments per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- userId: UUID of the user\n' +
      '- username: Username of the user\n' +
      '- imageCompletionId: UUID of the image completion\n',
  })
  @ApiOkResponse({
    type: ImageCompletionCommentDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated list of comments for the image completion.',
  })
  @ApiQuery({
    type: ImageCompletionCommentSearchRequest,
    description: 'Query parameters for searching and paginating comments.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the image completion to retrieve comments for.',
    type: String,
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - \`userId\`: Must be a valid UUID.
      - \`username\`: Must be a valid username.
      - \`imageCompletionId\`: Must be a valid UUID.
      - Invalid or unavailable parameters.
      - Failed to analyze request.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description:
      'Forbidden. User does not have access to this image completion.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @UsePipes(new ValidationPipe())
  async find(
    @Query() query: ImageCompletionCommentSearchRequest,
    @Res() res: Response,
    @Param('id', new ParseUUIDPipe()) id: string,
    @Request() request,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;
    const filters = this.validateSearchFilters(inputFilters, id);

    const entities = await this.imageCompletionCommentProvider.findBy(
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    const totalCount = await this.imageCompletionCommentProvider.countBy(
      filters,
    );

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultiple(entities, request.user?.id));
  }

  @Post(':id/comments')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'image_completion_comment_create',
    summary: 'Add a comment to an image completion',
    description:
      'Adds a new comment to the specified image completion.\n\n' +
      'Required Query Parameters:\n' +
      '- id: UUID of the image completion\n' +
      'Required Body Parameters:\n' +
      '- comment: Content of the comment\n',
  })
  @ApiBody({
    type: ImageCompletionCommentRequest,
    description: 'Comment creation parameters.',
  })
  @ApiNoContentResponse({ description: 'Comment added successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - \`comment\`: Must be a non-empty string.
      - Invalid or unavailable parameters.
      - Failed to analyze request body.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User does not have permission to comment.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the image completion to comment on.',
    type: String,
  })
  async comment(
    @Body() requestBody: ImageCompletionCommentRequest,
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ) {
    const entity = await this.imageCompletionProvider.getBy({
      id: id,
    });

    await this.requestManager.comment(entity, request.user.id, requestBody);
  }

  @Delete(':imageCompletionId/comments/:commentId')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'image_completion_comment_delete',
    summary: 'Delete a comment from an image completion',
    description:
      'Deletes the specified comment from the image completion if the user is the author.\n\n' +
      'Required Parameters:\n' +
      '- imageCompletionId: UUID of the image completion\n' +
      '- commentId: UUID of the comment to delete\n',
  })
  @ApiNoContentResponse({ description: 'Comment deleted successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`imageCompletionId\`: Must be a valid UUID.
      - \`commentId\`: Must be a valid UUID.
      - Invalid or unavailable parameters.
      - Failed to analyze request.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description:
      'Forbidden. User does not have permission to delete this comment.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiParam({
    name: 'imageCompletionId',
    description: 'UUID of the image completion.',
    type: String,
  })
  @ApiParam({
    name: 'commentId',
    description: 'UUID of the comment to delete.',
    type: String,
  })
  async delete(
    @Request() request,
    @Param('imageCompletionId', new ParseUUIDPipe()) imageCompletionId: string,
    @Param('commentId', new ParseUUIDPipe()) commentId: string,
  ) {
    const entity = await this.imageCompletionCommentProvider.getBy({
      id: commentId,
      imageCompletionId: imageCompletionId,
      userId: request.user.id,
    });

    await this.requestManager.delete(entity);
  }

  validateSearchFilters(
    filters: ImageCompletionCommentSearchRequest,
    imageCompletionId: string,
  ): any {
    return { ...filters, imageCompletionId: imageCompletionId };
  }
}
