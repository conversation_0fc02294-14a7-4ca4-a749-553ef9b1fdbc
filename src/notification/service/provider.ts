import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { AbstractProvider } from 'src/core/service/abstract.provider';
import { FindManyOptions, IsNull, Repository } from 'typeorm';
import { NotificationEntity } from '../entity/notification.entity';

@Injectable()
export class NotificationProvider extends AbstractProvider<NotificationEntity> {
  constructor(
    @InjectRepository(NotificationEntity)
    repository: Repository<NotificationEntity>,
    logger: Logger,
  ) {
    super(repository, logger);
  }

  prepareFindManyOptions(
    criteria: any,
    page: number,
    limit: number,
    sortBy?: string,
    sortOrder = 'ASC',
  ): FindManyOptions<NotificationEntity> {
    if (criteria.unreadOnly) {
      criteria.readAt = IsNull();
    }

    delete criteria.unreadOnly;

    return super.prepareFindManyOptions(
      criteria,
      page,
      limit,
      sortBy,
      sortOrder,
    );
  }
}
