import { Controller, Get, Query, Request, Res } from '@nestjs/common';
import { NotificationProvider } from '../service/provider';
import { NotificationResponseMapper } from '../service/response-mapper';
import { NotificationDto } from '../dto/notification.dto';
import {
  ApiOkResponse,
  ApiQuery,
  ApiTags,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiBadRequestResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import { NotificationSearchRequest } from '../dto/notification.search-request';
import { setPaginationHeaders } from 'src/core/utils/pagination';

@ApiTags('notifications')
@Controller('users/current/notifications')
export class ReadController {
  constructor(
    private provider: NotificationProvider,
    private responseMapper: NotificationResponseMapper,
  ) {}

  @ApiOperation({
    operationId: 'notifications_list',
    summary: 'List notifications',
    description:
      'Retrieves a paginated list of notifications for the current user.\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of notifications per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n',
  })
  @ApiOkResponse({
    type: NotificationDto,
    isArray: true,
    description: 'Paginated list of notifications.',
  })
  @ApiQuery({
    type: NotificationSearchRequest,
    description: 'Query parameters for searching and paginating notifications.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - Filter parameters must be valid values.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Get()
  async find(
    @Request() request,
    @Query() query: NotificationSearchRequest,
    @Res() res: Response,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;
    const filters = this.validateSearchFilters(inputFilters, request.user.id);

    const entities = await this.provider.findBy(
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    const totalCount = await this.provider.countBy(filters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(this.responseMapper.mapMultiple(entities));
  }

  validateSearchFilters(
    filters: NotificationSearchRequest,
    currentUserId: string,
  ): any {
    return { ...filters, user: { id: currentUserId } };
  }
}
