import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsInt, IsNumber, IsOptional } from 'class-validator';
import { StatusEnum } from '../entity/image-edit.entity';

export class ImageEditInternalRequest {
  @IsOptional()
  @IsEnum(StatusEnum, {
    message: 'Status must be a valid value',
  })
  @ApiProperty({ enum: StatusEnum })
  status?: StatusEnum;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  generationSeconds?: number;

  @ApiProperty()
  @IsInt()
  @IsOptional()
  width?: number;

  @ApiProperty()
  @IsInt()
  @IsOptional()
  height?: number;
}
