import { ApiProperty } from '@nestjs/swagger';

export class ChatRateLimitDto {
  @ApiProperty({
    description: 'Number of messages sent by the user in the last hour',
  })
  messageCount: number;

  @ApiProperty({
    description: 'Maximum number of messages allowed per hour',
  })
  limit: number;

  @ApiProperty({
    description: 'Number of messages remaining before hitting the limit',
  })
  remaining: number;
}
