import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1730072465103 implements MigrationInterface {
  name = 'Api1730072465103';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "board_image_completion"
            ADD "deleted_at" TIMESTAMP
        `);
    await queryRunner.query(`
            ALTER TABLE "board_image_completion"
            ADD "created_at" TIMESTAMP NOT NULL DEFAULT now()
        `);
    await queryRunner.query(`
            ALTER TABLE "board_image_completion"
            ADD "updated_at" TIMESTAMP NOT NULL DEFAULT now()
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "board_image_completion" DROP COLUMN "updated_at"
        `);
    await queryRunner.query(`
            ALTER TABLE "board_image_completion" DROP COLUMN "created_at"
        `);
    await queryRunner.query(`
            ALTER TABLE "board_image_completion" DROP COLUMN "deleted_at"
        `);
  }
}
