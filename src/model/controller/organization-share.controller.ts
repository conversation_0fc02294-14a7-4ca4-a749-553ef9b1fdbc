import {
  Controller,
  Delete,
  Get,
  HttpCode,
  Param,
  ParseUUI<PERSON>ipe,
  Put,
  Request,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/auth/service/jwt-auth.guard';
import {
  ModelOrganizationDto,
  ModelOrganizationListDto,
} from '../dto/model-organization.dto';
// Removed ModelOrganizationShareRequest since we now use path parameters
// Removed ModelOrganizationUnshareRequest since it was unused
import { ModelOrganizationManager } from '../service/model-organization.manager';
import { ModelOrganizationResponseMapper } from '../service/model-organization.response-mapper';

@ApiTags('models')
@Controller('models')
export class OrganizationShareController {
  constructor(
    private modelOrganizationManager: ModelOrganizationManager,
    private responseMapper: ModelOrganizationResponseMapper,
  ) {}

  @ApiOperation({
    operationId: 'models_share_with_organization',
    summary: 'Share a model with an organization',
    description:
      'Shares a private model with an organization, allowing all organization members to access it.\n\n' +
      'Requirements:\n' +
      '- User must be the model owner\n' +
      '- Model must be private (public models cannot be shared)\n' +
      '- Model cannot already be shared with the organization',
  })
  @ApiParam({
    name: 'modelId',
    description: 'The unique identifier of the model to share',
    type: 'string',
    format: 'uuid',
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The unique identifier of the organization to share with',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponse({
    type: ModelOrganizationDto,
    description: 'Model successfully shared with organization.',
  })
  @ApiResponse({
    status: 400,
    description: `
      Bad Request. Possible reasons:
      - Model is not private
      - Model is already shared with this organization
      - Invalid organization ID
    `,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden. User is not the model owner.',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found. The model or organization could not be found.',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. An unexpected error occurred.',
  })
  @ApiBearerAuth()
  @Put(':modelId/organizations/:organizationId')
  @UseGuards(JwtAuthGuard)
  async shareWithOrganization(
    @Param('modelId', new ParseUUIDPipe()) modelId: string,
    @Param('organizationId', new ParseUUIDPipe()) organizationId: string,
    @Request() req,
  ): Promise<ModelOrganizationDto> {
    const share = await this.modelOrganizationManager.shareModelWithOrganization(
        modelId,
        organizationId,
        req.user,
      );

    return await this.responseMapper.map(share);
  }

  @ApiOperation({
    operationId: 'models_unshare_from_organization',
    summary: 'Unshare a model from an organization',
    description:
      'Removes organization access to a model.\n\n' +
      'Requirements:\n' +
      '- User must be the model owner\n' +
      '- Model must currently be shared with the organization',
  })
  @ApiParam({
    name: 'modelId',
    description: 'The unique identifier of the model to unshare',
    type: 'string',
    format: 'uuid',
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The unique identifier of the organization to unshare from',
    type: 'string',
    format: 'uuid',
  })
  @ApiNoContentResponse({
    description: 'Model successfully unshared from organization.',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request. Invalid model or organization ID.',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden. User is not the model owner.',
  })
  @ApiResponse({
    status: 404,
    description: `
      Not Found. Possible reasons:
      - Model could not be found
      - Organization could not be found
      - Model is not shared with this organization
    `,
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. An unexpected error occurred.',
  })
  @ApiBearerAuth()
  @Delete(':modelId/organizations/:organizationId')
  @UseGuards(JwtAuthGuard)
  @HttpCode(204)
  async unshareFromOrganization(
    @Param('modelId', new ParseUUIDPipe()) modelId: string,
    @Param('organizationId', new ParseUUIDPipe()) organizationId: string,
    @Request() req,
  ): Promise<void> {
    await this.modelOrganizationManager.unshareModelFromOrganization(
      modelId,
      organizationId,
      req.user,
    );
  }

  @ApiOperation({
    operationId: 'models_list_organization_shares',
    summary: 'List organizations a model is shared with',
    description:
      'Retrieves a list of organizations that have access to the specified model.\n\n' +
      'Requirements:\n' +
      '- User must be the model owner',
  })
  @ApiParam({
    name: 'modelId',
    description: 'The unique identifier of the model',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponse({
    type: ModelOrganizationListDto,
    description: 'List of organizations that have access to the model.',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden. User is not the model owner.',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found. The model could not be found.',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. An unexpected error occurred.',
  })
  @ApiBearerAuth()
  @Get(':modelId/organizations')
  @UseGuards(JwtAuthGuard)
  async listOrganizationShares(
    @Param('modelId', new ParseUUIDPipe()) modelId: string,
    @Request() req,
  ): Promise<ModelOrganizationListDto> {
    const shares = await this.modelOrganizationManager.getOrganizationsForModel(
      modelId,
      req.user,
    );

    return await this.responseMapper.mapToListDto(modelId, shares);
  }
}
