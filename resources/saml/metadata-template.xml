<?xml version="1.0" encoding="UTF-8"?>
<EntityDescriptor entityID="{{entityID}}"
    xmlns="urn:oasis:names:tc:SAML:2.0:metadata"
    validUntil="{{validUntil}}"
    cacheDuration="{{cacheDuration}}">

    <SPSSODescriptor protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol">
        <KeyDescriptor use="signing">
            <ds:KeyInfo xmlns:ds="http://www.w3.org/2000/09/xmldsig#">
                <ds:X509Data>
                    <ds:X509Certificate>{{certificate}}</ds:X509Certificate>
                </ds:X509Data>
            </ds:KeyInfo>
        </KeyDescriptor>

        <KeyDescriptor use="encryption">
            <ds:KeyInfo xmlns:ds="http://www.w3.org/2000/09/xmldsig#">
                <ds:X509Data>
                    <ds:X509Certificate>{{certificate}}</ds:X509Certificate>
                </ds:X509Data>
            </ds:KeyInfo>
        </KeyDescriptor>

        <AssertionConsumerService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
            Location="{{acsUrl}}"
            index="1"
            isDefault="true"/>
        
        <SingleLogoutService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
            Location="{{slsUrl}}"/>
    </SPSSODescriptor>

    <Organization>
        <OrganizationName xml:lang="en">{{organizationName}}</OrganizationName>
        <OrganizationDisplayName xml:lang="en">{{organizationDisplayName}}</OrganizationDisplayName>
        <OrganizationURL xml:lang="en">{{organizationURL}}</OrganizationURL>
    </Organization>

    <ContactPerson contactType="technical">
        <GivenName>{{contactGivenName}}</GivenName>
        <EmailAddress>{{contactEmail}}</EmailAddress>
    </ContactPerson>
</EntityDescriptor>
