import { IsBoolean, IsEnum, IsOptional } from 'class-validator';
import { BaseSearchRequest } from 'src/core/dto/base.search-request';
import { PrivacyEnum } from '../entity/image-completion.entity';
import { Transform } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export enum StatusEnum {
  NEW = 'new',
  READY = 'ready',
}

export class ImageCompletionRelatedSearchRequest extends BaseSearchRequest {
  @IsOptional()
  @IsEnum(PrivacyEnum)
  @Transform(({ value }) => value.toLowerCase())
  @ApiProperty({ enum: PrivacyEnum })
  privacy?: PrivacyEnum;

  @IsOptional()
  @IsEnum(StatusEnum)
  @Transform(({ value }) => value.toLowerCase())
  @ApiProperty({ enum: StatusEnum })
  status: StatusEnum = StatusEnum.READY;

  // eslint-disable-next-line @typescript-eslint/no-inferrable-types
  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  isNsfw: boolean = false;
}
