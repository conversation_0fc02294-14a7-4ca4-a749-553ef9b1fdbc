import {
  Controller,
  Delete,
  Param,
  Request,
  HttpCode,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiNoContentResponse,
  ApiBadRequestResponse,
  ApiParam,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { BookmarkRequestManager } from '../service/bookmark.request-manager';

@ApiTags('bookmark')
@Controller('bookmarks')
export class DeleteController {
  constructor(private readonly requestManager: BookmarkRequestManager) {}

  @Delete(':id')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'bookmark_delete',
    summary: 'Delete a bookmark',
    description:
      'Deletes the specified bookmark for the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- id: ID of the image/model to delete the bookmark for\n',
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the image/model to delete the Bookmark',
    type: String,
  })
  @ApiNoContentResponse({
    description: 'Bookmark deleted successfully.',
  })
  @ApiBadRequestResponse({
    description: `
      Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - Bookmark does not exist for this user.
      - Invalid or unavailable parameters.
      - Failed to analyze request body.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async delete(
    @Param('id', new ParseUUIDPipe()) referenceId: string,
    @Request() request,
  ): Promise<void> {
    const user = request.user.id;

    await this.requestManager.delete(referenceId, user);
  }
}
