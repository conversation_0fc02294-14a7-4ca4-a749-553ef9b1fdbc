import {
  Body,
  Controller,
  Param,
  ParseUUI<PERSON>ipe,
  Patch,
  Request,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiBearerAuth,
  ApiParam,
  ApiResponse,
} from '@nestjs/swagger';
import { ModelProvider } from 'src/model/service/provider';
import { ModelRequestManager } from '../service/request-manager';
import { ModelResponseMapper } from '../service/response-mapper';
import { ModelDto } from '../dto/model.dto';
import { ModelUpdateRequest } from '../dto/model.update-request';

@ApiTags('models')
@Controller('models')
export class UpdateController {
  constructor(
    private provider: ModelProvider,
    private requestManager: ModelRequestManager,
    private responseMapper: ModelResponseMapper,
  ) {}

  @ApiOperation({
    operationId: 'model_update',
    summary: 'Update model information',
    description:
      'Updates the information of an existing model.\n\n' +
      'Path Parameters:\n' +
      '- id: UUID of the model\n\n' +
      'Request Body:\n' +
      '- privacy: New privacy setting (public, private, licensed)\n' +
      '- type: New model type\n' +
      '- description: New model description\n' +
      '- website: New website URL\n' +
      '- settings: New model settings',
  })
  @ApiParam({
    name: 'id',
    description: 'Model ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiBody({ type: ModelUpdateRequest })
  @ApiOkResponse({
    type: ModelDto,
    description: 'The model was successfully updated',
  })
  @ApiBadRequestResponse({
    description: 'Invalid request parameters or model ID format',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. The user is not authenticated',
  })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden. User does not have permission to update this model',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found. The model to update could not be found',
  })
  @ApiBearerAuth()
  @Patch(':id')
  async update(
    @Body() requestBody: ModelUpdateRequest,
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<ModelDto> {
    const entity = await this.provider.getBy({
      user: { id: request.user.id },
      id: id,
    });

    await this.requestManager.update(entity, requestBody);

    return await this.responseMapper.map(entity);
  }
}
