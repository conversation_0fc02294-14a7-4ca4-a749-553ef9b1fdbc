import { Injectable } from '@nestjs/common';
import { UserEntity } from '../../user/entity/user.entity';
import { SubscriptionInternalRequest } from '../dto/subscription.internal-request';
import { SubscriptionRequest } from '../dto/subscription.request';
import { SubscriptionEntity } from '../entity/subscription.entity';
import { CreditPackageProvider } from './credit-package.provider';
import { SubscriptionManager } from './manager';

@Injectable()
export class SubscriptionRequestManager {
  constructor(
    private manager: SubscriptionManager,
    private creditPackageProvider: CreditPackageProvider,
  ) {}

  async create(
    request: SubscriptionRequest,
    user: UserEntity,
  ): Promise<SubscriptionEntity> {
    const entity = new SubscriptionEntity();

    const creditPackage = await this.creditPackageProvider.get(
      request.creditPackageId,
    );

    entity.user = user;
    entity.name = creditPackage.name;
    entity.price = creditPackage.price;
    entity.creditPackage = creditPackage;

    return await this.manager.create(entity);
  }

  async handleInternalActivation(
    subscription: SubscriptionEntity,
    requestBody: SubscriptionInternalRequest,
  ) {
    subscription.expiresAt = requestBody.expiresAt ?? subscription.expiresAt;
    subscription.externalReference =
      requestBody.externalReference ?? subscription.externalReference;

    await this.manager.activateSubscription(subscription);
  }

  async refreshPendingSubscriptions() {
    await this.manager.refreshPendingSubscriptions();
  }
}
