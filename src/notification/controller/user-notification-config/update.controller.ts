import { Controller, Param, Request, Put } from '@nestjs/common';
import { Body } from '@nestjs/common/decorators/http/route-params.decorator';
import { UserNotificationConfigRequestManager } from '../../service/user-notification-config.request-manager';
import { UserNotificationConfigResponseMapper } from '../../service/user-notification-config.response-mapper';
import { UserNotificationConfigRequest } from '../../dto/user-notification-config.request';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
  ApiResponse,
} from '@nestjs/swagger';
import { UserNotificationConfigDto } from '../../dto/user-notification-config.dto';
import { NotificationChannel } from 'src/notification/enum/notification-channel.enum';

@ApiTags('user')
@Controller('users/current/notification-config')
export class UpdateController {
  constructor(
    private requestManager: UserNotificationConfigRequestManager,
    private responseMapper: UserNotificationConfigResponseMapper,
  ) {}

  @ApiOperation({
    operationId: 'user_notification_configure_channel',
    summary: 'Configure notification channel',
    description:
      'Configures the notification channel for the current user.\n\n' +
      'Required Parameters:\n' +
      '- channel: Notification channel (e.g., email, sms, etc.)\n' +
      'Required Body Parameters:\n' +
      '- isEnabled: Whether the channel is enabled\n' +
      '- settings: Channel-specific settings\n',
  })
  @ApiParam({
    name: 'channel',
    description: 'The notification channel to configure',
    enum: NotificationChannel,
  })
  @ApiBody({
    type: UserNotificationConfigRequest,
    description: 'Notification configuration parameters.',
  })
  @ApiOkResponse({
    type: UserNotificationConfigDto,
    description: 'Notification channel configured successfully.',
  })
  @ApiNotFoundResponse({ description: 'Not Found. Channel not found.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`channel\`: Must be a valid notification channel.
      - \`isEnabled\`: Must be a valid boolean value.
      - \`settings\`: Must be a valid object.
  `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. An unexpected error occurred.',
  })
  @Put(':channel')
  async configure(
    @Body() requestBody: UserNotificationConfigRequest,
    @Request() request,
    @Param() params,
  ): Promise<UserNotificationConfigDto> {
    const userNotificationConfig = await this.requestManager.configure(
      request.user.id,
      params.channel,
      requestBody,
    );

    return this.responseMapper.map(userNotificationConfig);
  }
}
