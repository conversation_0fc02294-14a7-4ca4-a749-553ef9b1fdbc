import {
  Controller,
  Put,
  Param,
  Request,
  Get,
  Delete,
  HttpCode,
  Body,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiQuery,
  ApiTags,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiParam,
} from '@nestjs/swagger';
import { ModelProvider } from '../service/provider';
import { AssetManager } from '../service/asset.manager';
import { ModelImageRequest } from '../dto/model-image.request';
import { ModelManager } from '../service/manager';

@ApiTags('model')
@Controller('models/:id/images')
export class ImageController {
  constructor(
    private provider: ModelProvider,
    private assetManager: AssetManager,
    private modelManager: ModelManager,
  ) {}

  @Get()
  @ApiOperation({
    operationId: 'model_image_list',
    summary: 'List model images',
    description:
      'Retrieves a list of images for the specified model.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the model\n',
  })
  @ApiOkResponse({ type: [String], description: 'List of image URLs.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - Model does not exist.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description:
      'Forbidden. User does not have permission to view images for this model.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiParam({ name: 'id', description: 'UUID of the model.' })
  async getModelImages(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<string[]> {
    const entity = await this.provider.getBy({
      user: { id: request.user.id },
      id: id,
    });

    return await this.assetManager.generateSignedUrls(entity, 'images');
  }

  @Put()
  @ApiOperation({
    operationId: 'model_image_generate_upload_url',
    summary: 'Generate upload URLs for model images',
    description:
      'Generates upload URLs for images for the specified model.\n\n' +
      'Required Query Parameters:\n' +
      '- id: UUID of the model\n' +
      'Required Body Parameters:\n' +
      '- extension: File extension of the images\n' +
      '- numberOfImages: Number of images to generate upload URLs for\n',
  })
  @ApiOkResponse({ type: [String], description: 'List of upload URLs.' })
  @ApiBadRequestResponse({
    description: 'Bad Request. Invalid parameters or missing required fields.',
  })
  @ApiUnauthorizedResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - \`extension\`: Must be a valid file extension.
      - \`numberOfImages\`: Must be a positive integer.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiForbiddenResponse({
    description:
      'Forbidden. User does not have permission to upload images for this model.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiParam({ name: 'id', description: 'UUID of the model.' })
  @ApiBody({ type: ModelImageRequest, description: 'Image upload parameters.' })
  async generateUploadUrl(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
    @Body() requestBody: ModelImageRequest,
  ): Promise<string[]> {
    const user = request.user;
    const model = await this.provider.getBy({
      user: { id: user.id },
      id: id,
    });

    this.assetManager.generateStoragePath(model);

    await this.modelManager.save(model);

    const urls = await this.assetManager.generateImageUploadUrl(
      model,
      requestBody.numberOfImages,
      requestBody.extension,
    );

    return urls;
  }

  @Delete('/:image')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'model_image_delete',
    summary: 'Delete model image',
    description:
      'Deletes the specified image from the model.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the model\n' +
      '- image: Image file name to be deleted\n',
  })
  @ApiNoContentResponse({ description: 'Image deleted successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - \`image\`: Must be a valid image file name.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description:
      'Forbidden. User does not have permission to delete images for this model.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiParam({ name: 'id', description: 'UUID of the model.' })
  @ApiParam({ name: 'image', description: 'Image file name to be deleted.' })
  @ApiQuery({
    name: 'image',
    required: true,
    type: String,
    description: 'Image file name to be deleted',
  })
  async deleteImage(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
    @Param('image') image: string,
  ): Promise<void> {
    const user = request.user;
    const modelEntity = await this.provider.getBy({
      user: { id: user.id },
      id: id,
    });

    await this.assetManager.deleteAsset(modelEntity, 'images', image);
  }
}
