import { ApiProperty } from '@nestjs/swagger';

export class OrganizationSubscriptionDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  price: number;

  @ApiProperty()
  organizationId: string;

  @ApiProperty()
  creditPackageId: string;

  @ApiProperty()
  status: string;

  @ApiProperty({ required: false })
  paidAt?: Date;

  @ApiProperty({ required: false })
  stripeCheckoutUrl?: string;

  @ApiProperty({ required: false })
  stripeCheckoutSessionId?: string;

  @ApiProperty()
  expiresAt: Date;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}
