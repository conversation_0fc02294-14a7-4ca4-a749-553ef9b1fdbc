import { Controller, Post, Request } from '@nestjs/common';
import { Body } from '@nestjs/common/decorators/http/route-params.decorator';
import { UserExternalProfileDto } from '../dto/user-external-profile.dto';
import { UserExternalProfileRequest } from '../request/user-external-profile.request';
import { UserExternalProfileRequestManager } from '../service/request-manager';
import { UserExternalProfileResponseMapper } from '../service/response-mapper';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { UserProvider } from '../../user/service/provider';

@ApiTags('user')
@Controller('users/current/external-profiles')
export class CreateController {
  constructor(
    private requestManager: UserExternalProfileRequestManager,
    private responseMapper: UserExternalProfileResponseMapper,
    private userProvider: UserProvider,
  ) {}

  @Post()
  @ApiOperation({
    operationId: 'user_external_profile_create',
    summary: 'Create external profile for current user',
    description:
      'Creates a new external profile for the current authenticated user.\n\n' +
      'Required Body Parameters:\n' +
      '- externalId: External profile ID\n' +
      '- platform: External provider name\n' +
      '- username: External profile username\n' +
      '- profileData: External profile data\n',
  })
  @ApiBody({ type: UserExternalProfileRequest })
  @ApiOkResponse({
    type: UserExternalProfileDto,
    description: 'External profile created successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`externalId\`: Must be a valid string.
      - \`platform\`: Must be a valid string.
      - \`username\`: Must be a valid string.
      - \`profileData\`: Must be a valid object.
      - Invalid or unavailable parameters.
  `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async createCurrentExternal(
    @Body() requestBody: UserExternalProfileRequest,
    @Request() request,
  ): Promise<UserExternalProfileDto> {
    const user = await this.userProvider.get(request.user.id);

    return this.requestManager
      .create(requestBody, user)
      .then((userExternalProfile) =>
        this.responseMapper.map(userExternalProfile),
      );
  }
}
