import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1699118272837 implements MigrationInterface {
    name = 'Api1699118272837'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "subscription"
            ADD "paid_at" TIMESTAMP
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription"
            ALTER COLUMN "expires_at" DROP NOT NULL
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "subscription"
            ALTER COLUMN "expires_at"
            SET NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription" DROP COLUMN "paid_at"
        `);
    }

}
