import { Injectable } from '@nestjs/common';
import { ImageCompletionDto } from 'src/image-completion/dto/image-completion.dto';
import { ImageCompletionResponseMapper } from 'src/image-completion/service/response-mapper';
import { PublicBoardDto } from '../dto/public.board.dto';
import { BoardImageCompletionEntity } from '../entity/board-image-completion.entity';
import { BoardResponseMapper } from './response-mapper';

@Injectable()
export class BoardImageCompletionResponseMapper {
  constructor(
    private boardResponseMapper: BoardResponseMapper,
    private imageCompletionResponseMapper: ImageCompletionResponseMapper,
  ) {}

  async mapBoards(
    boardImageCompletionEntities: BoardImageCompletionEntity[],
  ): Promise<PublicBoardDto[]> {
    return Promise.all(
      boardImageCompletionEntities.map((boardImageCompletionEntity) =>
        this.boardResponseMapper.mapPublic(boardImageCompletionEntity.board),
      ),
    );
  }

  async mapImages(
    boardImageCompletionEntities: BoardImageCompletionEntity[],
  ): Promise<ImageCompletionDto[]> {
    const imageCompletions = boardImageCompletionEntities.map(
      (boardImageCompletionEntity) =>
        boardImageCompletionEntity.imageCompletion,
    );

    return await this.imageCompletionResponseMapper.mapMultiple(
      imageCompletions,
    );
  }
}
