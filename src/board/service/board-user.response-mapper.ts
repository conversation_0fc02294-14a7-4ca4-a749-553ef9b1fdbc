import { Injectable } from '@nestjs/common';
import { UserProvider } from 'src/user/service/provider';
import { UserResponseMapper } from '../../user/service/response-mapper';
import { BoardUserDto } from '../dto/board-user.dto';
import { BoardUserEntity } from '../entity/board-user.entity';
import { BoardProvider } from './provider';
import { BoardResponseMapper } from './response-mapper';

@Injectable()
export class BoardUserResponseMapper {
  constructor(
    private userResponseMapper: UserResponseMapper,
    private boardResponseMapper: BoardResponseMapper,
    private boardProvider: BoardProvider,
    private userProvider: UserProvider,
  ) {}

  async map(boardUser: BoardUserEntity): Promise<BoardUserDto> {
    const board =
      boardUser.board ?? (await this.boardProvider.get(boardUser.boardId));

    const invitedBy = boardUser.invitedBy
      ? this.userResponseMapper.mapPublic(
          await this.userProvider.get(boardUser.invitedBy),
        )
      : null;

    const dto = {
      id: boardUser.id,
      board: await this.boardResponseMapper.map(board),
      isAdmin: boardUser.isAdmin,
      status: boardUser.status,
      invitedBy,
      invitedAt: boardUser.invitedAt,
      approvedBy: boardUser.approvedBy,
      approvedAt: boardUser.approvedAt,
      createdAt: boardUser.createdAt,
      user: this.userResponseMapper.mapPublic(boardUser.user),
    } as Partial<BoardUserDto>;

    return dto as BoardUserDto;
  }

  async mapMultiple(
    boardUserEntities: BoardUserEntity[],
  ): Promise<BoardUserDto[]> {
    return Promise.all(
      boardUserEntities.map((boardUserEntity) => this.map(boardUserEntity)),
    );
  }
}
