# Overview

This document provides a detailed guide on how to structure a new module in a NestJS application. It includes descriptions of each component, along with code snippets to illustrate their implementation.

## Module Structure

The module is organized into several key components, each responsible for a specific aspect of the module's functionality. Here's an overview of the typical structure:

```
new-module/
│
├── controller/
│   ├── create.controller.ts
│   ├── delete.controller.ts
│   ├── read.controller.ts
│   └── update.controller.ts
│
├── dto/
│   ├── new-module.dto.ts
│   ├── new-module.request.ts
│   └── new-module.search-request.ts
│
├── entity/
│   └── new-module.entity.ts
│
├── service/
│   ├── new-module.provider.ts
│   ├── new-module.manager.ts
│   ├── new-module.request-manager.ts
│   └── new-module.response-mapper.ts
│
└── new-module.module.ts
```

## Components

### Controllers

Controllers handle HTTP requests and define the routes for the module. They are responsible for orchestrating the flow of data between the client and the server.

- **Create Controller**: Handles POST requests to create new resources.
- **Update Controller**: Handles PUT requests to update existing resources.
- **Delete Controller**: Handles DELETE requests to remove resources.
- **Read Controller**: Handles GET requests to retrieve resources.

### DTOs

Data Transfer Objects (DTOs) define the structure of data sent and received by the API. They ensure data consistency and validation.

- **new-module.dto.ts**: Defines the structure of the data returned to the client.
- **new-module.request.ts**: Defines the structure of the data required to create or update a resource.
- **new-module.search-request.ts**: Defines the structure of the search request parameters.

### Entity

The entity represents the database table or collection. It defines the schema for the data stored in the database.

- **new-module.entity.ts**: Defines the structure of the database entity.

### Services

Services implement business logic and data access. They are responsible for processing requests, interacting with the database, and returning responses.

- **new-module.provider.ts**: Handles data retrieval and querying logic.
- **new-module.manager.ts**: Handles business logic and modifications.
- **new-module.request-manager.ts**: Orchestrates request processing and validation.
- **new-module.response-mapper.ts**: Maps entities to DTOs for responses.

### Module

The module configures the components of the module, including controllers, providers, and other services.

- **new-module.module.ts**: Defines the module and its components.
