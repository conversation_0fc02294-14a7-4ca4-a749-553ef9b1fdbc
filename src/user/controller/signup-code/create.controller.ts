import { Controller, Post, Request } from '@nestjs/common';
import { Body } from '@nestjs/common/decorators/http/route-params.decorator';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { SignupCodeDto } from '../../dto/signup-code.dto';
import { SignupCodeRequest } from '../../dto/signup-code.request';
import { SignupCodeRequestManager } from '../../service/signup-code.request-manager';
import { SignupCodeResponseMapper } from '../../service/signup-code.response-mapper';

@ApiTags('signup-code')
@Controller('signup_codes')
export class CreateController {
  constructor(
    private responseMapper: SignupCodeResponseMapper,
    private requestManager: SignupCodeRequestManager,
  ) {}

  @ApiOperation({
    operationId: 'signup_code_create',
    summary: 'Create a signup code',
    description:
      'Creates a new signup code for the current user.\n\n' +
      'Requirements:\n' +
      '- User must be authenticated\n\n' +
      'Required Parameters:\n' +
      '- userId: The unique identifier of the user to create the signup code for\n' +
      '- notes: Signup code notes\n\n' +
      'Returns the created signup code object.',
  })
  @ApiBody({
    type: SignupCodeRequest,
    description: 'Signup code creation parameters.',
  })
  @ApiOkResponse({
    type: SignupCodeDto,
    description: 'Signup code created successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`notes\`: Must be a valid string.
      - \`userId\`: Must be a valid UUID.
      - Invalid or unavailable parameters.
  `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Post()
  async create(
    @Request() request,
    @Body() signupCodeRequest: SignupCodeRequest,
  ): Promise<SignupCodeDto> {
    signupCodeRequest.userId = request.user.id;
    const signupCode = await this.requestManager.create(signupCodeRequest);

    return this.responseMapper.map(signupCode);
  }
}
