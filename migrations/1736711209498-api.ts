import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1736711209498 implements MigrationInterface {
    name = 'Api1736711209498'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TYPE "public"."image_edit_mode_enum" AS ENUM('in', 'out')
        `);
        await queryRunner.query(`
            CREATE TABLE "image_edit" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "original_image_completion_id" uuid NOT NULL,
                "generated_image_completion_id" uuid,
                "user_id" uuid NOT NULL,
                "status" character varying NOT NULL DEFAULT 'new',
                "mode" "public"."image_edit_mode_enum" NOT NULL,
                "width" integer,
                "height" integer,
                "mask_url" text,
                "image_completions_count" integer NOT NULL,
                "deleted_at" TIMESTAMP,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_941307281e54da63186b2b169e6" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE INDEX "idx_image_edit_original_image_completion_id" ON "image_edit" ("original_image_completion_id")
        `);
        await queryRunner.query(`
            CREATE INDEX "idx_image_edit_generated_image_completion_id" ON "image_edit" ("generated_image_completion_id")
        `);
        await queryRunner.query(`
            CREATE INDEX "idx_image_edit_user_id" ON "image_edit" ("user_id")
        `);
        await queryRunner.query(`
            CREATE INDEX "idx_image_edit_deleted_at" ON "image_edit" ("deleted_at")
        `);
        await queryRunner.query(`
            CREATE INDEX "idx_image_edit_created_at" ON "image_edit" ("created_at")
        `);
        await queryRunner.query(`
            CREATE TABLE "image_edit_image_completion" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "image_edit_id" uuid NOT NULL,
                "image_completion_id" uuid NOT NULL,
                "is_saved" boolean NOT NULL DEFAULT false,
                CONSTRAINT "PK_151e76c6c233c78f3d91b7dfbd7" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE INDEX "idx_image_edit_image_completion_image_edit_id" ON "image_edit_image_completion" ("image_edit_id")
        `);
        await queryRunner.query(`
            CREATE INDEX "idx_image_edit_image_completion_image_completion_id" ON "image_edit_image_completion" ("image_completion_id")
        `);
        await queryRunner.query(`
            ALTER TABLE "image_edit"
            ADD CONSTRAINT "FK_b45b48fc973cbea3f3805ccee86" FOREIGN KEY ("original_image_completion_id") REFERENCES "image_completion"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "image_edit"
            ADD CONSTRAINT "FK_b7792358a67fc1f574558c2616a" FOREIGN KEY ("generated_image_completion_id") REFERENCES "image_completion"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "image_edit"
            ADD CONSTRAINT "FK_3ed2d43330a0d14cf1112c05283" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "image_edit_image_completion"
            ADD CONSTRAINT "FK_baa16ca05a863af8e9747ae621c" FOREIGN KEY ("image_edit_id") REFERENCES "image_edit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "image_edit_image_completion"
            ADD CONSTRAINT "FK_0e38bb7b48ae646612034cba015" FOREIGN KEY ("image_completion_id") REFERENCES "image_completion"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "image_edit_image_completion" DROP CONSTRAINT "FK_0e38bb7b48ae646612034cba015"
        `);
        await queryRunner.query(`
            ALTER TABLE "image_edit_image_completion" DROP CONSTRAINT "FK_baa16ca05a863af8e9747ae621c"
        `);
        await queryRunner.query(`
            ALTER TABLE "image_edit" DROP CONSTRAINT "FK_3ed2d43330a0d14cf1112c05283"
        `);
        await queryRunner.query(`
            ALTER TABLE "image_edit" DROP CONSTRAINT "FK_b7792358a67fc1f574558c2616a"
        `);
        await queryRunner.query(`
            ALTER TABLE "image_edit" DROP CONSTRAINT "FK_b45b48fc973cbea3f3805ccee86"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."idx_image_edit_image_completion_image_completion_id"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."idx_image_edit_image_completion_image_edit_id"
        `);
        await queryRunner.query(`
            DROP TABLE "image_edit_image_completion"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."idx_image_edit_created_at"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."idx_image_edit_deleted_at"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."idx_image_edit_user_id"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."idx_image_edit_generated_image_completion_id"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."idx_image_edit_original_image_completion_id"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."idx_image_edit_original_image_completion_id"
        `);
        await queryRunner.query(`
            DROP TABLE "image_edit"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."image_edit_mode_enum"
        `);
    }

}
