import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1701028922308 implements MigrationInterface {
    name = 'Api1701028922308'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "locale" character varying
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "locale"
        `);
    }

}
