import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1715692996566 implements MigrationInterface {
    name = 'Api1715692996566'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "image_completion_model" DROP CONSTRAINT "FK_8c85fbe5ccf3f8c84e514682c9d"
        `);
        await queryRunner.query(`
            ALTER TABLE "image_completion_model"
            ALTER COLUMN "image_completion_id"
            SET NOT NULL
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_cb24a4b667e103bc038f6c7292" ON "image_completion" ("user_id")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_515f78ab9c42dfc41f6bb356c1" ON "image_completion" ("is_hot")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_8c85fbe5ccf3f8c84e514682c9" ON "image_completion_model" ("image_completion_id")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_b647c482570bb6aaf5d5a39f84" ON "image_completion_model" ("model_id")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_98469faf3de8c60756a3cfb62a" ON "image_completion_like" ("image_completion_id")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_859471e9802d87a41700443b57" ON "image_completion_like" ("user_id")
        `);
        await queryRunner.query(`
            ALTER TABLE "image_completion_model"
            ADD CONSTRAINT "FK_8c85fbe5ccf3f8c84e514682c9d" FOREIGN KEY ("image_completion_id") REFERENCES "image_completion"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "image_completion_model" DROP CONSTRAINT "FK_8c85fbe5ccf3f8c84e514682c9d"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_859471e9802d87a41700443b57"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_98469faf3de8c60756a3cfb62a"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_b647c482570bb6aaf5d5a39f84"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_8c85fbe5ccf3f8c84e514682c9"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_515f78ab9c42dfc41f6bb356c1"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_cb24a4b667e103bc038f6c7292"
        `);
        await queryRunner.query(`
            ALTER TABLE "image_completion_model"
            ALTER COLUMN "image_completion_id" DROP NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "image_completion_model"
            ADD CONSTRAINT "FK_8c85fbe5ccf3f8c84e514682c9d" FOREIGN KEY ("image_completion_id") REFERENCES "image_completion"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    }

}
