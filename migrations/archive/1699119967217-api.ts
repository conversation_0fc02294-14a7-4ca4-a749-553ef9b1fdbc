import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1699119967217 implements MigrationInterface {
    name = 'Api1699119967217'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "subscription" DROP CONSTRAINT "FK_7dd09a6b2a5612c4c04485711d0"
        `);
        await queryRunner.query(`
            ALTER TABLE "credit_package" DROP CONSTRAINT "PK_07044d60296ffa58ab2a6e2460a"
        `);
        await queryRunner.query(`
            ALTER TABLE "credit_package" DROP COLUMN "id"
        `);
        await queryRunner.query(`
            ALTER TABLE "credit_package"
            ADD "id" uuid NOT NULL DEFAULT uuid_generate_v4()
        `);
        await queryRunner.query(`
            ALTER TABLE "credit_package"
            ADD CONSTRAINT "PK_07044d60296ffa58ab2a6e2460a" PRIMARY KEY ("id")
        `);
        await queryRunner.query(`
            ALTER TABLE "credit_package"
            ALTER COLUMN "expires_after_months" DROP NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "credit_package"
            ALTER COLUMN "expires_after_days" DROP NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription" DROP CONSTRAINT "PK_8c3e00ebd02103caa1174cd5d9d"
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription" DROP COLUMN "id"
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription"
            ADD "id" uuid NOT NULL DEFAULT uuid_generate_v4()
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription"
            ADD CONSTRAINT "PK_8c3e00ebd02103caa1174cd5d9d" PRIMARY KEY ("id")
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription" DROP COLUMN "credit_package_id"
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription"
            ADD "credit_package_id" uuid
        `);
        await queryRunner.query(`
            ALTER TABLE "transaction" DROP CONSTRAINT "PK_89eadb93a89810556e1cbcd6ab9"
        `);
        await queryRunner.query(`
            ALTER TABLE "transaction" DROP COLUMN "id"
        `);
        await queryRunner.query(`
            ALTER TABLE "transaction"
            ADD "id" uuid NOT NULL DEFAULT uuid_generate_v4()
        `);
        await queryRunner.query(`
            ALTER TABLE "transaction"
            ADD CONSTRAINT "PK_89eadb93a89810556e1cbcd6ab9" PRIMARY KEY ("id")
        `);
        await queryRunner.query(`
            ALTER TABLE "user_credit_balance" DROP CONSTRAINT "PK_5320169003bbca4fa53fd184c71"
        `);
        await queryRunner.query(`
            ALTER TABLE "user_credit_balance" DROP COLUMN "id"
        `);
        await queryRunner.query(`
            ALTER TABLE "user_credit_balance"
            ADD "id" uuid NOT NULL DEFAULT uuid_generate_v4()
        `);
        await queryRunner.query(`
            ALTER TABLE "user_credit_balance"
            ADD CONSTRAINT "PK_5320169003bbca4fa53fd184c71" PRIMARY KEY ("id")
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription"
            ADD CONSTRAINT "FK_7dd09a6b2a5612c4c04485711d0" FOREIGN KEY ("credit_package_id") REFERENCES "credit_package"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "subscription" DROP CONSTRAINT "FK_7dd09a6b2a5612c4c04485711d0"
        `);
        await queryRunner.query(`
            ALTER TABLE "user_credit_balance" DROP CONSTRAINT "PK_5320169003bbca4fa53fd184c71"
        `);
        await queryRunner.query(`
            ALTER TABLE "user_credit_balance" DROP COLUMN "id"
        `);
        await queryRunner.query(`
            ALTER TABLE "user_credit_balance"
            ADD "id" SERIAL NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "user_credit_balance"
            ADD CONSTRAINT "PK_5320169003bbca4fa53fd184c71" PRIMARY KEY ("id")
        `);
        await queryRunner.query(`
            ALTER TABLE "transaction" DROP CONSTRAINT "PK_89eadb93a89810556e1cbcd6ab9"
        `);
        await queryRunner.query(`
            ALTER TABLE "transaction" DROP COLUMN "id"
        `);
        await queryRunner.query(`
            ALTER TABLE "transaction"
            ADD "id" SERIAL NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "transaction"
            ADD CONSTRAINT "PK_89eadb93a89810556e1cbcd6ab9" PRIMARY KEY ("id")
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription" DROP COLUMN "credit_package_id"
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription"
            ADD "credit_package_id" integer
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription" DROP CONSTRAINT "PK_8c3e00ebd02103caa1174cd5d9d"
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription" DROP COLUMN "id"
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription"
            ADD "id" SERIAL NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription"
            ADD CONSTRAINT "PK_8c3e00ebd02103caa1174cd5d9d" PRIMARY KEY ("id")
        `);
        await queryRunner.query(`
            ALTER TABLE "credit_package"
            ALTER COLUMN "expires_after_days"
            SET NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "credit_package"
            ALTER COLUMN "expires_after_months"
            SET NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "credit_package" DROP CONSTRAINT "PK_07044d60296ffa58ab2a6e2460a"
        `);
        await queryRunner.query(`
            ALTER TABLE "credit_package" DROP COLUMN "id"
        `);
        await queryRunner.query(`
            ALTER TABLE "credit_package"
            ADD "id" SERIAL NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "credit_package"
            ADD CONSTRAINT "PK_07044d60296ffa58ab2a6e2460a" PRIMARY KEY ("id")
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription"
            ADD CONSTRAINT "FK_7dd09a6b2a5612c4c04485711d0" FOREIGN KEY ("credit_package_id") REFERENCES "credit_package"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    }

}
