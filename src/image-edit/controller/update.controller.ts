import {
  Controller,
  Put,
  Param,
  Body,
  Request,
  ParseUUI<PERSON>ipe,
  HttpCode,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBody,
  ApiOkResponse,
  ApiBadRequestResponse,
  ApiParam,
  ApiNoContentResponse,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { ImageEditDto } from '../dto/image-edit.dto';
import { ImageEditRequestManager } from '../service/request-manager';
import { ImageEditResponseMapper } from '../service/response-mapper';
import { ImageEditProvider } from '../service/provider';
import { ImageEditUpdateRequest } from '../dto/image-edit.update-request';

@ApiTags('image-edit')
@Controller('image-edits')
export class UpdateController {
  constructor(
    private readonly provider: ImageEditProvider,
    private readonly requestManager: ImageEditRequestManager,
    private readonly responseMapper: ImageEditResponseMapper,
  ) {}

  @Put(':id')
  @ApiOperation({
    operationId: 'image_edit_update',
    summary: 'Update image edit',
    description:
      'Updates the specified image edit for the authenticated user.\n\n' +
      'Required Query Parameters:\n' +
      '- id: ID of the image edit to update\n' +
      'Optional Body Parameters:\n' +
      '- mode: New mode of the image edit\n' +
      '- originalImageCompletionId: New original image completion ID\n' +
      '- width: New width of the image edit\n' +
      '- height: New height of the image edit\n' +
      '- imageCompletionsCount: New number of image completions to generate\n' +
      '- prompt: New prompt for the image edit\n' +
      '- mask: New mask for the image edit\n' +
      '- inputImageUrl: New array of input image URLs for the image edit\n' +
      '- settings: New settings for the image edit\n' +
      '- webhookUrl: New webhook URL for the image edit\n' +
      '- hidePrompt: Hide prompt for the image edit\n',
  })
  @ApiParam({ name: 'id', description: 'ID of the image edit to update' })
  @ApiBody({ type: ImageEditUpdateRequest })
  @ApiOkResponse({
    type: ImageEditDto,
    description: 'Image edit updated successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - \`mode\`: Must be a valid value from the \`EditMode\` enum.
      - \`originalImageCompletionId\`: Must be a valid UUID.
      - \`width\`: Must be a valid number between 480 and 2160.
      - \`height\`: Must be a valid number between 480 and 2160.
      - \`imageCompletionsCount\`: Must be a valid number between 1 and 5.
      - \`prompt\`: Must be a valid string.
      - \`mask\`: Must be a valid string.
      - \`inputImageUrl\`: Must be an array of valid URLs.
      - \`settings\`: Must be a valid object.
      - \`webhookUrl\`: Must be a valid URL.
      - \`hidePrompt\`: Must be a valid boolean.
      - Image edit does not exist.
      - Invalid or unavailable parameters.
      - Failed to analyze request body.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description:
      'Forbidden. User does not have permission to update this image edit.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async update(
    @Param('id', new ParseUUIDPipe()) id: string,
    @Body() requestBody: ImageEditUpdateRequest,
    @Request() request,
  ): Promise<ImageEditDto> {
    const user = request.user;
    const entity = await this.requestManager.update(id, requestBody, user);

    return await this.responseMapper.map(entity);
  }

  @Put(':id/interruption')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'image_edit_interrupt',
    summary: 'Interrupt image edit',
    description:
      'Interrupts the specified image edit for the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- id: ID of the image edit to interrupt\n',
  })
  @ApiNoContentResponse({ description: 'Image edit interrupted successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - Image edit does not exist.
      - Image edit is not in a valid state for the requested update.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description:
      'Forbidden. User does not have permission to interrupt this image edit.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async interruption(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ) {
    const entity = await this.provider.getBy({
      user: { id: request.user.id },
      id: id,
    });

    await this.requestManager.interrupt(entity);
  }

  @Put(':id/image-completions/:imageCompletionId')
  @ApiOperation({
    operationId: 'image_edit_save_image_completion',
    summary: 'Save image completion to image edit',
    description:
      'Saves the specified image completion to the image edit for the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- id: ID of the image edit to update\n' +
      '- imageCompletionId: ID of the image completion to be saved\n',
  })
  @ApiParam({ name: 'id', description: 'ID of the image edit to update' })
  @ApiParam({
    name: 'imageCompletionId',
    description: 'ID of the image completion to be saved',
  })
  @ApiOkResponse({
    type: ImageEditDto,
    description: 'Image completion saved to image edit successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - \`imageCompletionId\`: Must be a valid UUID.
      - Image edit does not exist.
      - Image completion does not exist.
      - Image completion is not part of the image edit.
      - Invalid or unavailable parameters.
  `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description:
      'Forbidden. User does not have permission to update this image edit.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async saveImageCompletion(
    @Param('id', new ParseUUIDPipe()) id: string,
    @Param('imageCompletionId', new ParseUUIDPipe()) imageCompletionId: string,
    @Request() request,
  ): Promise<ImageEditDto> {
    const user = request.user;
    await this.requestManager.saveImageCompletion(id, imageCompletionId, user);

    const imageEdit = await this.provider.get(id);

    return await this.responseMapper.map(imageEdit);
  }
}
