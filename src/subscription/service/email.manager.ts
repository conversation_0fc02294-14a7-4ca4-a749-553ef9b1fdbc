import { Injectable } from '@nestjs/common';
import { Logger } from 'nestjs-pino';
import * as path from 'path';
import { AppConfigurationService } from 'src/core/service/app-configuration.service';
import { Mailer } from 'src/core/service/mailer.service';
import { UserProvider } from 'src/user/service/provider';
import { SubscriptionEntity } from '../entity/subscription.entity';

@Injectable()
export class EmailManager {
  constructor(
    private appConfig: AppConfigurationService,
    private mailer: Mailer,
    private logger: Logger,
    private userProvider: UserProvider,
  ) {}

  async sendPaidEmail(subscription: SubscriptionEntity) {
    const user = await this.userProvider.get(subscription.userId);
    const subject = `LetzAI Payment successful! Enjoy your new plan ✨`;

    const replacements = {
      subject: subject,
      username: user?.username || user?.name || '',
      planname: subscription.name,
    };

    const template = path.join(
      this.appConfig.templateDir,
      'email',
      'subscription',
      'paid.html',
    );

    try {
      await this.mailer.send(template, replacements, subject, user.email);
    } catch (error) {
      this.logger.error('Error sending subscription paid email', {
        subscriptionId: subscription.id,
        error: error,
      });
    }
  }

  async sendRenewedEmail(subscription: SubscriptionEntity) {
    const user = await this.userProvider.get(subscription.userId);
    const subject = `Your LetzAI subscription was renewed!`;

    const replacements = {
      subject: subject,
      username: user?.username || user?.name || '',
      planname: subscription.name,
    };

    const template = path.join(
      this.appConfig.templateDir,
      'email',
      'subscription',
      'renewed.html',
    );

    try {
      await this.mailer.send(template, replacements, subject, user.email);
    } catch (error) {
      this.logger.error('Error sending subscription renewed email', {
        subscriptionId: subscription.id,
        error: error,
      });
    }
  }

  async sendCancellationEmail(subscription: SubscriptionEntity) {
    const user = await this.userProvider.get(subscription.userId);
    const subject = `Your LetzAI subscription was cancelled!`;

    const replacements = {
      subject: subject,
      username: user?.username || user?.name || '',
    };

    const template = path.join(
      this.appConfig.templateDir,
      'email',
      'subscription',
      'cancellation.html',
    );

    try {
      await this.mailer.send(template, replacements, subject, user.email);
    } catch (error) {
      this.logger.error('Error sending subscription cancelled email', {
        subscriptionId: subscription.id,
        error: error,
      });
    }
  }

  async sendPaymentFailedEmail(subscription: SubscriptionEntity, error?: string) {
    const user = await this.userProvider.get(subscription.userId);
    const subject = `Payment Failed - Action Required for Your LetzAI Subscription`;

    const replacements = {
      subject: subject,
      username: user?.username || user?.name || '',
      planname: subscription.name,
      error: error || 'Payment processing failed',
    };

    const template = path.join(
      this.appConfig.templateDir,
      'email',
      'subscription',
      'payment-failed.html',
    );

    try {
      await this.mailer.send(template, replacements, subject, user.email);
    } catch (emailError) {
      this.logger.error('Error sending payment failed email', {
        subscriptionId: subscription.id,
        error: emailError,
      });
    }
  }

  async sendPaymentFailedInitialEmail(subscription: SubscriptionEntity, gracePeriodInfo?: any) {
    const user = await this.userProvider.get(subscription.userId);
    const subject = `Payment Failed - Your LetzAI Subscription Needs Attention`;

    const replacements = {
      subject: subject,
      username: user?.username || user?.name || '',
      planname: subscription.name,
    };

    const template = path.join(
      this.appConfig.templateDir,
      'email',
      'subscription',
      'payment-failed-initial.html',
    );

    try {
      await this.mailer.send(template, replacements, subject, user.email);
    } catch (error) {
      this.logger.error('Error sending initial payment failed email', {
        subscriptionId: subscription.id,
        error: error,
      });
    }
  }

  async sendPaymentFailedReminderEmail(subscription: SubscriptionEntity, gracePeriodInfo?: any) {
    const user = await this.userProvider.get(subscription.userId);
    const subject = `Reminder: Payment Failed - Update Your Payment Method`;

    const replacements = {
      subject: subject,
      username: user?.username || user?.name || '',
      planname: subscription.name,
    };

    const template = path.join(
      this.appConfig.templateDir,
      'email',
      'subscription',
      'payment-failed-reminder.html',
    );

    try {
      await this.mailer.send(template, replacements, subject, user.email);
    } catch (error) {
      this.logger.error('Error sending payment failed reminder email', {
        subscriptionId: subscription.id,
        error: error,
      });
    }
  }

  async sendPaymentFailedFinalWarningEmail(subscription: SubscriptionEntity, gracePeriodInfo?: any) {
    const user = await this.userProvider.get(subscription.userId);
    const subject = `Final Warning: Payment Failed - Subscription Will Be Cancelled`;

    const replacements = {
      subject: subject,
      username: user?.username || user?.name || '',
      planname: subscription.name,
    };

    const template = path.join(
      this.appConfig.templateDir,
      'email',
      'subscription',
      'payment-failed-final.html',
    );

    try {
      await this.mailer.send(template, replacements, subject, user.email);
    } catch (error) {
      this.logger.error('Error sending final payment failed warning email', {
        subscriptionId: subscription.id,
        error: error,
      });
    }
  }
}
