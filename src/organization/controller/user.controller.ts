import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  ForbiddenException,
  Get,
  HttpCode,
  Param,
  ParseUUIDPipe,
  Patch,
  Post,
  Put,
  Query,
  Request,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiQuery,
  ApiTags,
  ApiOperation,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { OrganizationProvider } from '../service/provider';
import { OrganizationInviteProvider } from '../service/organization-invite.provider';
import { OrganizationInviteRequest } from '../dto/organization-invite.request';
import { OrganizationInviteRequestManager } from '../service/organization-invite.request-manager';
import { StatusEnum } from '../enum/organization-user.enum';
import { BaseSearchRequest } from 'src/core/dto/base.search-request';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { Response } from 'express';
import { OrganizationInviteResponseMapper } from '../service/organization-invite.response-mapper';
import { OrganizationUserDto } from '../dto/organization-user.dto';
import { BaseFindResponseHeadersDto } from 'src/core/dto/base-find-response-headers.dto';
import { OrganizationUserSearchRequest } from '../dto/organization-user.search-request';
import { OrganizationUserProvider } from '../service/organization-user.provider';
import { OrganizationUserResponseMapper } from '../service/organization-user.response-mapper';
import { OrganizationUserUpdateRequest } from '../dto/organization-user.update-request';
import { OrganizationUserRequestManager } from '../service/organization-user.request.manager';
import { OrganizationInviteManager } from '../service/organization-invite.manager';

@ApiTags('organization / user')
@Controller('organizations')
export class UserController {
  constructor(
    private organizationProvider: OrganizationProvider,
    private organizationInviteProvider: OrganizationInviteProvider,
    private organizationInviteRequestManager: OrganizationInviteRequestManager,
    private organizationInviteManager: OrganizationInviteManager,
    private organizationInviteResponseMapper: OrganizationInviteResponseMapper,
    private organizationUserProvider: OrganizationUserProvider,
    private organizationUserResponseMapper: OrganizationUserResponseMapper,
    private organizationUserRequestManager: OrganizationUserRequestManager,
  ) {}

  @ApiOperation({
    operationId: 'organization_user_list',
    summary: 'List organization users',
    description:
      'Retrieves a paginated list of users for the specified organization.\n\n' +
      'Requirements:\n' +
      '- User must be a member of the organization' +
      '\n\n' +
      'Required Parameters:\n' +
      '- organizationId: UUID of the organization\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of users per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- search: Search term\n' +
      '- userId: UUID of the user\n',
  })
  @ApiOkResponse({
    type: OrganizationUserDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated list of organization users.',
  })
  @ApiQuery({
    type: OrganizationUserSearchRequest,
    description:
      'Query parameters for searching and paginating organization users.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User is not a member of the organization.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`organizationId\`: Must be a valid UUID.
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - \`userId\`: Must be a valid UUID.
      - \`search\`: Must be a valid string.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Get(':organizationId/users')
  @UsePipes(new ValidationPipe())
  async find(
    @Request() request,
    @Query() query: OrganizationUserSearchRequest,
    @Res() res: Response,
    @Param('organizationId', new ParseUUIDPipe()) organizationId: string,
  ): Promise<void> {
    const organization = await this.organizationProvider.get(organizationId);

    if (
      !(await this.organizationUserProvider.isMember(
        request.user.id,
        organization.id,
      ))
    ) {
      throw new ForbiddenException();
    }

    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;

    const filters = {
      ...inputFilters,
      organizationId: organization.id,
    };

    const entities = await this.organizationUserProvider.findBy(
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    const totalCount = await this.organizationUserProvider.countBy(filters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.organizationUserResponseMapper.mapMultiple(entities));
  }

  @ApiOperation({
    operationId: 'organization_user_invites_current',
    summary: 'List current user organization invites',
    description:
      'Retrieves a paginated list of pending organization invites for the current user.' +
      '\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of invites per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n',
  })
  @ApiOkResponse({
    type: OrganizationInviteResponseMapper,
    isArray: true,
    description: 'Paginated list of pending invites for the current user.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - Invalid or unavailable parameters.
    `,
  })
  @UsePipes(new ValidationPipe())
  @Get('current/invites')
  async findInvitesCurrent(
    @Request() request,
    @Query() query: BaseSearchRequest,
    @Res() res: Response,
  ) {
    const userId = request.user.id;
    const { page, limit, sortBy, sortOrder } = query;
    const entities = await this.organizationInviteProvider.findBy(
      { status: StatusEnum.PENDING, userId },
      page,
      limit,
      sortBy,
      sortOrder,
    );

    const totalCount = await this.organizationInviteProvider.countBy({
      status: StatusEnum.PENDING,
      userId,
    });

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.organizationInviteResponseMapper.mapMultiple(entities));
  }

  @ApiOperation({
    operationId: 'organization_user_invites_list',
    summary: 'List organization invites',
    description:
      'Retrieves a paginated list of pending invites for the specified organization.\n\n' +
      'Requirements:\n' +
      '- User must be an admin of the organization' +
      '\n\n' +
      'Required Parameters:\n' +
      '- organizationId: UUID of the organization\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of invites per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- search: Search term\n' +
      '- userId: UUID of the user\n',
  })
  @ApiOkResponse({
    type: OrganizationInviteResponseMapper,
    isArray: true,
    description: 'Paginated list of pending invites for the organization.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User is not an admin of the organization.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`organizationId\`: Must be a valid UUID.
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - \`userId\`: Must be a valid UUID.
      - \`search\`: Must be a valid string.
      - Invalid or unavailable parameters.
    `,
  })
  @UsePipes(new ValidationPipe())
  @Get(':organizationId/invites')
  async findInvites(
    @Request() request,
    @Query() query: OrganizationUserSearchRequest,
    @Res() res: Response,
    @Param('organizationId', new ParseUUIDPipe()) organizationId: string,
  ) {
    const organization = await this.organizationProvider.get(organizationId);
    const userId = request.user.id;

    const isAdmin = await this.organizationUserProvider.isAdmin(
      userId,
      organization.id,
    );

    if (!isAdmin) {
      throw new ForbiddenException();
    }

    const { page, limit, sortBy, sortOrder } = query;

    const entities = await this.organizationInviteProvider.findBy(
      { status: StatusEnum.PENDING, organizationId: organizationId },
      page,
      limit,
      sortBy,
      sortOrder,
    );
    const totalCount = await this.organizationInviteProvider.countBy({
      status: StatusEnum.PENDING,
      organizationId: organizationId,
    });
    setPaginationHeaders(res, totalCount, page, limit);
    res.send(await this.organizationInviteResponseMapper.mapMultiple(entities));
  }

  @ApiOperation({
    operationId: 'organization_user_invite',
    summary: 'Invite user to organization',
    description:
      'Invites a user to join the specified organization.\n\n' +
      'Requirements:\n' +
      '- User must be a manager of the organization' +
      '\n\n' +
      'Required Parameters:\n' +
      '- organizationId: UUID of the organization to invite the user to\n\n' +
      'Optional Body Parameters:\n' +
      '- email: Email of the user to invite\n' +
      '- userId: UUID of the user to invite\n',
  })
  @ApiNoContentResponse({ description: 'User invited successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`organizationId\`: Must be a valid UUID.
      - \`email\`: Must be a valid email.
      - \`userId\`: Must be a valid UUID.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User is not a manager of the organization.',
  })
  @Post(':organizationId/invites')
  @HttpCode(204)
  async invite(
    @Request() request,
    @Body() body: OrganizationInviteRequest,
    @Param('organizationId', new ParseUUIDPipe()) organizationId: string,
  ): Promise<void> {
    const organization = await this.organizationProvider.get(organizationId);
    const user = request.user;
    const isManager = await this.organizationUserProvider.isManager(
      user.id,
      organizationId,
    );

    if (!isManager) {
      throw new ForbiddenException();
    }

    await this.organizationInviteRequestManager.invite(
      organization,
      body,
      user,
    );
  }

  @ApiOperation({
    operationId: 'organization_user_get',
    summary: 'Get organization user by ID',
    description:
      'Retrieves details of a specific user in the organization.\n\n' +
      'Requirements:\n' +
      '- User must be a member of the organization' +
      '\n\n' +
      'Required Parameters:\n' +
      '- organizationId: UUID of the organization\n' +
      '- userId: UUID of the user\n',
  })
  @ApiOkResponse({
    type: OrganizationUserDto,
    description: 'Organization user details.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User is not a member of the organization.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`organizationId\`: Must be a valid UUID.
      - \`userId\`: Must be a valid UUID.
   `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Get(':organizationId/users/:userId')
  async get(
    @Request() request,
    @Param('organizationId', new ParseUUIDPipe()) organizationId: string,
    @Param('userId', new ParseUUIDPipe()) userId: string,
  ): Promise<OrganizationUserDto> {
    const organization = await this.organizationProvider.get(organizationId);
    const currentUser = request.user;

    if (
      !(await this.organizationUserProvider.isMember(
        currentUser.id,
        organization.id,
      ))
    ) {
      throw new ForbiddenException();
    }

    const organizationUser = await this.organizationUserProvider.getMember(
      userId,
      organization.id,
    );

    return await this.organizationUserResponseMapper.map(organizationUser);
  }

  @ApiOperation({
    operationId: 'organization_user_update',
    summary: 'Update organization user',
    description:
      'Updates the details or role of a user in the organization.\n\n' +
      'Requirements:\n' +
      '- User must be a manager of the organization' +
      '\n\n' +
      'Required Parameters:\n' +
      '- organizationId: UUID of the organization\n' +
      '- userId: UUID of the user\n\n' +
      'Optional Body Parameters:\n' +
      '- isAdmin: Whether the user is an admin\n',
  })
  @ApiNoContentResponse({
    description: 'Organization user updated successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`organizationId\`: Must be a valid UUID.
      - \`userId\`: Must be a valid UUID.
      - \`isAdmin\`: Must be a valid boolean.
  `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User is not a manager of the organization.',
  })
  @Patch(':organizationId/users/:userId')
  async updateUser(
    @Request() request,
    @Body() body: OrganizationUserUpdateRequest,
    @Param('organizationId', new ParseUUIDPipe()) organizationId: string,
    @Param('userId', new ParseUUIDPipe()) userId: string,
  ): Promise<void> {
    const organization = await this.organizationProvider.get(organizationId);

    if (
      !(await this.organizationUserProvider.isManager(
        request.user.id,
        organization.id,
      ))
    ) {
      throw new ForbiddenException();
    }

    if (
      !(await this.organizationUserProvider.isOwner(
        request.user.id,
        organization.id,
      ))
    ) {
      delete body.isAdmin;
    }

    await this.organizationUserRequestManager.updateUser(
      organization,
      userId,
      body,
    );
  }

  @ApiOperation({
    operationId: 'organization_user_remove',
    summary: 'Remove user from organization',
    description:
      'Removes a user from the organization.\n\n' +
      'Requirements:\n' +
      '- User must be a manager of the organization or the user themselves' +
      '\n\n' +
      'Required Parameters:\n' +
      '- organizationId: UUID of the organization\n' +
      '- userId: UUID of the user\n',
  })
  @ApiNoContentResponse({
    description: 'User removed from organization successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`organizationId\`: Must be a valid UUID.
      - \`userId\`: Must be a valid UUID.
  `,
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User is not authorized to remove this user.',
  })
  @Delete(':organizationId/users/:userId')
  async removeUser(
    @Request() request,
    @Param('organizationId', new ParseUUIDPipe()) organizationId: string,
    @Param('userId', new ParseUUIDPipe()) userId: string,
  ): Promise<void> {
    const organization = await this.organizationProvider.get(organizationId);

    if (
      request.user.id != userId &&
      !(await this.organizationUserProvider.isManager(
        request.user.id,
        organization.id,
      ))
    ) {
      throw new ForbiddenException();
    }

    await this.organizationUserRequestManager.removeUser(
      organization,
      userId,
      request.user.id,
    );
  }

  @ApiOperation({
    operationId: 'organization_user_invite_accept',
    summary: 'Accept organization invite',
    description:
      'Accepts a pending invite to join the organization.\n\n' +
      'Requirements:\n' +
      '- Invite must be pending\n' +
      '- User must be the invitee' +
      '\n\n' +
      'Required Parameters:\n' +
      '- organizationId: UUID of the organization\n' +
      '- organizationInviteId: UUID of the invite\n',
  })
  @ApiNoContentResponse({ description: 'Organization invite accepted.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`organizationId\`: Must be a valid UUID.
      - \`organizationInviteId\`: Must be a valid UUID.
  `,
  })
  @Put(':organizationId/invites/:organizationInviteId/acceptance')
  @HttpCode(204)
  async acceptance(
    @Request() request,
    @Param('organizationId', new ParseUUIDPipe()) organizationId: string,
    @Param('organizationInviteId', new ParseUUIDPipe())
    organizationInviteId: string,
  ) {
    const organization = await this.organizationProvider.get(organizationId);
    const userId = request.user.id;
    const organizationInvite = await this.organizationInviteProvider.getBy({
      id: organizationInviteId,
      userId: userId,
    });

    if (organizationInvite.status != StatusEnum.PENDING) {
      throw new BadRequestException();
    }

    await this.organizationInviteRequestManager.accept(
      organization,
      organizationInvite,
    );
  }

  @ApiOperation({
    operationId: 'organization_user_invite_reject',
    summary: 'Reject organization invite',
    description:
      'Rejects a pending invite to join the organization.\n\n' +
      'Requirements:\n' +
      '- Invite must be pending\n' +
      '- User must be the invitee' +
      '\n\n' +
      'Required Parameters:\n' +
      '- organizationId: UUID of the organization\n' +
      '- organizationInviteId: UUID of the invite\n',
  })
  @ApiNoContentResponse({ description: 'Organization invite rejected.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`organizationId\`: Must be a valid UUID.
      - \`organizationInviteId\`: Must be a valid UUID.
  `,
  })
  @Delete(':organizationId/invites/:organizationInviteId/acceptance')
  async rejection(
    @Request() request,
    @Param('organizationId', new ParseUUIDPipe()) organizationId: string,
    @Param('organizationInviteId', new ParseUUIDPipe())
    organizationInviteId: string,
  ) {
    const userId = request.user.id;
    const organization = await this.organizationProvider.get(organizationId);
    const organizationInvite = await this.organizationInviteProvider.getBy({
      id: organizationInviteId,
      userId: userId,
    });

    if (organizationInvite.status != StatusEnum.PENDING) {
      throw new BadRequestException();
    }

    await this.organizationInviteRequestManager.reject(
      organizationInvite,
      organization,
    );
  }

  @ApiOperation({
    operationId: 'organization_user_invite_remove',
    summary: 'Remove organization invite',
    description:
      'Removes a pending invite from the organization.\n\n' +
      'Requirements:\n' +
      '- User must be a manager of the organization' +
      '\n\n' +
      'Required Parameters:\n' +
      '- organizationId: UUID of the organization\n' +
      '- organizationInviteId: UUID of the invite\n',
  })
  @ApiNoContentResponse({
    description: 'Organization invite removed successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`organizationId\`: Must be a valid UUID.
      - \`organizationInviteId\`: Must be a valid UUID.
  `,
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User is not a manager of the organization.',
  })
  @Delete(':organizationId/invites/:organizationInviteId')
  async removeInvite(
    @Request() request,
    @Param('organizationId', new ParseUUIDPipe()) organizationId: string,
    @Param('organizationInviteId', new ParseUUIDPipe())
    organizationInviteId: string,
  ): Promise<void> {
    if (
      !(await this.organizationUserProvider.isManager(
        request.user.id,
        organizationId,
      ))
    ) {
      throw new ForbiddenException();
    }

    const organization = await this.organizationProvider.get(organizationId);
    const organizationInvite = await this.organizationInviteProvider.getBy({
      id: organizationInviteId,
      status: StatusEnum.PENDING,
    });

    await this.organizationInviteManager.delete(
      organization,
      organizationInvite,
    );
  }
}
