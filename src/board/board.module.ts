import { DynamicModule, forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ImageCompletionModule } from 'src/image-completion/module';
import { NotificationModule } from 'src/notification/module';
import { UserModule } from '../user/user.module';
import { CommentController } from './controller/comment.controller';
import { CreateController } from './controller/create.controller';
import { DeleteController } from './controller/delete.controller';
import { ImageController } from './controller/image.controller';
import { ReadController } from './controller/read.controller';
import { UpdateController } from './controller/update.controller';
import { UserController } from './controller/user.controller';
import { BoardCommentEntity } from './entity/board-comment.entity';
import { BoardImageCompletionEntity } from './entity/board-image-completion.entity';
import { BoardUserEntity } from './entity/board-user.entity';
import { BoardEntity } from './entity/board.entity';
import { UserCreatedListener } from './listener/user-created.listener';
import { BoardCommentManager } from './service/board-comment.manager';
import { BoardCommentProvider } from './service/board-comment.provider';
import { BoardCommentRequestManager } from './service/board-comment.request-manager';
import { BoardCommentResponseMapper } from './service/board-comment.response-mapper';
import { BoardImageCompletionManager } from './service/board-image-completion.manager';
import { BoardImageCompletionProvider } from './service/board-image-completion.provider';
import { BoardImageCompletionRequestManager } from './service/board-image-completion.request-manager';
import { BoardImageCompletionResponseMapper } from './service/board-image-completion.response-mapper';
import { BoardUserManager } from './service/board-user.manager';
import { BoardUserProvider } from './service/board-user.provider';
import { BoardUserRequestManager } from './service/board-user.request-manager';
import { BoardUserResponseMapper } from './service/board-user.response-mapper';
import { BoardManager } from './service/manager';
import { BoardProvider } from './service/provider';
import { BoardRequestManager } from './service/request-manager';
import { BoardResponseMapper } from './service/response-mapper';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      BoardEntity,
      BoardImageCompletionEntity,
      BoardUserEntity,
      BoardCommentEntity,
    ]),
    forwardRef(() => ImageCompletionModule),
    forwardRef(() => UserModule),
    forwardRef(() => NotificationModule),
  ],
  providers: [
    BoardManager,
    BoardProvider,
    BoardRequestManager,
    BoardResponseMapper,
    BoardUserProvider,
    BoardUserRequestManager,
    BoardUserResponseMapper,
    BoardUserManager,
    BoardImageCompletionProvider,
    BoardImageCompletionManager,
    BoardImageCompletionResponseMapper,
    BoardImageCompletionRequestManager,
    BoardCommentProvider,
    BoardCommentRequestManager,
    BoardCommentManager,
    BoardCommentResponseMapper,
    UserCreatedListener,
  ],
  exports: [BoardProvider, BoardManager, BoardUserManager, BoardResponseMapper],
})
export class BoardModule {
  static register(enableControllers: boolean): DynamicModule {
    return {
      module: BoardModule,
      controllers: enableControllers
        ? [
            CreateController,
            DeleteController,
            ReadController,
            UpdateController,
            UserController,
            ImageController,
            CommentController,
          ]
        : [],
    };
  }
}
