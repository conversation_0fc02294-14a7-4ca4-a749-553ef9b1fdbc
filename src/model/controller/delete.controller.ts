import {
  Controller,
  Delete,
  HttpCode,
  Param,
  ParseUUIDPipe,
  Request,
} from '@nestjs/common';
import {
  ApiNoContentResponse,
  ApiParam,
  ApiTags,
  ApiOperation,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { ModelManager } from '../service/manager';
import { ModelProvider } from '../service/provider';

@ApiTags('model')
@Controller('models')
export class DeleteController {
  constructor(private provider: ModelProvider, private manager: ModelManager) {}

  @Delete(':id')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'model_delete',
    summary: 'Delete model',
    description:
      'Deletes the specified model for the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the model to delete\n',
  })
  @ApiNoContentResponse({ description: 'Model deleted successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - Model does not exist.
      - Invalid or unavailable parameters.
   `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description:
      'Forbidden. User does not have permission to delete this model.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiParam({ name: 'id', description: 'UUID of the model to delete.' })
  async delete(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<void> {
    await this.provider
      .getBy({ user: { id: request.user.id }, id: id })
      .then((entity) => this.manager.delete(entity));
  }
}
