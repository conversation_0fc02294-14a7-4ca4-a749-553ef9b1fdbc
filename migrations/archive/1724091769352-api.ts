import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1724091769352 implements MigrationInterface {
  name = 'Api1724091769352';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "transaction"
            ADD "context" json
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "transaction" DROP COLUMN "context"
        `);
  }
}
