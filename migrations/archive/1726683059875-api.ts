import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1726683059875 implements MigrationInterface {
  name = 'Api1726683059875';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "user_account"
            ALTER COLUMN "system_version" DROP DEFAULT
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "user_account"
            ALTER COLUMN "system_version"
            SET DEFAULT '2'
        `);
  }
}
