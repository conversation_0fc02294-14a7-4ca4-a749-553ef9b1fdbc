import {
    Column,
    CreateDate<PERSON>olumn,
    DeleteDateColumn,
    Entity,
    Index,
    OneToMany,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { ModelOrganizationEntity } from '../../model/entity/model-organization.entity';
import { OrganizationSubscriptionEntity } from './organization-subscription.entity';
import { OrganizationUserEntity } from './organization-user.entity';

@Entity('organization')
export class OrganizationEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ type: 'text', nullable: true })
  website?: string;

  @Column({ nullable: true, unique: true })
  @Index('idx_organization_handle')
  handle?: string;

  @Column({ type: 'text', nullable: true })
  profilePicture?: string;

  @Column({ nullable: false, default: 0 })
  imagesGenerated: number;

  @Column({ nullable: false, default: 0 })
  imagesAvailable: number;

  @Column({ nullable: false, default: 0 })
  modelsAvailable: number;

  @Column({ default: true })
  includeWatermarks: boolean;

  @Column({ nullable: false, default: 1 })
  members: number;

  @Column({ nullable: false, default: 1 })
  seatsPurchased: number;

  @Column({ nullable: true })
  stripeCustomerId?: string;

  @OneToMany(
    () => OrganizationUserEntity,
    (organizationUserEntity) => organizationUserEntity.organization,
    { cascade: false, eager: false },
  )
  users: OrganizationUserEntity[];

  @OneToMany(
    () => OrganizationSubscriptionEntity,
    (OrganizationSubscription) => OrganizationSubscription.organization,
    { cascade: false, eager: true },
  )
  subscriptions: OrganizationSubscriptionEntity[];

  @OneToMany(
    () => ModelOrganizationEntity,
    (modelOrganization) => modelOrganization.organization,
    { cascade: false, eager: false },
  )
  modelShares: ModelOrganizationEntity[];

  @Column({ default: true })
  isActive: boolean;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt?: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
