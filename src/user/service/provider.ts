import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import {
  FindManyOptions,
  In,
  LessThanOrEqual,
  Like,
  MoreThanOrEqual,
  Repository,
} from 'typeorm';
import { AbstractProvider } from '../../core/service/abstract.provider';
import { UserEntity } from '../entity/user.entity';

@Injectable()
export class UserProvider extends AbstractProvider<UserEntity> {
  constructor(
    @InjectRepository(UserEntity)
    repository: Repository<UserEntity>,
    logger: Logger,
  ) {
    super(repository, logger);
  }

  countMonthlyStatistics(year: number, month: number) {
    // return this.repository.query(`
    //   SELECT EXTRACT(MONTH FROM created_at) AS month, EXTRACT (DAY FROM created_at) AS day,
    //   COUNT (*) AS amount FROM user_account WHERE created_at BETWEEN '${year}-${month}-01' AND '${year2}-${month2}-01'
    //   GROUP BY 1, 2
    //   ORDER BY 1, 2;
    //   `);

    const start = `${year}-${String(month).padStart(2, '0')}-01`;
    return this.repository.query(`
      WITH days AS (
        SELECT generate_series(
          DATE '${start}',
          (DATE '${start}' + INTERVAL '1 month - 1 day'),
          INTERVAL '1 day'
        )::date AS day
      )
      SELECT
        EXTRACT(DAY FROM days.day)::int AS day,
        COALESCE(COUNT(ua.id), 0) AS amount
      FROM days
      LEFT JOIN user_account ua
        ON DATE(ua.created_at) = days.day
      GROUP BY days.day
      ORDER BY days.day;
    `);
  }

  async countUserStatistics(initialDate: Date, endDate: Date) {
    const result = await this.repository.query(`
      SELECT COUNT(*) AS amount FROM user_account WHERE created_at BETWEEN '${initialDate.toISOString()}' AND '${endDate.toISOString()}';
      `);
    return parseInt(result[0].amount, 10);
  }

  countYearlyStatistics(year: number) {
    const start = `${year}-01-01`;
    const end = `${year + 1}-01-01`;
    return this.repository.query(
      `
    SELECT 
      EXTRACT(YEAR FROM created_at) AS year, 
      EXTRACT(MONTH FROM created_at) AS month,
      COUNT(*) AS amount 
    FROM user_account 
    WHERE created_at >= '${start}' AND created_at < '${end}'
    GROUP BY year, month
    ORDER BY year, month;`,
    );
  }

  findLeaderboard(
    start: string,
    end: string,
    page: number,
    limit: number,
    username: string = null,
  ) {
    let usernameFilter = '';

    if (username) {
      usernameFilter = `AND user_account.username = '${username}'`;
    }
    const customQuery = this.repository.query(`
      SELECT user_account.username AS username,
      user_account.profile_picture AS profile_picture,
      user_account.id AS userid,
      COUNT(image_completion_like.created_at)
      AS amount FROM image_completion_like
      LEFT JOIN image_completion ON image_completion_like.image_completion_id = image_completion.id
      LEFT JOIN user_account ON image_completion.user_id = user_account.id
      WHERE image_completion_like.created_at BETWEEN '${start}' AND '${end}' ${usernameFilter}
      GROUP BY user_account.id
      ORDER BY COUNT(image_completion_like.created_at BETWEEN '${start}' AND '${end}') DESC
      LIMIT ${limit}
      OFFSET ${(page - 1) * limit};
      `);
    return customQuery;
  }

  prepareFindManyOptions(
    criteria: any,
    page: number,
    limit: number,
    sortBy?: string,
    sortOrder = 'ASC',
  ): FindManyOptions<UserEntity> {
    if (criteria.userSignupCodeIds) {
      criteria.userSignupCodeId = In(criteria.userSignupCodeIds);
      delete criteria.userSignupCodeIds;
    }

    if (criteria.username) {
      criteria.username = Like(`%${criteria.username}%`);
    }

    const numericFields = [
      'followersCount',
      'followingCount',
      'imagesGenerated',
      'imagesAvailable',
      'modelsAvailable',
    ];

    numericFields.forEach((field) => {
      const minField = `${field}Min`;
      const maxField = `${field}Max`;

      if (criteria[minField] !== undefined) {
        criteria[field] = MoreThanOrEqual(criteria[minField]);
        delete criteria[minField];
      }

      if (criteria[maxField] !== undefined) {
        criteria[field] = {
          ...(criteria[field] || {}),
          ...LessThanOrEqual(criteria[maxField]),
        };
        delete criteria[maxField];
      }
    });

    const parentOptions = super.prepareFindManyOptions(
      criteria,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    return {
      ...parentOptions,
    };
  }
}
