import {
  Controller,
  Delete,
  Get,
  HttpCode,
  ParseUUIDPipe,
  Post,
  Put,
  Request,
} from '@nestjs/common';
import {
  Body,
  Param,
} from '@nestjs/common/decorators/http/route-params.decorator';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiNotFoundResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { In } from 'typeorm/find-options/operator/In';
import { UserProvider } from '../../user/service/provider';
import { SubscriptionDto } from '../dto/subscription.dto';
import { SubscriptionRequest } from '../dto/subscription.request';
import { SubscriptionStatusEnum } from '../entity/subscription-status.enum';
import { SubscriptionManager } from '../service/manager';
import { SubscriptionProvider } from '../service/provider';
import { SubscriptionRequestManager } from '../service/request-manager';
import { SubscriptionResponseMapper } from '../service/response-mapper';

@ApiTags('subscription')
@Controller('subscriptions')
export class SubscriptionController {
  constructor(
    private manager: SubscriptionManager,
    private requestManager: SubscriptionRequestManager,
    private responseMapper: SubscriptionResponseMapper,
    private subscriptionProvider: SubscriptionProvider,
    private userProvider: UserProvider,
  ) {}

  @Post()
  @ApiOperation({
    operationId: 'subscription_create',
    summary: 'Create a new subscription',
    description:
      'Creates a new subscription for the current user.\n\n' +
      'Requirements:\n' +
      '- User must be authenticated\n\n' +
      'Required Parameters:\n' +
      '- creditPackageId: UUID of the credit package to subscribe to.',
  })
  @ApiBody({
    type: SubscriptionRequest,
    description: 'Subscription creation parameters.',
  })
  @ApiOkResponse({
    type: SubscriptionDto,
    description: 'Subscription created successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`creditPackageId\`: Must be a valid UUID.
  `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async create(
    @Body() requestBody: SubscriptionRequest,
    @Request() request,
  ): Promise<SubscriptionDto> {
    const user = await this.userProvider.get(request.user.id);

    const subscription = await this.requestManager.create(requestBody, user);
    return await this.responseMapper.map(subscription);
  }

  @Get()
  @ApiOperation({
    operationId: 'subscription_get_current',
    summary: 'Get current active subscription',
    description:
      'Retrieves the current active subscription for the authenticated user.',
  })
  @ApiOkResponse({
    type: SubscriptionDto,
    description: 'Current active subscription.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - User does not have an active subscription.
  `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async find(@Request() request): Promise<SubscriptionDto> {
    const subscription =
      await this.subscriptionProvider.getCurrentlyActiveSubscriptionForUser(
        request.user.id,
      );

    return this.responseMapper.map(subscription);
  }

  @Get('/pending')
  @ApiOperation({
    operationId: 'subscription_list_pending',
    summary: 'List pending subscriptions',
    description:
      'Retrieves a list of pending subscriptions for the authenticated user.',
  })
  @ApiOkResponse({
    isArray: true,
    type: SubscriptionDto,
    description: 'List of pending subscriptions.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - Invalid or unavailable parameters.
  `,
  })
  async findPending(@Request() request): Promise<SubscriptionDto[]> {
    const subscriptions =
      await this.subscriptionProvider.findPendingSubscriptionsForUser(
        request.user.id,
      );

    return await this.responseMapper.mapMultiple(subscriptions);
  }

  @Put(':id/stripe-status')
  @ApiOperation({
    operationId: 'subscription_update_stripe_status',
    summary: 'Update Stripe status for subscription',
    description:
      'Updates the Stripe checkout or subscription status for a specific subscription.\n\n' +
      'Requirements:\n' +
      '- User must be authenticated\n' +
      '- Subscription must exist and belong to the user' +
      '\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the subscription\n',
  })
  @ApiOkResponse({
    type: SubscriptionDto,
    description: 'Subscription Stripe status updated.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
  `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiNotFoundResponse({
    description: 'Not Found. The subscription could not be found.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async stripeUpdateStatus(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ) {
    const subscription = await this.subscriptionProvider.getBy({
      user: { id: request.user.id },
      id: id,
    });

    if (subscription.stripeCheckoutSessionId) {
      await this.manager.updateStripeCheckoutStatus(subscription);
    } else {
      await this.manager.updateStripeSubscriptionStatus(subscription);
    }

    return await this.responseMapper.map(subscription);
  }

  @Delete(':id')
  @ApiOperation({
    operationId: 'subscription_cancel',
    summary: 'Cancel a subscription',
    description:
      'Cancels a specific subscription for the authenticated user.\n\n' +
      'Requirements:\n' +
      '- User must be authenticated\n' +
      '- Subscription must be active, pending, or new' +
      '\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the subscription\n',
  })
  @ApiNoContentResponse({ description: 'Subscription cancelled successfully.' })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiNotFoundResponse({
    description: 'Not Found. The subscription could not be found.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
  `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @HttpCode(204)
  async delete(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<void> {
    const subscription = await this.subscriptionProvider.getBy({
      id: id,
      user: { id: request.user.id },
      status: In([
        SubscriptionStatusEnum.ACTIVE,
        SubscriptionStatusEnum.PENDING,
        SubscriptionStatusEnum.NEW,
      ]),
    });

    await this.manager.cancelSubscription(subscription);
  }
}
