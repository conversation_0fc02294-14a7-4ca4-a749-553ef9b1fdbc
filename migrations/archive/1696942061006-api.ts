import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1696942061006 implements MigrationInterface {
  name = 'Api1696942061006';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "image_completion"
            ADD "regenerations" integer NOT NULL DEFAULT '0'
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion"
            ADD "has_private_model" boolean NOT NULL DEFAULT false
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "image_completion" DROP COLUMN "has_private_model"
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion" DROP COLUMN "regenerations"
        `);
  }
}
