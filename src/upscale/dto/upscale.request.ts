import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON>N<PERSON>ber,
  IsOptional,
  IsString,
  IsUrl,
  IsUUI<PERSON>,
  <PERSON>,
  <PERSON>,
  ValidateIf,
} from 'class-validator';

export class UpscaleRequest {
  @ApiProperty()
  @IsString()
  @IsOptional()
  prompt: string;

  @ApiProperty()
  @IsUUID()
  @IsOptional()
  @ValidateIf((o) => !o.imageUrl)
  imageCompletionId: string;

  @ApiProperty()
  @IsUUID()
  @IsOptional()
  organizationId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @ValidateIf((o) => !o.imageCompletionId)
  imageUrl: string;

  @ApiProperty({ default: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1, { message: 'Strength must be at least 1' })
  @Max(5, { message: 'Strength must be at most 5' })
  strength = 1;

  @ApiProperty()
  @IsOptional()
  @IsUrl()
  webhookUrl?: string;
}
