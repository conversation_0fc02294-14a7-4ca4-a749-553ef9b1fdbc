import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1729529219260 implements MigrationInterface {
  name = 'Api1729529219260';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "board"
            ADD "thumbnail" text
        `);
    await queryRunner.query(`
            CREATE TYPE "public"."board_visibility_enum" AS ENUM('public', 'private', 'hidden')
        `);
    await queryRunner.query(`
            ALTER TABLE "board"
            ADD "visibility" "public"."board_visibility_enum" NOT NULL DEFAULT 'public'
        `);
    await queryRunner.query(`
            ALTER TABLE "board"
            ADD "challenge_deadline" TIMESTAMP
        `);
    await queryRunner.query(`
            ALTER TABLE "board_image_completion" DROP CONSTRAINT "FK_6ef39641c1c7448978e37df557d"
        `);
    await queryRunner.query(`
            ALTER TABLE "board_image_completion"
            ALTER COLUMN "image_completion_id"
            SET NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "board_image_completion"
            ADD CONSTRAINT "FK_6ef39641c1c7448978e37df557d" FOREIGN KEY ("image_completion_id") REFERENCES "image_completion"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "board_image_completion" DROP CONSTRAINT "FK_6ef39641c1c7448978e37df557d"
        `);
    await queryRunner.query(`
            ALTER TABLE "board_image_completion"
            ALTER COLUMN "image_completion_id" DROP NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "board_image_completion"
            ADD CONSTRAINT "FK_6ef39641c1c7448978e37df557d" FOREIGN KEY ("image_completion_id") REFERENCES "image_completion"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "board" DROP COLUMN "challenge_deadline"
        `);
    await queryRunner.query(`
            ALTER TABLE "board" DROP COLUMN "visibility"
        `);
    await queryRunner.query(`
            DROP TYPE "public"."board_visibility_enum"
        `);
    await queryRunner.query(`
            ALTER TABLE "board" DROP COLUMN "thumbnail"
        `);
  }
}
