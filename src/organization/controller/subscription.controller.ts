import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  NotFoundException,
  Param,
  Post,
  Put,
  Request,
  UnauthorizedException,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/auth/service/jwt-auth.guard';

import { UserCreditBalanceProvider } from 'src/subscription/service/user-credit-balance.provider';
import { In, Raw } from 'typeorm';
import { OrganizationCreditBalanceDto } from '../dto/organization-credit-balance.dto';
import { OrganizationSubscriptionDto } from '../dto/organization-subscription.dto';
import { OrganizationSubscriptionRequest } from '../dto/organization-subscription.request';
import { SubscriptionStatusEnum } from '../enum/subscription-status.enum';
import { OrganizationCreditBalanceResponseMapper } from '../service/organization-credit-balance.response-mapper';
import { OrganizationSubscriptionRequestManager } from '../service/organization-subscription-request.manager';
import { OrganizationSubscriptionManager } from '../service/organization-subscription.manager';
import { OrganizationSubscriptionProvider } from '../service/organization-subscription.provider';
import { OrganizationSubscriptionResponseMapper } from '../service/organization-subscription.response-mapper';
import { OrganizationUserProvider } from '../service/organization-user.provider';
import { OrganizationProvider } from '../service/provider';

@ApiTags('organization / subscription')
@Controller('organizations/:organizationId/subscriptions')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class SubscriptionController {
  constructor(
    private readonly requestManager: OrganizationSubscriptionRequestManager,
    private readonly manager: OrganizationSubscriptionManager,
    private readonly responseMapper: OrganizationSubscriptionResponseMapper,
    private readonly provider: OrganizationSubscriptionProvider,
    private readonly organizationProvider: OrganizationProvider,
    private readonly organizationUserProvider: OrganizationUserProvider,
    private organizationCreditBalanceResponseMapper: OrganizationCreditBalanceResponseMapper,
    private userCreditBalanceProvider: UserCreditBalanceProvider,
  ) {}

  @Post()
  @ApiOperation({
    operationId: 'organization_subscription_create',
    summary: 'Create organization subscription',
    description:
      'Creates a new subscription for the specified organization.\n\n' +
      'Requirements:\n' +
      '- User must be a manager of the organization\n' +
      '- Organization must exist' +
      'Required Parameters:\n' +
      '- organizationId: UUID of the organization to subscribe to\n\n' +
      'Optional Parameters:\n' +
      '- creditPackageId: UUID of the credit package to subscribe to\n',
  })
  @ApiOkResponse({
    type: OrganizationSubscriptionDto,
    description: 'Organization subscription created successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`organizationId\`: Must be a valid UUID.
      - \`creditPackageId\`: Must be a valid UUID.
   `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User is not a manager of the organization.',
  })
  @ApiNotFoundResponse({
    description: 'Not Found. The organization could not be found.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. An unexpected error occurred.',
  })
  async create(
    @Param('organizationId') organizationId: string,
    @Body() request: OrganizationSubscriptionRequest,
    @Request() httpRequest,
  ) {
    const organizationSubscription = await this.requestManager.create(
      organizationId,
      request,
      httpRequest.user,
    );

    return await this.responseMapper.map(organizationSubscription);
  }

  @Get('credit-balances')
  @ApiOperation({
    operationId: 'organization_subscription_credit_balances',
    summary: 'List organization credit balances',
    description:
      'Retrieves a list of credit balances for the specified organization.\n\n' +
      'Requirements:\n' +
      '- User must be a member of the organization' +
      '\n\n' +
      'Required Parameters:\n' +
      '- organizationId: UUID of the organization to retrieve credit balances for',
  })
  @ApiOkResponse({
    type: OrganizationCreditBalanceDto,
    isArray: true,
    description: 'List of organization credit balances.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not a member of the organization.',
  })
  @ApiNotFoundResponse({
    description: 'Not Found. The organization could not be found.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. An unexpected error occurred.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`organizationId\`: Must be a valid UUID.
   `,
  })
  async findCredits(
    @Request() request,
    @Param('organizationId') organizationId: string,
  ): Promise<OrganizationCreditBalanceDto[]> {
    const isMember = await this.organizationUserProvider.isMember(
      request.user.id,
      organizationId,
    );
    if (!isMember) {
      throw new UnauthorizedException('organization.not_member');
    }
    const now = new Date();
    const entities = await this.userCreditBalanceProvider.findBy(
      {
        organizationId: organizationId,
        expiresAt: Raw((alias) => `( ${alias} IS NULL OR ${alias} > :now )`, {
          now,
        }),
      },
      1,
      100,
    );

    return await this.organizationCreditBalanceResponseMapper.mapMultiple(
      entities,
    );
  }

  @Get('current')
  @ApiOperation({
    operationId: 'organization_subscription_current',
    summary: 'Get current organization active subscription',
    description:
      'Retrieves the current active subscription for the specified organization.\n\n' +
      'Requirements:\n' +
      '- User must be a member of the organization' +
      '\n\n' +
      'Required Parameters:\n' +
      '- organizationId: UUID of the organization to retrieve the current active subscription for',
  })
  @ApiOkResponse({
    type: OrganizationSubscriptionDto,
    description: 'Current active organization subscription.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not a member of the organization.',
  })
  @ApiNotFoundResponse({
    description:
      'Not Found. No active subscription found for the organization.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`organizationId\`: Must be a valid UUID.
   `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. An unexpected error occurred.',
  })
  async getCurrentSubscription(
    @Param('organizationId') organizationId: string,
    @Request() httpRequest,
  ) {
    const organization = await this.organizationProvider.get(organizationId);

    const isMember = await this.organizationUserProvider.isMember(
      httpRequest.user.id,
      organization.id,
    );

    if (!isMember) {
      throw new UnauthorizedException('organization.not_member');
    }

    const organizationSubscription =
      await this.provider.findCurrentSubscription(organizationId);

    if (!organizationSubscription) {
      throw new NotFoundException('organization.subscription.not_found');
    }

    return await this.responseMapper.map(organizationSubscription);
  }

  @Get('pending')
  @ApiOperation({
    operationId: 'organization_subscription_pending',
    summary: 'Get current organization pending subscription',
    description:
      'Retrieves the current pending subscription for the specified organization.\n\n' +
      'Requirements:\n' +
      '- User must be a manager of the organization' +
      '\n\n' +
      'Required Parameters:\n' +
      '- organizationId: UUID of the organization to retrieve the current pending subscription for',
  })
  @ApiOkResponse({
    type: OrganizationSubscriptionDto,
    description: 'Current pending organization subscription.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not a manager of the organization.',
  })
  @ApiNotFoundResponse({
    description:
      'Not Found. No pending subscription found for the organization.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`organizationId\`: Must be a valid UUID.
   `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. An unexpected error occurred.',
  })
  async getPendingSubscription(
    @Param('organizationId') organizationId: string,
    @Request() httpRequest,
  ) {
    const organization = await this.organizationProvider.get(organizationId);

    const isManager = await this.organizationUserProvider.isManager(
      httpRequest.user.id,
      organization.id,
    );

    if (!isManager) {
      throw new UnauthorizedException('organization.not_manager');
    }

    const organizationSubscription = await this.provider.getBy({
      organization: { id: organizationId },
      status: In([SubscriptionStatusEnum.PENDING, SubscriptionStatusEnum.NEW]),
    });

    if (!organizationSubscription) {
      throw new NotFoundException('organization.subscription.not_found');
    }

    return await this.responseMapper.map(organizationSubscription);
  }

  @Put(':subscriptionId/stripe-status')
  @ApiOperation({
    operationId: 'organization_subscription_update_stripe_status',
    summary: 'Update Stripe status for organization subscription',
    description:
      'Updates the Stripe checkout status for a specific organization subscription.\n\n' +
      'Requirements:\n' +
      '- User must be a manager of the organization\n' +
      '- Subscription must exist' +
      '\n\n' +
      'Required Parameters:\n' +
      '- organizationId: UUID of the organization\n' +
      '- subscriptionId: UUID of the subscription',
  })
  @ApiOkResponse({
    type: OrganizationSubscriptionDto,
    description: 'Organization subscription Stripe status updated.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`organizationId\`: Must be a valid UUID.
      - \`subscriptionId\`: Must be a valid UUID.
  `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not a manager of the organization.',
  })
  @ApiNotFoundResponse({
    description: 'Not Found. The subscription could not be found.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. An unexpected error occurred.',
  })
  async stripeUpdateStatus(
    @Param('organizationId') organizationId: string,
    @Param('subscriptionId') subscriptionId: string,
    @Request() request,
  ) {
    const organization = await this.organizationProvider.get(organizationId);

    const isManager = await this.organizationUserProvider.isManager(
      request.user.id,
      organization.id,
    );

    if (!isManager) {
      throw new UnauthorizedException('organization.not_manager');
    }

    const subscription = await this.provider.getBy({
      organization: { id: organizationId },
      id: subscriptionId,
    });

    if (!subscription) {
      throw new NotFoundException('organization.subscription.not_found');
    }

    await this.manager.updateStripeCheckoutStatus(subscription);

    return await this.responseMapper.map(subscription);
  }

  @Get()
  @ApiOperation({
    operationId: 'organization_subscription_list',
    summary: 'List organization subscriptions',
    description:
      'Retrieves a list of all subscriptions for the specified organization.\n\n' +
      'Requirements:\n' +
      '- User must be a manager of the organization' +
      '\n\n' +
      'Required Parameters:\n' +
      '- organizationId: UUID of the organization',
  })
  @ApiOkResponse({
    type: [OrganizationSubscriptionDto],
    description: 'List of organization subscriptions.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not a manager of the organization.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`organizationId\`: Must be a valid UUID.
  `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. An unexpected error occurred.',
  })
  async list(
    @Param('organizationId') organizationId: string,
    @Request() request,
  ) {
    const organization = await this.organizationProvider.get(organizationId);

    const isManager = await this.organizationUserProvider.isManager(
      request.user.id,
      organization.id,
    );

    if (!isManager) {
      throw new UnauthorizedException('organization.not_manager');
    }

    const subscriptions = await this.provider.findBy(
      {
        organization: { id: organizationId },
      },
      1,
      999,
    );

    return Promise.all(
      subscriptions.map((sub) => this.responseMapper.map(sub)),
    );
  }

  @Delete(':subscriptionId')
  @ApiOperation({
    operationId: 'organization_subscription_cancel',
    summary: 'Cancel organization subscription',
    description:
      'Cancels a specific subscription for the organization.\n\n' +
      'Requirements:\n' +
      '- User must be a manager of the organization\n' +
      '- Subscription must be active, pending, or new' +
      '\n\n' +
      'Required Parameters:\n' +
      '- organizationId: UUID of the organization\n' +
      '- subscriptionId: UUID of the subscription',
  })
  @ApiNoContentResponse({
    description: 'Organization subscription cancelled successfully.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not a manager of the organization.',
  })
  @ApiNotFoundResponse({
    description: 'Not Found. The subscription could not be found.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`organizationId\`: Must be a valid UUID.
      - \`subscriptionId\`: Must be a valid UUID.
  `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. An unexpected error occurred.',
  })
  @HttpCode(204)
  async cancel(
    @Param('organizationId') organizationId: string,
    @Param('subscriptionId') subscriptionId: string,
    @Request() request,
  ): Promise<void> {
    const organization = await this.organizationProvider.get(organizationId);

    const isManager = await this.organizationUserProvider.isManager(
      request.user.id,
      organization.id,
    );

    if (!isManager) {
      throw new UnauthorizedException('organization.not_manager');
    }

    const subscription = await this.provider.getBy({
      organization: { id: organizationId },
      id: subscriptionId,
      status: In([
        SubscriptionStatusEnum.ACTIVE,
        SubscriptionStatusEnum.PENDING,
        SubscriptionStatusEnum.NEW,
      ]),
    });

    if (!subscription) {
      throw new NotFoundException('organization.subscription.not_found');
    }

    await this.manager.cancelSubscription(subscription);
  }
}
