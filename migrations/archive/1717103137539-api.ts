import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1717103137539 implements MigrationInterface {
    name = 'Api1717103137539'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "model_version"
            ADD "prompt" text
        `);
        await queryRunner.query(`
            UPDATE "model_version" SET prompt = ( SELECT prompt FROM model WHERE id = model_version.model_id )
        `);
        await queryRunner.query(`
            UPDATE model_version 
            SET prompt = '<lora:'||model_id::VARCHAR||'_'||version::VARCHAR||': 0.80> '||model_id::VARCHAR||'_'||version::VARCHAR
            WHERE version > 1;
        `);
        await queryRunner.query(`
            UPDATE model_version 
            SET prompt = '<lora:'||model_id::VARCHAR||': 0.80> '||model_id::VARCHAR
            WHERE version = 1;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "model_version" DROP COLUMN "prompt"
        `);
    }
}
