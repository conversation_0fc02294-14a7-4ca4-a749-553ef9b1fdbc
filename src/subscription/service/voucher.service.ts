import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { Logger } from 'nestjs-pino';
import Stripe from 'stripe';

export interface VoucherValidationResult {
  isValid: boolean;
  promotionCode?: Stripe.PromotionCode;
  discountAmount?: number;
  discountPercent?: number;
  errorMessage?: string;
}

@Injectable()
export class VoucherService {
  constructor(
    @Inject('Stripe') private readonly stripe: Stripe,
    private readonly logger: Logger,
  ) {}

  /**
   * Validate a voucher/promotion code with Stripe
   */
  async validateVoucher(
    voucherCode: string,
    customerId?: string,
  ): Promise<VoucherValidationResult> {
    this.logger.log('voucher.validate', {
      voucherCode,
      customerId,
    });

    try {
      // Search for the promotion code
      const promotionCodes = await this.stripe.promotionCodes.list({
        code: voucherCode,
        active: true,
        limit: 1,
      });

      if (promotionCodes.data.length === 0) {
        return {
          isValid: false,
          errorMessage: 'voucher.invalid_code',
        };
      }

      const promotionCode = promotionCodes.data[0];

      // Check if the promotion code is active and not expired
      const now = Math.floor(Date.now() / 1000);
      if (
        !promotionCode.active ||
        (promotionCode.expires_at && promotionCode.expires_at < now)
      ) {
        return {
          isValid: false,
          errorMessage: 'voucher.expired_code',
        };
      }

      // Check if the promotion code has usage limits
      if (
        promotionCode.max_redemptions &&
        promotionCode.times_redeemed >= promotionCode.max_redemptions
      ) {
        return {
          isValid: false,
          errorMessage: 'voucher.usage_limit_reached',
        };
      }

      // If a customer is provided, check if they can use this promotion code
      if (customerId) {
        const canUse = await this.checkCustomerEligibility(
          promotionCode,
          customerId,
        );
        if (!canUse.eligible) {
          return {
            isValid: false,
            errorMessage: canUse.reason,
          };
        }
      }

      // Extract discount information
      const coupon = promotionCode.coupon;
      let discountAmount: number | undefined;
      let discountPercent: number | undefined;

      if (coupon.amount_off) {
        discountAmount = coupon.amount_off;
      }

      if (coupon.percent_off) {
        discountPercent = coupon.percent_off;
      }

      this.logger.log('voucher.valid', {
        voucherCode,
        discountAmount,
        discountPercent,
        couponId: coupon.id,
      });

      return {
        isValid: true,
        promotionCode,
        discountAmount,
        discountPercent,
      };
    } catch (error) {
      this.logger.error('voucher.validation_error', {
        error,
        voucherCode,
      });

      return {
        isValid: false,
        errorMessage: 'voucher.validation_failed',
      };
    }
  }

  /**
   * Calculate the discount amount for a given price
   */
  calculateDiscountAmount(
    originalAmount: number,
    validationResult: VoucherValidationResult,
  ): number {
    if (!validationResult.isValid || !validationResult.promotionCode) {
      return 0;
    }

    const coupon = validationResult.promotionCode.coupon;

    // Fixed amount discount
    if (coupon.amount_off) {
      return Math.min(coupon.amount_off, originalAmount);
    }

    // Percentage discount
    if (coupon.percent_off) {
      const discountAmount = Math.round(
        (originalAmount * coupon.percent_off) / 100,
      );
      
      // For percentage coupons, the max discount is typically handled by Stripe
      // We'll return the calculated percentage discount
      return discountAmount;
    }

    return 0;
  }

  /**
   * Check if a customer is eligible for a promotion code
   */
  private async checkCustomerEligibility(
    promotionCode: Stripe.PromotionCode,
    customerId: string,
  ): Promise<{ eligible: boolean; reason?: string }> {
    try {
      // Check customer-specific restrictions
      if (promotionCode.restrictions?.first_time_transaction) {
        // Check if this customer has made payments before
        const invoices = await this.stripe.invoices.list({
          customer: customerId,
          status: 'paid',
          limit: 1,
        });

        if (invoices.data.length > 0) {
          return {
            eligible: false,
            reason: 'voucher.not_first_time_customer',
          };
        }
      }

      // Check minimum amount restrictions
      if (promotionCode.restrictions?.minimum_amount) {
        // This would need to be checked against the actual invoice amount
        // For now, we'll consider it eligible and let Stripe handle it during checkout
      }

      // Check if customer has already used this promotion code
      const subscriptions = await this.stripe.subscriptions.list({
        customer: customerId,
        limit: 100,
      });

      for (const subscription of subscriptions.data) {
        if (subscription.discount?.promotion_code === promotionCode.id) {
          return {
            eligible: false,
            reason: 'voucher.already_used',
          };
        }
      }

      return { eligible: true };
    } catch (error) {
      this.logger.error('voucher.eligibility_check_error', {
        error,
        promotionCodeId: promotionCode.id,
        customerId,
      });

      // On error, allow the voucher and let Stripe handle validation during payment
      return { eligible: true };
    }
  }

  /**
   * Apply a promotion code to a Stripe subscription update
   */
  async applyVoucherToSubscriptionUpdate(
    subscriptionUpdateParams: Stripe.SubscriptionUpdateParams,
    voucherCode: string,
    customerId?: string,
  ): Promise<Stripe.SubscriptionUpdateParams> {
    const validation = await this.validateVoucher(voucherCode, customerId);

    if (!validation.isValid) {
      throw new BadRequestException(validation.errorMessage || 'voucher.invalid');
    }

    // Apply the promotion code to the subscription update
    return {
      ...subscriptionUpdateParams,
      promotion_code: validation.promotionCode!.id,
    };
  }
}
