import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEnum,
  IsIn,
  IsOptional,
  IsString,
  IsUrl,
  IsUUID,
} from 'class-validator';
import { QueueEnum } from '../entity/image-completion.entity';

export class ImageCompletionRequest {
  @ApiProperty()
  @IsString()
  @IsOptional()
  prompt: string;

  @ApiProperty()
  @IsUUID()
  @IsOptional()
  imageCompletionId?: string;

  @ApiProperty()
  @IsUUID()
  @IsOptional()
  organizationId?: string;

  @ApiProperty()
  @IsOptional()
  @IsEnum(QueueEnum, {
    message: 'Queue must be a valid value',
  })
  @ApiProperty({ enum: QueueEnum })
  queue?: QueueEnum = QueueEnum.SLOW;

  @ApiProperty({ type: 'object', required: false })
  @IsOptional()
  generationSettings?: any;

  // eslint-disable-next-line @typescript-eslint/no-inferrable-types
  @ApiProperty({ default: false })
  @IsBoolean()
  hideFromUserProfile: boolean = false;

  @ApiProperty()
  @IsOptional()
  hasWatermark?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsUrl()
  webhookUrl?: string;

  @IsOptional()
  @ApiProperty()
  @IsIn([2, 3])
  systemVersion?: number;
}
