import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { UserOAuthDto } from '../dto/user-oauth.dto';
import * as jwksClient from 'jwks-rsa';
import * as util from 'util';
import axios from 'axios';

interface AppleJwtToken {
  header: {
    kid: string;
  };
  payload: any;
}

@Injectable()
export class OAuthService {
  constructor(
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {}

  async validateGoogleToken(accessToken: string): Promise<UserOAuthDto> {
    const googleTokenInfoUrl = `https://www.googleapis.com/oauth2/v1/tokeninfo?access_token=${accessToken}`;
    const response = await axios.get(googleTokenInfoUrl);

    if (response.status !== 200) {
      throw new Error('Failed to validate Google token');
    }

    return {
      oAuthId: response.data.user_id,
      name: response.data.name,
      email: response.data.email,
    };
  }

  async validateFacebookToken(accessToken: string): Promise<UserOAuthDto> {
    const facebookAccessToken =
      this.configService.get<string>('FACEBOOK_CLIENT_ID') +
      '|' +
      this.configService.get<string>('FACEBOOK_CLIENT_SECRET');

    const facebookDebugTokenUrl =
      'https://graph.facebook.com/debug_token' +
      `?input_token=${accessToken}` +
      `&access_token=${facebookAccessToken}`;

    const tokenResponse = await axios.get(facebookDebugTokenUrl);

    if (
      tokenResponse.status !== 200 ||
      tokenResponse.data.data.is_valid !== true
    ) {
      throw new Error('Failed to validate Facebook token');
    }

    const profileUrl =
      'https://graph.facebook.com/v17.0/me?fields=name,email' +
      `&access_token=${accessToken}`;

    const profileResponse = await axios.get(profileUrl);

    return {
      oAuthId: profileResponse.data.id,
      name: profileResponse.data.name,
      email: profileResponse.data.email,
    };
  }

  async validateAppleToken(accessToken: string): Promise<UserOAuthDto> {
    const client = jwksClient({
      jwksUri: 'https://appleid.apple.com/auth/keys',
    });

    const decodedHeader = this.jwtService.decode(accessToken, {
      complete: true,
    }) as AppleJwtToken;
    const getSigningKey = util.promisify(client.getSigningKey);
    const key = await getSigningKey(decodedHeader.header.kid);
    const signingKey = key.getPublicKey();

    const decodedToken = this.jwtService.verify(accessToken, {
      publicKey: signingKey,
    });

    return {
      oAuthId: decodedToken.sub,
      name: decodedToken.name,
      email: decodedToken.email,
    };
  }
}
