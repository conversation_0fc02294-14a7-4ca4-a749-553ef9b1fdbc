import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { AbstractProvider } from 'src/core/service/abstract.provider';
import { UserEntity } from 'src/user/entity/user.entity';
import { OrganizationUserProvider } from 'src/organization/service/organization-user.provider';
import {
  Brackets,
  FindManyOptions,
  FindOneOptions,
  ILike,
  Like,
  Raw,
  Repository,
  SelectQueryBuilder,
} from 'typeorm';
import {
  ModelEntity,
  PrivacyEnum as ModelPrivacyEnum,
  PrivacyEnum,
} from '../entity/model.entity';
import { ModelOrganizationProvider } from './model-organization.provider';
import { StatusEnum } from '../enum/status.enum';

@Injectable()
export class ModelProvider extends AbstractProvider<ModelEntity> {
  constructor(
    @InjectRepository(ModelEntity)
    repository: Repository<ModelEntity>,
    logger: Logger,
    private modelOrganizationProvider: ModelOrganizationProvider,
    private organizationUserProvider: OrganizationUserProvider,
  ) {
    super(repository, logger);
  }

  /**
   * Check if a model is accessible to a user through ownership, public status, or organization membership
   */
  async isModelAccessibleToUser(
    modelId: string,
    user: UserEntity,
  ): Promise<boolean> {
    const model = await this.repository.findOne({
      where: { id: modelId },
      relations: { user: true },
    });

    if (!model) {
      return false;
    }

    // Model owner can always access
    if (model.userId === user.id) {
      return true;
    }

    // Public models are accessible to everyone
    if (model.privacy === ModelPrivacyEnum.PUBLIC) {
      return true;
    }

    // Check organization-based access for private models
    if (model.privacy === ModelPrivacyEnum.PRIVATE) {
      // Get user's organization IDs
      const userOrganizations =
        await this.organizationUserProvider.getUserOrganizations(user.id);
      const userOrgIds = userOrganizations.map((org) => org.organizationId);

      if (userOrgIds.length > 0) {
        // Check if model is shared with any of user's organizations
        const organizationIds =
          await this.modelOrganizationProvider.getOrganizationIdsForModel(
            modelId,
          );
        return organizationIds.some((orgId) => userOrgIds.includes(orgId));
      }
    }

    return false;
  }

  countMonthlyStatistics(year: number, month: number) {
    // return this.repository.query(`
    //   SELECT EXTRACT(MONTH FROM created_at) AS month, EXTRACT (DAY FROM created_at) AS day,
    //   COUNT (*) AS amount FROM model WHERE created_at BETWEEN '${year}-${month}-01' AND '${year2}-${month2}-01'
    //   GROUP BY 1, 2
    //   ORDER BY 1, 2;
    //   `);

    const start = `${year}-${String(month).padStart(2, '0')}-01`;
    return this.repository.query(`
      WITH days AS (
        SELECT generate_series(
          DATE '${start}',
          (DATE '${start}' + INTERVAL '1 month - 1 day'),
          INTERVAL '1 day'
        )::date AS day
      )
      SELECT
        EXTRACT(DAY FROM days.day)::int AS day,
        COALESCE(COUNT(m.id), 0) AS amount
      FROM days
      LEFT JOIN model m
        ON DATE(m.created_at) = days.day
      GROUP BY days.day
      ORDER BY days.day;
    `);
  }

  async countModelStatistics(initialDate: Date, endDate: Date) {
    const result = await this.repository.query(`
      SELECT COUNT(*) AS amount FROM model WHERE created_at >= '${initialDate.toISOString()}' AND created_at < '${endDate.toISOString()}';
    `);
    return parseInt(result[0].amount, 10);
  }

  countYearlyStatistics(year: number) {
    const start = `${year}-01-01`;
    const end = `${year + 1}-01-01`;
    const customQuery = this.repository.query(
      `
      SELECT
        EXTRACT(YEAR FROM created_at) AS year,
        EXTRACT(MONTH FROM created_at) AS month,
        COUNT(*) AS amount
      FROM model
      WHERE created_at >= '${start}' AND created_at < '${end}'
      GROUP BY year, month
      ORDER BY year, month;`,
    );
    return customQuery;
  }

  async findBy(
    criteria: any,
    page: number,
    limit: number,
    sortBy = 'createdAt',
    sortOrder: 'ASC' | 'DESC' | 'RANDOM' = 'ASC',
  ): Promise<ModelEntity[]> {
    const queryBuilder = this.prepareQueryBuilder(criteria);

    queryBuilder.take(limit);
    queryBuilder.skip((page - 1) * limit);

    if (sortOrder === 'RANDOM') {
      queryBuilder.addSelect('RANDOM()', 'random_order');
      queryBuilder.orderBy('random_order', 'ASC');
    } else {
      queryBuilder.orderBy(`model.${sortBy}`, sortOrder);
    }

    return await queryBuilder.getMany();
  }

  async findByNames(
    modelNames: string[],
    user: UserEntity,
  ): Promise<ModelEntity[]> {
    // Get user's organization IDs for organization-based access
    const userOrganizations =
      await this.organizationUserProvider.getUserOrganizations(user.id);
    const userOrganizationIds = userOrganizations.map(
      (org) => org.organizationId,
    );

    const queryBuilder = this.repository
      .createQueryBuilder('model')
      .leftJoinAndSelect('model.user', 'user')
      .leftJoinAndSelect('model.modelOrganizations', 'modelOrganizations')
      .where('model.name IN (:...names)', { names: modelNames });

    if (userOrganizationIds.length > 0) {
      // Include organization-shared models
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('model.user.id = :userId', { userId: user.id })
            .orWhere('model.privacy = :privacy', {
              privacy: ModelPrivacyEnum.PUBLIC,
            })
            .orWhere('modelOrganizations.organizationId IN (:...orgIds)', {
              orgIds: userOrganizationIds,
            });
        }),
      );
    } else {
      // No organization access, only own models and public models
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('model.user.id = :userId', { userId: user.id }).orWhere(
            'model.privacy = :privacy',
            { privacy: ModelPrivacyEnum.PUBLIC },
          );
        }),
      );
    }

    return await queryBuilder.getMany();
  }

  async countBy(criteria: any): Promise<number> {
    const queryBuilder = this.prepareQueryBuilder(criteria);

    return await queryBuilder.getCount();
  }

  createBaseQueryBuilder(): SelectQueryBuilder<ModelEntity> {
    return this.repository
      .createQueryBuilder('model')
      .innerJoinAndSelect('model.user', 'user')
      .leftJoinAndSelect('model.modelOrganizations', 'modelOrganizations');
  }

  prepareQueryBuilder(criteria: any): SelectQueryBuilder<ModelEntity> {
    const where = { ...criteria };
    const queryBuilder = this.createBaseQueryBuilder();

    if (Array.isArray(criteria)) {
      criteria.forEach((crit, index) => {
        const conditions = this.prepareConditions(crit, `criteria_${index}`);
        if (conditions.length > 0) {
          const [where, parameters] = conditions;
          if (index === 0) {
            queryBuilder.where(`(${where})`, parameters);
          } else {
            queryBuilder.orWhere(`(${where})`, parameters);
          }
        }
      });

      return queryBuilder;
    }

    const conditions = this.prepareConditions(where, 'criteria');
    if (conditions.length > 0) {
      const [where, parameters] = conditions;
      queryBuilder.where(where, parameters);
    }

    return queryBuilder;
  }

  prepareConditions(criteria: any, alias: string): [string, any] {
    const conditions: string[] = [];
    const parameters: any = {};

    Object.keys(criteria).forEach((key) => {
      if (key === 'userId') {
        conditions.push(`model.userId = :${alias}_userId`);
        parameters[`${alias}_userId`] = criteria[key];
        return;
      }

      if (key === 'username') {
        conditions.push(`user.username = :${alias}_username`);
        parameters[`${alias}_username`] = criteria[key];
        return;
      }

      if (key === 'systemVersion') {
        conditions.push(
          `CAST(model.systemVersions AS jsonb) @> :${alias}_systemVersion`,
        );
        parameters[`${alias}_systemVersion`] = JSON.stringify([criteria[key]]);
        return;
      }

      if (key === 'organizationId') {
        conditions.push(
          `modelOrganizations.organizationId = :${alias}_organizationId`,
        );
        parameters[`${alias}_organizationId`] = criteria[key];
        return;
      }

      if (key === 'organizationIds') {
        conditions.push(
          `modelOrganizations.organizationId IN (:...${alias}_organizationIds)`,
        );
        parameters[`${alias}_organizationIds`] = criteria[key];
        return;
      }

      if (key === 'search') {
        conditions.push(`model.name LIKE :${alias}_search`);
        parameters[`${alias}_search`] = `%${criteria[key]}%`;
        return;
      }

      conditions.push(`model.${key} = :${alias}_${key}`);
      parameters[`${alias}_${key}`] = criteria[key];
    });

    return [conditions.join(' AND '), parameters];
  }

  prepareFindOneOptions(criteria: any): FindOneOptions {
    if (criteria.userId) {
      criteria.user = { id: criteria.userId };
      delete criteria.userId;
    }

    if (criteria.username) {
      criteria.user = { username: criteria.username };
      delete criteria.username;
    }

    return {
      where: criteria,
      relations: {
        user: true,
      },
    };
  }

  prepareFindManyOptions(
    criteria: any,
    page: number,
    limit: number,
    sortBy?: string,
    sortOrder = 'ASC',
  ): FindManyOptions<ModelEntity> {
    const parentOptions = super.prepareFindManyOptions(
      criteria,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    criteria = Array.isArray(criteria)
      ? criteria.map((crit) => this.prepareCriteria(crit))
      : this.prepareCriteria(criteria);

    return {
      ...parentOptions,
      where: criteria,
      relations: {
        user: true,
      },
    };
  }

  prepareCriteria(criteria: any): any {
    if (criteria.search) {
      criteria.name = Like(`${criteria.search}%`);
      delete criteria.search;
    }

    criteria.user = {};

    if (criteria.userId) {
      criteria.user.id = criteria.userId;
      delete criteria.userId;
    }

    if (criteria.username) {
      criteria.user.username = criteria.username;
      delete criteria.username;
    }

    if (criteria.type) {
      criteria.type = ILike(`%${criteria.type}%`);
    }

    if (criteria.description) {
      criteria.description = ILike(`%${criteria.description}%`);
    }

    if (criteria.systemVersion) {
      criteria.systemVersions = Raw(
        (alias) => `CAST(${alias} AS jsonb) @> :value`,
        { value: JSON.stringify([criteria.systemVersion]) },
      );

      delete criteria.systemVersion;
    }

    return criteria;
  }

  async findTopModelUsers(limit = 10): Promise<any[]> {
    const result = await this.repository
      .createQueryBuilder('model')
      .select('model.userId', 'userId')
      .addSelect('user.username', 'username')
      .addSelect('user.email', 'email')
      .addSelect('COUNT(model.id)::integer', 'model_count')
      .addSelect(
        'SUM(CASE WHEN model.privacy = :publicPrivacy THEN 1 ELSE 0 END)::integer',
        'public_models',
      )
      .addSelect(
        'SUM(CASE WHEN model.privacy = :privatePrivacy THEN 1 ELSE 0 END)::integer',
        'private_models',
      )
      .innerJoin('model.user', 'user')
      .where('model.status = :status', { status: StatusEnum.AVAILABLE })
      .andWhere('model.deletedAt IS NULL')
      .setParameter('publicPrivacy', PrivacyEnum.PUBLIC)
      .setParameter('privatePrivacy', PrivacyEnum.PRIVATE)
      .groupBy('model.userId')
      .addGroupBy('user.username')
      .addGroupBy('user.email')
      .orderBy('model_count', 'DESC')
      .limit(limit)
      .getRawMany();

    return result.map((item) => ({
      userId: item.userId,
      username: item.username,
      email: item.email,
      modelCount: parseInt(item.model_count, 10),
      publicModels: parseInt(item.public_models, 10),
      privateModels: parseInt(item.private_models, 10),
    }));
  }
}
