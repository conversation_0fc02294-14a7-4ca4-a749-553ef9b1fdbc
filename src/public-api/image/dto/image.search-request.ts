import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsA<PERSON>y,
  IsBoolean,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { BaseSearchRequest } from 'src/core/dto/base.search-request';
import { PrivacyEnum } from 'src/model/entity/model.entity';
import { StatusEnum } from 'src/model/enum/status.enum';

export class ImageSearchRequest extends BaseSearchRequest {
  @IsOptional()
  @IsUUID()
  @ApiProperty({ required: false })
  userId: string;

  @IsOptional()
  @IsString()
  @ApiProperty({ required: false })
  username: string;

  @IsOptional()
  @IsEnum(PrivacyEnum)
  @Transform(({ value }) => value.toLowerCase())
  @ApiProperty({ enum: PrivacyEnum })
  privacy?: PrivacyEnum;

  @IsOptional()
  @IsEnum(StatusEnum)
  @Transform(({ value }) => value.toLowerCase())
  @ApiProperty({ enum: StatusEnum })
  status?: StatusEnum;

  @IsOptional()
  @IsNumber()
  @ApiProperty({ required: false })
  systemVersion: number;

  @IsOptional()
  @IsBoolean()
  @ApiProperty({ required: false })
  isHot: boolean;

  // eslint-disable-next-line @typescript-eslint/no-inferrable-types
  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  includeNsfw: boolean = false;

  @IsOptional()
  @IsBoolean()
  @ApiProperty({ required: false })
  onlyFollowing: boolean;

  @IsOptional()
  @IsArray()
  @ApiProperty({ required: false })
  modelIds: string[];
}
