import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { AbstractProvider } from 'src/core/service/abstract.provider';
import {
  FindManyOptions,
  FindOneOptions,
  In,
  MoreThan,
  Repository,
} from 'typeorm';
import { OrganizationSubscriptionEntity } from '../entity/organization-subscription.entity';
import { SubscriptionStatusEnum } from '../enum/subscription-status.enum';

@Injectable()
export class OrganizationSubscriptionProvider extends AbstractProvider<OrganizationSubscriptionEntity> {
  constructor(
    @InjectRepository(OrganizationSubscriptionEntity)
    protected readonly repository: Repository<OrganizationSubscriptionEntity>,
    protected readonly logger: Logger,
  ) {
    super(repository, logger);
  }

  async findPendingSubscriptionsForOrganization(
    organizationId: string,
  ): Promise<OrganizationSubscriptionEntity[]> {
    return this.repository.find({
      where: {
        organization: { id: organizationId },
        status: In([
          SubscriptionStatusEnum.NEW,
          SubscriptionStatusEnum.PENDING,
        ]),
      },
      relations: ['organization', 'creditPackage'],
      order: {
        createdAt: 'DESC',
      },
    });
  }

  async findCurrentSubscription(
    organizationId: string,
  ): Promise<OrganizationSubscriptionEntity | null> {
    return this.repository.findOne({
      where: {
        organization: { id: organizationId },
        status: In([
          SubscriptionStatusEnum.ACTIVE,
          SubscriptionStatusEnum.INACTIVE,
        ]),
        expiresAt: MoreThan(new Date()),
      },
      relations: ['organization', 'creditPackage'],
      order: {
        createdAt: 'DESC',
      },
    });
  }

  async getByStripeCheckoutSessionId(
    stripeCheckoutSessionId: string,
  ): Promise<OrganizationSubscriptionEntity | null> {
    return this.repository.findOne({
      where: { stripeCheckoutSessionId },
      relations: ['organization', 'creditPackage'],
    });
  }

  prepareFindOneOptions(
    criteria: any,
  ): FindOneOptions<OrganizationSubscriptionEntity> {
    return {
      where: criteria,
      relations: {
        creditPackage: true,
        organization: true,
      },
    };
  }

  prepareFindManyOptions(
    criteria: any,
    page: number,
    limit: number,
    sortBy?: string,
    sortOrder = 'ASC',
  ): FindManyOptions<OrganizationSubscriptionEntity> {
    const parentOptions = super.prepareFindManyOptions(
      criteria,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    return {
      ...parentOptions,
      relations: {
        creditPackage: true,
        organization: true,
      },
    };
  }
}
