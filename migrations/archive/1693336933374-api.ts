import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1693336933374 implements MigrationInterface {
    name = 'Api1693336933374'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "model"
            ALTER COLUMN "is_active"
            SET DEFAULT true
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "model"
            ALTER COLUMN "is_active"
            SET DEFAULT false
        `);
    }

}
