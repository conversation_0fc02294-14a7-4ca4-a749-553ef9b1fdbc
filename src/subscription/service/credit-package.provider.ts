import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { AbstractProvider } from 'src/core/service/abstract.provider';
import { Repository } from 'typeorm';
import { CreditPackageEntity } from '../entity/credit-package.entity';

@Injectable()
export class CreditPackageProvider extends AbstractProvider<CreditPackageEntity> {
  constructor(
    @InjectRepository(CreditPackageEntity)
    repository: Repository<CreditPackageEntity>,
    logger: Logger,
  ) {
    super(repository, logger);
  }

  /**
   * Find yearly credit packages by target entity
   */
  async findYearlyPackages(
    targetEntity: string = 'user',
  ): Promise<CreditPackageEntity[]> {
    return this.repository.find({
      where: {
        targetEntity,
        expiresAfterMonths: 12,
        isVisible: true,
        isRecurring: true,
      },
      order: {
        price: 'ASC',
      },
    });
  }

  /**
   * Find monthly credit packages by target entity
   */
  async findMonthlyPackages(
    targetEntity: string = 'user',
  ): Promise<CreditPackageEntity[]> {
    return this.repository.find({
      where: {
        targetEntity,
        expiresAfterMonths: 1,
        isVisible: true,
        isRecurring: true,
      },
      order: {
        price: 'ASC',
      },
    });
  }

  /**
   * Find the yearly variant of a monthly package by name pattern
   */
  async findYearlyVariant(
    monthlyPackageName: string,
  ): Promise<CreditPackageEntity | null> {
    const yearlyName = `${monthlyPackageName} - Yearly`;
    return this.repository.findOne({
      where: {
        name: yearlyName,
        isVisible: true,
      },
    });
  }

  /**
   * Find the monthly variant of a yearly package by name pattern
   */
  async findMonthlyVariant(
    yearlyPackageName: string,
  ): Promise<CreditPackageEntity | null> {
    const monthlyName = yearlyPackageName.replace(' - Yearly', '');
    return this.repository.findOne({
      where: {
        name: monthlyName,
        isVisible: true,
      },
    });
  }

  /**
   * Check if a package is yearly based on expiration months
   */
  isYearlyPackage(creditPackage: CreditPackageEntity): boolean {
    return creditPackage.expiresAfterMonths === 12;
  }

  /**
   * Check if a package is monthly based on expiration months
   */
  isMonthlyPackage(creditPackage: CreditPackageEntity): boolean {
    return creditPackage.expiresAfterMonths === 1;
  }
}
