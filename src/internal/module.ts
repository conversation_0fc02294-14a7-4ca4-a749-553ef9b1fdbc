import { DynamicModule, forwardRef, Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { CoreModule } from 'src/core/core.module';
import { jwtConfig } from '../auth/config/jwt.config';
import { MailerController } from './controller/mailer.controller';
import { UserStatsController } from './controller/user-stats.controller';
import { ModelModule } from 'src/model/module';
import { TopModelUsersResponseMapper } from './service/top-model-users.response-mapper';

@Module({
  imports: [
    PassportModule,
    CoreModule,
    forwardRef(() => ModelModule),
    JwtModule.register(jwtConfig),
  ],
  providers: [TopModelUsersResponseMapper],
})
export class InternalModule {
  static register(enableControllers: boolean): DynamicModule {
    return {
      module: InternalModule,
      controllers: enableControllers
        ? [User<PERSON>tatsController, MailerController]
        : [],
    };
  }
}
