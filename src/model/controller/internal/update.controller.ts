import {
  <PERSON>,
  HttpCode,
  Param,
  ParseUUIDPipe,
  <PERSON>,
  Put,
} from '@nestjs/common';
import { Body } from '@nestjs/common/decorators/http/route-params.decorator';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { ModelDto } from '../../dto/model.dto';
import { ModelInternalRequest } from '../../dto/model.internal-request';
import { ModelProvider } from '../../service/provider';
import { ModelRequestManager } from '../../service/request-manager';
import { ModelResponseMapper } from '../../service/response-mapper';

@ApiTags('model / internal')
@Controller('internal/models')
export class UpdateController {
  constructor(
    private provider: ModelProvider,
    private requestManager: ModelRequestManager,
    private responseMapper: ModelResponseMapper,
  ) {}

  @Patch(':id')
  @ApiOperation({
    operationId: 'internal_model_update',
    summary: 'Update internal model',
    description:
      'Updates the specified model for internal use.\n\n' +
      'Required Query Parameters:\n' +
      '- id: UUID of the model to update\n\n' +
      'Optional Body Parameters:\n' +
      '- name: New name of the model\n' +
      '- trainingMode: New training mode of the model\n' +
      '- type: New type of the model\n' +
      '- class: New class of the model\n' +
      '- description: New description of the model\n' +
      '- website: New website of the model\n' +
      '- webhookUrl: New webhook URL of the model\n' +
      '- settings: New settings of the model\n' +
      '- isActive: New active status of the model\n' +
      '- status: New status of the model\n' +
      '- progress: New progress of the model\n' +
      '- prompt_v1: New prompt v1 of the model\n' +
      '- prompt: New prompt of the model\n' +
      '- usages: New number of usages of the model\n' +
      '- likes: New number of likes of the model\n' +
      '- reports: New number of reports of the model\n' +
      '- version: New version of the model\n' +
      '- storagePath: New storage path of the model\n' +
      '- generationData: New generation data of the model\n' +
      '- thumbnail: New thumbnail of the model\n',
  })
  @ApiBody({
    type: ModelInternalRequest,
    description: 'Model update parameters.',
  })
  @ApiOkResponse({ type: ModelDto, description: 'Model updated successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - \`status\`: Must be a valid value from the \`StatusEnum\`.
      - \`progress\`: Must be a valid number between 0 and 100.
      - \`prompt_v1\`: Must be a valid string.
      - \`prompt\`: Must be a valid string.
      - \`usages\`: Must be a valid number.
      - \`likes\`: Must be a valid number.
      - \`reports\`: Must be a valid number.
      - \`version\`: Must be a valid number.
      - \`storagePath\`: Must be a valid string.
      - \`generationData\`: Must be a valid string.
      - \`thumbnail\`: Must be a valid string.
      - \`trainingMode\`: Must be a valid string.
      - Model does not exist.
      - Invalid or unavailable parameters.
      - Failed to analyze request body.
    `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async update(
    @Body() requestBody: ModelInternalRequest,
    @Param() params,
  ): Promise<ModelDto> {
    return this.provider
      .get(params.id)
      .then((entity) => this.requestManager.updateInternal(entity, requestBody))
      .then((entity) => this.responseMapper.mapInternal(entity));
  }

  @Put(':id/training')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'internal_model_train',
    summary: 'Start internal model training',
    description:
      'Starts training for the specified model (internal use).\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the model to train\n',
  })
  @ApiNoContentResponse({ description: 'Model training started successfully.' })
  @ApiBadRequestResponse({ description: '`id`: Must be a valid UUID.' })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async trainModel(@Param('id', new ParseUUIDPipe()) id: string) {
    const entity = await this.provider.get(id);

    await this.requestManager.trainModel(entity);
  }

  @Put(':id/training/:systemVersion')
  @ApiOperation({
    operationId: 'internal_model_start_training',
    summary: 'Start training for a specific system version',
    description:
      'Starts training for the specified model and system version (internal use).\n\n' +
      'Required Query Parameters:\n' +
      '- id: UUID of the model to train\n' +
      '- systemVersion: System version number\n\n' +
      'Required Body Parameters:\n' +
      '- status: New status of the model\n' +
      '- progress: New progress of the model\n' +
      '- prompt_v1: New prompt v1 of the model\n' +
      '- prompt: New prompt of the model\n' +
      '- usages: New number of usages of the model\n' +
      '- likes: New number of likes of the model\n' +
      '- reports: New number of reports of the model\n' +
      '- version: New version of the model\n' +
      '- storagePath: New storage path of the model\n' +
      '- generationData: New generation data of the model\n' +
      '- thumbnail: New thumbnail of the model\n',
  })
  @ApiBody({ type: ModelInternalRequest, description: 'Training parameters.' })
  @ApiOkResponse({
    type: ModelDto,
    description: 'Model training started successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - \`systemVersion\`: Must be a valid number.
      - \`status\`: Must be a valid value from the \`StatusEnum\`.
      - \`progress\`: Must be a valid number between 0 and 100.
      - \`prompt_v1\`: Must be a valid string.
      - \`prompt\`: Must be a valid string.
      - \`usages\`: Must be a valid number.
      - \`likes\`: Must be a valid number.
      - \`reports\`: Must be a valid number.
      - \`version\`: Must be a valid number.
      - \`storagePath\`: Must be a valid string.
      - \`generationData\`: Must be a valid string.
      - \`thumbnail\`: Must be a valid string.
      - Model does not exist.
      - Invalid or unavailable parameters.
      - Failed to analyze request body.
    `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async startTraining(
    @Param('id', new ParseUUIDPipe()) id: string,
    @Param('systemVersion') systemVersion: number,
  ): Promise<ModelDto> {
    return this.provider
      .get(id)
      .then((entity) =>
        this.requestManager.startTraining(entity, systemVersion),
      )
      .then((entity) => this.responseMapper.mapInternal(entity));
  }

  @Put(':id/training-reset')
  @ApiOperation({
    operationId: 'internal_model_reset_training',
    summary: 'Reset training status for internal model',
    description:
      'Resets the training status for the specified model (internal use).\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the model to reset training status\n',
  })
  @ApiOkResponse({
    type: ModelDto,
    description: 'Model training status reset successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - Model does not exist.
    `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async resetTraining(
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<ModelDto> {
    return this.provider
      .get(id)
      .then((entity) => this.requestManager.reseTrainingStatus(entity))
      .then((entity) => this.responseMapper.mapInternal(entity));
  }

  @Put(':id/training/:systemVersion/conclusion')
  @ApiOperation({
    operationId: 'internal_model_finish_training',
    summary: 'Finish training for a specific system version',
    description:
      'Marks the specified model and system version as finished training (internal use).\n\n' +
      'Required Query Parameters:\n' +
      '- id: UUID of the model\n' +
      '- systemVersion: System version number\n\n' +
      'Required Body Parameters:\n' +
      '- status: New status of the model\n' +
      '- progress: New progress of the model\n' +
      '- prompt_v1: New prompt v1 of the model\n' +
      '- prompt: New prompt of the model\n' +
      '- usages: New number of usages of the model\n' +
      '- likes: New number of likes of the model\n' +
      '- reports: New number of reports of the model\n' +
      '- version: New version of the model\n' +
      '- storagePath: New storage path of the model\n' +
      '- generationData: New generation data of the model\n' +
      '- thumbnail: New thumbnail of the model\n',
  })
  @ApiBody({ type: ModelInternalRequest, description: 'Training parameters.' })
  @ApiOkResponse({
    type: ModelDto,
    description: 'Model training finished successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - \`systemVersion\`: Must be a valid number.
      - \`status\`: Must be a valid value from the \`StatusEnum\`.
      - \`progress\`: Must be a valid number between 0 and 100.
      - \`prompt_v1\`: Must be a valid string.
      - \`prompt\`: Must be a valid string.
      - \`usages\`: Must be a valid number.
      - \`likes\`: Must be a valid number.
      - \`reports\`: Must be a valid number.
      - \`version\`: Must be a valid number.
      - \`storagePath\`: Must be a valid string.
      - \`generationData\`: Must be a valid string.
      - \`thumbnail\`: Must be a valid string.
      - Model does not exist.
      - Invalid or unavailable parameters.
      - Failed to analyze request body.
    `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async finishTraining(
    @Param('id', new ParseUUIDPipe()) id: string,
    @Param('systemVersion') systemVersion: number,
    @Body() requestBody: ModelInternalRequest,
  ): Promise<ModelDto> {
    return this.provider
      .get(id)
      .then((entity) =>
        this.requestManager.finishTraining(entity, systemVersion, requestBody),
      )
      .then((entity) => this.responseMapper.mapInternal(entity));
  }

  @Put(':id/training/:systemVersion/failure')
  @ApiOperation({
    operationId: 'internal_model_fail_training',
    summary: 'Fail training for a specific system version',
    description:
      'Marks the specified model and system version as failed training (internal use).\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the model\n' +
      '- systemVersion: System version number\n',
  })
  @ApiBody({ type: ModelInternalRequest, description: 'Training parameters.' })
  @ApiOkResponse({ type: ModelDto, description: 'Model training failed.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - \`systemVersion\`: Must be a valid number between 2 and 3.
      - Model does not exist.
      - Invalid or unavailable parameters.
      - Failed to analyze request body.
    `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async failedTraining(
    @Param('id', new ParseUUIDPipe()) id: string,
    @Param('systemVersion') systemVersion: number,
  ): Promise<ModelDto> {
    return this.provider
      .get(id)
      .then((entity) => this.requestManager.failTraining(entity, systemVersion))
      .then((entity) => this.responseMapper.mapInternal(entity));
  }

  @Patch(':id/version/:version')
  @ApiOperation({
    operationId: 'internal_model_set_version',
    summary: 'Restore internal model version',
    description:
      'Restores the specified version of the model (internal use).\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the model\n' +
      '- version: Version number to restore\n',
  })
  @ApiOkResponse({
    type: ModelDto,
    description: 'Model version restored successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - \`version\`: Must be a valid number.
      - Model does not exist.
      - Invalid or unavailable parameters.
   `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async setVersion(
    @Param('id', new ParseUUIDPipe()) id: string,
    @Param('version') version: number,
  ): Promise<ModelDto> {
    return this.provider
      .get(id)
      .then((entity) => this.requestManager.restoreVersion(entity, version))
      .then((entity) => this.responseMapper.mapInternal(entity));
  }

  @Put(':id/retraining/:systemVersion')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'internal_model_retrain_v3',
    summary: 'Retrain internal model for system version',
    description:
      'Retrains the specified model for the given system version (internal use).\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the model\n' +
      '- systemVersion: System version number\n',
  })
  @ApiNoContentResponse({
    description: 'Model retraining started successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - \`systemVersion\`: Must be a valid number.
      - Model does not exist.
      - Invalid or unavailable parameters.
  `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async retrainV3(
    @Param('id') id: string,
    @Param('systemVersion') systemVersion: number,
  ): Promise<void> {
    const entity = await this.provider.get(id);

    //await this.manager.retrainSystemVersion(entity, systemVersion);
  }
}
