import { DynamicModule, Module, forwardRef } from '@nestjs/common';
import { AuthModule } from 'src/auth/auth.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CoreModule } from '../core/core.module';
import { UserModule } from '../user/user.module';
import { CreateController } from './controller/create.controller';
import { DeleteController } from './controller/delete.controller';
import { ReadController } from './controller/read.controller';
import { UserImageEntity } from './entity/user-image.entity';
import { UserImageManager } from './service/user-image.manager';
import { UserImageProvider } from './service/user-image.provider';
import { UserImageRequestManager } from './service/user-image.request-manager';
import { UserImageResponseMapper } from './service/user-image.response-mapper';

@Module({
  imports: [
    CoreModule,
    forwardRef(() => AuthModule),
    forwardRef(() => UserModule),
    TypeOrmModule.forFeature([UserImageEntity]),
  ],
  providers: [
    UserImageManager,
    UserImageProvider,
    UserImageRequestManager,
    UserImageResponseMapper,
  ],
  exports: [
    UserImageManager,
    UserImageProvider,
    UserImageRequestManager,
    UserImageResponseMapper,
  ],
})
export class UserImageModule {
  static register(enableControllers: boolean): DynamicModule {
    return {
      module: UserImageModule,
      controllers: enableControllers ? [
        CreateController,
        ReadController,
        DeleteController,
      ] : [],
    };
  }
}
