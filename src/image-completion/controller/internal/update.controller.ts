import {
  <PERSON>,
  HttpCode,
  Param,
  ParseUUIDPip<PERSON>,
  <PERSON>,
  Put,
} from '@nestjs/common';
import { Body } from '@nestjs/common/decorators/http/route-params.decorator';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiUnauthorizedResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { ImageCompletionDto } from '../../dto/image-completion.dto';
import { ImageCompletionInternalRequest } from '../../dto/image-completion.internal-request';
import { ImageCompletionProvider } from '../../service/provider';
import { ImageCompletionRequestManager } from '../../service/request-manager';
import { ImageCompletionResponseMapper } from '../../service/response-mapper';

@ApiTags('image_completion / internal')
@Controller('internal/image_completions')
export class UpdateController {
  constructor(
    private provider: ImageCompletionProvider,
    private requestManager: ImageCompletionRequestManager,
    private responseMapper: ImageCompletionResponseMapper,
  ) {}

  @Patch(':id')
  @ApiOperation({
    operationId: 'internal_image_completion_update',
    summary: 'Update internal image completion',
    description:
      'Updates the specified image completion for internal use.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the image completion to update\n' +
      'Optional Body Parameters: \n' +
      '- status: New status of the image completion\n' +
      '- progress: New progress of the image completion\n' +
      '- previewImage: New preview image of the image completion\n' +
      '- privacy: New privacy of the image completion\n' +
      '- promptSystem: New prompt system of the image completion\n' +
      '- baseModel: New base model of the image completion\n' +
      '- likes: New number of likes of the image completion\n' +
      '- reports: New number of reports of the image completion\n' +
      '- statusDetail: New status detail of the image completion\n' +
      '- imagePaths: New image paths of the image completion\n' +
      '- hasWatermark: New watermark status of the image completion\n' +
      '- generationSettings: New generation settings of the image completion\n' +
      '- generationData: New generation data of the image completion\n' +
      '- generatedByUnit: New generated by unit of the image completion\n' +
      '- generationSeconds: New generation seconds of the image completion\n' +
      '- isNsfw: New NSFW status of the image completion\n' +
      '- isUnsafe: New unsafe status of the image completion\n' +
      '- isHot: New hot status of the image completion\n' +
      '- isActive: New active status of the image completion\n' +
      '- isResized: New resized status of the image completion\n',
  })
  @ApiBody({
    type: ImageCompletionInternalRequest,
    description: 'Image completion update parameters.',
  })
  @ApiOkResponse({
    type: ImageCompletionDto,
    description: 'Image completion updated successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - Invalid or unavailable parameters.
      - Failed to analyze request body.
      - Image completion does not exist.
      - Image completion is not in a valid state for the requested update.
      - \`status\`: Must be a valid value from the \`StatusEnum\`.
      - \`privacy\`: Must be a valid value from the \`PrivacyEnum\`.
      - \`progress\`: Must be a valid number between 0 and 100.
      - \`previewImage\`: Must be a valid URL.
      - \`promptSystem\`: Must be a valid string.
      - \`baseModel\`: Must be a valid string.
      - \`likes\`: Must be a valid number.
      - \`reports\`: Must be a valid number.
      - \`statusDetail\`: Must be a valid string.
      - \`imagePaths\`: Must be a valid array of strings.
      - \`hasWatermark\`: Must be a valid boolean.
      - \`generationSettings\`: Must be a valid object.
      - \`generationData\`: Must be a valid string.
      - \`generatedByUnit\`: Must be a valid string.
      - \`generationSeconds\`: Must be a valid number.
      - \`isNsfw\`: Must be a valid boolean.
      - \`isUnsafe\`: Must be a valid boolean.
      - \`isHot\`: Must be a valid boolean.
      - \`isActive\`: Must be a valid boolean.
      - \`isResized\`: Must be a valid boolean.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the image completion to update.',
    type: String,
  })
  async update(
    @Body() requestBody: ImageCompletionInternalRequest,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<ImageCompletionDto> {
    const entity = await this.provider.get(id);

    await this.requestManager.updateInternal(entity, requestBody);

    return await this.responseMapper.mapInternal(entity);
  }

  @Put(':id/resizing')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'internal_image_completion_resizing',
    summary: 'Mark image completion as resized',
    description:
      'Marks the specified image completion as resized for internal use.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the image completion to mark as resized\n',
  })
  @ApiNoContentResponse({
    description: 'Image completion marked as resized successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - Image completion does not exist.
      - Image completion is not in a valid state for the requested update.
      - Invalid or unavailable parameters.
      - Failed to analyze request body.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the image completion to mark as resized.',
    type: String,
  })
  async finishedResizing(
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<void> {
    const entity = await this.provider.get(id);

    await this.requestManager.markAsResized(entity);
  }
}
