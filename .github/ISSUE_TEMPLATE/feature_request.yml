name: ✨ Feature Request
description: Propose a new feature or API behavior based on frontend needs
title: "[FEATURE] "
labels: [feature]
body:
  - type: textarea
    id: use_case_and_flow
    attributes:
      label: Use Case & User Flow
      description: Describe the problem this feature solves and the flow the user will go through in the frontend.
      placeholder: |
        e.g., "Users want to browse products by category. On the products page, they select a category from a dropdown, and the list updates to show only matching products."

  - type: textarea
    id: expected_interaction
    attributes:
      label: Expected Frontend/API Interaction
      description: Describe how the frontend is expected to interact with the API (e.g., endpoints, parameters, response structure).
      placeholder: |
        e.g., "Frontend sends GET /products?category_id=123 and expects a JSON array with id, name, price, and image URL."

  - type: textarea
    id: expected_outcomes
    attributes:
      label: Expected Outcomes / Side Effects
      description: What should happen as a result of this feature? What should the user see or be able to do?
      placeholder: |
        e.g., "The product list updates to show only items from the selected category. If no products are found, display an empty state."

