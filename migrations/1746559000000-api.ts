import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1746559000000 implements MigrationInterface {
  name = 'Api1746559000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        UPDATE credit_package 
        SET seats = 10
        WHERE name = 'Enterprise Basic - Monthly';
    `);

    await queryRunner.query(`
        UPDATE credit_package 
        SET seats = 30
        WHERE name = 'Enterprise Basic - Plus';
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        UPDATE credit_package 
        SET seats = NULL
        WHERE name IN ('Enterprise Basic - Monthly', 'Enterprise Basic - Plus');
    `);
  }
}
