# Services

Implement business logic and data access.

## new-module.provider.ts

Handles data retrieval and querying logic.

```typescript
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NewModuleEntity } from '../entity/new-module.entity';

@Injectable()
export class NewModuleProvider {
  constructor(
    @InjectRepository(NewModuleEntity)
    private readonly repository: Repository<NewModuleEntity>,
  ) {}

  async get(id: string): Promise<NewModuleEntity | undefined> {
    return this.repository.findOne({ where: { id } });
  }

  async findBy(
    filters: Partial<NewModuleEntity>,
    page: number,
    limit: number,
    sortBy: string,
    sortOrder: 'ASC' | 'DESC',
  ): Promise<NewModuleEntity[]> {
    return this.repository.find({
      where: filters,
      order: { [sortBy]: sortOrder },
      skip: (page - 1) * limit,
      take: limit,
    });
  }

  async countBy(filters: Partial<NewModuleEntity>): Promise<number> {
    return this.repository.count({ where: filters });
  }
}
```

## new-module.manager.ts

Handles business logic and modifications.

```typescript
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NewModuleEntity } from '../entity/new-module.entity';

@Injectable()
export class NewModuleManager {
  constructor(
    @InjectRepository(NewModuleEntity)
    private repository: Repository<NewModuleEntity>,
  ) {}

  async create(entity: NewModuleEntity): Promise<NewModuleEntity> {
    return this.save(entity);
  }

  async update(entity: NewModuleEntity): Promise<NewModuleEntity> {
    return this.save(entity);
  }

  async delete(entity: NewModuleEntity): Promise<void> {
    await this.repository.softDelete(entity.id);
  }

  async save(entity: NewModuleEntity): Promise<NewModuleEntity> {
    return this.repository.save(entity);
  }
}
```

## new-module.request-manager.ts

Orchestrates request processing and validation.

```typescript
import { Injectable, NotFoundException } from '@nestjs/common';
import { NewModuleEntity } from '../entity/new-module.entity';
import { NewModuleRequest } from '../dto/new-module.request';
import { NewModuleProvider } from './new-module.provider';
import { NewModuleManager } from './new-module.manager';

@Injectable()
export class NewModuleRequestManager {
  constructor(
    private readonly provider: NewModuleProvider,
    private readonly manager: NewModuleManager,
  ) {}

  async create(request: NewModuleRequest, user: any): Promise<NewModuleEntity> {
    const newEntity = new NewModuleEntity();
    // Populate newEntity with data from request and user
    return this.manager.create(newEntity);
  }

  async update(id: string, request: NewModuleRequest, user: any): Promise<NewModuleEntity> {
    const entity = await this.provider.get(id);
    if (!entity) {
      throw new NotFoundException(`Resource with ID ${id} not found`);
    }
    // Update entity with data from request and user
    return this.manager.update(entity);
  }

  async delete(id: string, user: any): Promise<void> {
    const entity = await this.provider.get(id);
    if (!entity) {
      throw new NotFoundException(`Resource with ID ${id} not found`);
    }
    await this.manager.delete(entity);
  }
}
```

## new-module.response-mapper.ts

Maps entities to DTOs for responses.

```typescript
import { Injectable } from '@nestjs/common';
import { NewModuleDto } from '../dto/new-module.dto';
import { NewModuleEntity } from '../entity/new-module.entity';

@Injectable()
export class NewModuleResponseMapper {
  map(entity: NewModuleEntity): NewModuleDto {
    return {
      id: entity.id,
      name: entity.name,
      description: entity.description,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  mapMultiple(entities: NewModuleEntity[]): NewModuleDto[] {
    return entities.map(entity => this.map(entity));
  }
}
```
