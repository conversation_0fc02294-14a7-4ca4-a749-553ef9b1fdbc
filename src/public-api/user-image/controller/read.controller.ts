import {
  <PERSON>,
  Get,
  Param,
  Pa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Request,
  UseGuards,
} from '@nestjs/common';
import {
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiBearerAuth,
  ApiResponse,
  ApiBadRequestResponse,
} from '@nestjs/swagger';
import { JwtIntegrationAuthGuard } from '../../../auth/service/jwt-integration-auth.guard';
import { UserImageProvider } from '../../../user-image/service/user-image.provider';
import { UserImageResponseMapper } from '../../../user-image/service/user-image.response-mapper';
import { UserImageDto } from '../dto/user-image.dto';

@ApiTags('user-images')
@Controller('user-images')
@ApiBearerAuth()
export class ReadController {
  constructor(
    private readonly userImageProvider: UserImageProvider,
    private readonly responseMapper: UserImageResponseMapper,
  ) {}

  @ApiOperation({
    operationId: 'user_image_list',
    summary: 'List user images',
    description:
      'Returns a list of images uploaded by the authenticated user.\n\n' +
      'The list is sorted by creation date in descending order, with a maximum of 100 items per page.',
  })
  @ApiOkResponse({
    type: [UserImageDto],
    description: 'List of user images retrieved successfully',
  })
  @ApiBadRequestResponse({
    description: 'Invalid request parameters',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. The user is not authenticated',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden. User does not have permission to list images',
  })
  @ApiResponse({
    status: 422,
    description: 'Unprocessable Entity. Invalid request parameters',
  })
  @ApiResponse({
    status: 429,
    description: 'Too Many Requests. Please try again later',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. An unexpected error occurred',
  })
  @Get()
  @UseGuards(JwtIntegrationAuthGuard)
  async list(@Request() request): Promise<UserImageDto[]> {
    const images = await this.userImageProvider.findBy(
      { userId: request.user.id },
      1, // page
      100, // limit
      'createdAt', // sortBy
      'DESC', // sortOrder
    );

    return this.responseMapper.mapMany(images);
  }

  @ApiOperation({
    operationId: 'user_image_get',
    summary: 'Get a user image',
    description:
      'Returns details of a specific image from your library.\n\n' +
      'Path Parameters:\n' +
      '- id: UUID of the image',
  })
  @ApiParam({
    name: 'id',
    description: 'Image ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponse({
    type: UserImageDto,
    description: 'The user image was successfully retrieved',
  })
  @ApiBadRequestResponse({
    description: 'Invalid image ID format',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. The user is not authenticated',
  })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden. User does not have permission to access this image',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found. The requested image could not be found',
  })
  @ApiResponse({
    status: 422,
    description: 'Unprocessable Entity. Invalid request parameters',
  })
  @ApiResponse({
    status: 429,
    description: 'Too Many Requests. Please try again later',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. An unexpected error occurred',
  })
  @Get(':id')
  @UseGuards(JwtIntegrationAuthGuard)
  async get(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<UserImageDto> {
    const image = await this.userImageProvider.getBy({
      id,
      userId: request.user.id,
    });

    return this.responseMapper.map(image);
  }
}
