import {
  Column,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { ModelEntity } from '../../model/entity/model.entity';
import { ImageCompletionEntity } from './image-completion.entity';

@Entity('image_completion_model')
export class ImageCompletionModelEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => ImageCompletionEntity)
  imageCompletion: ImageCompletionEntity;

  @Column({ type: 'uuid', nullable: false })
  @Index()
  imageCompletionId: string;

  @ManyToOne(() => ModelEntity, { eager: true })
  model: ModelEntity;

  @Column({ type: 'uuid', nullable: true })
  @Index()
  modelId: string;

  @Column({ default: false })
  markedAsUsed: boolean;
}
