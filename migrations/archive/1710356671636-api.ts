import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1710356671636 implements MigrationInterface {
    name = 'Api1710356671636'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "model"
            ADD "description" text
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "model" DROP COLUMN "description"
        `);
    }

}
