import {
  Controller,
  Delete,
  HttpCode,
  Param,
  ParseUUI<PERSON>ipe,
  Request,
} from '@nestjs/common';
import {
  ApiNoContentResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiBearerAuth,
  ApiResponse,
} from '@nestjs/swagger';
import { UpscaleProvider } from 'src/upscale/service/provider';
import { UpscaleManager } from 'src/upscale/service/manager';

@ApiTags('upscale')
@Controller('upscale')
@ApiBearerAuth()
export class DeleteController {
  constructor(
    private provider: UpscaleProvider,
    private manager: UpscaleManager,
  ) {}

  @ApiOperation({
    operationId: 'upscale_delete',
    summary: 'Delete an upscale',
    description:
      'Deletes an existing upscale request.\n\n' +
      'Path Parameters:\n' +
      '- id: UUID of the upscale to delete',
  })
  @ApiParam({
    name: 'id',
    description: 'The unique identifier of the upscale to delete',
    type: 'string',
    format: 'uuid',
  })
  @ApiNoContentResponse({
    description: 'The upscale was successfully deleted.',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request. The request parameters are invalid.',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden. User does not have permission to delete this upscale.',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found. The upscale to delete could not be found.',
  })
  @ApiResponse({
    status: 422,
    description: 'Unprocessable Entity. Invalid request parameters.',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. An unexpected error occurred.',
  })
  @Delete(':id')
  @HttpCode(204)
  async get(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<void> {
    await this.provider
      .getBy({ user: { id: request.user.id }, id: id })
      .then((entity) => this.manager.delete(entity));
  }
}
