import { Controller, Post, Body, Request } from '@nestjs/common';
import {
  ApiTags,
  ApiBody,
  ApiOkResponse,
  ApiBadRequestResponse,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { BookmarkDto } from '../dto/bookmark.dto';
import { BookmarkRequest } from '../dto/bookmark.request';
import { BookmarkRequestManager } from '../service/bookmark.request-manager';
import { BookmarkResponseMapper } from '../service/bookmark.response-mapper';

@ApiTags('bookmark')
@Controller('bookmarks')
export class CreateController {
  constructor(
    private readonly requestManager: BookmarkRequestManager,
    private readonly responseMapper: BookmarkResponseMapper,
  ) {}

  @Post()
  @ApiOperation({
    operationId: 'bookmark_create',
    summary: 'Create a new bookmark',
    description:
      'Creates a new bookmark for the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- referenceId: ID of the image/model to bookmark\n' +
      '- type: Type of the bookmark (e.g., "image", "model")\n\n' +
      'Returns the created bookmark object.',
  })
  @ApiBody({
    type: BookmarkRequest,
    description: 'Bookmark creation parameters (referenceId, type, etc).',
  })
  @ApiOkResponse({
    type: BookmarkDto,
    description: 'Bookmark created successfully.',
  })
  @ApiBadRequestResponse({
    description: `
      Bad Request. Possible reasons:\n
      - \`referenceId\`: Must be a valid UUID.
      - \`type\`: Must be a valid value ("image", "model").
      - Bookmark already exists for this reference and user.
      - Missing required fields.
      - Invalid or unavailable parameters.
      - Failed to analyze request body.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiInternalServerErrorResponse({
    description:
      'Internal Server Error. Unexpected error occurred during bookmark creation.',
  })
  async create(
    @Body() requestBody: BookmarkRequest,
    @Request() request,
  ): Promise<BookmarkDto> {
    const user = request.user;
    const newBookmark = await this.requestManager.create(requestBody, user);

    return this.responseMapper.map(newBookmark, request.user.id);
  }
}
