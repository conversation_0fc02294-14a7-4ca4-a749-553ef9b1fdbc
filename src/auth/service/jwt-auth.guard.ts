import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { AuthGuard } from '@nestjs/passport';
import { Request } from 'express';
import { Logger } from 'nestjs-pino';
import { UserProvider } from 'src/user/service/provider';
import {
  IS_AUTH_OPTIONAL_KEY,
  IS_PUBLIC_KEY,
} from '../../core/security/public-routes';
import { UserAuthTokenEntity } from '../entity/user-auth-token.entity';
import { UserAuthTokenProvider } from './user-auth-token.provider';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') implements CanActivate {
  constructor(
    protected jwtService: JwtService,
    protected userProvider: UserProvider,
    protected userAuthTokenProvider: UserAuthTokenProvider,
    protected reflector: Reflector,
    protected logger: Logger,
  ) {
    super();
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);

    const isAuthOptional = this.reflector.getAllAndOverride<boolean>(
      IS_AUTH_OPTIONAL_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!token && !isAuthOptional) {
      throw new UnauthorizedException();
    }

    if (token) {
      try {
        const payload = this.jwtService.verify(token);

        request.user = await this.userProvider.get(payload.id);

        const userAuthToken = await this.findUserAuthToken(
          request.user.id,
          token,
        );

        if (!userAuthToken) {
          throw new UnauthorizedException('Invalid token');
        }
      } catch (e) {
        this.logger.verbose('Invalid token', e);
        throw new UnauthorizedException('Invalid token');
      }
    }

    return true;
  }

  findUserAuthToken(
    userId: string,
    token: string,
  ): Promise<UserAuthTokenEntity> {
    return this.userAuthTokenProvider.findOneBy({
      user: { id: userId },
      token: token,
      type: 'access',
    });
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];

    return type === 'Bearer' ? token : undefined;
  }
}
