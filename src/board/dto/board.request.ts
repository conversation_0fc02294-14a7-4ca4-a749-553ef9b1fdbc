import { ApiProperty } from '@nestjs/swagger';
import {
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';
import { SafetyEnum } from '../enum/board-safety.enum';
import { VisibilityEnum } from '../enum/board-visibility.enum';

export class BoardRequest {
  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  name: string;

  @IsOptional()
  @IsString()
  @ApiProperty()
  description?: string;

  @ApiProperty()
  @IsOptional()
  @IsEnum(VisibilityEnum, {
    message: 'Visibility must be a valid value',
  })
  @ApiProperty({ enum: VisibilityEnum })
  visibility?: VisibilityEnum;

  @ApiProperty()
  @IsOptional()
  @IsEnum(SafetyEnum, {
    message: 'Safety must be a valid value',
  })
  @ApiProperty({ enum: SafetyEnum })
  safety?: SafetyEnum;

  @IsOptional()
  @IsDateString()
  @ApiProperty()
  challengeDeadline?: string;
}
