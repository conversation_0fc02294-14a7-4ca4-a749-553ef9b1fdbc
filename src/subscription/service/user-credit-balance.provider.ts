import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { UserEntity } from 'src/user/entity/user.entity';
import {
  Brackets,
  FindManyOptions,
  FindOneOptions,
  MoreThan,
  Repository,
} from 'typeorm';
import { AbstractProvider } from '../../core/service/abstract.provider';
import { CreditTypeEnum } from '../entity/credit-type.enum';
import { UserCreditBalanceEntity } from '../entity/user-credit-balance.entity';

@Injectable()
export class UserCreditBalanceProvider extends AbstractProvider<UserCreditBalanceEntity> {
  constructor(
    @InjectRepository(UserCreditBalanceEntity)
    repository: Repository<UserCreditBalanceEntity>,
    logger: Logger,
  ) {
    super(repository, logger);
  }

  async getBalance(user: UserEntity, creditType: CreditTypeEnum) {
    const balances = await this.repository.find({
      where: [
        {
          user: { id: user.id },
          creditType,
          expiresAt: MoreThan(new Date()),
        },
        {
          user: { id: user.id },
          creditType,
          expiresAt: null,
        },
      ],
    });

    let userBalance = 0;

    balances.forEach((balance) => {
      userBalance += balance.balance;
    });

    return userBalance;
  }

  async getNextExpiring(
    creditType: CreditTypeEnum,
    userId?: string,
    organizationId?: string,
  ): Promise<UserCreditBalanceEntity> {
    const queryBuilder = this.repository.createQueryBuilder('ucb');

    queryBuilder
      .where('ucb.creditType = :creditType', { creditType })
      .andWhere('ucb.balance > 0')
      .andWhere(
        new Brackets((qb) => {
          qb.where('ucb.expiresAt IS NULL').orWhere('ucb.expiresAt > :now', {
            now: new Date(),
          });
        }),
      )
      .orderBy('ucb.expiresAt', 'ASC');

    if (userId) {
      queryBuilder.andWhere('ucb.userId = :userId', { userId });
    }

    if (organizationId) {
      queryBuilder.andWhere('ucb.organizationId = :organizationId', {
        organizationId,
      });
    }

    const userCreditBalance = await queryBuilder.getOne();

    if (!userCreditBalance) {
      throw new Error('user_credit_balance.not_found');
    }

    return userCreditBalance;
  }

  prepareFindOneOptions(criteria: any): FindOneOptions {
    return {
      where: criteria,
      relations: {
        user: true,
      },
    };
  }

  prepareFindManyOptions(
    criteria: any,
    page: number,
    limit: number,
    sortBy?: string,
    sortOrder = 'ASC',
  ): FindManyOptions<UserCreditBalanceEntity> {
    if (criteria.userId) {
      criteria.user = { id: criteria.userId };
      delete criteria.userId;
    }

    const parentOptions = super.prepareFindManyOptions(
      criteria,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    return {
      ...parentOptions,
      relations: {
        user: true,
      },
    };
  }
}
