import {
  Column,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { ModelEntity } from '../../model/entity/model.entity';
import { ImageEditEntity } from './image-edit.entity';

@Entity('image_edit_model')
export class ImageEditModelEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => ImageEditEntity)
  imageEdit: ImageEditEntity;

  @Column({ type: 'uuid', nullable: false })
  @Index()
  imageEditId: string;

  @ManyToOne(() => ModelEntity, { eager: true })
  model: ModelEntity;

  @Column({ type: 'uuid', nullable: true })
  @Index()
  modelId: string;

  @Column({ default: false })
  markedAsUsed: boolean;
}
