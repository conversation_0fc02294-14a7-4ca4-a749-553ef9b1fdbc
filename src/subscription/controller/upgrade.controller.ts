import {
  Controller,
  Post,
  Body,
  Request,
  Get,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiOkResponse,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiInternalServerErrorResponse,
  ApiBody,
} from '@nestjs/swagger';
import { UserProvider } from '../../user/service/provider';
import { SubscriptionUpgradeRequest } from '../dto/subscription-upgrade.request';
import { SubscriptionUpgradePreviewDto } from '../dto/subscription-upgrade-preview.dto';
import { SubscriptionDto } from '../dto/subscription.dto';
import { UpgradeManager } from '../service/upgrade.manager';
import { SubscriptionResponseMapper } from '../service/response-mapper';

@ApiTags('subscription')
@Controller('subscriptions/upgrade')
export class UpgradeController {
  constructor(
    private readonly upgradeManager: UpgradeManager,
    private readonly userProvider: UserProvider,
    private readonly responseMapper: SubscriptionResponseMapper,
  ) {}

  @Get('preview')
  @ApiOperation({
    operationId: 'subscription_upgrade_preview',
    summary: 'Preview subscription upgrade with voucher support',
    description:
      'Previews a subscription upgrade with detailed cost breakdown, proration details, and voucher discount calculations.\n\n' +
      'Requirements:\n' +
      '- User must be authenticated\n' +
      '- User must have an active subscription\n\n' +
      'Required Parameters:\n' +
      '- targetCreditPackageId: UUID of the credit package to upgrade to\n\n' +
      'Optional Parameters:\n' +
      '- voucherCode: Voucher/discount code to apply to the upgrade',
  })
  @ApiOkResponse({
    type: SubscriptionUpgradePreviewDto,
    description: 'Upgrade preview with cost breakdown and voucher discounts.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`targetCreditPackageId\`: Must be a valid UUID.
      - User does not have an active subscription.
      - Target package is invalid or not found.
      - Upgrade is not allowed (e.g., downgrade attempt).
      - Invalid voucher code.
  `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async previewUpgrade(
    @Request() request,
    @Query('targetCreditPackageId') targetCreditPackageId: string,
    @Query('voucherCode') voucherCode?: string,
  ): Promise<SubscriptionUpgradePreviewDto> {
    const user = await this.userProvider.get(request.user.id);
    
    const upgradeRequest: SubscriptionUpgradeRequest = {
      targetCreditPackageId,
      voucherCode,
    };

    return await this.upgradeManager.previewUpgrade(user, upgradeRequest);
  }

  @Post('execute')
  @ApiOperation({
    operationId: 'subscription_upgrade_execute',
    summary: 'Execute subscription upgrade with voucher support',
    description:
      'Executes a subscription upgrade with optional voucher code application.\n\n' +
      'Requirements:\n' +
      '- User must be authenticated\n' +
      '- User must have an active subscription\n' +
      '- For paid upgrades, proration must be confirmed\n\n' +
      'Required Parameters:\n' +
      '- targetCreditPackageId: UUID of the credit package to upgrade to\n\n' +
      'Optional Parameters:\n' +
      '- confirmProration: Whether to confirm proration charges (required for Stripe subscriptions)\n' +
      '- voucherCode: Voucher/discount code to apply to the upgrade',
  })
  @ApiBody({
    type: SubscriptionUpgradeRequest,
    description: 'Upgrade execution parameters with optional voucher code.',
  })
  @ApiOkResponse({
    type: SubscriptionDto,
    description: 'Subscription upgraded successfully with voucher applied.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`targetCreditPackageId\`: Must be a valid UUID.
      - User does not have an active subscription.
      - Target package is invalid or not found.
      - Upgrade is not allowed (e.g., downgrade attempt).
      - Proration confirmation required but not provided.
      - Invalid voucher code.
  `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async executeUpgrade(
    @Body() requestBody: SubscriptionUpgradeRequest,
    @Request() request,
  ): Promise<SubscriptionDto> {
    const user = await this.userProvider.get(request.user.id);

    const upgradedSubscription = await this.upgradeManager.executeUpgrade(
      user,
      requestBody,
    );

    return await this.responseMapper.map(upgradedSubscription);
  }

  @Get('options')
  @ApiOperation({
    operationId: 'subscription_upgrade_options',
    summary: 'Get available upgrade options',
    description:
      'Retrieves all available upgrade options for the current user\'s subscription.\n\n' +
      'Requirements:\n' +
      '- User must be authenticated\n' +
      '- User must have an active subscription',
  })
  @ApiOkResponse({
    isArray: true,
    description: 'List of available upgrade options.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async getUpgradeOptions(@Request() request): Promise<any[]> {
    const user = await this.userProvider.get(request.user.id);
    return await this.upgradeManager.getUpgradeOptions(user);
  }
}
