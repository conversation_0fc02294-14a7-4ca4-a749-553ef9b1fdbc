import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { UserEntity } from '../../user/entity/user.entity';

@Entity('user_image')
export class UserImageEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', nullable: false })
  @Index('idx_user_image_user_id')
  userId: string;

  @ManyToOne(() => UserEntity)
  user: UserEntity;

  @Column({ type: 'text', nullable: false })
  imagePath: string;

  @Column({ type: 'text', nullable: true })
  storageBucket?: string;

  @Column({ type: 'text', nullable: true })
  storagePath?: string;

  @Column({ type: 'text', nullable: true })
  filename?: string;

  @Column({ type: 'text', nullable: true })
  originalFilename?: string;

  @Column({ type: 'text', nullable: true })
  mimeType?: string;

  @Column({ type: 'int', nullable: true })
  fileSize?: number;

  @Column('jsonb', { nullable: true })
  metadata?: Record<string, any>;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt?: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
