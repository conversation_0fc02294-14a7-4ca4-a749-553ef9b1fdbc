import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1699634218170 implements MigrationInterface {
  name = 'Api1699634218170';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "image_completion"
            ADD "generation_seconds" integer NOT NULL DEFAULT '0'
        `);
    await queryRunner.query(`
            ALTER TABLE "transaction"
            ADD "reference" character varying DEFAULT NULL
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "transaction" DROP COLUMN "reference"
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion" DROP COLUMN "generation_seconds"
        `);
  }
}
