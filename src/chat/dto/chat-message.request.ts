import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON>Array,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';
import { ChatAttachedFileDto } from './chat-attached-file.dto';

enum MessageRole {
  USER = 'user',
  ASSISTANT = 'assistant',
}

export class ChatMessageRequest {
  @ApiProperty({
    description:
      'Content of the message, can include text, images, and other rich content',
  })
  @IsNotEmpty()
  @IsString()
  content: string;

  @ApiProperty({
    description: 'Attached files associated with the message',
    type: [ChatAttachedFileDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  attachedFiles?: ChatAttachedFileDto[];

  @ApiProperty({
    description: 'Role of the message sender',
    enum: MessageRole,
    default: MessageRole.USER,
  })
  @IsEnum(MessageRole)
  role: MessageRole;
}
