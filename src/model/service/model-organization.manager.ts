import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { Logger } from 'nestjs-pino';
import { OrganizationUserProvider } from '../../organization/service/organization-user.provider';
import { UserEntity } from '../../user/entity/user.entity';
import { ModelOrganizationEntity } from '../entity/model-organization.entity';
import { ModelEntity, PrivacyEnum } from '../entity/model.entity';
import { ModelProvider } from './provider';
import { ModelOrganizationProvider } from './model-organization.provider';

@Injectable()
export class ModelOrganizationManager {
  constructor(
    private modelOrganizationProvider: ModelOrganizationProvider,
    private modelProvider: ModelProvider,
    private organizationUserProvider: OrganizationUserProvider,
    private logger: Logger,
  ) {}

  /**
   * Share a model with an organization
   */
  async shareModelWithOrganization(
    modelId: string,
    organizationId: string,
    sharedByUser: UserEntity,
  ): Promise<ModelOrganizationEntity> {
    // Get the model and verify it exists
    const model = await this.modelProvider.get(modelId);
    if (!model) {
      throw new NotFoundException('Model not found');
    }

    // // Only private models can be shared with organizations
    // if (model.privacy !== PrivacyEnum.PRIVATE) {
    //   throw new BadRequestException('Only private models can be shared with organizations');
    // }

    // Verify the user has permission to share this model (only model owner can share)
    await this.validateUserCanShareModel(model, sharedByUser);

    // Check if the model is already shared with this organization
    const existingShare =
      await this.modelOrganizationProvider.findByModelAndOrganization(
        modelId,
        organizationId,
      );

    if (existingShare) {
      throw new BadRequestException(
        'Model is already shared with this organization',
      );
    }

    // Create the share relationship
    const modelOrganization = new ModelOrganizationEntity();
    modelOrganization.modelId = modelId;
    modelOrganization.organizationId = organizationId;
    modelOrganization.sharedByUserId = sharedByUser.id;

    const savedShare = await this.modelOrganizationProvider.save(
      modelOrganization,
    );

    this.logger.log(
      `Model ${modelId} shared with organization ${organizationId} by user ${sharedByUser.id}`,
    );

    return savedShare;
  }

  /**
   * Unshare a model from an organization
   */
  async unshareModelFromOrganization(
    modelId: string,
    organizationId: string,
    requestingUser: UserEntity,
  ): Promise<void> {
    // Get the model and verify it exists
    const model = await this.modelProvider.get(modelId);
    if (!model) {
      throw new NotFoundException('Model not found');
    }

    // Get the existing share relationship
    const existingShare =
      await this.modelOrganizationProvider.findByModelAndOrganization(
        modelId,
        organizationId,
      );

    if (!existingShare) {
      throw new NotFoundException('Model is not shared with this organization');
    }

    // Verify the user has permission to unshare this model (only model owner can unshare)
    await this.validateUserCanUnshareModel(model, requestingUser);

    // Remove the share relationship
    await this.modelOrganizationProvider.removeShare(modelId, organizationId);

    this.logger.log(
      `Model ${modelId} unshared from organization ${organizationId} by user ${requestingUser.id}`,
    );
  }

  /**
   * Get all organizations a model is shared with (only model owner can view this)
   */
  async getOrganizationsForModel(
    modelId: string,
    requestingUser: UserEntity,
  ): Promise<ModelOrganizationEntity[]> {
    // Get the model and verify it exists
    const model = await this.modelProvider.get(modelId);
    if (!model) {
      throw new NotFoundException('Model not found');
    }

    // Only model owner can view sharing information
    if (model.userId !== requestingUser.id) {
      throw new ForbiddenException(
        'Only model owners can view sharing information',
      );
    }

    return await this.modelOrganizationProvider.findByModelId(modelId);
  }

  /**
   * Get all models shared with an organization (only organization members can view this)
   */
  async getModelsForOrganization(
    organizationId: string,
    requestingUser: UserEntity,
  ): Promise<ModelOrganizationEntity[]> {
    // Check if user is a member of the organization
    const isMember = await this.organizationUserProvider.isMember(
      requestingUser.id,
      organizationId,
    );
    if (!isMember) {
      throw new ForbiddenException(
        'Only organization members can view organization models',
      );
    }

    return await this.modelOrganizationProvider.findByOrganizationId(
      organizationId,
    );
  }

  /**
   * Check if a user can access a model through organization membership
   */
  async canUserAccessModelThroughOrganization(
    modelId: string,
    userId: string,
  ): Promise<boolean> {
    // Get all organizations that have access to this model
    const organizationIds =
      await this.modelOrganizationProvider.getOrganizationIdsForModel(modelId);

    if (organizationIds.length === 0) {
      return false;
    }

    // Check if the user is a member of any of these organizations
    for (const organizationId of organizationIds) {
      const isMember = await this.organizationUserProvider.isMember(
        userId,
        organizationId,
      );
      if (isMember) {
        return true;
      }
    }

    return false;
  }

  /**
   * Validate that a user can share a model (simplified: only model owner can share)
   */
  private async validateUserCanShareModel(
    model: ModelEntity,
    user: UserEntity,
  ): Promise<void> {
    // Only model owner can share their models
    const isModelOwner = model.userId === user.id;

    if (!isModelOwner) {
      throw new ForbiddenException('Only model owners can share their models');
    }
  }

  /**
   * Validate that a user can unshare a model (simplified: only model owner can unshare)
   */
  private async validateUserCanUnshareModel(
    model: ModelEntity,
    user: UserEntity,
  ): Promise<void> {
    // Only model owner can unshare their models
    const isModelOwner = model.userId === user.id;

    if (!isModelOwner) {
      throw new ForbiddenException(
        'Only model owners can unshare their models',
      );
    }
  }
}
