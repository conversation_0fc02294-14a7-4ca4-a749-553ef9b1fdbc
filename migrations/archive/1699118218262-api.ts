import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1699118218262 implements MigrationInterface {
  name = 'Api1699118218262';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "image_completion" DROP CONSTRAINT "FK_cb24a4b667e103bc038f6c72927"
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion"
            ALTER COLUMN "user_id"
            SET NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion_like" DROP CONSTRAINT "FK_98469faf3de8c60756a3cfb62a5"
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion_like"
            ALTER COLUMN "image_completion_id"
            SET NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion"
            ADD CONSTRAINT "FK_cb24a4b667e103bc038f6c72927" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion_like"
            ADD CONSTRAINT "FK_98469faf3de8c60756a3cfb62a5" FOREIGN KEY ("image_completion_id") REFERENCES "image_completion"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion_like"
            ADD CONSTRAINT "FK_859471e9802d87a41700443b578" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "image_completion_like" DROP CONSTRAINT "FK_859471e9802d87a41700443b578"
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion_like" DROP CONSTRAINT "FK_98469faf3de8c60756a3cfb62a5"
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion" DROP CONSTRAINT "FK_cb24a4b667e103bc038f6c72927"
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion_like"
            ALTER COLUMN "image_completion_id" DROP NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion_like"
            ADD CONSTRAINT "FK_98469faf3de8c60756a3cfb62a5" FOREIGN KEY ("image_completion_id") REFERENCES "image_completion"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion"
            ALTER COLUMN "user_id" DROP NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion"
            ADD CONSTRAINT "FK_cb24a4b667e103bc038f6c72927" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }
}
