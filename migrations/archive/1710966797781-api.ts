import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1710966797781 implements MigrationInterface {
    name = 'Api1710966797781'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "model"
            ADD "website" text
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "model" DROP COLUMN "website"
        `);
    }

}
