import { Controller, Post, Request, UseGuards } from '@nestjs/common';
import { Body } from '@nestjs/common/decorators/http/route-params.decorator';
import {
    ApiBearerAuth,
    ApiBody,
    ApiCreatedResponse,
    ApiOperation,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import { JwtIntegrationAuthGuard } from 'src/auth/service/jwt-integration-auth.guard';
import { ImageDto } from '../dto/image.dto';
import { ImageRequest } from '../dto/image.request';
import { ImageRequestManager } from '../service/request-manager';
import { ImageResponseMapper } from '../service/response-mapper';

@ApiTags('images')
@Controller('images')
@ApiBearerAuth()
export class CreateController {
  constructor(
    private requestManager: ImageRequestManager,
    private responseMapper: ImageResponseMapper,
    private jwtIntegrationAuthGuard: JwtIntegrationAuthGuard,
  ) {}

  @Post()
  @UseGuards(JwtIntegrationAuthGuard)
  @ApiOperation({
    operationId: 'images_create',
    summary: 'Create a new image',
    description:
      'Creates a new image generation task from a text prompt.\n\n' +
      'Supports both user integration and organization integration tokens.\n\n' +
      'Required Parameters:\n' +
      '- prompt: Text description of the desired image\n\n' +
      'Optional Parameters:\n' +
      '- width: Image width in pixels (480-2160, default: 1600)\n' +
      '- height: Image height in pixels (480-2160, default: 1600)\n' +
      '- quality: Generation quality level (1-6, default: 2)\n' +
      '- creativity: Creative variation level (1-6, default: 2)\n' +
      '- hasWatermark: Apply watermark (default: true)\n' +
      '- systemVersion: AI system version (2 or 3)\n' +
      '- mode: Generation mode ("default" or "sigma")\n' +
      '- organizationId: Organization UUID to deduct credits from (user must be a member)\n\n' +
      'Credit Deduction:\n' +
      '- If organizationId is provided, credits will be deducted from the organization account\n' +
      '- If organizationId is not provided, credits will be deducted from the user account\n' +
      '- User must be a member of the organization to use organization credits\n\n' +
      'Monitor the generation status using the GET /images/{id} endpoint.',
  })
  @ApiBody({
    type: ImageRequest,
    description:
      'The image creation request parameters including prompt, dimensions, and quality settings',
  })
  @ApiCreatedResponse({
    type: ImageDto,
    description:
      'The image generation request was successfully created and queued for processing',
  })
  @ApiResponse({
    status: 400,
    description: `
      Bad Request. Possible reasons:
      - \`width\`: Must be between 480 and 2160 pixels.
      - \`height\`: Must be between 480 and 2160 pixels.
      - \`quality\`: Must be between 1 and 6.
      - \`creativity\`: Must be between 1 and 6.
      - \`systemVersion\`: Must be either 2 or 3.
      - \`mode\`: Must be a valid value from the \`ModeEnum\`.
      - \`organizationId\`: Must be a valid UUID format.
      - Invalid or unavailable model referenced in prompt.
      - Failed to analyze prompt or parameters.
      - Organization does not exist.
    `,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. Authentication token is missing or invalid, or user is not a member of the specified organization',
  })
  @ApiResponse({
    status: 402,
    description: 'Payment Required. Insufficient credits to create the image (either user or organization credits depending on request)',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden. User does not have permission to create images',
  })
  @ApiResponse({
    status: 500,
    description:
      'Internal Server Error. An unexpected error occurred during image creation',
  })
  async create(
    @Body() requestBody: ImageRequest,
    @Request() request,
  ): Promise<ImageDto> {
    const token = this.extractTokenFromHeader(request);
    const authEntity = request.integrationAuthToken;
    const actor = request.organization ?? request.user;

    if (authEntity?.hideFromUserProfile) {
      requestBody.hideFromUserProfile = true;
    }

    const imageCompletion = await this.requestManager.create(requestBody, actor);
    return this.responseMapper.map(imageCompletion);
  }

  private extractTokenFromHeader(request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];

    return type === 'Bearer' ? token : undefined;
  }
}
