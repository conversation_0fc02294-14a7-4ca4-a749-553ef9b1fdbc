import {
  Controller,
  Delete,
  HttpC<PERSON>,
  Param,
  ParseUUI<PERSON>ipe,
  Request,
} from '@nestjs/common';
import {
  ApiNoContentResponse,
  ApiParam,
  ApiTags,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiOperation,
} from '@nestjs/swagger';
import { ChatConversationRequestManager } from '../service/chat-conversation.request-manager';
import { ChatProvider } from '../service/chat.provider';
import { ChatMessageManager } from '../service/chat-message.manager';

@ApiTags('chat')
@Controller('chat')
export class DeleteController {
  constructor(
    private readonly chatProvider: ChatProvider,
    private readonly conversationRequestManager: ChatConversationRequestManager,
    private readonly messageManager: ChatMessageManager,
  ) {}

  @Delete(':id')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'chat_delete_conversation',
    summary: 'Delete a chat conversation',
    description:
      'Deletes the specified chat conversation and all its messages for the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the chat conversation to delete',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the chat conversation to delete.',
    type: String,
  })
  @ApiNoContentResponse({
    description: 'Conversation and all its messages deleted successfully.',
  })
  @ApiBadRequestResponse({
    description: `
      Bad Request. Possible reasons:\n
      - Invalid conversation ID format
      - Conversation does not exist
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description:
      'Forbidden. User does not have permission to delete this conversation.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async delete(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<void> {
    const conversation = await this.chatProvider.getConversation(
      id,
      request.user.id,
    );

    // Delete all messages first
    await this.messageManager.deleteByConversationId(conversation.id);

    // Then delete the conversation
    await this.conversationRequestManager.delete(conversation);
  }
}
