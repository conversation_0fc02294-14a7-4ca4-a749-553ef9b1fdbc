import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { BoardImageCompletionEntity } from '../../board/entity/board-image-completion.entity';
import { ImageEditImageCompletionEntity } from '../../image-edit/entity/image-edit-image-completion.entity';
import { OrganizationEntity } from '../../organization/entity/organization.entity';
import { UpscaleEntity } from '../../upscale/entity/upscale.entity';
import { UserEntity } from '../../user/entity/user.entity';
import { ImageCompletionModelEntity } from './image-completion-model.entity';

export enum PrivacyEnum {
  PUBLIC = 'public',
  PRIVATE = 'private',
  LICENSED = 'licensed',
}

export enum QueueEnum {
  SLOW = 'slow',
  FAST = 'fast',
  // PRIORITY = 'priority',
  // CUSTOM = 'custom',
}

export enum StatusEnum {
  NEW = 'new',
  GENERATING = 'generating',
  READY = 'ready',
  HIDDEN = 'hidden',
  FAILED = 'failed',
  INTERRUPTED = 'interrupted',
  NOT_ALLOWED = 'not_allowed',
}

@Entity('image_completion')
@Index('idx_image_completion_feed', ['status', 'privacy', 'isNsfw'])
export class ImageCompletionEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', nullable: false })
  @Index('idx_image_completion_user_id')
  userId: string;

  @ManyToOne(() => UserEntity)
  user: UserEntity;

  @Column({ type: 'uuid', nullable: true })
  @Index('idx_image_completion_organization_id')
  organizationId: string;

  @ManyToOne(() => OrganizationEntity)
  organization: OrganizationEntity;

  @OneToOne(
    () => ImageEditImageCompletionEntity,
    (imageEditImageCompletion) => imageEditImageCompletion.imageCompletion,
  )
  imageEditImageCompletion: ImageEditImageCompletionEntity;

  @OneToMany(
    () => BoardImageCompletionEntity,
    (boardImageCompletion) => boardImageCompletion.imageCompletion,
    { cascade: false, eager: false },
  )
  boards: BoardImageCompletionEntity[];

  @OneToMany(
    () => ImageCompletionModelEntity,
    (imageCompletionModel) => imageCompletionModel.imageCompletion,
    { cascade: true, eager: true },
  )
  models: ImageCompletionModelEntity[];

  @OneToMany(() => UpscaleEntity, (upscale) => upscale.imageCompletion, {
    eager: true,
  })
  upscales: UpscaleEntity[];

  @Column({ type: 'uuid', nullable: true })
  @Index('idx_image_completion_regenerated_from_id')
  regeneratedFromId?: string;

  @ManyToOne(() => ImageCompletionEntity)
  regeneratedFrom?: ImageCompletionEntity;

  @Column({ type: 'uuid', nullable: true })
  @Index('idx_image_completion_original_image_completion_id')
  originalImageCompletionId: string;

  @ManyToOne(() => ImageCompletionEntity, { eager: false, cascade: false })
  originalImageCompletion?: ImageCompletionEntity;

  @Column({ type: 'text', nullable: true })
  prompt?: string;

  @Column({ type: 'text', nullable: true })
  promptSystem?: string;

  @Column({ type: 'text', nullable: true })
  baseModel?: string;

  @Column({ nullable: false, default: 'new' })
  status: string;

  @Column({ type: 'text', nullable: true })
  statusDetail?: string;

  @Column({ nullable: false, default: 0 })
  progress: number;

  @Column({ type: 'text', nullable: true })
  previewImage?: string;

  @Column({ nullable: false })
  @Index('idx_image_completion_privacy')
  privacy: string;

  @Column({ nullable: false })
  queue: string;

  @Column({ nullable: false, default: 0 })
  likes: number;

  @Column({ nullable: false, default: 0 })
  comments: number;

  @Column({ nullable: false, default: 0 })
  regenerations: number;

  @Column({ nullable: false, default: 0 })
  reports: number;

  @Column({ type: 'text', nullable: true })
  storageBucket?: string;

  @Column({ type: 'text', nullable: true })
  storagePath?: string;

  @Column({ type: 'json', nullable: true })
  imagePaths?: any;

  @Column({ type: 'json', nullable: true, default: {} })
  generationSettings?: any;

  @Column({ type: 'text', nullable: true })
  generationData?: string;

  @Column({ nullable: true })
  generatedByUnit: string;

  @Column({ nullable: false, default: 0 })
  generationSeconds: number;

  @Column({ nullable: false, default: 1 })
  systemVersion?: number;

  @Column({ type: 'text', nullable: true })
  webhookUrl?: string;

  @Column({ default: true })
  hasWatermark: boolean;

  @Column({ default: false })
  hasPrivateModel: boolean;

  @Column({ default: false })
  @Index('idx_image_completion_is_nsfw')
  isNsfw: boolean;

  @Column({ default: false })
  hideFromUserProfile: boolean;

  @Column({ default: false })
  hidePrompt: boolean;

  @Column({ default: false })
  isUnsafe: boolean;

  @Column({ default: false })
  @Index()
  isHot: boolean;

  @Column({ default: false })
  isActive: boolean;

  @Column({ default: false })
  isResized: boolean;

  @Column({ default: true })
  isBilled: boolean;

  @Column({ type: 'timestamp', nullable: true })
  publishedAt?: Date;

  @Column({ type: 'timestamp', nullable: true })
  blockedAt?: Date;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  @Index('idx_image_completion_deleted_at')
  deletedAt?: Date;

  @CreateDateColumn()
  @Index('idx_image_completion_created_at')
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  hasModel(modelId: string): boolean {
    return this.models.some(
      (imageCompletionModel) => imageCompletionModel.model.id === modelId,
    );
  }
}
