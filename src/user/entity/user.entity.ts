import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { UserFollowEntity } from './user-follow.entity';

@Entity('user_account')
export class UserEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: true })
  name?: string;

  @Column({ nullable: true, unique: true })
  @Index()
  email?: string;

  @Column({ nullable: true, unique: true })
  @Index()
  username?: string;

  @Column({ nullable: true })
  password?: string;

  @Column({ nullable: true })
  oAuthProvider?: string;

  @Column({ nullable: true })
  oAuthId?: string;

  @Column({ nullable: true })
  oAuthToken?: string;

  @Column({ type: 'text', nullable: true })
  profilePicture?: string;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ type: 'text', nullable: true })
  website?: string;

  @Column({ type: 'json', nullable: true })
  socialMediaAccounts?: string;

  @Column({ nullable: true })
  timezone?: string;

  @Column({ nullable: true })
  frontendTheme?: string;

  @Column({ nullable: true })
  currency?: string;

  @Column({ nullable: true })
  locale?: string;

  @Column({ nullable: true })
  stripeCustomerId?: string;

  @Column({ default: true })
  includeWatermarks: boolean;

  @Column({ default: false })
  hidePrompt: boolean;

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: false })
  isVerified: boolean;

  @Column({ type: 'date', nullable: true })
  birthday?: Date;

  @Column({ type: 'timestamp', nullable: true })
  lastLogin?: Date;

  @Column({ type: 'timestamp', nullable: true })
  blockedAt?: Date;

  @Column({ nullable: true })
  emailValidationCode?: string;

  @Column({ type: 'timestamp', nullable: true })
  emailValidationCodeExpiresAt?: Date;

  @Column({ type: 'timestamp', nullable: true })
  emailValidatedAt?: Date;

  @Column({ nullable: true })
  signupCode?: string;

  @Column({ type: 'uuid', nullable: true })
  userSignupCodeId?: string;

  @Column({ nullable: true })
  resetPasswordToken?: string;

  @Column({ type: 'timestamp', nullable: true })
  resetPasswordExpires?: Date;

  @Column({ nullable: true })
  modelQueue?: string;

  @Column({ nullable: true })
  imageQueue?: string;

  @Column({ nullable: true, default: 0 })
  imagesGenerated?: number;

  @Column({ nullable: true, default: 0 })
  imagesAvailable?: number;

  @Column({ nullable: true, default: 0 })
  modelsAvailable?: number;

  @Column({ nullable: true, default: 0 })
  followersCount?: number;

  @Column({ nullable: true, default: 0 })
  followingCount?: number;

  @Column({ type: 'json', nullable: true })
  tutorialSteps?: any;

  @Column({ type: 'json', nullable: true, default: {} })
  grants?: any;

  @Column({ nullable: false })
  systemVersion?: number;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt?: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ default: false })
  allowSwapping: boolean;

  @Column({ default: false })
  isBot: boolean;

  @OneToMany(() => UserFollowEntity, (userFollow) => userFollow.following, {
    lazy: true,
    eager: false,
  })
  followers: UserFollowEntity[];

  @OneToMany(() => UserFollowEntity, (userFollow) => userFollow.follower, {
    lazy: true,
    eager: false,
  })
  following: UserFollowEntity[];
}
