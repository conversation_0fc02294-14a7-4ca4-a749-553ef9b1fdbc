import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { Logger } from 'nestjs-pino';
import { ImageCompletionProvider } from 'src/image-completion/service/provider';
import { OrganizationUserProvider } from 'src/organization/service/organization-user.provider';
import { PaymentRequiredException } from 'src/subscription/exception/payment-required.exception';
import { UserEntity } from '../../user/entity/user.entity';
import { ImageEditInternalRequest } from '../dto/image-edit.internal-request';
import { ImageEditRequest } from '../dto/image-edit.request';
import {
  EditMode,
  ImageEditEntity,
  StatusEnum,
} from '../entity/image-edit.entity';
import { ImageEditManager } from './manager';
import { ImageEditProvider } from './provider';
import * as sharp from 'sharp';
import axios from 'axios';

@Injectable()
export class ImageEditRequestManager {
  constructor(
    private readonly manager: ImageEditManager,
    private readonly provider: ImageEditProvider,
    private readonly imageCompletionProvider: ImageCompletionProvider,
    private readonly imageEditProvider: ImageEditProvider,
    private readonly organizationUserProvider: OrganizationUserProvider,
    private readonly logger: Logger,
  ) {}

  async create(
    request: ImageEditRequest,
    user: UserEntity,
  ): Promise<ImageEditEntity> {
    const imageEdit = new ImageEditEntity();
    imageEdit.user = user;
    imageEdit.userId = user.id;

    // If mode is 'skin', always set imageCompletionsCount to 1
    imageEdit.imageCompletionsCount =
      request.mode === EditMode.SKIN ? 1 : request.imageCompletionsCount;

    imageEdit.inputImageUrl = request.inputImageUrl;

    if (request.originalImageCompletionId) {
      const originalImageCompletion = await this.imageCompletionProvider.get(
        request.originalImageCompletionId,
      );

      imageEdit.originalImageCompletion = originalImageCompletion;

      if (originalImageCompletion.imageEditImageCompletion) {
        try {
          const originalImageEdit =
            originalImageCompletion.imageEditImageCompletion.imageEdit ??
            (await this.imageEditProvider.get(
              originalImageCompletion.imageEditImageCompletion.imageEditId,
            ));

          imageEdit.inputImageUrl = originalImageEdit.inputImageUrl;
        } catch (error) {
          this.logger.debug(
            'Error fetching the edit of the original image completion',
            {
              image_completion: originalImageCompletion.id,
              error: error.message,
            },
          );
        }
      }
    }

    if (user.hidePrompt) {
      imageEdit.hidePrompt = user.hidePrompt;
    }

    // Map request data to entity
    this.mapRequestData(imageEdit, request);

    // If width or height is missing, get from original image
    if (!imageEdit.width || !imageEdit.height) {
      try {
        // First, check if we have an originalImageCompletion
        if (imageEdit.originalImageCompletion) {
          // Try to get dimensions from originalImageCompletion's generationSettings
          if (imageEdit.originalImageCompletion.generationSettings) {
            const settings =
              imageEdit.originalImageCompletion.generationSettings;

            if (settings.width && !imageEdit.width) {
              imageEdit.width = settings.width;
              this.logger.debug('Using width from originalImageCompletion', {
                width: imageEdit.width,
                imageCompletionId: imageEdit.originalImageCompletion.id,
              });
            }

            if (settings.height && !imageEdit.height) {
              imageEdit.height = settings.height;
              this.logger.debug('Using height from originalImageCompletion', {
                height: imageEdit.height,
                imageCompletionId: imageEdit.originalImageCompletion.id,
              });
            }
          }
        }

        // If we still don't have dimensions, fall back to getting them from inputImageUrl
        if (!imageEdit.width || !imageEdit.height) {
          const imageUrl = imageEdit.inputImageUrl;

          // Get image dimensions using Sharp
          if (imageUrl) {
            const response = await axios.get(imageUrl, {
              responseType: 'arraybuffer',
            });
            const metadata = await sharp(response.data).metadata();

            if (metadata.width && metadata.height) {
              if (!imageEdit.width) {
                imageEdit.width = metadata.width;
                this.logger.debug('Using width from inputImageUrl', {
                  width: imageEdit.width,
                });
              }

              if (!imageEdit.height) {
                imageEdit.height = metadata.height;
                this.logger.debug('Using height from inputImageUrl', {
                  height: imageEdit.height,
                });
              }
            }
          }
        }
      } catch (error) {
        this.logger.error('Error getting image dimensions', {
          error: error.message,
          inputImageUrl: imageEdit.inputImageUrl,
        });
      }
    }

    if (request.organizationId) {
      if (
        !(await this.organizationUserProvider.isMember(
          user.id,
          request.organizationId,
        ))
      ) {
        throw new UnauthorizedException();
      }

      imageEdit.organizationId = request.organizationId;
    }

    return await this.manager.create(imageEdit);
  }

  async update(
    id: string,
    request: ImageEditRequest,
    user: UserEntity,
  ): Promise<ImageEditEntity> {
    const imageEdit = await this.provider.getBy({
      id: id,
      userId: user.id,
    });
    this.mapRequestData(imageEdit, request);

    return await this.manager.update(imageEdit);
  }

  async saveImageCompletion(
    id: string,
    imageCompletionId: string,
    user: UserEntity,
  ): Promise<ImageEditEntity> {
    const imageEdit = await this.provider.getBy({
      id,
      userId: user.id,
    });

    if (!imageEdit.hasImageCompletion(imageCompletionId)) {
      throw new NotFoundException('Image completion not found');
    }

    const imageCompletion = await this.imageCompletionProvider.get(
      imageCompletionId,
    );

    return await this.manager.saveImageCompletion(imageEdit, imageCompletion);
  }

  async delete(id: string, user: UserEntity): Promise<void> {
    const imageEdit = await this.provider.getBy({
      id,
      user,
    });

    await this.manager.delete(imageEdit);
  }

  mapRequestData(entity: ImageEditEntity, request: ImageEditRequest): void {
    entity.mode = 'mode' in request ? request.mode : entity.mode;
    entity.width = 'width' in request ? request.width : entity.width;
    entity.height = 'height' in request ? request.height : entity.height;
    entity.prompt = 'prompt' in request ? request.prompt : entity.prompt;
    entity.mask = 'mask' in request ? request.mask : entity.mask;
    entity.webhookUrl =
      'webhookUrl' in request ? request.webhookUrl : entity.webhookUrl;
    entity.settings =
      'settings' in request ? request.settings : entity.settings;
    entity.hidePrompt =
      'hidePrompt' in request ? request.hidePrompt : entity.hidePrompt;
  }

  async updateInternal(
    id: string,
    request: ImageEditInternalRequest,
  ): Promise<ImageEditEntity> {
    const imageEdit = await this.provider.get(id);

    imageEdit.status = 'status' in request ? request.status : imageEdit.status;
    imageEdit.generationSeconds =
      'generationSeconds' in request
        ? request.generationSeconds
        : imageEdit.generationSeconds;
    imageEdit.width = 'width' in request ? request.width : imageEdit.width;
    imageEdit.height = 'height' in request ? request.height : imageEdit.height;

    return await this.manager.update(imageEdit);
  }

  async startGeneration(id: string): Promise<void> {
    const entity = await this.provider.get(id);

    if (entity.status != StatusEnum.NEW) {
      throw new BadRequestException('Image edit is not new');
    }

    await this.manager.startGeneration(entity);
  }

  async finishGeneration(
    id: string,
    request: ImageEditInternalRequest,
  ): Promise<void> {
    const entity = await this.provider.get(id);

    if (entity.status != StatusEnum.GENERATING) {
      throw new BadRequestException('Image edit is not being generated');
    }

    entity.generationSeconds =
      'generationSeconds' in request
        ? request.generationSeconds
        : entity.generationSeconds;

    await this.manager.finishGeneration(entity);
  }

  async failGeneration(id: string): Promise<void> {
    const entity = await this.provider.get(id);

    if (entity.status != StatusEnum.GENERATING) {
      throw new BadRequestException('Image edit is not being generated');
    }

    await this.manager.failGeneration(entity);
  }

  async interrupt(entity: ImageEditEntity): Promise<void> {
    if (
      entity.status != StatusEnum.GENERATING &&
      entity.status != StatusEnum.NEW
    ) {
      throw new BadRequestException('Image edit is not being generated');
    }

    try {
      await this.manager.interrupt(entity);
    } catch (e) {
      if ('user_credit_balance.not_found' === e.message) {
        throw new PaymentRequiredException();
      }

      this.logger.error('Error interrupting image generation', {
        imageCompletion: entity,
        error: e.message,
      });

      throw new BadRequestException(e.message);
    }
  }
}
