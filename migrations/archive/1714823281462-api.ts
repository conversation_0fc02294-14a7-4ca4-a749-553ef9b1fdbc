import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1714823281462 implements MigrationInterface {
  name = 'Api1714823281462';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "user_follow" DROP CONSTRAINT "UQ_6ead635e1a8378c275c536bd0dc"
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "user_follow" DROP CONSTRAINT "UQ_38021b4aa91c54b9ca467c0776e"
        `);
  }
}
