import {
  <PERSON>,
  Get,
  Param,
  Query,
  Request,
  Res,
  ParseUUIDPipe,
  UsePipes,
  ValidationPipe,
  UnauthorizedException,
  NotFoundException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOkResponse,
  ApiBadRequestResponse,
  ApiParam,
  ApiQuery,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiInternalServerErrorResponse,
  ApiForbiddenResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import { ImageEditDto } from '../dto/image-edit.dto';
import { ImageEditSearchRequest } from '../dto/image-edit.search-request';
import { ImageEditProvider } from '../service/provider';
import { ImageEditResponseMapper } from '../service/response-mapper';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { ImageEditMaskDto } from '../dto/image-edit-mask.dto';
import { ImageEditImageCompletionProvider } from '../service/image-edit-image-completion.provider';

@ApiTags('image-edit')
@Controller('image-edits')
export class ReadController {
  constructor(
    private readonly provider: ImageEditProvider,
    private readonly responseMapper: ImageEditResponseMapper,
    private imageEditImageCompletionProvider: ImageEditImageCompletionProvider,
  ) {}

  @Get()
  @ApiOperation({
    operationId: 'image_edit_list',
    summary: 'List image edits',
    description:
      'Retrieves a paginated list of image edits for the authenticated user.\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of edits per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- status: Filter by status\n',
  })
  @ApiOkResponse({
    type: ImageEditDto,
    isArray: true,
    description: 'Paginated list of image edits.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - Filter parameters must be valid values.
      - \`status\`: Must be a valid value from the \`StatusEnum\`.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiQuery({
    type: ImageEditSearchRequest,
    description: 'Query parameters for searching and paginating image edits.',
  })
  @UsePipes(new ValidationPipe())
  async find(
    @Query() query: ImageEditSearchRequest,
    @Res() res: Response,
    @Request() request,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;

    const filters = {
      ...inputFilters,
      userId: request.user.id,
    };

    const entities = await this.provider.findBy(
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    );
    const totalCount = await this.provider.countBy(filters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultiple(entities));
  }

  @Get(':id')
  @ApiOperation({
    operationId: 'image_edit_get',
    summary: 'Get image edit by ID',
    description:
      'Retrieves a specific image edit by its UUID for the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the image edit\n',
  })
  @ApiOkResponse({
    type: ImageEditDto,
    description: 'Returns the image edit with the specified ID.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - Image edit does not exist.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User does not have access to this image edit.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiParam({ name: 'id', description: 'ID of the resource to retrieve' })
  async get(
    @Param('id', new ParseUUIDPipe()) id: string,
    @Request() request,
  ): Promise<ImageEditDto> {
    const entity = await this.provider.get(id);

    if (entity.userId != request.user?.id) {
      throw new UnauthorizedException(
        "You are not allowed to view other users' images.",
      );
    }

    return await this.responseMapper.map(entity);
  }

  @Get(':imageCompletionId/mask')
  @ApiOperation({
    operationId: 'image_edit_mask_get',
    summary: 'Get mask for image completion',
    description:
      'Retrieves the mask for the specified image completion if the user is the owner.\n\n' +
      'Required Parameters:\n' +
      '- imageCompletionId: UUID of the image completion\n',
  })
  @ApiOkResponse({
    type: ImageEditMaskDto,
    description: 'Returns the mask for the image completion.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`imageCompletionId\`: Must be a valid UUID.
      - Image completion does not exist.
      - Image completion is not part of an image edit.
      - Image edit does not have a mask.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User does not have access to this mask.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiParam({
    name: 'imageCompletionId',
    description: 'ID of the image completion to retrieve the mask for',
  })
  async getMaskByImageCompletion(
    @Param('imageCompletionId', new ParseUUIDPipe()) imageCompletionId: string,
    @Request() request,
  ): Promise<ImageEditMaskDto> {
    const imageEditImageCompletion =
      await this.imageEditImageCompletionProvider.getBy({
        imageCompletionId: imageCompletionId,
      });

    const imageEdit = imageEditImageCompletion.imageEdit;

    if (!imageEdit) {
      throw new NotFoundException(
        'No image edit found for this image completion',
      );
    }

    if (!imageEdit.mask) {
      throw new NotFoundException('No mask found for this image edit');
    }

    if (imageEdit.userId != request.user?.id) {
      throw new UnauthorizedException(
        "You are not allowed to view other users' image masks.",
      );
    }

    return { mask: imageEdit.mask } as ImageEditMaskDto;
  }
}
