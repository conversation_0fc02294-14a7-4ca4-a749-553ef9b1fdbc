# Module

Configures the components of the module.

## new-module.module.ts

Defines the module and its components.

```typescript
import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CreateController } from './controller/create.controller';
import { DeleteController } from './controller/delete.controller';
import { ReadController } from './controller/read.controller';
import { UpdateController } from './controller/update.controller';
import { NewModuleEntity } from './entity/new-module.entity';
import { NewModuleProvider } from './service/new-module.provider';
import { NewModuleManager } from './service/new-module.manager';
import { NewModuleRequestManager } from './service/new-module.request-manager';
import { NewModuleResponseMapper } from './service/new-module.response-mapper';

@Module({
  imports: [TypeOrmModule.forFeature([NewModuleEntity])],
  providers: [
    NewModuleProvider,
    NewModuleManager,
    NewModuleRequestManager,
    NewModuleResponseMapper,
  ],
})
export class NewModuleModule {
  static register(enableControllers: boolean): DynamicModule {
    return {
      module: NewModuleModule,
      controllers: enableControllers
        ? [CreateController, DeleteController, ReadController, UpdateController]
        : [],
    };
  }
}
```
