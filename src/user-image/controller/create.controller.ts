import { Body, Controller, Post, Request, UseGuards } from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/service/jwt-auth.guard';
import { UserProvider } from '../../user/service/provider';
import { UserImageDto } from '../dto/user-image.dto';
import { UserImageRequest } from '../dto/user-image.request';
import { UserImageRequestManager } from '../service/user-image.request-manager';
import { UserImageResponseMapper } from '../service/user-image.response-mapper';

@ApiTags('user-images')
@Controller('user-images')
@UseGuards(JwtAuthGuard)
export class CreateController {
  constructor(
    private readonly userProvider: UserProvider,
    private readonly requestManager: UserImageRequestManager,
    private readonly responseMapper: UserImageResponseMapper,
  ) {}

  @ApiOperation({
    summary: 'Create a new user image',
    description: `Creates a new image entry and generates an upload URL.

The response includes an 'uploadUrl' property which is a pre-signed S3 URL that can be used to upload the image file.

To upload the image:
1. Make a PUT request to the uploadUrl with the image file as the request body
2. Set the Content-Type header to match the image type (e.g., 'image/jpeg', 'image/png')
3. No authentication is required for this PUT request as the URL is pre-signed

Example using curl:
\`\`\`
curl -X PUT -H "Content-Type: image/jpeg" --data-binary "@/path/to/image.jpg" "https://pre-signed-url-from-response"
\`\`\`

Example using JavaScript fetch:
\`\`\`javascript
const response = await fetch(uploadUrl, {
  method: 'PUT',
  headers: {
    'Content-Type': 'image/jpeg'
  },
  body: imageFile // File or Blob object
});
\`\`\`

After successful upload, the image will be accessible via the 'imageUrl' property in the response.`,
  })
  @ApiBody({ type: UserImageRequest })
  @ApiOkResponse({ type: UserImageDto })
  @ApiBadRequestResponse()
  @Post()
  async createUserImage(
    @Body() userImageRequest: UserImageRequest,
    @Request() request,
  ): Promise<UserImageDto> {
    const user = await this.userProvider.get(request.user.id);

    const userImage = await this.requestManager.create(userImageRequest, user);

    return this.responseMapper.map(userImage, true);
  }
}
