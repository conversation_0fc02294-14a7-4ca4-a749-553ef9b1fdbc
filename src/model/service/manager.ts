import { SQSClient } from '@aws-sdk/client-sqs';
import { ConflictException, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import * as path from 'path';
import { AppConfigurationService } from 'src/core/service/app-configuration.service';
import { Notifier } from 'src/notification/service/notifier';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { Mailer } from '../../core/service/mailer.service';
import { ModelEntity, PrivacyEnum } from '../entity/model.entity';
import { StatusEnum } from '../enum/status.enum';
import { ModelPublishedEvent } from '../event/model-published.event';
import { ModelUnpublishedEvent } from '../event/model-unpublished.event';
import { ModelTrainingFinishedNotification } from '../notification/model-training-finished.notification';
import { AssetManager } from './asset.manager';
import { ModelVersionManager } from './model-version.manager';
import { ModelVersionProvider } from './model-version.provider';
import { ModelDeletedEvent } from '../event/model-deleted.event';
import axios from 'axios';
import { ModelResponseMapper } from './response-mapper';

@Injectable()
export class ModelManager {
  constructor(
    @InjectRepository(ModelEntity)
    private repository: Repository<ModelEntity>,
    private assetManager: AssetManager,
    private configService: ConfigService,
    private appConfig: AppConfigurationService,
    private eventEmitter: EventEmitter2,
    @Inject('SQS') private sqs: SQSClient,
    private mailer: Mailer,
    private notifier: Notifier,
    private logger: Logger,
    private modelVersionProvider: ModelVersionProvider,
    private modelVersionManager: ModelVersionManager,
    private responseMapper: ModelResponseMapper,
  ) {}

  async create(entity: ModelEntity): Promise<ModelEntity> {
    await this.validateModel(entity);

    entity.id = uuidv4();

    this.assetManager.generateStoragePath(entity);

    await this.save(entity);

    return entity;
  }

  async trainModel(model: ModelEntity, systemVersions: number[]) {
    let modelVersion = await this.modelVersionProvider.findModelVersion(
      model.id,
      model.version,
    );

    if (!modelVersion || modelVersion?.status === StatusEnum.FINISHED) {
      if (modelVersion) {
        model.version++;
      }

      modelVersion = await this.modelVersionManager.create(
        model,
        model.version,
        systemVersions,
      );
    }

    for (const systemVersion of systemVersions) {
      await this.modelVersionManager.trainSystemVersion(
        modelVersion,
        systemVersion,
      );
    }

    model.status = StatusEnum.PENDING;
    await this.repository.save(model);
  }

  async startTraining(
    model: ModelEntity,
    systemVersion: number,
  ): Promise<ModelEntity> {
    const modelVersion = await this.modelVersionProvider.getModelVersion(
      model.id,
      model.version,
    );
    await this.modelVersionManager.startTraining(modelVersion, systemVersion);

    model.status = StatusEnum.TRAINING;
    await this.update(model);

    return model;
  }

  async failTraining(
    model: ModelEntity,
    systemVersion: number,
  ): Promise<ModelEntity> {
    const modelVersion = await this.modelVersionProvider.getModelVersion(
      model.id,
      model.version,
    );
    await this.modelVersionManager.failTraining(modelVersion, systemVersion);

    model.status = StatusEnum.FAILED;
    await this.update(model);

    return model;
  }

  async finishTraining(
    model: ModelEntity,
    systemVersion: number,
    prompt: string,
  ): Promise<ModelEntity> {
    const modelVersion = await this.modelVersionProvider.getModelVersion(
      model.id,
      model.version,
    );
    await this.modelVersionManager.finishTraining(
      modelVersion,
      systemVersion,
      prompt,
    );

    model.systemVersions = [];
    modelVersion.systemVersionsData.map((data) => {
      if (
        data.status == StatusEnum.FINISHED &&
        !model.systemVersions.includes(data.systemVersion)
      ) {
        model.systemVersions.push(data.systemVersion);
      }
    });

    if (modelVersion.status === StatusEnum.FINISHED) {
      model.status =
        model.origin === 'public' ? StatusEnum.AVAILABLE : StatusEnum.FINISHED;
      await this.update(model);

      modelVersion.status = StatusEnum.FINISHED;
      modelVersion.trainedAt = new Date();

      await this.notifier.dispatch(
        new ModelTrainingFinishedNotification(model.userId, {
          id: model.id,
          userId: model.userId,
          modelName: model.name,
          finishedAt: new Date(),
        }),
      );

      // Send webhook if URL is configured
      if (model.webhookUrl) {
        try {
          const modelDto = await this.responseMapper.map(model);
          await axios.post(model.webhookUrl, modelDto, {
            headers: {
              'Content-Type': 'application/json',
            },
            timeout: 5000, // 5 second timeout
          });

          this.logger.log('Webhook notification sent successfully', {
            modelId: model.id,
            webhookUrl: model.webhookUrl,
          });
        } catch (error) {
          this.logger.error('Failed to send webhook notification', {
            modelId: model.id,
            webhookUrl: model.webhookUrl,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }
    }

    return model;
  }

  async reseTrainingStatus(model: ModelEntity): Promise<ModelEntity> {
    const modelVersion = await this.modelVersionProvider.getModelVersion(
      model.id,
      model.version,
    );
    await this.modelVersionManager.reseTrainingStatus(modelVersion);

    model.status = StatusEnum.NEW;
    await this.repository.save(model);

    return model;
  }

  async restoreVersion(
    model: ModelEntity,
    version: number,
  ): Promise<ModelEntity> {
    const modelVersion = await this.modelVersionProvider.getModelVersion(
      model.id,
      model.version,
    );

    model.storagePath = modelVersion.filename;
    model.version = version;
    model.systemVersions = modelVersion.systemVersionsData.map(
      (systemVersionData) => systemVersionData.systemVersion,
    );

    return await this.save(model);
  }

  // async writeToQueue(entity: ModelEntity) {
  //   const systemVersions = [2, 3];

  //   systemVersions.map(async (systemVersion) => {
  //     const queueUrl = this.getQueueUrl(entity, systemVersion);

  //     await this.sqs.send(
  //       new SendMessageCommand({
  //         QueueUrl: queueUrl,
  //         MessageBody: JSON.stringify(
  //           await this.responseMapper.mapInternal(entity),
  //         ),
  //       }),
  //     );
  //   });
  // }

  getQueueUrl(model: ModelEntity, version: string | number): string {
    const { user } = model;

    if (user.modelQueue) {
      return this.configService.get<string>(
        'MODEL_SQS_QUEUE_URL_' + user.modelQueue.toUpperCase(),
      );
    }

    return this.configService.get<string>('MODEL_SQS_QUEUE_URL_V' + version);
  }

  async update(entity: ModelEntity): Promise<ModelEntity> {
    await this.validateModel(entity);

    if (entity.status === StatusEnum.FINISHED && !entity.finishedEmailSentAt) {
      try {
        await this.sendModelFinishedEmail(entity);
      } catch (error) {
        this.logger.error('Error sending model finished email', {
          modelId: entity.id,
          error: error,
        });
      }
    }

    return await this.save(entity);
  }

  async activate(entity: ModelEntity) {
    entity.status = StatusEnum.AVAILABLE;

    await this.save(entity);
  }

  async save(entity: ModelEntity): Promise<ModelEntity> {
    if (null === entity.publishedAt && entity.privacy === PrivacyEnum.PUBLIC) {
      entity.publishedAt = new Date();

      await this.repository.save(entity);

      // await this.notifier.dispatch(
      //   new ModelPublishedNotification(entity.userId, {
      //     id: entity.id,
      //     userId: entity.userId,
      //     modelName: entity.name,
      //     publishedAt: entity.publishedAt,
      //   }),
      // );

      this.eventEmitter.emit(
        'model.published',
        new ModelPublishedEvent({
          id: entity.id,
          userId: entity.user.id,
        }),
      );
    } else if (
      null !== entity.publishedAt &&
      entity.privacy === PrivacyEnum.PRIVATE
    ) {
      entity.publishedAt = null;

      await this.repository.save(entity);

      this.eventEmitter.emit(
        'model.unpublished',
        new ModelUnpublishedEvent({
          id: entity.id,
          userId: entity.user.id,
        }),
      );
    }

    return await this.repository.save(entity);
  }

  async sendModelFinishedEmail(model: ModelEntity) {
    const subject = `Congrats! Your AI Model @${model.name} is ready! Activate it now!`;

    const replacements = {
      subject: subject,
      modelName: model.name,
      modelPrivacy: model.privacy,
      username: model.user.username || model.user.name,
      activationLink: `${this.appConfig.asString('FRONTEND_URL')}/models/${
        model.name
      }`,
    };

    const template = path.join(
      this.appConfig.templateDir,
      'email',
      'model',
      'finished.html',
    );

    try {
      await this.mailer.send(template, replacements, subject, model.user.email);

      model.finishedEmailSentAt = new Date();
    } catch (error) {
      this.logger.error('Error sending model finished email', {
        modelId: model.id,
        error: error,
      });
    }
  }

  async delete(entity: ModelEntity): Promise<void> {
    entity.name = `${entity.name}-${new Date().toISOString()}-${uuidv4()}`;

    await this.repository.save(entity);

    await this.repository.softDelete(entity.id);

    this.eventEmitter.emit(
      'model.deleted',
      new ModelDeletedEvent({
        id: entity.id,
        userId: entity.userId,
      }),
    );
  }

  private async validateModel(entity: ModelEntity): Promise<void> {
    const queryBuilder = this.repository
      .createQueryBuilder('model')
      .where('model.name = :name', { name: entity.name });

    if (entity.id) {
      queryBuilder.andWhere('model.id != :id', { id: entity.id });
    }

    // // Case 1: Model is Public
    // if (entity.privacy === PrivacyEnum.PUBLIC) {
    //   queryBuilder.andWhere('model.privacy = :public', {
    //     public: PrivacyEnum.PUBLIC,
    //   });
    // }

    // // Case 2: Model is Private (or switching to public)
    // else {
    //   queryBuilder.andWhere(
    //     '(model.privacy = :public OR (model.privacy = :private AND model.user_id = :userId))',
    //     {
    //       public: PrivacyEnum.PUBLIC,
    //       private: PrivacyEnum.PRIVATE,
    //       userId: entity.user.id || entity.user,
    //     },
    //   );
    // }

    // // Only add this condition for existing entities
    // if (entity.id) {
    //   queryBuilder.andWhere('model.id != :id', { id: entity.id });
    // }

    const existingModel = await queryBuilder.getOne();

    if (existingModel) {
      throw new ConflictException('A model with this name already exists');
    }
  }
}
