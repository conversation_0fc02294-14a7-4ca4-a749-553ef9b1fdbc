import {
  Controller,
  Get,
  Query,
  Request,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiOkResponse,
  ApiQuery,
  ApiTags,
  ApiOperation,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import { BaseFindResponseHeadersDto } from 'src/core/dto/base-find-response-headers.dto';
import { AuthOptional } from 'src/core/security/public-routes';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import {
  PrivacyEnum as ImageCompletionPrivacyEnum,
  StatusEnum as ImageCompletionStatusEnum,
} from 'src/image-completion/entity/image-completion.entity';
import { FeedItemDto } from '../dto/feed-item.dto';
import { FeedSearchRequest } from '../dto/feed.search-request';
import { FeedProvider } from '../service/provider';
import { FeedRequestManager } from '../service/request-manager';
import { FeedResponseMapper } from '../service/response-mapper';

@ApiTags('feed')
@Controller('feed')
export class ReadController {
  constructor(
    private provider: FeedProvider,
    private responseMapper: FeedResponseMapper,
    private requestManager: FeedRequestManager,
  ) {}

  @Get()
  @ApiOperation({
    operationId: 'feed_list',
    summary: 'List feed items',
    description:
      'Retrieves a paginated list of feed items. If authenticated, results may be personalized.\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of items per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- onlyFollowing: If true, only show items from followed users\n' +
      '- prompt: Filter by prompt\n' +
      '- userId: Filter by user ID\n' +
      '- username: Filter by username\n' +
      '- systemVersion: Filter by system version\n' +
      '- isHot: Filter by hot images\n' +
      '- includeNsfw: Include NSFW content\n' +
      '- editedImages: Filter by edited images\n' +
      '- day: Filter by items created today\n' +
      '- week: Filter by items created this week\n' +
      '- month: Filter by items created this month\n' +
      '- year: Filter by items created this year\n',
  })
  @ApiOkResponse({
    type: FeedItemDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated list of feed items.',
  })
  @ApiQuery({
    type: FeedSearchRequest,
    description: 'Query parameters for searching and paginating feed items.',
  })
  @ApiBadRequestResponse({
    description: `
      Bad Request. Possible reasons:\n
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - Filter parameters must be valid values.
      - \`userId\`: Must be a valid UUID.
      - \`username\`: Must be a valid username.
      - \`systemVersion\`: Must be a valid system version.
      - \`isHot\`: Must be a valid boolean value.
      - \`includeNsfw\`: Must be a valid boolean value.
      - \`editedImages\`: Must be a valid boolean value.
      - \`day\`: Must be a valid boolean value.
      - \`week\`: Must be a valid boolean value.
      - \`month\`: Must be a valid boolean value.
      - \`year\`: Must be a valid boolean value.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @UsePipes(new ValidationPipe())
  @AuthOptional()
  async find(
    @Request() request,
    @Query() query: FeedSearchRequest,
    @Res() res: Response,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;

    const filters = await this.requestManager.sanitizeSearchFilters(
      inputFilters,
      request.user,
    );

    const objects = await this.provider.find(
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    const totalCount = await this.provider.count(filters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultiple(objects, request.user?.id));
  }

  validateSearchFilters(
    filters: FeedSearchRequest,
    currentUserId: string,
  ): any {
    const criteria = {
      followerId: null,
      ...filters,
    };

    if (filters.onlyFollowing) {
      criteria.followerId = currentUserId;
    }

    return {
      ...criteria,
      privacy: ImageCompletionPrivacyEnum.PUBLIC,
      status: ImageCompletionStatusEnum.READY,
      isNsfw: false,
    };
  }
}
