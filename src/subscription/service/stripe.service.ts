import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { Logger } from 'nestjs-pino';
import Stripe from 'stripe';
import { CreditPackageEntity } from '../entity/credit-package.entity';
import { CreditPackageManager } from './credit-package.manager';
import { CreditPackageProvider } from './credit-package.provider';

@Injectable()
export class StripeService {
  constructor(
    @Inject('Stripe') private readonly stripe: Stripe,
    private readonly creditPackageManager: CreditPackageManager,
    private readonly creditPackageProvider: CreditPackageProvider,
    private readonly logger: Logger,
  ) {}

  /**
   * Create or get Stripe product and price for a credit package
   */
  async getOrCreateStripePriceId(
    creditPackage: CreditPackageEntity,
  ): Promise<string> {
    if (creditPackage.stripePriceId) {
      return creditPackage.stripePriceId;
    }

    try {
      // Create a Stripe product if it doesn't exist
      if (!creditPackage.stripeProductId) {
        const product = await this.stripe.products.create({
          name: creditPackage.name,
          metadata: {
            creditPackageId: creditPackage.id,
            targetEntity: creditPackage.targetEntity,
            expiresAfterMonths:
              creditPackage.expiresAfterMonths?.toString() || '1',
          },
        });

        creditPackage.stripeProductId = product.id;
      }

      // Create a new price with appropriate billing interval
      const priceParams: Stripe.PriceCreateParams = {
        product: creditPackage.stripeProductId,
        unit_amount: creditPackage.price,
        currency: creditPackage.currency || 'eur',
      };

      // Set up recurring billing for subscription packages
      if (creditPackage.isRecurring) {
        if (this.isYearlyPackage(creditPackage)) {
          priceParams.recurring = {
            interval: 'year',
          };
        } else {
          priceParams.recurring = {
            interval: 'month',
            interval_count: creditPackage.expiresAfterMonths || 1,
          };
        }
      }

      const price = await this.stripe.prices.create(priceParams);
      creditPackage.stripePriceId = price.id;

      // Update the credit package
      await this.creditPackageManager.save(creditPackage);

      this.logger.log('stripe.price_created', {
        creditPackageId: creditPackage.id,
        stripePriceId: price.id,
        stripeProductId: creditPackage.stripeProductId,
        isYearly: this.isYearlyPackage(creditPackage),
      });

      return price.id;
    } catch (error) {
      this.logger.error('Failed to create Stripe price', {
        error,
        creditPackageId: creditPackage.id,
        creditPackageName: creditPackage.name,
      });
      throw new BadRequestException(
        'subscription.stripe_price_creation_failed',
      );
    }
  }

  /**
   * Create Stripe products and prices for all yearly credit packages
   */
  async createYearlyStripeProducts(): Promise<void> {
    try {
      // Get all yearly packages that don't have Stripe products/prices
      const yearlyPackages = await this.creditPackageProvider.findBy(
        {
          expiresAfterMonths: 12,
          isRecurring: true,
          isVisible: true,
        },
        1,
        100,
      );

      this.logger.log('stripe.create_yearly_products', {
        packageCount: yearlyPackages.length,
      });

      for (const creditPackage of yearlyPackages) {
        if (!creditPackage.stripePriceId) {
          await this.getOrCreateStripePriceId(creditPackage);
        }
      }

      this.logger.log('stripe.yearly_products_created', {
        processedPackages: yearlyPackages.length,
      });
    } catch (error) {
      this.logger.error('Failed to create yearly Stripe products', { error });
      throw new BadRequestException('subscription.yearly_stripe_setup_failed');
    }
  }

  /**
   * Handle pro-rating when upgrading from monthly to yearly subscription
   */
  async calculateProration(
    currentSubscription: Stripe.Subscription,
    newPriceId: string,
  ): Promise<Stripe.UpcomingInvoice> {
    try {
      // Calculate the upcoming invoice with the new price
      const upcomingInvoice = await this.stripe.invoices.retrieveUpcoming({
        customer: currentSubscription.customer as string,
        subscription: currentSubscription.id,
        subscription_items: [
          {
            id: currentSubscription.items.data[0].id,
            price: newPriceId,
          },
        ],
        subscription_proration_behavior: 'create_prorations',
      });

      this.logger.log('stripe.proration_calculated', {
        subscriptionId: currentSubscription.id,
        newPriceId,
        proratedAmount: upcomingInvoice.amount_due,
      });

      return upcomingInvoice;
    } catch (error) {
      this.logger.error('Failed to calculate proration', {
        error,
        subscriptionId: currentSubscription.id,
        newPriceId,
      });
      throw new BadRequestException(
        'subscription.proration_calculation_failed',
      );
    }
  }

  /**
   * Update Stripe subscription to yearly billing
   */
  async upgradeToYearlySubscription(
    stripeSubscriptionId: string,
    yearlyPriceId: string,
  ): Promise<Stripe.Subscription> {
    try {
      const subscription = await this.stripe.subscriptions.retrieve(
        stripeSubscriptionId,
      );

      const updatedSubscription = await this.stripe.subscriptions.update(
        stripeSubscriptionId,
        {
          items: [
            {
              id: subscription.items.data[0].id,
              price: yearlyPriceId,
            },
          ],
          proration_behavior: 'create_prorations',
        },
      );

      this.logger.log('stripe.subscription_upgraded_to_yearly', {
        subscriptionId: stripeSubscriptionId,
        yearlyPriceId,
        newPeriodEnd: updatedSubscription.current_period_end,
      });

      return updatedSubscription;
    } catch (error) {
      this.logger.error('Failed to upgrade subscription to yearly', {
        error,
        subscriptionId: stripeSubscriptionId,
        yearlyPriceId,
      });
      throw new BadRequestException('subscription.yearly_upgrade_failed');
    }
  }

  /**
   * Check if a credit package is yearly based on expiration months
   */
  private isYearlyPackage(creditPackage: CreditPackageEntity): boolean {
    return creditPackage.expiresAfterMonths === 12;
  }

  /**
   * Validate that a Stripe price exists and is active
   */
  async validateStripePriceId(priceId: string): Promise<boolean> {
    try {
      const price = await this.stripe.prices.retrieve(priceId);
      return price.active;
    } catch (error) {
      this.logger.error('Failed to validate Stripe price', {
        error,
        priceId,
      });
      return false;
    }
  }

  /**
   * Get Stripe subscription details
   */
  async getStripeSubscription(
    subscriptionId: string,
  ): Promise<Stripe.Subscription> {
    try {
      return await this.stripe.subscriptions.retrieve(subscriptionId);
    } catch (error) {
      this.logger.error('Failed to retrieve Stripe subscription', {
        error,
        subscriptionId,
      });
      throw new BadRequestException('subscription.stripe_retrieval_failed');
    }
  }
}
