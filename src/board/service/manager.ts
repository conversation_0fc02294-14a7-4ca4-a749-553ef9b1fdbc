import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserEntity } from '../../user/entity/user.entity';
import { BoardUserEntity } from '../entity/board-user.entity';
import { BoardEntity } from '../entity/board.entity';
import { StatusEnum } from '../enum/boardUser-status.enum';

@Injectable()
export class BoardManager {
  constructor(
    @InjectRepository(BoardEntity)
    private boardRepository: Repository<BoardEntity>,
    @InjectRepository(BoardUserEntity)
    private boardUserRepository: Repository<BoardUserEntity>,
  ) {}

  async create(board: BoardEntity, owner: UserEntity): Promise<BoardEntity> {
    await this.boardRepository.save(board);

    const boardUser = new BoardUserEntity();
    boardUser.user = owner;
    boardUser.userId = owner.id;
    boardUser.board = board;
    boardUser.boardId = board.id;
    boardUser.isOwner = true;
    boardUser.isAdmin = true;
    boardUser.status = StatusEnum.APPROVED;
    boardUser.approvedAt = new Date();
    boardUser.approvedBy = owner.id;

    await this.boardUserRepository.save(boardUser);

    return board;
  }

  async update(board: BoardEntity): Promise<BoardEntity> {
    return await this.boardRepository.save(board);
  }

  async save(board: BoardEntity) {
    await this.boardRepository.save(board);
  }

  async delete(board: BoardEntity): Promise<void> {
    await this.boardUserRepository.softDelete({ boardId: board.id });

    await this.boardRepository.softDelete(board.id);
  }
}
