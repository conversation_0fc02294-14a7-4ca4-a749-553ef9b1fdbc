import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { UserEntity } from '../../user/entity/user.entity';
import { OrganizationEntity } from './organization.entity';
import { StatusEnum } from '../enum/organization-user.enum';

@Entity('organization_invite')
export class OrganizationInviteEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => OrganizationEntity)
  organization: OrganizationEntity;

  @Column({ type: 'uuid', nullable: false })
  @Index()
  organizationId: string;

  @ManyToOne(() => UserEntity, { eager: true })
  user: UserEntity;

  @Column({ type: 'uuid', nullable: true })
  @Index()
  userId: string;

  @Column({ default: false })
  isAdmin: boolean;

  @Column({ nullable: true })
  email: string;

  @Column({
    type: 'enum',
    enum: StatusEnum,
    default: StatusEnum.PENDING,
    nullable: false,
  })
  status: StatusEnum;

  @Column({ type: 'uuid', nullable: true })
  invitedBy: string;

  @Column({ type: 'timestamp', nullable: true })
  invitedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  acceptedAt: Date;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt?: Date;

  @CreateDateColumn()
  createdAt: Date;
}
