import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1724943737262 implements MigrationInterface {
  name = 'Api1724943737262';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "model"
            ADD "system_versions" json NOT NULL DEFAULT '[]'
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "model" DROP COLUMN "system_versions"
        `);
  }
}
