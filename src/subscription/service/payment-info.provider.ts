import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, In } from 'typeorm';
import { Logger } from 'nestjs-pino';
import { SubscriptionEntity } from '../entity/subscription.entity';
import { SubscriptionStatusEnum } from '../entity/subscription-status.enum';

export interface PaymentFilters {
  status?: SubscriptionStatusEnum[];
  minAmount?: number;
  maxAmount?: number;
  startDate?: Date;
  endDate?: Date;
  paymentMethod?: string[];
}

@Injectable()
export class PaymentInfoProvider {
  constructor(
    @InjectRepository(SubscriptionEntity)
    private readonly subscriptionRepository: Repository<SubscriptionEntity>,
    private readonly logger: Logger,
  ) {}

  async getPayments(
    userId: string,
    filters: PaymentFilters = {},
    page: number = 1,
    limit: number = 10,
  ): Promise<{ payments: SubscriptionEntity[]; total: number }> {
    try {
      const queryBuilder = this.subscriptionRepository
        .createQueryBuilder('subscription')
        .leftJoinAndSelect('subscription.creditPackage', 'creditPackage')
        .where('subscription.userId = :userId', { userId });

      // Apply filters
      if (filters.status && filters.status.length > 0) {
        queryBuilder.andWhere('subscription.status IN (:...statuses)', {
          statuses: filters.status,
        });
      }

      if (filters.minAmount !== undefined) {
        queryBuilder.andWhere('subscription.price >= :minAmount', {
          minAmount: filters.minAmount,
        });
      }

      if (filters.maxAmount !== undefined) {
        queryBuilder.andWhere('subscription.price <= :maxAmount', {
          maxAmount: filters.maxAmount,
        });
      }

      if (filters.startDate && filters.endDate) {
        queryBuilder.andWhere('subscription.paidAt BETWEEN :startDate AND :endDate', {
          startDate: filters.startDate,
          endDate: filters.endDate,
        });
      } else if (filters.startDate) {
        queryBuilder.andWhere('subscription.paidAt >= :startDate', {
          startDate: filters.startDate,
        });
      } else if (filters.endDate) {
        queryBuilder.andWhere('subscription.paidAt <= :endDate', {
          endDate: filters.endDate,
        });
      }

      // Payment method filter (based on external reference pattern)
      if (filters.paymentMethod && filters.paymentMethod.length > 0) {
        const conditions = filters.paymentMethod.map((method, index) => {
          if (method === 'stripe') {
            return `subscription.externalReference LIKE :stripe${index}`;
          }
          return `subscription.externalReference LIKE :other${index}`;
        });
        
        const parameters: any = {};
        filters.paymentMethod.forEach((method, index) => {
          if (method === 'stripe') {
            parameters[`stripe${index}`] = 'sub_%';
          } else {
            parameters[`other${index}`] = `${method}_%`;
          }
        });

        queryBuilder.andWhere(`(${conditions.join(' OR ')})`, parameters);
      }

      // Pagination
      const offset = (page - 1) * limit;
      queryBuilder.skip(offset).take(limit);

      // Order by payment date (most recent first)
      queryBuilder.orderBy('subscription.paidAt', 'DESC');

      const [payments, total] = await queryBuilder.getManyAndCount();

      return { payments, total };
    } catch (error) {
      this.logger.error('Failed to fetch payments', {
        userId,
        filters,
        error: error.message,
      });
      throw error;
    }
  }

  async getPaymentById(
    userId: string,
    paymentId: string,
  ): Promise<SubscriptionEntity | null> {
    try {
      return await this.subscriptionRepository.findOne({
        where: {
          id: paymentId,
          userId,
        },
        relations: ['creditPackage'],
      });
    } catch (error) {
      this.logger.error('Failed to fetch payment by ID', {
        userId,
        paymentId,
        error: error.message,
      });
      throw error;
    }
  }

  async getPaymentSummary(userId: string): Promise<{
    totalPaid: number;
    totalPayments: number;
    averagePayment: number;
    lastPaymentDate?: Date;
  }> {
    try {
      const result = await this.subscriptionRepository
        .createQueryBuilder('subscription')
        .select([
          'SUM(subscription.price) as totalPaid',
          'COUNT(*) as totalPayments',
          'AVG(subscription.price) as averagePayment',
          'MAX(subscription.paidAt) as lastPaymentDate',
        ])
        .where('subscription.userId = :userId', { userId })
        .andWhere('subscription.status IN (:...statuses)', {
          statuses: [SubscriptionStatusEnum.ACTIVE, SubscriptionStatusEnum.INACTIVE],
        })
        .andWhere('subscription.paidAt IS NOT NULL')
        .getRawOne();

      return {
        totalPaid: parseFloat(result.totalPaid) || 0,
        totalPayments: parseInt(result.totalPayments) || 0,
        averagePayment: parseFloat(result.averagePayment) || 0,
        lastPaymentDate: result.lastPaymentDate || undefined,
      };
    } catch (error) {
      this.logger.error('Failed to fetch payment summary', {
        userId,
        error: error.message,
      });
      throw error;
    }
  }
}
