import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, Matches } from 'class-validator';

export class OrganizationRequest {
  @IsString()
  @IsOptional()
  @ApiProperty()
  name: string;

  @IsOptional()
  @ApiProperty()
  @Matches(/^[a-zA-Z0-9_.]+$/, {
    message:
      '<PERSON><PERSON> must contain only alphanumeric characters, underscores, and dots',
  })
  handle: string;

  @IsOptional()
  @ApiProperty()
  description: string;

  @IsOptional()
  @ApiProperty()
  website: string;

  @IsOptional()
  @ApiProperty()
  currency: string;

  @IsOptional()
  @ApiProperty()
  locale: string;

  @IsOptional()
  @ApiProperty()
  includeWatermarks: boolean;

  @IsOptional()
  @ApiProperty()
  imageQueue: string;

  @IsOptional()
  @ApiProperty()
  modelQueue: string;

  @IsOptional()
  @ApiProperty()
  systemVersion: number;
}
