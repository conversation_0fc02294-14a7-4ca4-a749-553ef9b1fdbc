import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1703859144250 implements MigrationInterface {
  name = 'Api1703859144250';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "grants" json DEFAULT '{}'
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "grants"
        `);
  }
}
