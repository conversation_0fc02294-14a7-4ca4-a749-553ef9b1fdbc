import {
  Body,
  Controller,
  Post,
  Request,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOkResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtIntegrationAuthGuard } from 'src/auth/service/jwt-integration-auth.guard';
import { ModelCreateRequest } from '../dto/model.create-request';
import { ModelDto } from '../dto/model.dto';
import { ModelRequestManager } from '../service/request-manager';
import { ModelResponseMapper } from '../service/response-mapper';

@ApiTags('models')
@Controller('models')
@UseGuards(JwtIntegrationAuthGuard)
@ApiBearerAuth()
export class CreateController {
  constructor(
    private requestManager: ModelRequestManager,
    private responseMapper: ModelResponseMapper,
  ) {}

  @Post()
  @ApiOperation({
    operationId: 'models_create',
    summary: 'Create a new model',
    description:
      'Creates a new AI model for image generation.\n\n' +
      'Request Body Parameters:\n' +
      '- name: Model identifier (alphanumeric, underscores, dots)\n' +
      '- privacy: Access level (public, private, organization)\n' +
      '- type: Model type classification\n' +
      '- class: Model category\n' +
      '- description: Optional description of the model\n' +
      '- website: Optional website URL\n' +
      '- settings: Optional array of settings (NO_ADULT_CONTENT, NO_VIOLENCE, etc)\n' +
      '- trainingDataUrls: Array of image URLs for training (1-50 URLs required)\n' +
      '- webhookUrl: Optional URL to receive a POST notification when training is complete\n' +
      '- trainingMode: Optional training mode (e.g., "default", "sigma")\n' +
      '- organizationId: Organization UUID to deduct credits from (user must be a member)\n\n' +
      'Credit Deduction:\n' +
      '- If organizationId is provided, credits will be deducted from the organization account\n' +
      '- If organizationId is not provided, credits will be deducted from the user account\n' +
      '- User must be a member of the organization to use organization credits\n\n' +
      'Note: Training process starts automatically after creation.',
  })
  @ApiOkResponse({
    type: ModelDto,
    description: 'The model was successfully created.',
  })
  @ApiBody({
    type: ModelCreateRequest,
    description: 'The model creation request payload',
  })
  @ApiResponse({
    status: 400,
    description: `
      Bad Request. Possible reasons:
      - \`name\`: Must contain only alphanumeric characters, underscores, and dots.
      - \`name\`: Must be shorter than or equal to 255 characters.
      - \`privacy\`: Must be a valid value from the \`PrivacyEnum\`.
      - \`type\`: Must contain only allowed characters (alphanumeric, commas, hyphens, etc.).
      - \`class\`: Must be a valid value from the \`ClassEnum\`.
      - \`trainingDataUrls\`: Must be an array of valid URLs (max 50).
      - \`webhookUrl\`: Must be a valid URL.
      - \`trainingMode\`: Must be a valid value from the \`TrainingModeEnum\`.
      - \`organizationId\`: Must be a valid UUID format.
      - Missing or invalid \`class\` field (mandatory).
      - Failed to analyze model type due to invalid training data URLs.
      - Organization does not exist.
    `,
  })
  @ApiResponse({
    status: 401,
    description:
      'Unauthorized. The user is not authenticated, or user is not a member of the specified organization.',
  })
  @ApiResponse({
    status: 402,
    description:
      'Payment Required. Insufficient credits to create the model (either user or organization credits depending on request).',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found. The requested resource could not be found.',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. An unexpected error occurred.',
  })
  @UsePipes(new ValidationPipe())
  async create(
    @Body() request: ModelCreateRequest,
    @Request() req,
  ): Promise<ModelDto> {
    const entity = await this.requestManager.create(request, req.user);

    return this.responseMapper.map(entity);
  }
}
