import { DynamicModule, Module, forwardRef } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthModule } from '../auth/auth.module';
import { AwsModule } from '../aws/aws.module';
import { CoreModule } from '../core/core.module';
import { NotificationModule } from '../notification/module';
import { jwtConfig } from '../auth/config/jwt.config';
import { ImageCompletionModule } from '../image-completion/module';
import { UserModule } from '../user/user.module';
import { CreateController } from './controller/create.controller';
import { DeleteController } from './controller/delete.controller';
import { ImageController } from './controller/image.controller';
import { ReadController as InternalReadController } from './controller/internal/read.controller';
import { Update<PERSON>ontroller as InternalUpdateController } from './controller/internal/update.controller';
import { ReadController } from './controller/read.controller';
import { ThumbnailController } from './controller/thumbnail.controller';
import { UpdateController } from './controller/update.controller';
import { ModelVersionEntity } from './entity/model-version.entity';
import { ModelEntity } from './entity/model.entity';
import { AssetManager } from './service/asset.manager';
import { ModelManager } from './service/manager';
import { ModelVersionManager } from './service/model-version.manager';
import { ModelVersionProvider } from './service/model-version.provider';
import { ModelProvider } from './service/provider';
import { ModelRequestManager } from './service/request-manager';
import { ModelResponseMapper } from './service/response-mapper';
import { ModelCommentProvider } from './service/model-comment.provider';
import { ModelCommentRequestManager } from './service/model-comment.request-manager';
import { ModelCommentResponseMapper } from './service/model-comment.response-mapper';
import { ModelCommentManager } from './service/model-comment.manager';
import { ModelCommentEntity } from './entity/model-comment.entity';
import { CommentController } from './controller/comment.controller';
import { ModelCommentLikeEntity } from './entity/model-comment-like.entity';
import { ModelOrganizationEntity } from './entity/model-organization.entity';
import { ModelCommentLikeProvider } from './service/model-comment-like.provider';
import { ModelOrganizationProvider } from './service/model-organization.provider';
import { ModelOrganizationManager } from './service/model-organization.manager';
import { ModelOrganizationResponseMapper } from './service/model-organization.response-mapper';
import { OrganizationShareController } from './controller/organization-share.controller';
import { ModelCommentLikeRequestManager } from './service/model-comment-like.request-manager';
import { ModelCommentLikeResponseMapper } from './service/model-comment-like.response-mapper';
import { ModelCommentLikeManager } from './service/model-comment-like.manager';
import { CommentLikeController } from './controller/comment-like.controller';
import { BookmarkModule } from '../bookmark/module';
import { ModelTypeManager } from './service/model-type.manager';
import { OrganizationModule } from '../organization/organization.module';

@Module({
  exports: [
    AssetManager,
    ModelProvider,
    ModelManager,
    ModelResponseMapper,
    ModelVersionProvider,
    ModelVersionManager,
    ModelTypeManager,
    ModelOrganizationProvider,
    ModelOrganizationManager,
    ModelOrganizationResponseMapper,
  ],
  imports: [
    AwsModule,
    CoreModule,
    TypeOrmModule.forFeature([
      ModelEntity,
      ModelVersionEntity,
      ModelCommentEntity,
      ModelCommentLikeEntity,
      ModelOrganizationEntity,
    ]),
    PassportModule,
    JwtModule.register(jwtConfig),
    forwardRef(() => AuthModule),
    forwardRef(() => ImageCompletionModule),
    forwardRef(() => NotificationModule),
    forwardRef(() => UserModule),
    forwardRef(() => BookmarkModule),
    forwardRef(() => OrganizationModule),
  ],
  providers: [
    ModelResponseMapper,
    ModelProvider,
    ModelVersionProvider,
    AssetManager,
    ModelRequestManager,
    ModelManager,
    ModelVersionManager,
    ModelCommentProvider,
    ModelCommentRequestManager,
    ModelCommentResponseMapper,
    ModelCommentManager,
    ModelCommentLikeProvider,
    ModelCommentLikeRequestManager,
    ModelCommentLikeResponseMapper,
    ModelCommentLikeManager,
    ModelTypeManager,
    ModelOrganizationProvider,
    ModelOrganizationManager,
    ModelOrganizationResponseMapper,
  ],
})
export class ModelModule {
  static register(enableControllers: boolean): DynamicModule {
    return {
      module: ModelModule,
      controllers: enableControllers
        ? [
            CreateController,
            DeleteController,
            ReadController,
            UpdateController,
            CommentLikeController,
            CommentController,
            ImageController,
            ThumbnailController,
            InternalReadController,
            InternalUpdateController,
            OrganizationShareController,
          ]
        : [],
    };
  }
}
