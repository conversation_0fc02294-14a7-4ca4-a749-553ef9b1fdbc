import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { Repository } from 'typeorm';
import { CreditPackageEntity } from '../entity/credit-package.entity';
import { CreditTypeEnum } from '../entity/credit-type.enum';
import { CreditPackageProvider } from './credit-package.provider';

describe('CreditPackageProvider', () => {
  let provider: CreditPackageProvider;
  let repository: Repository<CreditPackageEntity>;
  let logger: Logger;

  const mockRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    findOneBy: jest.fn(),
    countBy: jest.fn(),
  };

  const mockLogger = {
    error: jest.fn(),
    log: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreditPackageProvider,
        {
          provide: getRepositoryToken(CreditPackageEntity),
          useValue: mockRepository,
        },
        {
          provide: Logger,
          useValue: mockLogger,
        },
      ],
    }).compile();

    provider = module.get<CreditPackageProvider>(CreditPackageProvider);
    repository = module.get<Repository<CreditPackageEntity>>(
      getRepositoryToken(CreditPackageEntity),
    );
    logger = module.get<Logger>(Logger);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('findYearlyPackages', () => {
    it('should find yearly packages for user entity', async () => {
      const mockYearlyPackages: CreditPackageEntity[] = [
        {
          id: '1',
          name: 'Pro - Yearly',
          price: 7490,
          expiresAfterMonths: 12,
          targetEntity: 'user',
          isVisible: true,
          isRecurring: true,
          creditTypes: { [CreditTypeEnum.IMAGE]: 36000, [CreditTypeEnum.MODEL]: 20 },
        } as CreditPackageEntity,
      ];

      mockRepository.find.mockResolvedValue(mockYearlyPackages);

      const result = await provider.findYearlyPackages('user');

      expect(mockRepository.find).toHaveBeenCalledWith({
        where: {
          targetEntity: 'user',
          expiresAfterMonths: 12,
          isVisible: true,
          isRecurring: true,
        },
        order: {
          price: 'ASC',
        },
      });
      expect(result).toEqual(mockYearlyPackages);
    });

    it('should find yearly packages for organization entity', async () => {
      const mockYearlyPackages: CreditPackageEntity[] = [
        {
          id: '2',
          name: 'Enterprise Basic - Yearly',
          price: 500000,
          expiresAfterMonths: 12,
          targetEntity: 'organization',
          isVisible: true,
          isRecurring: true,
          seats: 10,
          creditTypes: { [CreditTypeEnum.IMAGE]: 100000, [CreditTypeEnum.MODEL]: 1 },
        } as CreditPackageEntity,
      ];

      mockRepository.find.mockResolvedValue(mockYearlyPackages);

      const result = await provider.findYearlyPackages('organization');

      expect(mockRepository.find).toHaveBeenCalledWith({
        where: {
          targetEntity: 'organization',
          expiresAfterMonths: 12,
          isVisible: true,
          isRecurring: true,
        },
        order: {
          price: 'ASC',
        },
      });
      expect(result).toEqual(mockYearlyPackages);
    });
  });

  describe('findMonthlyPackages', () => {
    it('should find monthly packages for user entity', async () => {
      const mockMonthlyPackages: CreditPackageEntity[] = [
        {
          id: '3',
          name: 'Pro',
          price: 749,
          expiresAfterMonths: 1,
          targetEntity: 'user',
          isVisible: true,
          isRecurring: true,
          creditTypes: { [CreditTypeEnum.IMAGE]: 36000, [CreditTypeEnum.MODEL]: 20 },
        } as CreditPackageEntity,
      ];

      mockRepository.find.mockResolvedValue(mockMonthlyPackages);

      const result = await provider.findMonthlyPackages('user');

      expect(mockRepository.find).toHaveBeenCalledWith({
        where: {
          targetEntity: 'user',
          expiresAfterMonths: 1,
          isVisible: true,
          isRecurring: true,
        },
        order: {
          price: 'ASC',
        },
      });
      expect(result).toEqual(mockMonthlyPackages);
    });
  });

  describe('findYearlyVariant', () => {
    it('should find yearly variant of a monthly package', async () => {
      const mockYearlyPackage: CreditPackageEntity = {
        id: '4',
        name: 'Pro - Yearly',
        price: 7490,
        expiresAfterMonths: 12,
        targetEntity: 'user',
        isVisible: true,
        isRecurring: true,
        creditTypes: { [CreditTypeEnum.IMAGE]: 36000, [CreditTypeEnum.MODEL]: 20 },
      } as CreditPackageEntity;

      mockRepository.findOne.mockResolvedValue(mockYearlyPackage);

      const result = await provider.findYearlyVariant('Pro');

      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: {
          name: 'Pro - Yearly',
          isVisible: true,
        },
      });
      expect(result).toEqual(mockYearlyPackage);
    });

    it('should return null if yearly variant does not exist', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      const result = await provider.findYearlyVariant('NonExistent');

      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: {
          name: 'NonExistent - Yearly',
          isVisible: true,
        },
      });
      expect(result).toBeNull();
    });
  });

  describe('findMonthlyVariant', () => {
    it('should find monthly variant of a yearly package', async () => {
      const mockMonthlyPackage: CreditPackageEntity = {
        id: '5',
        name: 'Pro',
        price: 749,
        expiresAfterMonths: 1,
        targetEntity: 'user',
        isVisible: true,
        isRecurring: true,
        creditTypes: { [CreditTypeEnum.IMAGE]: 36000, [CreditTypeEnum.MODEL]: 20 },
      } as CreditPackageEntity;

      mockRepository.findOne.mockResolvedValue(mockMonthlyPackage);

      const result = await provider.findMonthlyVariant('Pro - Yearly');

      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: {
          name: 'Pro',
          isVisible: true,
        },
      });
      expect(result).toEqual(mockMonthlyPackage);
    });

    it('should return null if monthly variant does not exist', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      const result = await provider.findMonthlyVariant('NonExistent - Yearly');

      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: {
          name: 'NonExistent',
          isVisible: true,
        },
      });
      expect(result).toBeNull();
    });
  });

  describe('isYearlyPackage', () => {
    it('should return true for yearly packages', () => {
      const yearlyPackage: CreditPackageEntity = {
        expiresAfterMonths: 12,
      } as CreditPackageEntity;

      const result = provider.isYearlyPackage(yearlyPackage);

      expect(result).toBe(true);
    });

    it('should return false for non-yearly packages', () => {
      const monthlyPackage: CreditPackageEntity = {
        expiresAfterMonths: 1,
      } as CreditPackageEntity;

      const result = provider.isYearlyPackage(monthlyPackage);

      expect(result).toBe(false);
    });
  });

  describe('isMonthlyPackage', () => {
    it('should return true for monthly packages', () => {
      const monthlyPackage: CreditPackageEntity = {
        expiresAfterMonths: 1,
      } as CreditPackageEntity;

      const result = provider.isMonthlyPackage(monthlyPackage);

      expect(result).toBe(true);
    });

    it('should return false for non-monthly packages', () => {
      const yearlyPackage: CreditPackageEntity = {
        expiresAfterMonths: 12,
      } as CreditPackageEntity;

      const result = provider.isMonthlyPackage(yearlyPackage);

      expect(result).toBe(false);
    });
  });
});
