// model-version-manager.spec.ts

import { SendMessageCommandOutput } from '@aws-sdk/client-sqs';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { ModelDto } from '../dto/model.dto';
import { ModelVersionEntity } from '../entity/model-version.entity';
import { ModelEntity } from '../entity/model.entity';
import { StatusEnum } from '../enum/status.enum';
import { ModelVersionManager } from './model-version.manager';
import { ModelProvider } from './provider';
import { ModelResponseMapper } from './response-mapper';
import { UserProvider } from '../../user/service/provider';
import { Logger } from 'nestjs-pino';

describe('ModelVersionManager', () => {
  let service: ModelVersionManager;
  let repository: Repository<ModelVersionEntity>;
  let sqsClient: { send: jest.Mock };
  let configService: ConfigService;
  let responseMapper: { mapInternal: jest.Mock };
  let modelProvider: ModelProvider;

  const mockModelDto: ModelDto = {
    id: uuidv4(),
    name: 'Test Model',
    username: 'testuser',
    type: 'some type',
    class: 'some class',
    description: 'A test model',
    status: 'active',
    progress: 100,
    privacy: 'public',
    usages: 10,
    imagesAvailable: 5,
    likes: 20,
    isBookmarked: false,
    comments: 0,
    version: 1,
    createdAt: new Date(),
    systemVersions: [3],
  };

  const mockSendMessageCommandOutput: SendMessageCommandOutput = {
    $metadata: {
      httpStatusCode: 200,
      requestId: 'some-request-id',
    },
    MessageId: 'some-message-id',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ModelVersionManager,
        {
          provide: getRepositoryToken(ModelVersionEntity),
          useClass: Repository,
        },
        {
          provide: 'SQS',
          useValue: {
            send: jest.fn().mockResolvedValue(mockSendMessageCommandOutput),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: ModelResponseMapper,
          useValue: {
            mapInternal: jest.fn().mockResolvedValue(mockModelDto),
          },
        },
        {
          provide: ModelProvider,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: UserProvider,
          useValue: {
            get: jest.fn().mockResolvedValue({
              id: 'user-id',
              modelQueue: null,
            }),
          },
        },
        {
          provide: Logger,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<ModelVersionManager>(ModelVersionManager);
    repository = module.get<Repository<ModelVersionEntity>>(
      getRepositoryToken(ModelVersionEntity),
    );
    sqsClient = module.get('SQS');
    configService = module.get<ConfigService>(ConfigService);
    responseMapper = module.get(ModelResponseMapper);
    modelProvider = module.get<ModelProvider>(ModelProvider);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('trainSystemVersion', () => {
    it('should create systemVersionData if it does not exist and enqueue the training task', async () => {
      const model = new ModelEntity();
      model.id = uuidv4();
      model.userId = 'user-id';

      const modelVersion = new ModelVersionEntity();
      modelVersion.id = uuidv4();
      modelVersion.version = 1;
      modelVersion.modelId = model.id;
      modelVersion.model = model;
      modelVersion.systemVersionsData = [];

      const systemVersion = 1;

      const savedModelVersion = { ...modelVersion };
      savedModelVersion.systemVersionsData = [
        {
          systemVersion: systemVersion,
          status: StatusEnum.PENDING,
        },
      ];
      savedModelVersion.status = StatusEnum.PENDING;

      jest.spyOn(repository, 'save').mockResolvedValue(savedModelVersion);
      jest.spyOn(configService, 'get').mockReturnValue('queue-url');

      await service.trainSystemVersion(modelVersion, systemVersion);

      expect(repository.save).toHaveBeenCalledWith(savedModelVersion);
      expect(configService.get).toHaveBeenCalledWith(
        'MODEL_SQS_QUEUE_URL_V' + systemVersion,
      );
      expect(responseMapper.mapInternal).toHaveBeenCalled();
      expect(sqsClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: {
            QueueUrl: 'queue-url',
            MessageBody: JSON.stringify(mockModelDto),
          },
        }),
      );
    });

    it('should update existing systemVersionData status to PENDING and enqueue the training task', async () => {
      const systemVersion = 1;
      const existingSystemVersionData = {
        systemVersion: systemVersion,
        status: StatusEnum.FAILED,
      };

      const model = new ModelEntity();
      model.id = uuidv4();
      model.userId = 'user-id';

      const modelVersion = new ModelVersionEntity();
      modelVersion.id = uuidv4();
      modelVersion.version = 1;
      modelVersion.modelId = model.id;
      modelVersion.model = model;
      modelVersion.systemVersionsData = [existingSystemVersionData];

      const updatedModelVersion = { ...modelVersion };
      updatedModelVersion.systemVersionsData = [
        {
          systemVersion: systemVersion,
          status: StatusEnum.PENDING,
        },
      ];
      updatedModelVersion.status = StatusEnum.PENDING;

      jest.spyOn(repository, 'save').mockResolvedValue(updatedModelVersion);
      jest.spyOn(configService, 'get').mockReturnValue('queue-url');

      await service.trainSystemVersion(modelVersion, systemVersion);

      expect(repository.save).toHaveBeenCalledWith(updatedModelVersion);
      expect(configService.get).toHaveBeenCalledWith(
        'MODEL_SQS_QUEUE_URL_V' + systemVersion,
      );
      expect(responseMapper.mapInternal).toHaveBeenCalled();
      expect(sqsClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: {
            QueueUrl: 'queue-url',
            MessageBody: JSON.stringify(mockModelDto),
          },
        }),
      );
    });
  });

  describe('create', () => {
    it('should create a new ModelVersionEntity with systemVersionsData', async () => {
      const model = new ModelEntity();
      model.id = uuidv4();
      const version = 1;
      const systemVersions = [1, 2];

      const modelVersion = new ModelVersionEntity();
      modelVersion.model = model;
      modelVersion.version = version;
      modelVersion.systemVersionsData = [
        { systemVersion: 1, status: StatusEnum.PENDING },
        { systemVersion: 2, status: StatusEnum.PENDING },
      ];

      jest.spyOn(repository, 'save').mockResolvedValue(modelVersion);

      const result = await service.create(model, version, systemVersions);

      expect(repository.save).toHaveBeenCalledWith(modelVersion);
      expect(result).toEqual(modelVersion);
    });
  });

  describe('startTraining', () => {
    it('should update the status of the model version and systemVersionData to TRAINING', async () => {
      const systemVersion = 1;
      const modelVersion = new ModelVersionEntity();
      modelVersion.id = uuidv4();
      modelVersion.systemVersionsData = [
        { systemVersion: 1, status: StatusEnum.PENDING },
        { systemVersion: 2, status: StatusEnum.PENDING },
      ];

      const updatedModelVersion = { ...modelVersion };
      updatedModelVersion.status = StatusEnum.TRAINING;
      updatedModelVersion.systemVersionsData = [
        { systemVersion: 1, status: StatusEnum.TRAINING },
        { systemVersion: 2, status: StatusEnum.PENDING },
      ];

      jest.spyOn(repository, 'save').mockResolvedValue(updatedModelVersion);

      const result = await service.startTraining(modelVersion, systemVersion);

      expect(repository.save).toHaveBeenCalledWith(updatedModelVersion);
      expect(result).toEqual(updatedModelVersion);
    });
  });

  describe('failTraining', () => {
    it('should update the status of the model version and systemVersionData to FAILED', async () => {
      const systemVersion = 1;
      const modelVersion = new ModelVersionEntity();
      modelVersion.id = uuidv4();
      modelVersion.systemVersionsData = [
        { systemVersion: 1, status: StatusEnum.TRAINING },
        { systemVersion: 2, status: StatusEnum.PENDING },
      ];

      const updatedModelVersion = { ...modelVersion };
      updatedModelVersion.status = StatusEnum.FAILED;
      updatedModelVersion.systemVersionsData = [
        { systemVersion: 1, status: StatusEnum.FAILED },
        { systemVersion: 2, status: StatusEnum.PENDING },
      ];

      jest.spyOn(repository, 'save').mockResolvedValue(updatedModelVersion);

      const result = await service.failTraining(modelVersion, systemVersion);

      expect(repository.save).toHaveBeenCalledWith(updatedModelVersion);
      expect(result).toEqual(updatedModelVersion);
    });
  });

  describe('finishTraining', () => {
    it('should update the systemVersionData and modelVersion status when all trainings are finished', async () => {
      const systemVersion = 1;
      const prompt = 'Sample prompt';
      const modelVersion = new ModelVersionEntity();
      modelVersion.id = uuidv4();
      modelVersion.systemVersionsData = [
        { systemVersion: 1, status: StatusEnum.TRAINING },
        { systemVersion: 2, status: StatusEnum.FINISHED },
      ];

      const updatedModelVersion = { ...modelVersion };
      updatedModelVersion.systemVersionsData = [
        {
          systemVersion: 1,
          status: StatusEnum.FINISHED,
          prompt: prompt,
          trainedAt: expect.any(Date),
        },
        { systemVersion: 2, status: StatusEnum.FINISHED },
      ];
      updatedModelVersion.status = StatusEnum.FINISHED;
      updatedModelVersion.trainedAt = expect.any(Date);

      jest.spyOn(repository, 'save').mockResolvedValue(updatedModelVersion);

      const result = await service.finishTraining(
        modelVersion,
        systemVersion,
        prompt,
      );

      expect(repository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          status: StatusEnum.FINISHED,
          trainedAt: expect.any(Date),
          systemVersionsData: expect.arrayContaining([
            expect.objectContaining({
              systemVersion: 1,
              status: StatusEnum.FINISHED,
              prompt: prompt,
              trainedAt: expect.any(Date),
            }),
            expect.objectContaining({
              systemVersion: 2,
              status: StatusEnum.FINISHED,
            }),
          ]),
        }),
      );
      expect(result).toEqual(updatedModelVersion);
    });

    it('should not update modelVersion status if not all trainings are finished', async () => {
      const systemVersion = 1;
      const prompt = 'Sample prompt';
      const modelVersion = new ModelVersionEntity();
      modelVersion.id = uuidv4();
      modelVersion.status = StatusEnum.TRAINING; // Initialize status
      modelVersion.systemVersionsData = [
        { systemVersion: 1, status: StatusEnum.TRAINING },
        { systemVersion: 2, status: StatusEnum.PENDING },
      ];

      const updatedModelVersion = { ...modelVersion };
      updatedModelVersion.systemVersionsData = [
        {
          systemVersion: 1,
          status: StatusEnum.FINISHED,
          prompt: prompt,
          trainedAt: expect.any(Date),
        },
        { systemVersion: 2, status: StatusEnum.PENDING },
      ];
      // modelVersion.status should remain StatusEnum.TRAINING

      jest.spyOn(repository, 'save').mockResolvedValue(updatedModelVersion);

      const result = await service.finishTraining(
        modelVersion,
        systemVersion,
        prompt,
      );

      expect(repository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          status: StatusEnum.TRAINING, // status remains unchanged
          systemVersionsData: expect.arrayContaining([
            expect.objectContaining({
              systemVersion: 1,
              status: StatusEnum.FINISHED,
              prompt: prompt,
              trainedAt: expect.any(Date),
            }),
            expect.objectContaining({
              systemVersion: 2,
              status: StatusEnum.PENDING,
            }),
          ]),
        }),
      );
      expect(result).toEqual(updatedModelVersion);
    });
  });

  describe('reseTrainingStatus', () => {
    it('should reset the training status of modelVersion and systemVersionsData', async () => {
      const modelVersion = new ModelVersionEntity();
      modelVersion.id = uuidv4();
      modelVersion.status = StatusEnum.FINISHED;
      modelVersion.systemVersionsData = [
        {
          systemVersion: 1,
          status: StatusEnum.FINISHED,
          prompt: 'Some prompt',
          trainedAt: new Date(),
        },
      ];

      const resetModelVersion = { ...modelVersion };
      resetModelVersion.status = StatusEnum.PENDING;
      resetModelVersion.systemVersionsData = [
        {
          systemVersion: 1,
          status: StatusEnum.PENDING,
        },
      ];

      jest.spyOn(repository, 'save').mockResolvedValue(resetModelVersion);

      const result = await service.reseTrainingStatus(modelVersion);

      expect(repository.save).toHaveBeenCalledWith(resetModelVersion);
      expect(result).toEqual(resetModelVersion);
    });
  });

  describe('writeToQueue', () => {
    it('should send a message to the correct SQS queue', async () => {
      const systemVersion = 1;
      const modelVersion = new ModelVersionEntity();
      modelVersion.id = uuidv4();
      modelVersion.modelId = uuidv4();

      const model = new ModelEntity();
      model.id = modelVersion.modelId;
      model.userId = 'user-id';

      jest.spyOn(configService, 'get').mockReturnValue('queue-url');
      jest.spyOn(modelProvider, 'get').mockResolvedValue(model);

      await service.writeToQueue(modelVersion, systemVersion);

      expect(configService.get).toHaveBeenCalledWith(
        'MODEL_SQS_QUEUE_URL_V' + systemVersion,
      );
      expect(modelProvider.get).toHaveBeenCalledWith(modelVersion.modelId);
      expect(responseMapper.mapInternal).toHaveBeenCalledWith(model);
      expect(sqsClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: {
            QueueUrl: 'queue-url',
            MessageBody: JSON.stringify(mockModelDto),
          },
        }),
      );
    });
  });

  describe('save', () => {
    it('should save the entity', async () => {
      const modelVersion = new ModelVersionEntity();
      modelVersion.id = uuidv4();
      jest.spyOn(repository, 'save').mockResolvedValue(modelVersion);

      const result = await service.save(modelVersion);

      expect(repository.save).toHaveBeenCalledWith(modelVersion);
      expect(result).toEqual(modelVersion);
    });
  });

  describe('delete', () => {
    it('should soft delete the entity', async () => {
      const modelVersion = new ModelVersionEntity();
      modelVersion.id = uuidv4();
      jest.spyOn(repository, 'softDelete').mockResolvedValue(undefined);

      await service.delete(modelVersion);

      expect(repository.softDelete).toHaveBeenCalledWith(modelVersion.id);
    });
  });
});
