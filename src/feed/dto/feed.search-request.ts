import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsBoolean,
  IsIn,
  IsInt,
  IsNumber,
  IsOptional,
  IsString,
  IsU<PERSON>D,
  <PERSON>,
  <PERSON>,
} from 'class-validator';

/* Removed BaseSearchRequest because the feed has a different sortOrder */
export class FeedSearchRequest {
  @IsOptional()
  @IsInt()
  @Min(1)
  @Transform((obj) => Number(obj.value))
  @ApiProperty({ description: 'Page number', required: false, default: 1 })
  page?: number = 1;

  @IsOptional()
  @IsString()
  @ApiProperty()
  prompt: string;

  @IsOptional()
  @IsUUID()
  @ApiProperty()
  userId: string;

  @IsOptional()
  @IsString()
  @ApiProperty()
  username: string;

  @IsOptional()
  @IsNumber()
  @ApiProperty()
  systemVersion: number;

  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  isHot: boolean;

  // eslint-disable-next-line @typescript-eslint/no-inferrable-types
  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  onlyFollowing: boolean = false;

  // eslint-disable-next-line @typescript-eslint/no-inferrable-types
  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  includeNsfw: boolean = false;

  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(50)
  @Transform((obj) => Number(obj.value))
  @ApiProperty({
    description: 'Number of items per page',
    required: false,
    default: 50,
  })
  limit?: number = 50;

  @IsOptional()
  @ApiProperty({ description: 'Field to sort by', required: false })
  sortBy?: string = 'createdAt';

  // Now explicitly redefine sortOrder here
  @IsOptional()
  @IsIn(['ASC', 'DESC', 'RANDOM'])
  @ApiProperty({
    description: 'Sort order',
    enum: ['ASC', 'DESC', 'RANDOM'],
    required: false,
  })
  sortOrder?: 'ASC' | 'DESC' | 'RANDOM' = 'DESC'; // default sort order is descending

  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  editedImages: boolean;

  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  day: boolean;

  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  week: boolean;

  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  month: boolean;

  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  year: boolean;
}
