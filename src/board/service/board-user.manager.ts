import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BoardUserEntity } from '../entity/board-user.entity';
import { BoardEntity } from '../entity/board.entity';
import { StatusEnum } from '../enum/boardUser-status.enum';
import { BoardManager } from './manager';

@Injectable()
export class BoardUserManager {
  constructor(
    @InjectRepository(BoardUserEntity)
    private repository: Repository<BoardUserEntity>,
    private boardManager: BoardManager,
  ) {}

  async create(boardUser: BoardUserEntity): Promise<BoardUserEntity> {
    return await this.repository.save(boardUser);
  }

  async update(boardUser: BoardUserEntity): Promise<BoardUserEntity> {
    return await this.repository.save(boardUser);
  }

  async approve(
    board: BoardEntity,
    boardUser: BoardUserEntity,
    approvedBy: string,
  ): Promise<void> {
    boardUser.status = StatusEnum.APPROVED;
    boardUser.approvedAt = new Date();
    boardUser.approvedBy = approvedBy;

    await this.update(boardUser);

    board.members++;
    await this.boardManager.update(board);
  }

  async deny(boardUser: BoardUserEntity): Promise<void> {
    boardUser.status = StatusEnum.DENIED;

    await this.update(boardUser);
  }

  async delete(board: BoardEntity, boardUser: BoardUserEntity): Promise<void> {
    await this.repository.softDelete(boardUser.id);

    if (boardUser.status == StatusEnum.APPROVED) {
      board.members--;
    }

    await this.boardManager.update(board);
  }
}
