import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1716145139511 implements MigrationInterface {
  name = 'Api1716145139511';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE "notification" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "user_id" uuid NOT NULL,
                "type" character varying NOT NULL,
                "channels" json NOT NULL,
                "context" json NOT NULL,
                "content" character varying,
                "read_at" TIMESTAMP,
                "deleted_at" TIMESTAMP,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_705b6c7cdf9b2c2ff7ac7872cb7" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            CREATE TYPE "public"."user_notification_config_channel_enum" AS ENUM('email', 'sms', 'push', 'app', 'chat')
        `);
    await queryRunner.query(`
            CREATE TABLE "user_notification_config" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "user_id" uuid NOT NULL,
                "channel" "public"."user_notification_config_channel_enum" NOT NULL,
                "external_id" character varying,
                "details" json,
                "is_active" boolean NOT NULL DEFAULT true,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "deleted_at" TIMESTAMP,
                CONSTRAINT "PK_04e36cc7c52f5527f4af53e6ec8" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            ALTER TABLE "notification"
            ADD CONSTRAINT "FK_928b7aa1754e08e1ed7052cb9d8" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "user_notification_config"
            ADD CONSTRAINT "FK_372302de510b8b25b6f1e8fe519" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "user_notification_config" DROP CONSTRAINT "FK_372302de510b8b25b6f1e8fe519"
        `);
    await queryRunner.query(`
            ALTER TABLE "notification" DROP CONSTRAINT "FK_928b7aa1754e08e1ed7052cb9d8"
        `);
    await queryRunner.query(`
            DROP TABLE "user_notification_config"
        `);
    await queryRunner.query(`
            DROP TYPE "public"."user_notification_config_channel_enum"
        `);
    await queryRunner.query(`
            DROP TABLE "notification"
        `);
  }
}
