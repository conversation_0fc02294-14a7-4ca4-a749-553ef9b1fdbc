import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

import { ImageCompletionEntity } from '../../image-completion/entity/image-completion.entity';
import { ImageEditEntity } from './image-edit.entity';

@Entity('image_edit_image_completion')
export class ImageEditImageCompletionEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => ImageEditEntity, { eager: false })
  imageEdit: ImageEditEntity;

  @Column({ type: 'uuid', nullable: false })
  @Index('idx_image_edit_image_completion_image_edit_id')
  imageEditId: string;

  @Column({ name: 'image_completion_id' })
  // @Index('idx_image_edit_image_completion_image_completion_id')
  imageCompletionId: string;

  @OneToOne(
    () => ImageCompletionEntity,
    (imageCompletion) => imageCompletion.imageEditImageCompletion,
  )
  @JoinColumn({ name: 'image_completion_id' })
  imageCompletion: ImageCompletionEntity;

  @Column({ default: false })
  isSaved: boolean;
}
