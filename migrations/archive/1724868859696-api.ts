import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1724868859696 implements MigrationInterface {
  name = 'Api1724868859696';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "model_version"
            ADD "system_versions_data" json
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "model_version" DROP COLUMN "system_versions_data"
        `);
  }
}
