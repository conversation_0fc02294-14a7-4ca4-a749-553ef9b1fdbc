import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNumber, IsOptional, IsString } from 'class-validator';
import { BaseSearchRequest } from '../../core/dto/base.search-request';

export class UserSearchRequest extends BaseSearchRequest {
  @IsOptional()
  @IsString()
  @ApiProperty({ required: false })
  username?: string;

  @IsOptional()
  @IsBoolean()
  @ApiProperty({ required: false })
  isVerified?: boolean;

  @IsOptional()
  @IsNumber()
  @ApiProperty({ required: false })
  followersCountMin?: number;

  @IsOptional()
  @IsNumber()
  @ApiProperty({ required: false })
  followersCountMax?: number;

  @IsOptional()
  @IsNumber()
  @ApiProperty({ required: false })
  followingCountMin?: number;

  @IsOptional()
  @IsNumber()
  @ApiProperty({ required: false })
  followingCountMax?: number;

  @IsOptional()
  @IsNumber()
  @ApiProperty({ required: false })
  imagesGeneratedMin?: number;

  @IsOptional()
  @IsNumber()
  @ApiProperty({ required: false })
  imagesGeneratedMax?: number;

  @IsOptional()
  @IsNumber()
  @ApiProperty({ required: false })
  imagesAvailableMin?: number;

  @IsOptional()
  @IsNumber()
  @ApiProperty({ required: false })
  imagesAvailableMax?: number;

  @IsOptional()
  @IsNumber()
  @ApiProperty({ required: false })
  modelsAvailableMin?: number;

  @IsOptional()
  @IsNumber()
  @ApiProperty({ required: false })
  modelsAvailableMax?: number;
}
