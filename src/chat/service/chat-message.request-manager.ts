import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { ChatMessageEntity } from '../entity/chat-message.entity';
import { ChatMessageRequest } from '../dto/chat-message.request';
import { ChatMessageManager } from './chat-message.manager';
import { ChatConversationEntity } from '../entity/chat-conversation.entity';
import { ChatMessageRelationshipEntity } from '../entity/chat-message-relationship.entity';
import { ChatMessageUpdateRequest } from '../dto/chat-message.update-request';

@Injectable()
export class ChatMessageRequestManager {
  constructor(private readonly manager: ChatMessageManager) {}

  async addMessage(
    conversation: ChatConversationEntity,
    userId: string,
    request: ChatMessageRequest,
  ): Promise<ChatMessageEntity> {
    if (request.role === 'user') {
      const hasExceededLimit = await this.manager.getMessageLimit(userId);

      if (hasExceededLimit) {
        throw new HttpException(
          'Rate limit exceeded. You have reached your hourly message limit.',
          HttpStatus.TOO_MANY_REQUESTS,
        );
      }
    }

    const message = new ChatMessageEntity();
    message.content = request.content;
    message.role = request.role;
    message.conversationId = conversation.id;

    if (request.attachedFiles && request.attachedFiles.length > 0) {
      message.attachedFiles = [];

      for (const attachedFile of request.attachedFiles) {
        const chatMessageRelationship = new ChatMessageRelationshipEntity();
        chatMessageRelationship.type = attachedFile.type;
        chatMessageRelationship.referenceId = attachedFile.id;

        message.attachedFiles.push(chatMessageRelationship);
      }
    }

    const savedMessage = await this.manager.create(message);

    return savedMessage;
  }

  async updateMessage(
    message: ChatMessageEntity,
    request: ChatMessageUpdateRequest,
  ): Promise<ChatMessageEntity> {
    if (request.attachedFiles && request.attachedFiles.length > 0) {
      message.attachedFiles = [];

      for (const attachedFile of request.attachedFiles) {
        const chatMessageRelationship = new ChatMessageRelationshipEntity();
        chatMessageRelationship.type = attachedFile.type;
        chatMessageRelationship.referenceId = attachedFile.id;

        message.attachedFiles.push(chatMessageRelationship);
      }
    }

    const savedMessage = await this.manager.update(message);

    return savedMessage;
  }

  async getUserMessageUsage(userId: string) {
    return this.manager.getUserMessageUsage(userId);
  }
}
