import { Controller, Get, NotFoundException } from '@nestjs/common';
import {
  ApiTags,
  ApiOkResponse,
  ApiOperation,
  ApiResponse,
  ApiBadRequestResponse,
} from '@nestjs/swagger';
import { GlobalNotificationProvider } from '../service/global-notification.provider';
import { GlobalNotificationResponseMapper } from '../service/global-notification.response-mapper';
import { GlobalNotificationDto } from '../dto/global-notification.dto';
import { AuthOptional } from 'src/core/security/public-routes';

@ApiTags('notifications')
@Controller('notifications/global')
export class NotificationGlobalController {
  constructor(
    private provider: GlobalNotificationProvider,
    private responseMapper: GlobalNotificationResponseMapper,
  ) {}

  @AuthOptional()
  @ApiOperation({
    operationId: 'notifications_global_get',
    summary: 'Get global notification',
    description: 'Retrieves the current global notification if it is active.',
  })
  @ApiOkResponse({
    type: GlobalNotificationDto,
    description: 'Returns the active global notification.',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found. No active global notification.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - Invalid or unavailable parameters.
    `,
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. An unexpected error occurred.',
  })
  @Get()
  async get(): Promise<GlobalNotificationDto> {
    const entity = await this.provider.get('global');

    if (!entity.isActive) {
      throw new NotFoundException();
    }

    return this.responseMapper.map(entity);
  }
}
