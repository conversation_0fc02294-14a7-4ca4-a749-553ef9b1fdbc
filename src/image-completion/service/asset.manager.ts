import { Inject, Injectable } from '@nestjs/common';
import { ImageCompletionEntity } from '../entity/image-completion.entity';
import {
  S3Client,
  GetObjectCommand,
  ListObjectsV2Command,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AssetManager {
  constructor(
    private configService: ConfigService,
    @Inject('S3') private s3: S3Client,
  ) {}

  generateStorageBucket(entity: ImageCompletionEntity): void {
    entity.storageBucket = this.configService.get<string>(
      'IMAGE_COMPLETION_S3_BUCKET_NAME',
    );
  }

  generateStoragePath(entity: ImageCompletionEntity): void {
    if (!entity.storageBucket) {
      this.generateStorageBucket(entity);
    }
    entity.storagePath = `${entity.user.id}/${entity.id}`;
  }

  async getObject(entity: ImageCompletionEntity, key: string): Promise<any> {
    const command = new GetObjectCommand({
      Bucket: entity.storageBucket,
      Key: key,
    });

    return await this.s3.send(command);
  }

  async generateSignedUrl(
    entity: ImageCompletionEntity,
    key: string,
    expiresIn: number,
  ): Promise<string> {
    const command = new GetObjectCommand({
      Bucket: entity.storageBucket,
      Key: key,
    });

    return getSignedUrl(this.s3, command, { expiresIn: expiresIn });
  }

  async generateSignedUrls(entity: ImageCompletionEntity): Promise<string[]> {
    const objects = await this.s3.send(
      new ListObjectsV2Command({
        Bucket: entity.storageBucket,
        Prefix: entity.storagePath,
      }),
    );

    if (!objects.hasOwnProperty('Contents')) {
      return [];
    }

    const urls = [];
    for (const object of objects.Contents) {
      urls.push(
        await this.generateSignedUrl(entity, object.Key, 60 * 60), // URL expires in 15 minutes
      );
    }

    return urls;
  }
}
