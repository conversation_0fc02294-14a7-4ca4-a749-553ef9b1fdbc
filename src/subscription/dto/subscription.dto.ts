import { ApiProperty } from '@nestjs/swagger';

export class SubscriptionDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  userId?: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  price: number;

  @ApiProperty()
  paidAt?: Date;

  @ApiProperty()
  expiresAt?: Date;

  @ApiProperty()
  stripeCheckoutUrl?: string;

  @ApiProperty()
  creditPackage: string;

  @ApiProperty()
  status: string;

  @ApiProperty()
  createdAt?: Date;
}
