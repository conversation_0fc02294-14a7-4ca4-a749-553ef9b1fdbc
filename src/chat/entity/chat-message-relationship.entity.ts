import {
  Column,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { ChatMessageEntity } from './chat-message.entity';

@Entity('chat_message_relationship')
export class ChatMessageRelationshipEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => ChatMessageEntity)
  chatMessage: ChatMessageEntity;

  @Column({ type: 'uuid', nullable: false })
  @Index('idx_chat_message_relationship_chat_message_id')
  chatMessageId: string;

  @Column()
  type: 'image_completion' | 'image_edit' | 'video';

  @Column()
  @Index('chat_message_relationship_reference_id')
  referenceId: string;
}
