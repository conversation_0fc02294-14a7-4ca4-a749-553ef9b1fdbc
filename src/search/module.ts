import { DynamicModule, forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserModule } from 'src/user/user.module';
import { UserSearchImageController } from './controller/user-search-image.controller';
import { UserSearchImageEntity } from './entity/user-search-image.entity';
import { UserSearchImageManager } from './service/user-search-image.manager';
import { UserSearchImageProvider } from './service/user-search-image.provider';
import { UserSearchImageRequestManager } from './service/user-search-image.request-manager';
import { UserSearchImageResponseMapper } from './service/user-search-image.response-mapper';
import { ImageCompletionModule } from 'src/image-completion/module';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserSearchImageEntity]),
    forwardRef(() => ImageCompletionModule),
    forwardRef(() => UserModule),
  ],
  providers: [
    UserSearchImageManager,
    UserSearchImageProvider,
    UserSearchImageRequestManager,
    UserSearchImageResponseMapper,
  ],
  exports: [
    UserSearchImageManager,
    UserSearchImageProvider,
    UserSearchImageRequestManager,
    UserSearchImageResponseMapper,
  ],
})
export class SearchModule {
  static register(enableControllers: boolean): DynamicModule {
    return {
      module: SearchModule,
      controllers: enableControllers ? [UserSearchImageController] : [],
    };
  }
}
