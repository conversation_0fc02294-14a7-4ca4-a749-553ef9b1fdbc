import { DynamicModule, Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { jwtConfig } from '../auth/config/jwt.config';
import { NotificationEntity } from './entity/notification.entity';
import { ReadController } from './controller/read.controller';
import { DeleteController as UserNotificationConfigDeleteController } from './controller/user-notification-config/delete.controller';
import { ReadController as UserNotificationConfigReadController } from './controller/user-notification-config/read.controller';
import { UpdateController as UserNotificationConfigUpdateController } from './controller/user-notification-config/update.controller';
import { NotificationProvider } from './service/provider';
import { NotificationManager } from './service/manager';
import { UserNotificationConfigRequestManager } from './service/user-notification-config.request-manager';
import { NotificationResponseMapper } from './service/response-mapper';
import { UserNotificationConfigManager } from './service/user-notification-config.manager';
import { UserNotificationConfigProvider } from './service/user-notification-config.provider';
import { UserNotificationConfigResponseMapper } from './service/user-notification-config.response-mapper';
import { UserNotificationConfigEntity } from './entity/user-notification-config.entity';
import { UserModule } from 'src/user/user.module';
import { Notifier } from './service/notifier';
import { UpdateController } from './controller/update.controller';
import { GlobalNotificationResponseMapper } from './service/global-notification.response-mapper';
import { GlobalNotificationProvider } from './service/global-notification.provider';
import { NotificationGlobalController } from './controller/global.controller';
import { GlobalNotificationEntity } from './entity/global-notification.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      NotificationEntity,
      UserNotificationConfigEntity,
      GlobalNotificationEntity,
    ]),
    PassportModule,
    forwardRef(() => UserModule),
    JwtModule.register(jwtConfig),
  ],
  exports: [Notifier],
  providers: [
    Notifier,
    NotificationProvider,
    NotificationManager,
    NotificationResponseMapper,
    GlobalNotificationProvider,
    GlobalNotificationResponseMapper,
    UserNotificationConfigProvider,
    UserNotificationConfigManager,
    UserNotificationConfigRequestManager,
    UserNotificationConfigResponseMapper,
  ],
})
export class NotificationModule {
  static register(enableControllers: boolean): DynamicModule {
    return {
      module: NotificationModule,
      controllers: enableControllers
        ? [
            UpdateController,
            ReadController,
            NotificationGlobalController,
            UserNotificationConfigDeleteController,
            UserNotificationConfigReadController,
            UserNotificationConfigUpdateController,
          ]
        : [],
    };
  }
}
