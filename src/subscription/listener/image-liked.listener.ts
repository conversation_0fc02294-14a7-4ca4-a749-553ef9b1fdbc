import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { ImageLikedEvent } from 'src/image-completion/event/image-liked.event';
import { UserManager } from 'src/user/service/manager';
import { UserProvider } from 'src/user/service/provider';
import { CreditTypeEnum } from '../entity/credit-type.enum';
import { TransactionTypeEnum } from '../entity/transaction-type.enum';
import { TransactionManager } from '../service/transaction.manager';

@Injectable()
export class ImageLikedListener {
  constructor(
    private userProvider: UserProvider,
    private userManager: UserManager,
    private transactionManager: TransactionManager,
  ) {}

  @OnEvent('image.liked', { async: true })
  async handleImageLikedEvent(event: ImageLikedEvent) {
    const user = await this.userProvider.get(event.userId);

    if (user.grants.imageLike === true) {
      return;
    }

    const amount = 50;

    await this.transactionManager.register(
      TransactionTypeEnum.TOP_UP,
      amount,
      CreditTypeEnum.IMAGE,
      'ImageCompletionLike|' + event.id,
      true,
      user.id,
    );

    user.grants.imageLike = true;

    await this.userManager.update(user);
  }
}
