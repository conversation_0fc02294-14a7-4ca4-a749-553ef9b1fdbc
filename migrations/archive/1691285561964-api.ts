import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1691285561964 implements MigrationInterface {
    name = 'Api1691285561964'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "user_external_profile" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "platform" character varying NOT NULL,
                "username" character varying,
                "external_id" character varying,
                "profile_data" json,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "user_id" uuid,
                CONSTRAINT "PK_43f139a28395b4203185e0166f4" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            ALTER TABLE "user_external_profile"
            ADD CONSTRAINT "FK_e571dd9a3e946673d221de40f82" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_external_profile" DROP CONSTRAINT "FK_e571dd9a3e946673d221de40f82"
        `);
        await queryRunner.query(`
            DROP TABLE "user_external_profile"
        `);
    }

}
