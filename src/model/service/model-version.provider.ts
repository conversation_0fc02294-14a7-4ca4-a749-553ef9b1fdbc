import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { AbstractProvider } from 'src/core/service/abstract.provider';
import { Repository } from 'typeorm';
import { ModelVersionEntity } from '../entity/model-version.entity';

@Injectable()
export class ModelVersionProvider extends AbstractProvider<ModelVersionEntity> {
  constructor(
    @InjectRepository(ModelVersionEntity)
    repository: Repository<ModelVersionEntity>,
    logger: Logger,
  ) {
    super(repository, logger);
  }

  async findByModelId(modelId: string): Promise<ModelVersionEntity[]> {
    return await this.repository.findBy({ model: { id: modelId } });
  }

  async getModelVersion(
    modelId: string,
    version: number,
  ): Promise<ModelVersionEntity> {
    return await this.getBy({
      model: { id: modelId },
      version: version,
    });
  }

  async findModelVersion(
    modelId: string,
    version: number,
  ): Promise<ModelVersionEntity> {
    return await this.findOneBy({
      model: { id: modelId },
      version: version,
    });
  }
}
