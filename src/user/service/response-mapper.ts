import { Injectable } from '@nestjs/common';
import { UserOAuthDto } from '../dto/user-oauth.dto';
import { UserSignupDto } from '../dto/user-signup.dto';
import { UserDto } from '../dto/user.dto';
import { UserEntity } from '../entity/user.entity';
import { ConfigService } from '@nestjs/config';
import { PublicUserDto } from '../dto/public.user.dto';
import { UserLeaderboardDTO } from '../dto/user-leaderboard.dto';
import { UserProvider } from './provider';

@Injectable()
export class UserResponseMapper {
  private cdnHost: string;

  constructor(
    private configService: ConfigService,
    private userProvider: UserProvider,
  ) {
    this.cdnHost = this.configService.get<string>('CDN_HOST');
  }

  map(user: UserEntity): UserDto {
    const userDto = new UserDto();

    userDto.id = user.id;
    userDto.name = user.name;
    userDto.username = user.username;
    userDto.email = user.email.toLowerCase();

    if (user.profilePicture) {
      userDto.profilePicture = this.mapProfilePicture(user);
    }

    userDto.description = user.description;
    userDto.website = user.website;
    userDto.socialMediaAccounts = user.socialMediaAccounts;
    userDto.timezone = user.timezone;
    userDto.frontendTheme = user.frontendTheme;
    userDto.imageQueue = user.imageQueue;
    userDto.currency = user.currency;
    userDto.locale = user.locale;
    userDto.birthday = user.birthday?.toString() || null;
    userDto.imagesGenerated = user.imagesGenerated;
    userDto.imagesAvailable = user.imagesAvailable;
    userDto.modelsAvailable = user.modelsAvailable;
    userDto.followersCount = user.followersCount;
    userDto.followingCount = user.followingCount;
    userDto.systemVersion = user.systemVersion;
    userDto.isEmailValidated = user.emailValidatedAt !== null;
    userDto.isActive = user.isActive;
    userDto.isBlocked = user.blockedAt !== null;
    userDto.isVerified = user.isVerified;
    userDto.isBot = user.isBot;
    userDto.allowSwapping = user.allowSwapping;
    userDto.includeWatermarks = user.includeWatermarks;
    userDto.hidePrompt = user.hidePrompt;
    userDto.tutorialSteps = user.tutorialSteps;

    return userDto;
  }

  mapPublic(user: UserEntity): PublicUserDto {
    const publicUserDto = new PublicUserDto();
    publicUserDto.id = user.id;
    publicUserDto.name = user.name;
    publicUserDto.username = user.username;

    if (user.profilePicture) {
      publicUserDto.profilePicture = this.mapProfilePicture(user);
    }

    publicUserDto.description = user.description;
    publicUserDto.website = user.website;
    publicUserDto.imagesGenerated = user.imagesGenerated;
    publicUserDto.imagesAvailable = user.imagesAvailable;
    publicUserDto.modelsAvailable = user.modelsAvailable;
    publicUserDto.followersCount = user.followersCount;
    publicUserDto.followingCount = user.followingCount;
    publicUserDto.isVerified = user.isVerified;

    return publicUserDto;
  }

  async mapLeaderboard(entity: any, rank: number = null) {
    const user = await this.userProvider.get(entity.userid);
    const leaderBoardDto = new UserLeaderboardDTO();

    if (rank) {
      leaderBoardDto.rank = rank;
    }
    leaderBoardDto.username = entity.username;
    leaderBoardDto.id = entity.userid;
    leaderBoardDto.profilePicture = this.mapProfilePicture(user);
    leaderBoardDto.amount = entity.amount;

    return leaderBoardDto;
  }

  async mapLeaderboardMultiple(entities: any, offset: number) {
    const leaderboard = [];

    for (let index = 0; index < entities.length && index < 25; index++) {
      leaderboard.push(
        await this.mapLeaderboard(entities[index], index + 1 + offset),
      );
    }
    return leaderboard;
  }

  mapProfilePicture(user: UserEntity): string | null {
    if (!user.profilePicture) {
      return null;
    }

    return `${this.cdnHost}/${user.profilePicture}`;
  }

  mapMultiple(users: UserEntity[]): UserDto[] {
    return users.map((user) => this.map(user));
  }

  mapMultiplePublic(users: UserEntity[]): PublicUserDto[] {
    return users.map((user) => this.mapPublic(user));
  }

  mapSignUp(user: UserEntity): UserSignupDto {
    const userSignupDto = new UserSignupDto();

    userSignupDto.id = user.id;
    userSignupDto.name = user.name;
    userSignupDto.email = user.email.toLowerCase();
    userSignupDto.username = user.username;
    userSignupDto.signupCode = user.signupCode;
    userSignupDto.isEmailValidated = user.emailValidatedAt !== null;
    userSignupDto.createdAt = user.createdAt;
    userSignupDto.isBot = user.isBot;

    return userSignupDto;
  }

  mapMultipleUserSignup(entities: UserEntity[]): any {
    return entities.map((user) => this.mapSignUp(user));
  }

  mapOAuthInfo(user: UserEntity): UserOAuthDto {
    return {
      oAuthId: user.oAuthId,
      oAuthToken: user.oAuthToken,
      provider: user.oAuthProvider,
    };
  }
}
