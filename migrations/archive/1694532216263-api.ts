import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1694532216263 implements MigrationInterface {
  name = 'Api1694532216263';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "model_queue" character varying
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "image_queue" character varying
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion_model" DROP CONSTRAINT "FK_b647c482570bb6aaf5d5a39f84e"
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion_model"
            ADD CONSTRAINT "FK_b647c482570bb6aaf5d5a39f84e" FOREIGN KEY ("model_id") REFERENCES "model"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "image_completion_model" DROP CONSTRAINT "FK_b647c482570bb6aaf5d5a39f84e"
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion_model"
            ALTER COLUMN "model_id" DROP NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion_model"
            ADD CONSTRAINT "FK_b647c482570bb6aaf5d5a39f84e" FOREIGN KEY ("model_id") REFERENCES "model"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "image_queue"
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "model_queue"
        `);
  }
}
