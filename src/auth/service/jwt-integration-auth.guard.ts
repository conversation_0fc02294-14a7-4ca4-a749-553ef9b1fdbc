import {
  CanActivate,
  ExecutionContext,
  Inject,
  Injectable,
  UnauthorizedException,
  forwardRef,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { Logger } from 'nestjs-pino';
import {
  IS_AUTH_OPTIONAL_KEY,
  IS_PUBLIC_KEY,
} from 'src/core/security/public-routes';
import { OrganizationProvider } from 'src/organization/service/provider';
import { UserProvider } from 'src/user/service/provider';
import { OrganizationAuthTokenProvider } from './organization-auth-token.provider';
import { UserAuthTokenProvider } from './user-auth-token.provider';

@Injectable()
export class JwtIntegrationAuthGuard implements CanActivate {
  constructor(
    private readonly jwtService: JwtService,
    private readonly userProvider: UserProvider,
    private readonly userAuthTokenProvider: UserAuthTokenProvider,
    @Inject(forwardRef(() => OrganizationProvider))
    private readonly organizationProvider: OrganizationProvider,
    private readonly organizationAuthTokenProvider: OrganizationAuthTokenProvider,
    private readonly reflector: Reflector,
    private readonly logger: Logger,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    const request: any = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);
    const isAuthOptional = this.reflector.getAllAndOverride<boolean>(
      IS_AUTH_OPTIONAL_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!token && !isAuthOptional) {
      throw new UnauthorizedException();
    }

    if (token) {
      try {
        const payload: any = this.jwtService.verify(token);

        if (payload.tokenType === 'organization') {
          request.organization = await this.organizationProvider.get(
            payload.organizationId,
          );
          const orgAuth = await this.organizationAuthTokenProvider.findOneBy({
            organization: { id: request.organization.id },
            token,
            type: 'integration',
          });
          if (!orgAuth) {
            throw new UnauthorizedException('Invalid token');
          }
          request.integrationAuthToken = orgAuth;
        } else {
          request.user = await this.userProvider.get(payload.id);
          const userAuth = await this.userAuthTokenProvider.findOneBy({
            user: { id: request.user.id },
            token,
            type: 'integration',
          });
          if (!userAuth) {
            throw new UnauthorizedException('Invalid token');
          }
          request.integrationAuthToken = userAuth;
        }
      } catch (err) {
        this.logger.verbose('Invalid integration token', err);
        throw new UnauthorizedException('Invalid token');
      }
    }

    return true;
  }

  private extractTokenFromHeader(request: any): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
