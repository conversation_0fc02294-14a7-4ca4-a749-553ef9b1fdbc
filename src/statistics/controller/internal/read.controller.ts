import { <PERSON>, Get, Param, Query, Res } from '@nestjs/common';
import { ApiOkResponse, ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { BaseFindResponseHeadersDto } from 'src/core/dto/base-find-response-headers.dto';
import { StatisticsProvider } from '../../service/provider';
import { StatisticsResponseMapper } from '../../service/response-mapper';
import { StatisticsDTO } from '../../dto/statistics.dto';
import { StatisticsSearchRequest } from '../../dto/statistics.search-request';

@ApiTags('statistics')
@Controller('internal/statistics')
export class ReadController {
  constructor(
    private provider: StatisticsProvider,
    private responseMapper: StatisticsResponseMapper,
  ) {}

  @ApiOkResponse({
    type: StatisticsDTO,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
  })
  @ApiQuery({ type: StatisticsSearchRequest })
  @Get()
  async find(
    @Res() res: Response,
    @Query() query: StatisticsSearchRequest,
  ): Promise<void> {
    const { initialDate, endDate } = query;

    // Adjust endDate to the last millisecond of the day
    const adjustedEndDate = new Date(endDate);
    adjustedEndDate.setHours(23, 59, 59, 999);

    const statistics = await this.provider.findStatistics(
      initialDate,
      adjustedEndDate,
    );
    res.send(await this.responseMapper.statisticTotalResponse(statistics));
  }

  @ApiParam({ name: 'year' })
  @Get('yearly/:year')
  async findYearly(@Res() res: Response, @Param() params): Promise<void> {
    const yearlyStatistics = await this.provider.findYearlyStatistics(
      Number(params.year),
    );
    res.send(await this.responseMapper.mapMultiple(yearlyStatistics));
  }

  @ApiParam({ name: 'month' })
  @ApiParam({ name: 'year' })
  @Get('monthly/:year/:month')
  async findMonthly(@Res() res: Response, @Param() params): Promise<void> {
    const monthlyStatistics = await this.provider.findMonthlyStatistics(
      Number(params.year),
      Number(params.month),
    );
    res.send(await this.responseMapper.mapMultiple(monthlyStatistics));
  }
}
