import { Injectable } from '@nestjs/common';
import { UserNotificationConfigDto } from '../dto/user-notification-config.dto';
import { UserNotificationConfigEntity } from '../entity/user-notification-config.entity';

@Injectable()
export class UserNotificationConfigResponseMapper {
  map(entity: UserNotificationConfigEntity): UserNotificationConfigDto {
    const dto = new UserNotificationConfigDto();

    dto.id = entity.id;
    dto.userId = entity.userId;
    dto.channel = entity.channel;
    dto.externalId = entity.externalId;
    dto.details = entity.details;
    dto.isActive = entity.isActive;
    dto.createdAt = entity.createdAt;

    return dto;
  }

  mapMultiple(
    entities: UserNotificationConfigEntity[],
  ): UserNotificationConfigDto[] {
    const dtos = [];

    for (const entity of entities) {
      dtos.push(this.map(entity));
    }

    return dtos;
  }
}
