import { BadRequestException } from '@nestjs/common';
import { Logger } from 'nestjs-pino';
import {
  EntityNotFoundError,
  EntityPropertyNotFoundError,
  FindManyOptions,
  FindOptionsOrder,
  Repository,
} from 'typeorm';
import { FindOneOptions } from 'typeorm/find-options/FindOneOptions';

export abstract class AbstractProvider<Entity> {
  protected constructor(
    protected repository: Repository<Entity>,
    protected logger: Logger,
  ) {}

  async findAll(page: number, limit: number): Promise<Entity[]> {
    return await this.repository.find(
      this.prepareFindManyOptions({}, page, limit),
    );
  }

  async findBy(
    criteria: any,
    page: number,
    limit: number,
    sortBy?: string,
    sortOrder?: string,
  ): Promise<Entity[]> {
    try {
      const options = this.prepareFindManyOptions(
        criteria,
        page,
        limit,
        sortBy,
        sortOrder,
      );

      return await this.repository.find(options);
    } catch (e) {
      this.logger.error('provider.findBy', {
        error: e.message,
        criteria: criteria,
        page: page,
        limit: limit,
        sortBy: sortBy,
        sortOrder: sortOrder,
      });

      if (e instanceof EntityPropertyNotFoundError) {
        throw new BadRequestException(`Property "${sortBy}" not found`);
      }

      throw e;
    }
  }

  async countBy(criteria: any): Promise<number> {
    const options = this.prepareFindManyOptions(criteria, 1, 10);

    return await this.repository.countBy(options.where);
  }

  async findOne(id: string): Promise<Entity> {
    const options = this.prepareFindOneOptions({ id: id });

    return await this.repository.findOne(options);
  }

  async findOneBy(criteria: any): Promise<Entity> {
    const options = this.prepareFindOneOptions(criteria);

    return await this.repository.findOne(options);
  }

  async get(id: string): Promise<Entity> {
    const entityObject = await this.findOne(id);

    if (!entityObject) {
      throw new EntityNotFoundError(this.repository.target, id);
    }

    return entityObject;
  }

  async getBy(criteria: any): Promise<Entity> {
    const entityObject = await this.findOneBy(criteria);

    if (!entityObject) {
      throw new EntityNotFoundError(this.repository.target, criteria);
    }

    return entityObject;
  }

  prepareFindOneOptions(criteria: any): FindOneOptions<Entity> {
    return { where: criteria };
  }

  prepareFindManyOptions(
    criteria: any,
    page: number,
    limit: number,
    sortBy?: string,
    sortOrder = 'ASC',
  ): FindManyOptions<Entity> {
    const skip = (page - 1) * limit;

    delete criteria.page;
    delete criteria.limit;

    const order: Record<string, 'ASC' | 'DESC'> = {};

    if (sortBy) {
      order[sortBy] = (sortOrder ?? 'ASC') as 'ASC' | 'DESC';
    }

    delete criteria.sortBy;
    delete criteria.sortOrder;

    const options: FindManyOptions<Entity> = {
      where: criteria,
      skip: skip,
      take: limit,
      order: order as FindOptionsOrder<Entity>,
    };

    return options;
  }
}
