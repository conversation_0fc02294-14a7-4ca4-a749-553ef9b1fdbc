import {
  registerDecorator,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
} from 'class-validator';
import { Injectable } from '@nestjs/common';
import { BlockedEmailDomainsService } from './blocked-email-domains.service';

@ValidatorConstraint({ name: 'isEmailDomainAllowed', async: false })
@Injectable()
export class IsEmailDomainAllowedConstraint implements ValidatorConstraintInterface {
  constructor(private readonly blockedEmailDomainsService: BlockedEmailDomainsService) {}

  validate(email: any, args: ValidationArguments) {
    if (!email || typeof email !== 'string') {
      return true; // Let other validators handle invalid email format
    }

    return !this.blockedEmailDomainsService.isEmailDomainBlocked(email);
  }

  defaultMessage(args: ValidationArguments) {
    return 'Email address from disposable or temporary email providers is not allowed. Please use a permanent email address.';
  }
}

export function IsEmailDomainAllowed(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsEmailDomainAllowedConstraint,
    });
  };
}
