import { Test, TestingModule } from '@nestjs/testing';
import { BookmarkRequestManager } from './bookmark.request-manager';
import { BookmarkProvider } from './bookmark.provider';
import { BookmarkManager } from './bookmark.manager';
import { ModelProvider } from 'src/model/service/provider';
import { ImageCompletionProvider } from 'src/image-completion/service/provider';

describe('BookmarkRequestManager', () => {
  let requestManager: BookmarkRequestManager;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BookmarkRequestManager,
        {
          provide: BookmarkProvider,
          useValue: {},
        },
        {
          provide: BookmarkManager,
          useValue: {},
        },
        {
          provide: ModelProvider,
          useValue: {},
        },
        {
          provide: ImageCompletionProvider,
          useValue: {},
        },
      ],
    }).compile();

    requestManager = module.get<BookmarkRequestManager>(BookmarkRequestManager);
  });

  it('should be defined', () => {
    expect(requestManager).toBeDefined();
  });

  // Add more tests for methods like create, delete
});
