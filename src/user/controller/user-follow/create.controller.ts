import { Controller, HttpCode, Post, Request } from '@nestjs/common';
import { Param } from '@nestjs/common/decorators/http/route-params.decorator';
import {
  ApiBadRequestResponse,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiParam,
  ApiTags,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiNotFoundResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { UserFollowDto } from '../../dto/user-follow.dto';
import { UserFollowManager } from '../../service/user-follow.manager';
import { UserFollowResponseMapper } from '../../service/user-follow.response-mapper';
import { UserProvider } from 'src/user/service/provider';

@ApiTags('user-follow')
@Controller('users/current/follows')
export class CreateController {
  constructor(
    private responseMapper: UserFollowResponseMapper,
    private manager: <PERSON><PERSON><PERSON><PERSON>owManager,
    private userProvider: UserProvider,
  ) {}

  @ApiOperation({
    operationId: 'user_follow_create',
    summary: 'Follow a user',
    description:
      'Creates a follow relationship between the current user and the specified user.\n\n' +
      'Requirements:\n' +
      '- User must be authenticated\n' +
      '- The user to follow must exist\n\n' +
      'Required Parameters:\n' +
      '- followingId: The unique identifier of the user to follow\n\n' +
      'Returns the created follow relationship object.',
  })
  @ApiOkResponse({
    type: UserFollowDto,
    description: 'Follow relationship created.',
  })
  @ApiCreatedResponse({ description: 'Follow relationship created.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`followingId\`: Must be a valid UUID.
  `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiNotFoundResponse({
    description: 'Not Found. The user to follow could not be found.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiParam({
    name: 'followingId',
    description: 'The unique identifier of the user to follow',
    type: 'string',
  })
  @Post(':followingId')
  @HttpCode(201)
  async create(
    @Request() request,
    @Param('followingId') followingId: string,
  ): Promise<UserFollowDto> {
    const user = request.user;

    const following = await this.userProvider.get(followingId);

    const userFollow = await this.manager.follow(user, following);

    return this.responseMapper.map(userFollow);
  }
}
