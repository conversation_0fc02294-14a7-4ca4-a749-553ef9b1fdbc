import {
  Controller,
  Get,
  Param,
  Query,
  <PERSON>s,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiOkResponse,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiOperation,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { ImageCompletionDto } from '../../dto/image-completion.dto';
import { ImageCompletionSearchRequest } from '../../dto/image-completion.search-request';
import { ImageCompletionProvider } from '../../service/provider';
import { ImageCompletionResponseMapper } from '../../service/response-mapper';

@ApiTags('image_completion / internal')
@Controller('internal/image_completions')
export class ReadController {
  constructor(
    private provider: ImageCompletionProvider,
    private responseMapper: ImageCompletionResponseMapper,
  ) {}

  @Get()
  @ApiOperation({
    operationId: 'internal_image_completion_list',
    summary: 'List internal image completions',
    description:
      'Retrieves a paginated list of image completions for internal use.\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of items per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- status: Filter by status\n' +
      '- privacy: Filter by privacy\n' +
      '- userId: Filter by user ID\n' +
      '- username: Filter by username\n' +
      '- prompt: Filter by prompt\n' +
      '- systemVersion: Filter by system version\n' +
      '- isHot: Filter by hot images\n' +
      '- includeNsfw: Include NSFW content\n' +
      '- onlyFollowing: Show only images from followed users\n' +
      '- modelIds[]: Filter by model IDs\n' +
      '- editedImages: Filter by edited images\n' +
      '- day: Filter by items created today\n' +
      '- week: Filter by items created this week\n' +
      '- month: Filter by items created this month\n' +
      '- year: Filter by items created this year\n',
  })
  @ApiOkResponse({
    type: ImageCompletionDto,
    isArray: true,
    description: 'Paginated list of image completions.',
  })
  @ApiQuery({
    type: ImageCompletionSearchRequest,
    description:
      'Query parameters for searching and paginating image completions.',
  })
  @ApiBadRequestResponse({
    description: `
      Bad Request. Possible reasons:\n
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - Filter parameters must be valid values.
      - \`userId\`: Must be a valid UUID.
      - \`username\`: Must be a valid username.
      - \`systemVersion\`: Must be a valid system version.
      - \`isHot\`: Must be a valid boolean value.
      - \`includeNsfw\`: Must be a valid boolean value.
      - \`editedImages\`: Must be a valid boolean value.
      - \`day\`: Must be a valid boolean value.
      - \`week\`: Must be a valid boolean value.
      - \`month\`: Must be a valid boolean value.
      - \`year\`: Must be a valid boolean value.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @UsePipes(new ValidationPipe())
  async find(
    @Query() query: ImageCompletionSearchRequest,
    @Res() res: Response,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;

    const entities = await this.provider.findBy(
      inputFilters,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    const totalCount = await this.provider.countBy(inputFilters);
    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultipleInternal(entities));
  }

  @Get(':id')
  @ApiOperation({
    operationId: 'internal_image_completion_get',
    summary: 'Get internal image completion by ID',
    description:
      'Retrieves a specific image completion by its UUID for internal use.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the image completion\n',
  })
  @ApiOkResponse({
    type: ImageCompletionDto,
    description: 'Returns the image completion with the specified ID.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the image completion to retrieve.',
    type: String,
  })
  @ApiBadRequestResponse({
    description: 'Bad Request. Invalid image completion ID format.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async get(@Param('id') id: string): Promise<ImageCompletionDto> {
    return this.provider
      .get(id)
      .then((entity) => this.responseMapper.mapInternal(entity));
  }
}
