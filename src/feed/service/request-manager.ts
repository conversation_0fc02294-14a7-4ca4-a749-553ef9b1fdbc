import { Injectable } from '@nestjs/common';
import { ImageCompletionSearchRequest } from 'src/image-completion/dto/image-completion.search-request';
import {
  PrivacyEnum as ImageCompletionPrivacyEnum,
  StatusEnum as ImageCompletionStatusEnum,
} from 'src/image-completion/entity/image-completion.entity';
import { ImageCompletionRequestManager } from 'src/image-completion/service/request-manager';
import { UserEntity } from 'src/user/entity/user.entity';
import { FeedSearchRequest } from '../dto/feed.search-request';
import { UserProvider } from 'src/user/service/provider';

@Injectable()
export class FeedRequestManager {
  constructor(
    private imageCompletionRequestManager: ImageCompletionRequestManager,
    private userProvider: UserProvider,
  ) {}

  async sanitizeSearchFilters(
    filters: FeedSearchRequest,
    currentUser: UserEntity,
  ): Promise<any> {
    if (filters.hasOwnProperty('username')) {
      const filteredUser = await this.userProvider.getBy({
        username: filters.username,
      });

      filters.userId = filteredUser.id;

      delete filters.username;
    }

    const criteria = {
      followerId: null,
      isNsfw: false,
      privacy: ImageCompletionPrivacyEnum.PUBLIC,
      status: ImageCompletionStatusEnum.READY,
      hideFromUserProfile: false,
      ...filters,
    };

    if (filters.onlyFollowing) {
      criteria.followerId = currentUser.id;
    }

    return criteria;
  }
}
