import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { ModelEntity } from './model.entity';
import { UserEntity } from '../../user/entity/user.entity';

@Entity('model_comment')
export class ModelCommentEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', nullable: false })
  @Index('idx_model_comment_model_id')
  modelId: string;

  @ManyToOne(() => ModelEntity)
  model: ModelEntity;

  @Column({ type: 'uuid', nullable: false })
  @Index('idx_model_comment_user_id')
  userId: string;

  @ManyToOne(() => UserEntity)
  user: UserEntity;

  @Column({ type: 'text' })
  comment: string;

  @Column({ nullable: false, default: 0 })
  likes: number;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt?: Date;

  @CreateDateColumn()
  createdAt: Date;
}
