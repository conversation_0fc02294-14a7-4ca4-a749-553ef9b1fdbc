import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1699334401927 implements MigrationInterface {
    name = 'Api1699334401927'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "credit_package"
            ADD "is_recurring" boolean NOT NULL DEFAULT true
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "credit_package" DROP COLUMN "is_recurring"
        `);
    }

}
