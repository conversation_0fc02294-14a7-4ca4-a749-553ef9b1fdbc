import { BadRequestException, Injectable } from '@nestjs/common';
import {
  ImageCompletionEntity,
  StatusEnum,
} from '../entity/image-completion.entity';
import { ImageCompletionLikeManager } from './like.manager';
import { ImageCompletionLikeProvider } from './like.provider';
import { LikesRankingSearchRequest } from '../dto/likes-ranking.search.request';
import { ImageCompletionLikeResponseMapper } from './like.response-mapper';

@Injectable()
export class ImageCompletionLikeRequestManager {
  constructor(
    private manager: ImageCompletionLikeManager,
    private provider: ImageCompletionLikeProvider,
    private responseMapper: ImageCompletionLikeResponseMapper,
  ) {}

  async like(
    imageCompletion: ImageCompletionEntity,
    userId: string,
  ): Promise<void> {
    if (imageCompletion.status != StatusEnum.READY) {
      throw new BadRequestException('Image not ready');
    }

    // if (imageCompletion.privacy != PrivacyEnum.PUBLIC) {
    //   throw new BadRequestException('Image not public');
    // }

    const existingLikes = await this.provider.countBy({
      imageCompletionId: imageCompletion.id,
      userId: userId,
    });

    if (existingLikes > 0) {
      throw new BadRequestException('Already liked');
    }

    await this.manager.like(imageCompletion, userId);
  }

  async unlike(
    imageCompletion: ImageCompletionEntity,
    userId: string,
  ): Promise<void> {
    // if (imageCompletion.privacy != PrivacyEnum.PUBLIC) {
    //   throw new BadRequestException('Image not public');
    // }

    const imageCompletionLike = await this.provider.getBy({
      imageCompletionId: imageCompletion.id,
      userId: userId,
    });

    await this.manager.unlike(imageCompletionLike);
  }

  async generateLikesRanking(query: LikesRankingSearchRequest): Promise<any> {
    const start = new Date();

    const { page, limit, ...filters } = query;
    if (filters.day) {
      start.setDate(start.getDate() - 1);
    } else if (filters.week) {
      start.setDate(start.getDate() - 7);
    } else if (filters.month) {
      start.setMonth(start.getMonth() - 1);
    } else {
      start.setFullYear(start.getFullYear() - 1);
    }

    const entities = await this.provider.findRanking(
      start.toISOString(),
      new Date().toISOString(),
      page,
      limit,
      filters.userId ?? null,
    );

    const totalCount = await this.provider.countRanking(
      start.toISOString(),
      new Date().toISOString(),
      filters.userId ?? null,
    );

    if (query.userId) {
      let entity = entities[0];

      if (!entity) {
        entity = {
          user_id: query.userId,
          likes_count: 0,
        };
      }

      return {
        dtos: await this.responseMapper.mapLikesRank(entity),
        totalCount,
      };
    }

    return {
      dtos: await this.responseMapper.mapLikesRankMultiple(entities),
      totalCount,
    };
  }
}
