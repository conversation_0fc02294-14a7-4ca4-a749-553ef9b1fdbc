import { Injectable } from '@nestjs/common';
import { ImageCompletionEntity } from 'src/image-completion/entity/image-completion.entity';
import { ImageCompletionResponseMapper } from 'src/image-completion/service/response-mapper';
import { FeedItemDto } from '../dto/feed-item.dto';

@Injectable()
export class FeedResponseMapper {
  constructor(
    private imageCompletionResponseMapper: ImageCompletionResponseMapper,
  ) {}

  async mapMultiple(
    entities: ImageCompletionEntity[],
    userId: string = null,
  ): Promise<any> {
    const images = [];

    for (const entity of entities) {
      images.push(await this.map(entity, userId));
    }

    return images;
  }

  async map(
    entity: ImageCompletionEntity,
    userId: string = null,
  ): Promise<FeedItemDto> {
    const dto = new FeedItemDto();

    dto.type = 'image';
    dto.imageCompletion = await this.imageCompletionResponseMapper.map(
      entity,
      true,
      true,
      userId,
    );

    return dto;
  }
}
