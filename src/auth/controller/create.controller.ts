import { Controller, Post, Request } from '@nestjs/common';
import { Body } from '@nestjs/common/decorators/http/route-params.decorator';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
} from '@nestjs/swagger';
import { UserProvider } from '../../user/service/provider';
import { UserAuthTokenResponseMapper } from '../service/response-mapper';
import { IntegrationTokenDto } from '../dto/integration-token.dto';
import { IntegrationTokenRequest } from '../dto/integration-token.request';
import { UserAuthTokenRequestManager } from '../service/request-manager';

@ApiTags('user')
@Controller('auth/integration-tokens')
export class CreateController {
  constructor(
    private requestManager: UserAuthTokenRequestManager,
    private responseMapper: UserAuthTokenResponseMapper,
    private userProvider: UserProvider,
  ) {}

  @Post()
  @ApiOperation({
    operationId: 'integration_token_create',
    summary: 'Create a new integration token',
    description:
      'Creates a new integration token for the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- name: Name for the integration token\n\n' +
      'Optional Parameters:\n' +
      '- hideFromUserProfile: Whether to hide the token from the user profile\n\n' +
      'Returns the created integration token object.',
  })
  @ApiBody({
    type: IntegrationTokenRequest,
    description: 'Integration token creation parameters.',
  })
  @ApiOkResponse({
    type: IntegrationTokenDto,
    description: 'Integration token created successfully.',
  })
  @ApiBadRequestResponse({
    description:
      'Bad Request. Possible reasons:\n\n' +
      '- Invalid token name format\n' +
      '- Token name already exists\n' +
      '- Invalid hideFromUserProfile format\n' +
      '- Missing token name\n',
  })
  async createAuth(
    @Body() requestBody: IntegrationTokenRequest,
    @Request() request,
  ): Promise<IntegrationTokenDto> {
    const user = await this.userProvider.get(request.user.id);

    return this.requestManager
      .create(requestBody, user)
      .then((entity) => this.responseMapper.map(entity));
  }
}
