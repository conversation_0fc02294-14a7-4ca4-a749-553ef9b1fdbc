import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1693680218101 implements MigrationInterface {
  name = 'Api1693680218101';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `INSERT INTO "user_signup_code" (code) VALUES ('A2G3-T4M5-X6Y7-Z8P9')`,
    );
    await queryRunner.query(
      `INSERT INTO "user_signup_code" (code) VALUES ('B5H6-C7D8-E9F2-G3J4')`,
    );
    await queryRunner.query(
      `INSERT INTO "user_signup_code" (code) VALUES ('K5M6-P7Q8-R9S2-T3V4')`,
    );
    await queryRunner.query(
      `INSERT INTO "user_signup_code" (code) VALUES ('U5W6-Y7Z8-A9B2-C3D4')`,
    );
    await queryRunner.query(
      `INSERT INTO "user_signup_code" (code) VALUES ('E5F6-G7H8-J9K2-M3N4')`,
    );
    await queryRunner.query(
      `INSERT INTO "user_signup_code" (code) VALUES ('V5X6-Q7R8-S9T2-U3W4')`,
    );
    await queryRunner.query(
      `INSERT INTO "user_signup_code" (code) VALUES ('M5N6-Y7Z8-P9A2-B3C4')`,
    );
    await queryRunner.query(
      `INSERT INTO "user_signup_code" (code) VALUES ('D5E6-F7G8-H9J2-K3M4')`,
    );
    await queryRunner.query(
      `INSERT INTO "user_signup_code" (code) VALUES ('W5X6-C7D8-E9F2-G3H4')`,
    );
    await queryRunner.query(
      `INSERT INTO "user_signup_code" (code) VALUES ('T5Y6-U7V8-W9X2-Y3Z4')`,
    );
    await queryRunner.query(
      `INSERT INTO "user_signup_code" (code) VALUES ('R5S6-T7U8-V9W2-X3Y4')`,
    );
    await queryRunner.query(
      `INSERT INTO "user_signup_code" (code) VALUES ('H5J6-K7M8-N9P2-Q3R4')`,
    );
    await queryRunner.query(
      `INSERT INTO "user_signup_code" (code) VALUES ('S5T6-U7V8-W9X2-Z3A4')`,
    );
    await queryRunner.query(
      `INSERT INTO "user_signup_code" (code) VALUES ('N5P6-Q7R8-S9T2-V3W4')`,
    );
    await queryRunner.query(
      `INSERT INTO "user_signup_code" (code) VALUES ('J5K6-M7N8-P9Q2-R3S4')`,
    );
    await queryRunner.query(
      `INSERT INTO "user_signup_code" (code) VALUES ('Z5A6-B7C8-D9E2-F3G4')`,
    );
    await queryRunner.query(
      `INSERT INTO "user_signup_code" (code) VALUES ('U5V6-W7X8-Y9Z2-A3B4')`,
    );
    await queryRunner.query(
      `INSERT INTO "user_signup_code" (code) VALUES ('F5G6-H7J8-K9M2-N3P4')`,
    );
    await queryRunner.query(
      `INSERT INTO "user_signup_code" (code) VALUES ('Q5R6-S7T8-U9V2-W3X4')`,
    );
    await queryRunner.query(
      `INSERT INTO "user_signup_code" (code) VALUES ('G5H6-J7K8-M9N2-P3Q4')`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
