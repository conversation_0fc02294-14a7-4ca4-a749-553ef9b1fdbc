import { Injectable } from '@nestjs/common';
import { Logger } from 'nestjs-pino';
import Stripe from 'stripe';
import { OrganizationSubscriptionManager } from './organization-subscription.manager';
import { OrganizationSubscriptionProvider } from './organization-subscription.provider';

@Injectable()
export class StripeWebhookHandler {
  constructor(
    private readonly logger: Logger,
    private readonly subscriptionManager: OrganizationSubscriptionManager,
    private readonly subscriptionProvider: OrganizationSubscriptionProvider,
  ) {}

  async handleCheckoutSessionCompleted(event: Stripe.Event) {
    const session = event.data.object as Stripe.Checkout.Session;

    this.logger.log('stripe.checkout_session_completed', {
      session,
    });

    if (!session.metadata?.subscriptionId) {
      return;
    }

    const subscription = await this.subscriptionProvider.get(
      session.metadata.subscriptionId,
    );

    if (!subscription) {
      this.logger.error('stripe.subscription_not_found', {
        subscriptionId: session.metadata.subscriptionId,
      });
      return;
    }

    await this.subscriptionManager.updateStripeCheckoutStatus(subscription);
  }

  async handleSubscriptionDeleted(event: Stripe.Event) {
    const subscription = event.data.object as Stripe.Subscription;

    const orgSubscription = await this.subscriptionProvider.getBy({
      externalReference: subscription.id,
    });

    if (!orgSubscription) {
      this.logger.error('stripe.subscription_not_found', {
        stripeSubscriptionId: subscription.id,
      });
      return;
    }

    await this.subscriptionManager.cancelSubscription(orgSubscription);
  }

  async handleSubscriptionUpdated(event: Stripe.Event) {
    const subscription = event.data.object as Stripe.Subscription;

    const orgSubscription = await this.subscriptionProvider.getBy({
      externalReference: subscription.id,
    });

    if (!orgSubscription) {
      return;
    }

    // Handle subscription updates (e.g., plan changes, payment method updates)
    // Implement as needed based on your requirements
  }
}
