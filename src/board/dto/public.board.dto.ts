import { ApiProperty } from '@nestjs/swagger';
import { PublicUserDto } from 'src/user/dto/public.user.dto';
import { SafetyEnum } from '../enum/board-safety.enum';
import { VisibilityEnum } from '../enum/board-visibility.enum';

export class PublicBoardDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  description?: string;

  @ApiProperty()
  members: number;

  @ApiProperty()
  owner: PublicUserDto;

  @ApiProperty()
  isMember: boolean;

  @ApiProperty()
  isAdmin: boolean;

  @ApiProperty()
  visibility: VisibilityEnum;

  @ApiProperty()
  safety: SafetyEnum;

  @ApiProperty()
  thumbnail?: { [key: string]: any };
}
