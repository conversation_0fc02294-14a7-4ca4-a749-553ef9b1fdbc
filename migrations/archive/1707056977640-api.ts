import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1707056977640 implements MigrationInterface {
    name = 'Api1707056977640'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
                    ALTER TABLE "image_completion"
                    ADD "is_hot" BOOLEAN NOT NULL DEFAULT FALSE
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "image_completion" DROP COLUMN "is_hot"
        `);
    }

}
