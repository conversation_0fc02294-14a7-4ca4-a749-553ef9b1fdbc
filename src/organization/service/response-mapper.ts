import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { OrganizationDto } from '../dto/organization.dto';
import { PublicOrganizationDto } from '../dto/public.organization.dto';
import { OrganizationEntity } from '../entity/organization.entity';
import { OrganizationUserProvider } from './organization-user.provider';
import { OrganizationDashboardDto } from '../dto/organization-dashboard.dto';
import { UserProvider } from 'src/user/service/provider';
import { UserResponseMapper } from 'src/user/service/response-mapper';
import { OrganizationDashboardSeatsDto } from '../dto/organization-dashboard-seats.dto';

@Injectable()
export class OrganizationResponseMapper {
  private cdnHost: string;

  constructor(
    private configService: ConfigService,
    private organizationUserProvider: OrganizationUserProvider,
    private userProvider: UserProvider,
    private userResponseMapper: UserResponseMapper,
  ) {
    this.cdnHost = this.configService.get<string>('CDN_HOST');
  }

  async map(
    organization: OrganizationEntity,
    currentUserId: string = null,
  ): Promise<OrganizationDto> {
    const organizationDto = new OrganizationDto();

    organizationDto.id = organization.id;
    organizationDto.name = organization.name;

    if (organization.profilePicture) {
      organizationDto.profilePicture = this.mapProfilePicture(organization);
    }

    if (currentUserId) {
      organizationDto.isAdmin = await this.organizationUserProvider.isManager(
        currentUserId,
        organization.id,
      );
    }

    organizationDto.description = organization.description;
    organizationDto.handle = organization.handle;
    organizationDto.website = organization.website;
    organizationDto.imagesGenerated = organization.imagesGenerated;
    organizationDto.imagesAvailable = organization.imagesAvailable;
    organizationDto.modelsAvailable = organization.modelsAvailable;
    organizationDto.members = organization.members;
    organizationDto.seatsPurchased = organization.seatsPurchased;
    organizationDto.isActive = organization.isActive;
    organizationDto.includeWatermarks = organization.includeWatermarks;

    return organizationDto;
  }

  mapPublic(organization: OrganizationEntity): PublicOrganizationDto {
    const publicOrganizationDto = new PublicOrganizationDto();
    publicOrganizationDto.id = organization.id;
    publicOrganizationDto.name = organization.name;

    if (organization.profilePicture) {
      publicOrganizationDto.profilePicture =
        this.mapProfilePicture(organization);
    }

    publicOrganizationDto.description = organization.description;
    publicOrganizationDto.handle = organization.handle;
    publicOrganizationDto.website = organization.website;
    publicOrganizationDto.imagesGenerated = organization.imagesGenerated;
    publicOrganizationDto.imagesAvailable = organization.imagesAvailable;
    publicOrganizationDto.modelsAvailable = organization.modelsAvailable;
    publicOrganizationDto.members = organization.members;

    return publicOrganizationDto;
  }

  mapProfilePicture(organization: OrganizationEntity): string | null {
    if (!organization.profilePicture) {
      return null;
    }

    return `${this.cdnHost}/${organization.profilePicture}`;
  }

  async mapDashboard(entity: any) {
    const dto = new OrganizationDashboardDto();

    dto.imagesGenerated = entity.imagesGenerated;

    return dto;
  }

  async mapSeatDashboard(entity: any) {
    const user = await this.userProvider.get(entity.user_id);

    const dto = new OrganizationDashboardSeatsDto();

    dto.user = this.userResponseMapper.mapPublic(user);
    dto.userId = entity.user_id;
    dto.imagesGenerated = entity.images_count;

    return dto;
  }

  async mapSeatDashboardMultiple(
    entities: any,
  ): Promise<OrganizationDashboardSeatsDto[]> {
    return Promise.all(entities.map((entity) => this.mapSeatDashboard(entity)));
  }

  async mapMultiple(
    organizations: OrganizationEntity[],
    currentUserId: string = null,
  ): Promise<OrganizationDto[]> {
    return Promise.all(
      organizations.map((organization) =>
        this.map(organization, currentUserId),
      ),
    );
  }
}
