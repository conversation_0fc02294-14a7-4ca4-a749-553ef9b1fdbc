import { DynamicModule, Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ImageCompletionModule } from 'src/image-completion/module';
import { NotificationModule } from 'src/notification/module';
import { OrganizationModule } from 'src/organization/organization.module';
import { CoreModule } from '../core/core.module';
import { CreateController } from './controller/create.controller';
import { DeleteController } from './controller/delete.controller';
import { PasswordResetController } from './controller/password-reset.controller';
import { ReadController } from './controller/read.controller';
import { <PERSON>reate<PERSON>ontroller as SignupCodeCreateController } from './controller/signup-code/create.controller';
import { DeleteController as SignupCodeDeleteController } from './controller/signup-code/delete.controller';
import { ReadController as SignupCodeReadController } from './controller/signup-code/read.controller';
import { UpdateController } from './controller/update.controller';
import { <PERSON><PERSON><PERSON><PERSON>roller as UserFollowCreateController } from './controller/user-follow/create.controller';
import { DeleteController as UserFollowDeleteController } from './controller/user-follow/delete.controller';
import { ReadController as UserFollowReadController } from './controller/user-follow/read.controller';
import { UpdateController as UserFollowUpdateController } from './controller/user-follow/update.controller';
import { SignupCodeEntity } from './entity/signup-code.entity';
import { UserFollowEntity } from './entity/user-follow.entity';
import { UserEntity } from './entity/user.entity';
import { ImageGeneratedListener } from './listener/image-generated.listener';
import { UserManager } from './service/manager';
import { UserProvider } from './service/provider';
import { UserRequestManager } from './service/request-manager';
import { UserResponseMapper } from './service/response-mapper';
import { SignupCodeManager } from './service/signup-code.manager';
import { SignupCodeProvider } from './service/signup-code.provider';
import { SignupCodeRequestManager } from './service/signup-code.request-manager';
import { SignupCodeResponseMapper } from './service/signup-code.response-mapper';
import { UserFollowManager } from './service/user-follow.manager';
import { UserFollowProvider } from './service/user-follow.provider';
import { UserFollowResponseMapper } from './service/user-follow.response-mapper';
import { ImagePublishedListener } from './listener/image-published.listener';
import { ImageUnpublishedListener } from './listener/image-unpublished.listener';
import { ModelPublishedListener } from './listener/model-published.listener';
import { ModelUnpublishedListener } from './listener/model-unpublished.listener';
import { ModelModule } from 'src/model/module';
import { UserDeletedListener } from './listener/user-deleted.listener';

@Module({
  imports: [
    CoreModule,
    forwardRef(() => ImageCompletionModule),
    forwardRef(() => NotificationModule),
    forwardRef(() => OrganizationModule),
    forwardRef(() => ModelModule),
    TypeOrmModule.forFeature([UserEntity]),
    TypeOrmModule.forFeature([UserFollowEntity]),
    TypeOrmModule.forFeature([SignupCodeEntity]),
  ],
  providers: [
    UserDeletedListener,
    ImageGeneratedListener,
    ImagePublishedListener,
    ImageUnpublishedListener,
    ModelPublishedListener,
    ModelUnpublishedListener,
    SignupCodeProvider,
    SignupCodeResponseMapper,
    SignupCodeRequestManager,
    SignupCodeManager,
    UserFollowProvider,
    UserFollowResponseMapper,
    UserFollowManager,
    UserResponseMapper,
    UserProvider,
    UserRequestManager,
    UserManager,
  ],
  exports: [UserProvider, UserManager, UserResponseMapper, SignupCodeProvider],
})
export class UserModule {
  static register(enableControllers: boolean): DynamicModule {
    return {
      module: UserModule,
      controllers: enableControllers
        ? [
            SignupCodeCreateController,
            SignupCodeDeleteController,
            SignupCodeReadController,
            CreateController,
            DeleteController,
            ReadController,
            UpdateController,
            PasswordResetController,
            UserFollowCreateController,
            UserFollowUpdateController,
            UserFollowDeleteController,
            UserFollowReadController,
          ]
        : [],
    };
  }
}
