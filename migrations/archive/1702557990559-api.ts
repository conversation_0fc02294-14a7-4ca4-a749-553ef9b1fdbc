import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1702557990559 implements MigrationInterface {
    name = 'Api1702557990559'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_signup_code"
            ADD "user_id" uuid
        `);
        await queryRunner.query(`
            ALTER TABLE "user_signup_code"
            ADD "usages" integer NOT NULL DEFAULT '0'
        `);
        await queryRunner.query(`
            ALTER TABLE "user_signup_code"
            ADD "limit" integer
        `);
        await queryRunner.query(`
            ALTER TABLE "user_signup_code"
            ADD "features" json DEFAULT '{}'
        `);
        await queryRunner.query(`
            ALTER TABLE "user_signup_code"
            ADD CONSTRAINT "FK_f83da055ad1a8a6f01603d0a11c" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_signup_code" DROP CONSTRAINT "FK_f83da055ad1a8a6f01603d0a11c"
        `);
        await queryRunner.query(`
            ALTER TABLE "user_signup_code" DROP COLUMN "features"
        `);
        await queryRunner.query(`
            ALTER TABLE "user_signup_code" DROP COLUMN "limit"
        `);
        await queryRunner.query(`
            ALTER TABLE "user_signup_code" DROP COLUMN "usages"
        `);
        await queryRunner.query(`
            ALTER TABLE "user_signup_code" DROP COLUMN "user_id"
        `);
    }

}
