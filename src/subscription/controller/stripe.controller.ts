import {
  Controller,
  HttpCode,
  Param,
  ParseUUIDPipe,
  Post,
  RawBodyRequest,
  Req,
} from '@nestjs/common';
import {
  ApiNoContentResponse,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { Request } from 'express';
import { Public } from 'src/core/security/public-routes';
import { SubscriptionManager } from '../service/manager';
import { SubscriptionProvider } from '../service/provider';
import { StripeRequestManager } from '../service/stripe.request-manager';

@ApiTags('stripe')
@Controller('stripe')
export class StripeController {
  constructor(
    private stripeRequestManager: StripeRequestManager,
    private subscriptionProvider: SubscriptionProvider,
    private subscriptionManager: SubscriptionManager,
  ) {}

  @Post('webhook')
  @ApiOperation({
    operationId: 'stripe_webhook',
    summary: 'Stripe webhook endpoint',
    description:
      'Handles incoming Stripe webhook events. Intended for Stripe use only.',
  })
  @ApiNoContentResponse({ description: 'Webhook processed successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - Invalid or unavailable parameters.
      - Failed to construct Stripe event.
  `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @HttpCode(204)
  @Public()
  async webhook(@Req() request: RawBodyRequest<Request>) {
    await this.stripeRequestManager.handleWebhook(request);
  }

  @Post(':subscriptionId/stripe-invoice')
  @ApiOperation({
    operationId: 'stripe_send_invoice',
    summary: 'Send Stripe invoice for subscription',
    description:
      'Sends a Stripe invoice for the specified subscription. Intended for internal or Stripe use only.' +
      '\n\n' +
      'Required Parameters:\n' +
      '- subscriptionId: UUID of the subscription\n',
  })
  @ApiNoContentResponse({ description: 'Invoice sent successfully.' })
  @ApiParam({
    name: 'subscriptionId',
    description: 'ID of the subscription',
    type: 'string',
    format: 'uuid',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`subscriptionId\`: Must be a valid UUID.
  `,
  })
  @ApiNotFoundResponse({
    description: 'Not Found. The subscription could not be found.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @HttpCode(204)
  @Public()
  async sendInvoice(
    @Req() request: RawBodyRequest<Request>,
    @Param('subscriptionId', new ParseUUIDPipe()) subscriptionId: string,
  ) {
    const subscription = await this.subscriptionProvider.get(subscriptionId);

    await this.subscriptionManager.sendStripeInvoice(subscription);
  }
}
