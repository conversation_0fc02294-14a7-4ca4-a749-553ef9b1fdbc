import { Body, Controller, Post, Request, UseGuards } from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiBody,
    ApiCreatedResponse,
    ApiOperation,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import { JwtIntegrationAuthGuard } from 'src/auth/service/jwt-integration-auth.guard';
import { ImageEditDto } from '../dto/image-edit.dto';
import { ImageEditRequest } from '../dto/image-edit.request';
import { ImageEditRequestManager } from '../service/request-manager';
import { ImageEditResponseMapper } from '../service/response-mapper';

@ApiTags('image-edit')
@Controller('image-edits')
@ApiBearerAuth()
export class CreateController {
  constructor(
    private readonly requestManager: ImageEditRequestManager,
    private readonly responseMapper: ImageEditResponseMapper,
  ) {}

  @Post()
  @UseGuards(JwtIntegrationAuthGuard)
  @ApiOperation({
    operationId: 'image_edit_create',
    summary: 'Create a new image edit',
    description:
      'Creates an image edit task that modifies an existing image using inpainting, outpainting, or skin editing.\n\n' +
      'Required Parameters:\n' +
      '- mode: "in" for inpainting (modify within image), "out" for outpainting (extend image), "skin" for skin editing, or "context" for context editing\n\n' +
      'Optional Parameters:\n' +
      '- originalImageCompletionId: UUID of the original image completion\n' +
      '- imageUrl: URL of the source image to edit\n' +
      '- prompt: Text description of the desired modifications\n' +
      '- mask: Base64 encoded image mask (required for inpainting)\n' +
      '- width/height: Target dimensions for the edited image in pixels\n' +
      '- imageCompletionsCount: Number of variations to generate (1-5, default: 3)\n' +
      '- organizationId: Organization UUID to deduct credits from (user must be a member)\n' +
      '- settings: Additional generation settings object\n' +
      '- webhookUrl: Optional URL to receive a POST notification when upscale is complete\n\n' +
      'Credit Deduction:\n' +
      '- If organizationId is provided, credits will be deducted from the organization account\n' +
      '- If organizationId is not provided, credits will be deducted from the user account\n' +
      '- User must be a member of the organization to use organization credits\n\n' +
      'Monitor the edit status using the GET /image-edits/{id} endpoint.',
  })
  @ApiBody({
    type: ImageEditRequest,
    description: 'The image edit request parameters',
  })
  @ApiCreatedResponse({
    type: ImageEditDto,
    description: 'The image edit was successfully created.',
  })
  @ApiResponse({
    status: 400,
    description: `
      Bad Request. Possible reasons:
      - Invalid input parameters
      - Source image does not exist
      - Source image is not in a valid state
      - Invalid edit settings
      - organizationId: Must be a valid UUID format
      - Organization does not exist
    `,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. The user is not authenticated, or user is not a member of the specified organization.',
  })
  @ApiResponse({
    status: 402,
    description:
      'Payment Required. Insufficient credits to create the image edit (either user or organization credits depending on request).',
  })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden. User does not have permission to create image edits.',
  })
  @ApiResponse({
    status: 500,
    description:
      'Internal Server Error. An unexpected error occurred during image edit creation.',
  })
  async create(
    @Body() requestBody: ImageEditRequest,
    @Request() request,
  ): Promise<ImageEditDto> {
    const user = request.user;
    const entity = await this.requestManager.create(requestBody, user);

    return await this.responseMapper.map(entity);
  }
}
