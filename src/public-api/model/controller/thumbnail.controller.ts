import {
  Body,
  Controller,
  Param,
  ParseUUI<PERSON>ipe,
  Put,
  Request,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { ModelProvider } from 'src/model/service/provider';
import { ModelRequestManager } from '../service/request-manager';
import { ModelResponseMapper } from '../service/response-mapper';
import { ModelDto } from '../dto/model.dto';
import { ModelThumbnailRequest } from '../dto/model-thumbnail.request';

@ApiTags('models')
@Controller('models')
export class ThumbnailController {
  constructor(
    private provider: ModelProvider,
    private requestManager: ModelRequestManager,
    private responseMapper: ModelResponseMapper,
  ) {}

  @ApiOperation({
    operationId: 'model_thumbnail',
    summary: 'Update model thumbnail',
    description:
      'Updates the thumbnail of a model.\n\n' +
      'Path Parameters:\n' +
      '- id: UUID of the model\n\n' +
      'Request Body:\n' +
      '- imageCompletionId: UUID of the image to use as thumbnail',
  })
  @ApiParam({
    name: 'id',
    description: 'The unique identifier of the model to update',
    type: 'string',
    format: 'uuid',
  })
  @ApiBody({
    type: ModelThumbnailRequest,
    description:
      'The thumbnail update request containing the image completion ID',
  })
  @ApiOkResponse({
    type: ModelDto,
    description: 'The model thumbnail was successfully updated.',
  })
  @ApiResponse({
    status: 400,
    description: `
      Bad Request. Possible reasons:
      - Invalid model ID format
      - Invalid image completion ID
      - Image completion not found
      - Image completion not ready
      - Image completion belongs to different user
    `,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden. User does not have permission to update this model.',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found. The model to update could not be found.',
  })
  @ApiResponse({
    status: 409,
    description: `
      Conflict. Possible reasons:
      - Model is currently being processed
      - Model is in an invalid state for updates
    `,
  })
  @ApiResponse({
    status: 422,
    description:
      'Unprocessable Entity. The request payload contains invalid data.',
  })
  @ApiResponse({
    status: 500,
    description:
      'Internal Server Error. An unexpected error occurred while updating the thumbnail.',
  })
  @ApiBearerAuth()
  @Put(':id/thumbnail')
  async updateThumbnail(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
    @Body() requestBody: ModelThumbnailRequest,
  ): Promise<ModelDto> {
    const model = await this.provider.getBy({
      user: { id: request.user.id },
      id: id,
    });

    await this.requestManager.updateThumbnail(
      model,
      requestBody.imageCompletionId,
    );

    return await this.responseMapper.map(model);
  }
}
