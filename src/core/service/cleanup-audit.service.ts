import { Injectable, Logger } from '@nestjs/common';
import { CleanupStatusEnum, CleanupOperationTypeEnum } from '../enum/cleanup-status.enum';

export interface CleanupOperation {
  operationType: CleanupOperationTypeEnum;
  userId: string;
  userEmail: string;
  status: CleanupStatusEnum;
  startTime: Date;
  endTime?: Date;
  itemsProcessed?: number;
  itemsDeleted?: number;
  itemsFailed?: number;
  error?: string;
  retryCount?: number;
  metadata?: Record<string, any>;
}

@Injectable()
export class CleanupAuditService {
  private operations: Map<string, CleanupOperation> = new Map();

  private readonly logger = new Logger(CleanupAuditService.name);

  constructor() {}

  /**
   * Start tracking a cleanup operation
   */
  startOperation(
    operationType: CleanupOperationTypeEnum,
    userId: string,
    userEmail: string,
    metadata?: Record<string, any>
  ): string {
    const operationId = `${operationType}_${userId}_${Date.now()}`;
    
    const operation: CleanupOperation = {
      operationType,
      userId,
      userEmail,
      status: CleanupStatusEnum.IN_PROGRESS,
      startTime: new Date(),
      retryCount: 0,
      metadata,
    };

    this.operations.set(operationId, operation);

    this.logger.log('Cleanup operation started', {
      operationId,
      operationType,
      userId,
      userEmail,
      metadata,
    });

    return operationId;
  }

  /**
   * Update operation progress
   */
  updateProgress(
    operationId: string,
    itemsProcessed: number,
    itemsDeleted: number,
    itemsFailed?: number
  ): void {
    const operation = this.operations.get(operationId);
    if (!operation) {
      this.logger.warn('Cleanup operation not found for progress update', { operationId });
      return;
    }

    operation.itemsProcessed = itemsProcessed;
    operation.itemsDeleted = itemsDeleted;
    operation.itemsFailed = itemsFailed || 0;

    this.logger.log('Cleanup operation progress', {
      operationId,
      operationType: operation.operationType,
      userId: operation.userId,
      itemsProcessed,
      itemsDeleted,
      itemsFailed,
    });
  }

  /**
   * Mark operation as completed successfully
   */
  completeOperation(
    operationId: string,
    itemsProcessed: number,
    itemsDeleted: number,
    itemsFailed?: number
  ): void {
    const operation = this.operations.get(operationId);
    if (!operation) {
      this.logger.warn('Cleanup operation not found for completion', { operationId });
      return;
    }

    operation.status = itemsFailed && itemsFailed > 0 
      ? CleanupStatusEnum.PARTIALLY_COMPLETED 
      : CleanupStatusEnum.COMPLETED;
    operation.endTime = new Date();
    operation.itemsProcessed = itemsProcessed;
    operation.itemsDeleted = itemsDeleted;
    operation.itemsFailed = itemsFailed || 0;

    const duration = operation.endTime.getTime() - operation.startTime.getTime();

    this.logger.log('Cleanup operation completed', {
      operationId,
      operationType: operation.operationType,
      userId: operation.userId,
      userEmail: operation.userEmail,
      status: operation.status,
      duration,
      itemsProcessed,
      itemsDeleted,
      itemsFailed,
    });
  }

  /**
   * Mark operation as failed
   */
  failOperation(operationId: string, error: string, retrying = false): void {
    const operation = this.operations.get(operationId);
    if (!operation) {
      this.logger.warn('Cleanup operation not found for failure', { operationId });
      return;
    }

    operation.status = retrying ? CleanupStatusEnum.RETRYING : CleanupStatusEnum.FAILED;
    operation.error = error;
    if (!retrying) {
      operation.endTime = new Date();
    }
    operation.retryCount = (operation.retryCount || 0) + (retrying ? 1 : 0);

    const duration = operation.endTime ? 
      operation.endTime.getTime() - operation.startTime.getTime() : null;

    this.logger.error('Cleanup operation failed', {
      operationId,
      operationType: operation.operationType,
      userId: operation.userId,
      userEmail: operation.userEmail,
      status: operation.status,
      duration,
      error,
      retryCount: operation.retryCount,
      retrying,
    });
  }

  /**
   * Get operation status
   */
  getOperation(operationId: string): CleanupOperation | undefined {
    return this.operations.get(operationId);
  }

  /**
   * Get all operations for a user
   */
  getUserOperations(userId: string): CleanupOperation[] {
    return Array.from(this.operations.values()).filter(op => op.userId === userId);
  }

  /**
   * Clean up old completed operations to prevent memory leaks
   */
  cleanupOldOperations(maxAgeHours = 24): void {
    const cutoffTime = new Date(Date.now() - maxAgeHours * 60 * 60 * 1000);
    
    for (const [operationId, operation] of this.operations.entries()) {
      if (operation.endTime && operation.endTime < cutoffTime) {
        this.operations.delete(operationId);
      }
    }
  }

  /**
   * Generate cleanup summary for a user
   */
  generateUserCleanupSummary(userId: string): {
    totalOperations: number;
    completedOperations: number;
    failedOperations: number;
    partiallyCompletedOperations: number;
    totalItemsDeleted: number;
    totalItemsFailed: number;
  } {
    const userOperations = this.getUserOperations(userId);
    
    return {
      totalOperations: userOperations.length,
      completedOperations: userOperations.filter(op => op.status === CleanupStatusEnum.COMPLETED).length,
      failedOperations: userOperations.filter(op => op.status === CleanupStatusEnum.FAILED).length,
      partiallyCompletedOperations: userOperations.filter(op => op.status === CleanupStatusEnum.PARTIALLY_COMPLETED).length,
      totalItemsDeleted: userOperations.reduce((sum, op) => sum + (op.itemsDeleted || 0), 0),
      totalItemsFailed: userOperations.reduce((sum, op) => sum + (op.itemsFailed || 0), 0),
    };
  }
}
