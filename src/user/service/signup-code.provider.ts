import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { Repository } from 'typeorm';
import { AbstractProvider } from '../../core/service/abstract.provider';
import { SignupCodeEntity } from '../entity/signup-code.entity';

@Injectable()
export class SignupCodeProvider extends AbstractProvider<SignupCodeEntity> {
  constructor(
    @InjectRepository(SignupCodeEntity)
    repository: Repository<SignupCodeEntity>,
    logger: Logger,
  ) {
    super(repository, logger);
  }
}
