import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsBoolean,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { BaseSearchRequest } from 'src/core/dto/base.search-request';
import { PrivacyEnum } from 'src/model/entity/model.entity';
import { StatusEnum } from 'src/model/enum/status.enum';

export class ModelSearchRequest extends BaseSearchRequest {
  @IsOptional()
  @IsString()
  @ApiProperty({ required: false })
  search: string;

  @IsOptional()
  @IsString()
  @ApiProperty({ required: false })
  name?: string;

  @IsOptional()
  @IsUUID()
  @ApiProperty({ required: false })
  userId?: string;

  @IsOptional()
  @IsString()
  @ApiProperty({ required: false })
  username?: string;

  @IsOptional()
  @IsString()
  @ApiProperty({ required: false })
  class?: string;

  @IsOptional()
  @IsString()
  @ApiProperty({ required: false })
  type?: string;

  @IsOptional()
  @IsString()
  @ApiProperty({ required: false })
  description?: string;

  @IsOptional()
  @IsEnum(PrivacyEnum)
  @Transform(({ value }) => value.toLowerCase())
  @ApiProperty({ required: false, enum: PrivacyEnum })
  privacy?: PrivacyEnum;

  @IsOptional()
  @IsNumber()
  @ApiProperty({ required: false })
  systemVersion?: number;

  @IsOptional()
  @IsEnum(StatusEnum)
  @Transform(({ value }) => value.toLowerCase())
  @ApiProperty({ required: false, enum: StatusEnum })
  status?: StatusEnum;

  @IsOptional()
  @IsBoolean()
  @ApiProperty({ required: false })
  isActive?: boolean;
}
