import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Logger } from 'nestjs-pino';
import {
  ImageCompletionEntity,
  StatusEnum,
} from 'src/image-completion/entity/image-completion.entity';
import { ImageCompletionResponseMapper as PrivateImageResponseMapper } from 'src/image-completion/service/response-mapper';
import { ImageEditResponseMapper } from 'src/image-edit/service/response-mapper';
import { ModelProvider } from 'src/model/service/provider';
import { ModelResponseMapper } from 'src/model/service/response-mapper';
import { UpscaleResponseMapper } from 'src/upscale/service/response-mapper';
import { UserResponseMapper } from 'src/user/service/response-mapper';
import { ImageDto } from '../dto/image.dto';

@Injectable()
export class ImageResponseMapper {
  private cdnHost: string;

  constructor(
    private modelResponseMapper: ModelResponseMapper,
    private configService: ConfigService,
    private userResponseMapper: UserResponseMapper,
    private upscaleResponseMapper: UpscaleResponseMapper,
    private privateImageResponseMapper: PrivateImageResponseMapper,
    private imageEditResponseMapper: ImageEditResponseMapper,
    private logger: Logger,
    private modelProvider: ModelProvider,
  ) {
    this.cdnHost = this.configService.get<string>('CDN_HOST');
  }

  generateProgressMessage(entity: ImageCompletionEntity): string {
    const statusMessages = {
      [StatusEnum.NEW]: 'Image is waiting to start. Keep requesting.',
      [StatusEnum.GENERATING]: 'Image is generating. Keep requesting.',
      [StatusEnum.READY]: 'Image is finished.',
    };

    return statusMessages[entity.status] || 'Image generation failed';
  }

  async map(entity: ImageCompletionEntity): Promise<ImageDto> {
    const dto = new ImageDto();

    dto.id = entity.id;
    dto.user = this.userResponseMapper.mapPublic(entity.user);
    if (!entity.hidePrompt) {
      dto.prompt = entity.prompt;
    }
    dto.status = entity.status;
    dto.progress = entity.progress;
    dto.progressMessage = this.generateProgressMessage(entity);
    dto.previewImage = entity.previewImage;
    dto.privacy = entity.privacy;
    dto.likes = entity.likes;
    dto.regenerations = entity.regenerations;
    dto.hasWatermark = entity.hasWatermark;
    dto.isNsfw = entity.isNsfw;
    dto.webhookUrl = entity.webhookUrl;
    dto.hidePrompt = entity.hidePrompt;
    dto.isHot = entity.isHot;
    dto.createdAt = entity.createdAt;
    dto.systemVersion = entity.systemVersion;
    dto.generationSettings = entity.generationSettings;

    if (entity.imageEditImageCompletion) {
      try {
        dto.imageEdit = await this.imageEditResponseMapper.map(
          entity.imageEditImageCompletion.imageEdit,
        );
      } catch (error) {
        this.logger.debug('Error mapping image edit', {
          image_completion: entity,
          error: error.message,
        });
      }
    }

    if (entity.imagePaths && entity.imagePaths.length > 0) {
      dto.imageVersions = this.privateImageResponseMapper.generateImageVersions(
        entity.imagePaths[0],
      );

      if (entity.upscales.length > 0) {
        dto.imageVersions.upscale = [];

        for (const upscale of entity.upscales) {
          if (upscale.status != 'ready') {
            continue;
          }

          dto.imageVersions.upscale.push(
            await this.upscaleResponseMapper.mapImage(upscale),
          );

          dto.imageVersions.upscale.sort((a, b) => b.createdAt - a.createdAt);
        }
      }
    }

    dto.models = [];
    if (entity.models?.length) {
      for (const imageCompletionModel of entity.models) {
        try {
          const model = await this.modelProvider.get(
            imageCompletionModel.modelId,
          );
          dto.models.push(
            await this.modelResponseMapper.map(model, true, false),
          );
        } catch (error) {
          console.error({
            message: 'Error mapping image models',
            image_completion_id: entity.id,
            image_completion_model_id: imageCompletionModel?.id,
            image_completion_model: imageCompletionModel,
            error: error.message,
          });
        }
      }
    }

    return dto;
  }

  async mapMultiple(
    entities: ImageCompletionEntity[],
    userId: string = null,
  ): Promise<ImageDto[]> {
    const images = [];

    for (const entity of entities) {
      images.push(await this.map(entity));
    }

    return images;
  }
}
