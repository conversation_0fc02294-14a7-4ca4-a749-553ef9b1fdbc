import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1699897717676 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `UPDATE credit_package SET credit_types = '{"IMAGE": 900, "MODEL": 1}' WHERE name = 'Just testing'`,
    );
    await queryRunner.query(
      `UPDATE credit_package SET credit_types = '{"IMAGE": 3600, "MODEL": 5}' WHERE name = 'Beginner'`,
    );
    await queryRunner.query(
      `UPDATE credit_package SET credit_types = '{"IMAGE": 10800, "MODEL": 10}' WHERE name = 'Fun'`,
    );
    await queryRunner.query(
      `UPDATE credit_package SET credit_types = '{"IMAGE": 36000, "MODEL": 20}' WHERE name = 'Pro'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
