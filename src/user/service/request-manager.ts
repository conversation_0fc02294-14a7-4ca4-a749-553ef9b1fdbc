import { BadRequestException, Injectable } from '@nestjs/common';
import { ImageCompletionProvider } from 'src/image-completion/service/provider';
import { UserEmailValidationRequest } from '../dto/user-email-validation.request';
import { UserForgotPasswordRequest } from '../dto/user-forgot-password.request';
import { UserPasswordResetRequest } from '../dto/user-password-reset.request';
import { UserPasswordUpdateRequest } from '../dto/user-password.update.request';
import { UserCreateRequest } from '../dto/user.create.request';
import { UserUpdateRequest } from '../dto/user.update.request';
import { UserEntity } from '../entity/user.entity';
import { UserManager } from './manager';
import { UserProvider } from './provider';
import { SignupCodeManager } from './signup-code.manager';
import { SignupCodeProvider } from './signup-code.provider';

@Injectable()
export class UserRequestManager {
  constructor(
    private userManager: UserManager,
    private userProvider: UserProvider,
    private imageCompletionProvider: ImageCompletionProvider,
    private signupCodeProvider: SignupCodeProvider,
    private signupCodeManager: SignupCodeManager,
  ) {}

  async create(request: UserCreateRequest): Promise<UserEntity> {
    const user = new UserEntity();

    this.mapCommonUserData(user, request);

    if (request.hasOwnProperty('password')) {
      user.password = this.userManager.encryptPassword(request.password);
    }

    if (request.hasOwnProperty('signupCode')) {
      user.signupCode = request.signupCode.toUpperCase();

      await this.processSignupCode(user);
    }

    await this.userManager.create(user, request.organizationId);

    await this.initiateEmailValidation(user);

    return user;
  }

  async update(
    user: UserEntity,
    userUpdateRequest: UserUpdateRequest,
  ): Promise<UserEntity> {
    this.mapCommonUserData(user, userUpdateRequest);

    user.includeWatermarks =
      userUpdateRequest.includeWatermarks ?? user.includeWatermarks;
    user.hidePrompt = userUpdateRequest.hidePrompt ?? user.hidePrompt;
    user.tutorialSteps = userUpdateRequest.tutorialSteps ?? user.tutorialSteps;
    user.systemVersion = userUpdateRequest.systemVersion ?? user.systemVersion;
    user.imageQueue = userUpdateRequest.imageQueue ?? user.imageQueue;

    if ('profilePicture' in userUpdateRequest) {
      if (!userUpdateRequest.profilePicture) {
        user.profilePicture = null;
      } else {
        try {
          const imageCompletion = await this.imageCompletionProvider.getBy({
            userId: user.id,
            id: userUpdateRequest.profilePicture,
          });

          user.profilePicture = imageCompletion.imagePaths[0];
        } catch (e) {
          throw new BadRequestException('Invalid profile picture id');
        }
      }
    }

    return this.userManager.update(user);
  }

  mapCommonUserData(
    user: UserEntity,
    request: UserCreateRequest | UserUpdateRequest,
  ): void {
    user.name = 'name' in request ? request.name : user.name;
    user.email = 'email' in request ? request.email : user.email;
    user.username =
      'username' in request ? request.username?.toLowerCase() : user.username;
    user.description =
      'description' in request ? request.description : user.description;
    user.birthday = 'birthday' in request ? request.birthday : user.birthday;
    user.website = 'website' in request ? request.website : user.website;
    user.socialMediaAccounts =
      'socialMediaAccounts' in request
        ? request.socialMediaAccounts
        : user.socialMediaAccounts;
    user.timezone = 'timezone' in request ? request.timezone : user.timezone;
    user.currency = 'currency' in request ? request.currency : user.currency;
    user.locale = 'locale' in request ? request.locale : user.locale;
    user.isBot = 'isBot' in request ? request.isBot : user.isBot;
    user.allowSwapping =
      'allowSwapping' in request ? request.allowSwapping : user.allowSwapping;
  }

  async updatePassword(
    user: UserEntity,
    userPasswordUpdateRequest: UserPasswordUpdateRequest,
  ) {
    if (
      !this.userManager.validateUserPassword(
        user,
        userPasswordUpdateRequest.oldPassword,
      )
    ) {
      throw new BadRequestException('Invalid password');
    }

    user.password = this.userManager.encryptPassword(
      userPasswordUpdateRequest.newPassword,
    );

    await this.userManager.update(user);
  }

  async initiateEmailValidation(user: UserEntity) {
    if (user.emailValidatedAt) {
      throw new BadRequestException('Email already validated');
    }

    await this.userManager.generateEmailValidationCode(user);

    await this.userManager.sendValidationEmail(user);
  }

  async validateEmail(request: UserEmailValidationRequest) {
    try {
      const user = await this.userProvider.get(request.id);

      await this.userManager.validateEmail(user, request.code);
    } catch (error) {
      const errorMessages: Record<string, string> = {
        InvalidCode: 'Invalid or missing email validation code.',
        ExpiredCode: 'Email validation code has expired.',
      };

      const message =
        errorMessages[error.message] || 'An unknown error occurred.';

      throw new BadRequestException(message);
    }
  }

  async processSignupCode(user: UserEntity) {
    const signupCode = await this.signupCodeProvider.findOneBy({
      code: user.signupCode,
      isActive: true,
    });

    if (!signupCode) {
      throw new BadRequestException('Invalid signup code');
    }

    user.userSignupCodeId = signupCode.id;

    await this.signupCodeManager.registerUsage(signupCode, user);
  }

  async initiatePasswordReset(request: UserForgotPasswordRequest) {
    const user = await this.userProvider.getBy({ email: request.email });

    await this.userManager.initiatePasswordReset(user);
  }

  async resetPassword(request: UserPasswordResetRequest) {
    const user = await this.userProvider.getBy({
      id: request.userId,
      resetPasswordToken: request.token,
    });

    if (user.resetPasswordExpires && new Date() > user.resetPasswordExpires) {
      user.resetPasswordToken = null;
      user.resetPasswordExpires = null;

      await this.userManager.update(user);

      throw new BadRequestException('Invalid or expired reset token');
    }

    user.password = this.userManager.encryptPassword(request.password);
    user.resetPasswordToken = null;
    user.resetPasswordExpires = null;

    await this.userManager.update(user);
  }
}
