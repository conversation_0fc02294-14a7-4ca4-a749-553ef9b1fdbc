import { Controller, Post, Body, HttpCode } from '@nestjs/common';
import { UserEntity } from '../../user/entity/user.entity';
import { UserAuthService } from '../service/user-auth.service';
import { UserAuthRequest } from '../dto/user-auth.request';
import { UserAuthTokenRequest } from '../dto/user-auth-token.request';
import { UserRefreshTokenRequest } from '../dto/user-refresh-token.request';
import { UserAccessTokenDto } from '../dto/user-access-token.dto';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { Public } from '../../core/security/public-routes';

@ApiTags('user')
@Controller('auth')
export class AuthController {
  constructor(private userAuthService: UserAuthService) {}

  @Public()
  @Post('login')
  @HttpCode(200)
  @ApiOperation({
    operationId: 'auth_login',
    summary: 'User Login',
    description:
      'Authenticate a user using email and password.\n\n' +
      'Required Parameters:\n' +
      '- email: User email address\n' +
      '- password: User password\n\n' +
      'Returns an access token and a refresh token if authentication is successful.',
  })
  @ApiBody({
    type: UserAuthRequest,
    description: 'User login credentials (email and password).',
  })
  @ApiResponse({
    status: 200,
    description: 'Login successful. Returns access and refresh tokens.',
    type: UserAccessTokenDto,
  })
  @ApiResponse({
    status: 400,
    description:
      'Bad Request. Possible Reasons: \n\n' +
      '- Invalid email format\n' +
      '- Invalid password format\n' +
      '- Missing email or password\n',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. Authentication failed.',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. Unexpected error during login.',
  })
  async login(
    @Body() userAuthRequest: UserAuthRequest,
  ): Promise<UserAccessTokenDto> {
    const user = await this.userAuthService.validatePassword(userAuthRequest);
    return this.generateTokensForUser(user);
  }

  @Public()
  @Post('oauth-login')
  @HttpCode(200)
  @ApiOperation({
    operationId: 'auth_oauth_login',
    summary: 'OAuth Login',
    description:
      'Authenticate a user using an OAuth token.\n\n' +
      'Required Parameters:\n' +
      '- provider: OAuth provider name\n' +
      '- accessToken: OAuth access token\n\n' +
      'Returns an access token and a refresh token if authentication is successful.',
  })
  @ApiBody({
    type: UserAuthTokenRequest,
    description: 'OAuth login credentials (OAuth token).',
  })
  @ApiResponse({
    status: 200,
    description: 'OAuth login successful. Returns access and refresh tokens.',
    type: UserAccessTokenDto,
  })
  @ApiResponse({
    status: 400,
    description:
      'Bad Request. Possible Reasons: \n\n' +
      '- Invalid OAuth provider name\n' +
      '- Invalid OAuth access token\n' +
      '- Missing OAuth provider or access token\n',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. OAuth authentication failed.',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. Unexpected error during OAuth login.',
  })
  async oAuthLogin(
    @Body() userAuthTokenRequest: UserAuthTokenRequest,
  ): Promise<UserAccessTokenDto> {
    const user = await this.userAuthService.validateOAuthToken(
      userAuthTokenRequest,
    );
    return this.generateTokensForUser(user);
  }

  @Public()
  @Post('token-refresh')
  @HttpCode(200)
  @ApiOperation({
    operationId: 'auth_token_refresh',
    summary: 'Refresh Token',
    description:
      'Refresh the access token using a valid refresh token.\n\n' +
      'Required Parameters:\n' +
      '- refreshToken: The refresh token issued during login\n\n' +
      'Returns a new access token and refresh token if the refresh token is valid.',
  })
  @ApiBody({
    type: UserRefreshTokenRequest,
    description: 'Refresh token request (refreshToken).',
  })
  @ApiResponse({
    status: 200,
    description: 'Token refreshed. Returns new access and refresh tokens.',
    type: UserAccessTokenDto,
  })
  @ApiResponse({
    status: 400,
    description:
      'Bad Request.Possible Reasons: \n\n' +
      '- Invalid or expired refresh token.',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. Refresh token authentication failed.',
  })
  @ApiResponse({
    status: 500,
    description:
      'Internal Server Error. Unexpected error during token refresh.',
  })
  async tokenRefresh(
    @Body() userRefreshTokenRequest: UserRefreshTokenRequest,
  ): Promise<UserAccessTokenDto> {
    return this.userAuthService.refreshToken(
      userRefreshTokenRequest.refreshToken,
    );
  }

  private async generateTokensForUser(
    user: UserEntity,
  ): Promise<UserAccessTokenDto> {
    return this.userAuthService.generateTokens(user);
  }
}
