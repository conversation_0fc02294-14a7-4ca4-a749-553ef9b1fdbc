import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1699975309961 implements MigrationInterface {
    name = 'Api1699975309961'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "include_watermarks" boolean NOT NULL DEFAULT true
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "include_watermarks"
        `);
    }

}
