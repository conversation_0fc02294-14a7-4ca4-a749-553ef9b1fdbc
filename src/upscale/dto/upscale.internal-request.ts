import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';
import { StatusEnum } from '../entity/upscale.entity';
import { UpscaleRequest } from './upscale.request';

export class UpscaleInternalRequest extends UpscaleRequest {
  @IsOptional()
  @IsEnum(StatusEnum, {
    message: 'Status must be a valid value',
  })
  @ApiProperty({ enum: StatusEnum })
  status: StatusEnum;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  progress: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  promptSystem?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  statusDetail?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  imagePath?: string;

  @ApiProperty()
  @IsOptional()
  @IsObject()
  generationSettings?: any;

  @ApiProperty()
  @IsOptional()
  @IsString()
  generationData?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  generatedByUnit?: string;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  generationSeconds: number;

  @ApiProperty()
  @IsOptional()
  isActive?: boolean;
}
