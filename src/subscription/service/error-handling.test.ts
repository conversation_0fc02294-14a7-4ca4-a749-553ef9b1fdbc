import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DateTime } from 'luxon';
import { Logger } from 'nestjs-pino';
import { Notifier } from 'src/notification/service/notifier';
import { UserManager } from 'src/user/service/manager';
import { UserProvider } from 'src/user/service/provider';
import Stripe from 'stripe';
import { Repository } from 'typeorm';
import { SubscriptionStatusEnum } from '../entity/subscription-status.enum';
import { SubscriptionEntity } from '../entity/subscription.entity';
import { CreditPackageProvider } from './credit-package.provider';
import { DunningService } from './dunning.service';
import { EmailManager } from './email.manager';
import { SubscriptionManager } from './manager';
import { SubscriptionProvider } from './provider';
import { StripeService } from './stripe.service';
import { TransactionManager } from './transaction.manager';
import { UserCreditBalanceManager } from './user-credit-balance.manager';

describe('Yearly Subscription Error Handling', () => {
  let subscriptionManager: SubscriptionManager;
  let dunningService: DunningService;
  let repository: Repository<SubscriptionEntity>;
  let stripe: Stripe;
  let emailManager: EmailManager;
  let logger: Logger;

  const mockRepository = {
    save: jest.fn(),
    softDelete: jest.fn(),
  };

  const mockStripe = {
    subscriptions: {
      cancel: jest.fn(),
      retrieve: jest.fn(),
    },
  };

  const mockEmailManager = {
    sendPaymentFailedEmail: jest.fn(),
    sendPaymentFailedInitialEmail: jest.fn(),
    sendPaymentFailedReminderEmail: jest.fn(),
    sendPaymentFailedFinalWarningEmail: jest.fn(),
  };

  const mockSubscriptionProvider = {
    findBy: jest.fn(),
    get: jest.fn(),
  };

  const mockLogger = {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SubscriptionManager,
        DunningService,
        {
          provide: getRepositoryToken(SubscriptionEntity),
          useValue: mockRepository,
        },
        {
          provide: SubscriptionProvider,
          useValue: mockSubscriptionProvider,
        },
        {
          provide: 'Stripe',
          useValue: mockStripe,
        },
        {
          provide: UserProvider,
          useValue: {},
        },
        {
          provide: UserManager,
          useValue: {},
        },
        {
          provide: CreditPackageProvider,
          useValue: {},
        },
        {
          provide: StripeService,
          useValue: {},
        },
        {
          provide: TransactionManager,
          useValue: {},
        },
        {
          provide: ConfigService,
          useValue: {},
        },
        {
          provide: UserCreditBalanceManager,
          useValue: {},
        },
        {
          provide: EmailManager,
          useValue: mockEmailManager,
        },
        {
          provide: EventEmitter2,
          useValue: { emit: jest.fn() },
        },
        {
          provide: Logger,
          useValue: mockLogger,
        },
        {
          provide: Notifier,
          useValue: {},
        },
      ],
    }).compile();

    subscriptionManager = module.get<SubscriptionManager>(SubscriptionManager);
    dunningService = module.get<DunningService>(DunningService);
    repository = module.get<Repository<SubscriptionEntity>>(
      getRepositoryToken(SubscriptionEntity),
    );
    stripe = module.get<Stripe>('Stripe');
    emailManager = module.get<EmailManager>(EmailManager);
    logger = module.get<Logger>(Logger);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('handleYearlyPaymentFailure', () => {
    it('should handle payment failure for yearly subscription', async () => {
      const subscription = {
        id: 'sub_123',
        userId: 'user_123',
        creditPackageId: 'pkg_123',
        creditPackage: { expiresAfterMonths: 12 },
        status: SubscriptionStatusEnum.ACTIVE,
        externalReference: 'stripe_sub_123',
      } as SubscriptionEntity;

      const error = new Error('Payment failed');

      await subscriptionManager.handleYearlyPaymentFailure(subscription, error);

      expect(mockRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          status: SubscriptionStatusEnum.PAST_DUE,
        }),
      );

      expect(mockLogger.error).toHaveBeenCalledWith(
        'subscription.yearly_payment_failed',
        expect.objectContaining({
          subscriptionId: 'sub_123',
          userId: 'user_123',
          error: 'Payment failed',
        }),
      );

      expect(mockEmailManager.sendPaymentFailedEmail).toHaveBeenCalledWith(
        subscription,
        error,
      );
    });

    it('should throw error for non-yearly subscription', async () => {
      const subscription = {
        creditPackage: { expiresAfterMonths: 1 },
      } as SubscriptionEntity;

      const error = new Error('Payment failed');

      await expect(
        subscriptionManager.handleYearlyPaymentFailure(subscription, error),
      ).rejects.toThrow('subscription.not_yearly');
    });

    it('should continue if email sending fails', async () => {
      const subscription = {
        id: 'sub_123',
        userId: 'user_123',
        creditPackage: { expiresAfterMonths: 12 },
        status: SubscriptionStatusEnum.ACTIVE,
      } as SubscriptionEntity;

      const error = new Error('Payment failed');
      mockEmailManager.sendPaymentFailedEmail.mockRejectedValue(
        new Error('Email failed'),
      );

      await subscriptionManager.handleYearlyPaymentFailure(subscription, error);

      expect(mockRepository.save).toHaveBeenCalled();
      expect(mockLogger.error).toHaveBeenCalledWith(
        'subscription.payment_failure_email_failed',
        expect.any(Object),
      );
    });
  });

  describe('retryYearlyPayment', () => {
    it('should successfully retry payment when Stripe subscription is active', async () => {
      const subscription = {
        id: 'sub_123',
        userId: 'user_123',
        creditPackage: { expiresAfterMonths: 12 },
        status: SubscriptionStatusEnum.PAST_DUE,
        externalReference: 'stripe_sub_123',
      } as SubscriptionEntity;

      mockStripe.subscriptions.retrieve.mockResolvedValue({
        status: 'active',
      });

      const result = await subscriptionManager.retryYearlyPayment(subscription);

      expect(result.success).toBe(true);
      expect(mockRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          status: SubscriptionStatusEnum.ACTIVE,
          renewedAt: expect.any(Date),
        }),
      );
    });

    it('should handle still past due status', async () => {
      const subscription = {
        id: 'sub_123',
        userId: 'user_123',
        creditPackage: { expiresAfterMonths: 12 },
        status: SubscriptionStatusEnum.PAST_DUE,
        externalReference: 'stripe_sub_123',
      } as SubscriptionEntity;

      mockStripe.subscriptions.retrieve.mockResolvedValue({
        status: 'past_due',
      });

      const result = await subscriptionManager.retryYearlyPayment(subscription);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Payment still past due in Stripe');
    });

    it('should throw error for non-yearly subscription', async () => {
      const subscription = {
        creditPackage: { expiresAfterMonths: 1 },
      } as SubscriptionEntity;

      await expect(
        subscriptionManager.retryYearlyPayment(subscription),
      ).rejects.toThrow('subscription.not_yearly');
    });

    it('should throw error for non-past-due subscription', async () => {
      const subscription = {
        creditPackage: { expiresAfterMonths: 12 },
        status: SubscriptionStatusEnum.ACTIVE,
      } as SubscriptionEntity;

      await expect(
        subscriptionManager.retryYearlyPayment(subscription),
      ).rejects.toThrow('subscription.not_past_due');
    });
  });

  describe('handleGracePeriod', () => {
    it('should return grace period info for past due yearly subscription', async () => {
      // Set to 6 days ago to ensure we're still in grace period with 1 day remaining
      const sixDaysAgo = DateTime.now().minus({ days: 6 }).toJSDate();
      const subscription = {
        id: 'sub_123',
        userId: 'user_123',
        creditPackage: { expiresAfterMonths: 12 },
        status: SubscriptionStatusEnum.PAST_DUE,
        paidAt: sixDaysAgo,
      } as SubscriptionEntity;

      const result = await subscriptionManager.handleGracePeriod(subscription);

      expect(result.inGracePeriod).toBe(true);
      expect(result.daysRemaining).toBe(1); // 1 day remaining in grace period
      expect(result.gracePeriodEnds).toBeInstanceOf(Date);
    });

    it('should return false for non-yearly subscription', async () => {
      const subscription = {
        creditPackage: { expiresAfterMonths: 1 },
      } as SubscriptionEntity;

      const result = await subscriptionManager.handleGracePeriod(subscription);

      expect(result.inGracePeriod).toBe(false);
    });

    it('should return false for non-past-due subscription', async () => {
      const subscription = {
        creditPackage: { expiresAfterMonths: 12 },
        status: SubscriptionStatusEnum.ACTIVE,
      } as SubscriptionEntity;

      const result = await subscriptionManager.handleGracePeriod(subscription);

      expect(result.inGracePeriod).toBe(false);
    });
  });

  describe('calculateYearlyRefund', () => {
    it('should calculate correct refund for early cancellation', () => {
      const oneMonthAgo = DateTime.now().minus({ months: 1 }).toJSDate();
      const subscription = {
        creditPackage: { price: 7490, expiresAfterMonths: 12 },
        paidAt: oneMonthAgo,
      } as SubscriptionEntity;

      const result = subscriptionManager['calculateYearlyRefund'](subscription);

      expect(result.totalMonths).toBe(12);
      expect(result.unusedMonths).toBe(11);
      expect(result.refundAmount).toBeGreaterThan(0);
      expect(result.refundAmount).toBeLessThan(7490);
    });

    it('should return zero refund for subscription without payment date', () => {
      const subscription = {
        creditPackage: { price: 7490, expiresAfterMonths: 12 },
        paidAt: null,
      } as SubscriptionEntity;

      const result = subscriptionManager['calculateYearlyRefund'](subscription);

      expect(result.refundAmount).toBe(0);
      expect(result.unusedMonths).toBe(0);
    });
  });
});
