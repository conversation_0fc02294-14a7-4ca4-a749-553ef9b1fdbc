import {
  <PERSON>,
  Get,
  Param,
  <PERSON>rseUUI<PERSON>ip<PERSON>,
  Query,
  <PERSON>s,
  UsePipes,
  ValidationPipe,
  Request,
  HttpCode,
  Put,
  Delete,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiQuery,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiUnauthorizedResponse,
  ApiInternalServerErrorResponse,
  ApiForbiddenResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import { BaseFindResponseHeadersDto } from 'src/core/dto/base-find-response-headers.dto';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { ImageCompletionCommentProvider } from '../service/comment.provider';
import { ImageCompletionCommentLikeProvider } from '../service/image-completion-comment-like.provider';
import { ImageCompletionCommentLikeRequestManager } from '../service/image-completion-comment-like.request-manager';
import { ImageCompletionCommentLikeResponseMapper } from '../service/image-completion-comment-like.response-mapper';
import { ImageCompletionCommentLikeDto } from '../dto/image-completion-comment-like.dto';
import { ImageCompletionCommentLikeSearchRequest } from '../dto/image-completion-comment-like.search-request';

@ApiTags('image_completions_comment / likes')
@Controller('image_completion_comment')
export class CommentLikeController {
  constructor(
    private imageCompletionCommentProvider: ImageCompletionCommentProvider,
    private imageCompletionCommentLikeProvider: ImageCompletionCommentLikeProvider,
    private requestManager: ImageCompletionCommentLikeRequestManager,
    private responseMapper: ImageCompletionCommentLikeResponseMapper,
  ) {}

  @Get(':id/likes')
  @ApiOperation({
    operationId: 'image_completion_comment_like_list',
    summary: 'List likes for a comment',
    description:
      'Retrieves a paginated list of likes for the specified image completion comment.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the image completion comment\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of likes per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- username: Username of the user\n' +
      '- userId: UUID of the user\n',
  })
  @ApiOkResponse({
    type: ImageCompletionCommentLikeDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated list of likes for the comment.',
  })
  @ApiQuery({
    type: ImageCompletionCommentLikeSearchRequest,
    description: 'Query parameters for searching and paginating likes.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the image completion comment.',
    type: String,
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - Invalid query parameters.
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - \`userId\`: Must be a valid UUID.
      - \`username\`: Must be a valid username.
      - Comment does not exist.
      - Invalid filter values (e.g., userId, username).
      - Failed to analyze request.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User does not have access to this comment.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @UsePipes(new ValidationPipe())
  async find(
    @Query() query: ImageCompletionCommentLikeSearchRequest,
    @Res() res: Response,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;
    const filters = this.validateSearchFilters(inputFilters, id);

    const entities = await this.imageCompletionCommentLikeProvider.findBy(
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    const totalCount = await this.imageCompletionCommentLikeProvider.countBy(
      filters,
    );

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultiple(entities));
  }

  @Get('likes')
  @ApiOperation({
    operationId: 'image_completion_comment_like_list_user',
    summary: 'List likes by current user',
    description:
      'Retrieves a paginated list of likes made by the authenticated user on image completion comments.\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of likes per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- userId: UUID of the user\n' +
      '- username: Username of the user\n',
  })
  @ApiOkResponse({
    type: ImageCompletionCommentLikeDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated list of likes by the user.',
  })
  @ApiQuery({
    type: ImageCompletionCommentLikeSearchRequest,
    description: 'Query parameters for searching and paginating likes.',
  })
  @ApiBadRequestResponse({
    description: `
      Bad Request. Possible reasons:\n
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - Filter parameters must be valid values.
      - \`userId\`: Must be a valid UUID.
      - \`username\`: Must be a valid username.
      - Invalid or unavailable parameters.`,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @UsePipes(new ValidationPipe())
  async findLikes(
    @Query() query: ImageCompletionCommentLikeSearchRequest,
    @Request() request,
    @Res() res: Response,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;

    delete inputFilters.username;
    inputFilters.userId = request.user.id;

    const entities = await this.imageCompletionCommentLikeProvider.findBy(
      inputFilters,
      page,
      limit,
      sortBy,
      sortOrder,
    );
    const totalCount = await this.imageCompletionCommentLikeProvider.countBy(
      inputFilters,
    );

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultiple(entities, true));
  }

  @Put(':id/likes')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'image_completion_comment_like',
    summary: 'Like a comment',
    description:
      'Likes the specified image completion comment as the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the image completion comment to like\n',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the image completion comment to like.',
    type: String,
  })
  @ApiNoContentResponse({ description: 'Comment liked successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - Comment does not exist.
      - User has already liked the comment.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async like(@Request() request, @Param('id', new ParseUUIDPipe()) id: string) {
    const entity = await this.imageCompletionCommentProvider.get(id);
    await this.requestManager.like(entity, request.user.id);
  }

  @Delete(':id/likes')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'image_completion_comment_unlike',
    summary: 'Unlike a comment',
    description:
      'Removes the like from the specified image completion comment as the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the image completion comment to unlike\n',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the image completion comment to unlike.',
    type: String,
  })
  @ApiNoContentResponse({ description: 'Comment unliked successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - Comment does not exist.
      - User has not liked the comment.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async unlike(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ) {
    const entity = await this.imageCompletionCommentProvider.get(id);
    await this.requestManager.unlike(entity, request.user.id);
  }

  validateSearchFilters(
    filters: ImageCompletionCommentLikeSearchRequest,
    imageCompletionCommentId: string,
  ): any {
    return { ...filters, imageCompletionCommentId: imageCompletionCommentId };
  }
}
