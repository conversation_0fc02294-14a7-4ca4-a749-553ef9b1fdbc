import { ApiProperty } from '@nestjs/swagger';

export class UserImageDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  userId: string;

  @ApiProperty({
    description: 'URL to access the uploaded image. This URL will be available after uploading the image using the uploadUrl.'
  })
  imageUrl: string;

  @ApiProperty({
    required: false,
    description: 'Pre-signed S3 URL for uploading the image file. Make a PUT request to this URL with the image as the request body and appropriate Content-Type header.'
  })
  uploadUrl?: string;

  @ApiProperty({ required: false })
  originalFilename?: string;

  @ApiProperty({ required: false })
  mimeType?: string;

  @ApiProperty({ required: false })
  fileSize?: number;

  @ApiProperty({ required: false })
  metadata?: Record<string, any>;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}
