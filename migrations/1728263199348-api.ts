import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1728263199348 implements MigrationInterface {
  name = 'Api1728263199348';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "model"
            ADD "is_featured" boolean NOT NULL DEFAULT false
        `);
    await queryRunner.query(`
            ALTER TABLE "model"
            ADD "is_sponsored" boolean NOT NULL DEFAULT false
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "model" DROP COLUMN "is_sponsored"
        `);
    await queryRunner.query(`
            ALTER TABLE "model" DROP COLUMN "is_featured"
        `);
  }
}
