import { ApiProperty } from '@nestjs/swagger';
import { ChatAttachedFileDto } from './chat-attached-file.dto';

export class ChatMessageDto {
  @ApiProperty({ description: 'Unique identifier for the message' })
  id: string;

  @ApiProperty({
    description:
      'Content of the message in JSON format, can include text, images, and other rich content',
  })
  content: string;

  @ApiProperty({
    description: 'Role of the message sender (user or assistant)',
  })
  role: string;

  @ApiProperty({
    description: 'ID of the conversation this message belongs to',
  })
  conversationId: string;

  @ApiProperty({
    description: 'Attached files associated with the message',
    type: [ChatAttachedFileDto],
    required: false,
  })
  attachedFiles?: ChatAttachedFileDto[];

  @ApiProperty({
    description: 'ID of the user who sent the message (only for user messages)',
    required: false,
  })
  userId?: string;

  @ApiProperty({ description: 'Creation date of the message' })
  createdAt: Date;
}
