import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { UserImageDto } from '../dto/user-image.dto';
import { UserImageEntity } from '../entity/user-image.entity';
import { UserImageManager } from './user-image.manager';

@Injectable()
export class UserImageResponseMapper {
  private cdnHost: string;

  constructor(
    private configService: ConfigService,
    private manager: UserImageManager,
  ) {
    this.cdnHost = this.configService.get<string>('CDN_HOST', '');
  }

  async map(
    entity: UserImageEntity,
    generateUploadUrl: boolean = false,
  ): Promise<UserImageDto> {
    const dto = new UserImageDto();

    dto.id = entity.id;
    dto.userId = entity.userId;

    if (this.cdnHost) {
      dto.imageUrl = `${this.cdnHost}/${entity.imagePath}`;
    } else {
      // Otherwise, generate a signed URL as fallback
      dto.imageUrl = await this.manager.generateSignedUrl(entity);
    }

    dto.originalFilename = entity.originalFilename;
    dto.mimeType = entity.mimeType;
    dto.fileSize = entity.fileSize;
    dto.metadata = entity.metadata;
    dto.createdAt = entity.createdAt;
    dto.updatedAt = entity.updatedAt;

    if (generateUploadUrl) {
      // Get file extension safely, defaulting to 'jpg' if filename is undefined
      const extension = entity.filename && entity.filename.includes('.')
        ? entity.filename.split('.').pop()
        : 'jpg';

      dto.uploadUrl = await this.manager.generateImageUploadUrl(
        entity,
        extension,
      );
    }

    return dto;
  }

  async mapMany(
    entities: UserImageEntity[],
    generateUploadUrls: boolean = false,
  ): Promise<UserImageDto[]> {
    // Map all entities in parallel
    return Promise.all(
      entities.map(entity => this.map(entity, generateUploadUrls))
    );
  }
}
