import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1694372782111 implements MigrationInterface {
    name = 'Api1694372782111'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "model" DROP COLUMN "generation_data"
        `);
        await queryRunner.query(`
            ALTER TABLE "model"
            ADD "generation_data" text
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "model" DROP COLUMN "generation_data"
        `);
        await queryRunner.query(`
            ALTER TABLE "model"
            ADD "generation_data" json
        `);
    }

}
