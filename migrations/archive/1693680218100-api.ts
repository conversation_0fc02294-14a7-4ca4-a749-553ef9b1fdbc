import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1693680218100 implements MigrationInterface {
    name = 'Api1693680218100'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "user_signup_code" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "code" character varying,
                "notes" text,
                "is_active" boolean NOT NULL DEFAULT true,
                "deleted_at" TIMESTAMP,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "UQ_418e58d1eb8687dc449d4d37a0e" UNIQUE ("code"),
                CONSTRAINT "PK_d7f7e1f5cc5c53d1eb12ac885eb" PRIMARY KEY ("id")
            )
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DROP TABLE "user_signup_code"
        `);
    }

}
