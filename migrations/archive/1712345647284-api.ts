import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1712345647284 implements MigrationInterface {
  name = 'Api1712345647284';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "model_version"
            ADD "status" character varying DEFAULT 'pending'
        `);
    await queryRunner.query(`
            UPDATE model_version SET status = 'pending' WHERE status IS NULL
        `);
    await queryRunner.query(`
                ALTER TABLE "model_version" ALTER COLUMN "status" SET NOT NULL
            `);
    await queryRunner.query(`
            ALTER TABLE "model" ALTER COLUMN "status" TYPE character varying
        `);
    await queryRunner.query(`
            ALTER TABLE "model" ALTER COLUMN "status" SET DEFAULT 'new'
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "model" DROP COLUMN "status"
        `);
    await queryRunner.query(`
            CREATE TYPE "public"."model_status_enum" AS ENUM(
                'new',
                'pending',
                'training',
                'finished',
                'available',
                'failed'
            )
        `);
    await queryRunner.query(`
            ALTER TABLE "model"
            ADD "status" "public"."model_status_enum" NOT NULL DEFAULT 'new'
        `);
    await queryRunner.query(`
            ALTER TABLE "model_version" DROP COLUMN "status"
        `);
  }
}
