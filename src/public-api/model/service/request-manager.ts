import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { Logger } from 'nestjs-pino';
import { ImageCompletionProvider } from 'src/image-completion/service/provider';
import { ModelEntity, PrivacyEnum } from 'src/model/entity/model.entity';
import { ModelManager } from 'src/model/service/manager';
import { ModelTypeManager } from 'src/model/service/model-type.manager';
import { OrganizationUserProvider } from 'src/organization/service/organization-user.provider';
import { UserEntity } from 'src/user/entity/user.entity';
import {
  PrivacyEnum as ImageCompletionPrivacyEnum,
  StatusEnum as ImageCompletionStatusEnum,
} from '../../../image-completion/entity/image-completion.entity';
import { AssetManager as ImageCompletionAssetManager } from '../../../image-completion/service/asset.manager';
import { AssetManager } from '../../../model/service/asset.manager';
import { ModelCreateRequest } from '../dto/model.create-request';
import { ModelUpdateRequest } from '../dto/model.update-request';
import { TrainingDataService } from './training-data.service';

@Injectable()
export class ModelRequestManager {
  constructor(
    private manager: ModelManager,
    private assetManager: AssetManager,
    @Inject(forwardRef(() => ImageCompletionAssetManager))
    private imageCompletionAssetManager: ImageCompletionAssetManager,
    @Inject(forwardRef(() => ImageCompletionProvider))
    private imageCompletionProvider: ImageCompletionProvider,
    private modelTypeManager: ModelTypeManager,
    private organizationUserProvider: OrganizationUserProvider,
    private trainingDataService: TrainingDataService,
    private logger: Logger,
  ) {}

  async create(
    request: ModelCreateRequest,
    user: UserEntity,
  ): Promise<ModelEntity> {
    // Validate organization membership if organizationId is provided
    if (request.organizationId) {
      const isMember = await this.organizationUserProvider.isMember(
        user.id,
        request.organizationId,
      );
      if (!isMember) {
        throw new UnauthorizedException(
          'User is not a member of the specified organization',
        );
      }
    }

    const entity = new ModelEntity();
    entity.user = user;
    entity.origin = 'public';

    this.mapRequestData(entity, request);

    if (!entity.privacy) {
      entity.privacy = PrivacyEnum.PRIVATE;
    }

    // Note: organizationId for credit deduction is handled separately
    // Models use N:M relationship for organization sharing instead

    if (request.trainingDataUrls?.length) {
      try {
        // Validate all images in parallel before creating the model
        await Promise.all(
          request.trainingDataUrls.map((url) =>
            this.trainingDataService.validateImage(url),
          ),
        );

        // Only create the model if all images are valid
        await this.manager.create(entity);
        // Now proceed with downloading and storing the images
        await this.trainingDataService.downloadAndStoreImages(
          entity,
          request.trainingDataUrls,
        );

        if (!entity.type) {
          try {
            const trainingDataUrls = await this.assetManager.generateSignedUrls(
              entity,
              'images',
            );

            entity.type = await this.modelTypeManager.analyzeModelType(
              trainingDataUrls,
              entity.class,
            );
          } catch (error) {
            this.logger.error('Failed to analyze model type', {
              modelId: entity?.id,
              userId: user.id,
              error: error.message,
              stack: error.stack,
            });

            throw new BadRequestException(
              'Could not generate type. Please try again or try setting your type manually if the request keeps failing.',
            );
          }
        }

        await this.manager.trainModel(entity, ModelEntity.activeSystemVersions);
      } catch (error) {
        this.logger.error('Failed to process training data', {
          modelId: entity?.id,
          userId: user.id,
          error: error.message,
          stack: error.stack,
        });

        if (entity.id) {
          await this.manager.delete(entity);
        }

        throw error;
      }
    } else {
      // If no training data, just create the model
      await this.manager.create(entity);
    }

    return entity;
  }

  async update(
    entity: ModelEntity,
    request: ModelUpdateRequest,
  ): Promise<ModelEntity> {
    this.mapRequestData(entity, request);

    return await this.manager.update(entity);
  }

  mapRequestData(
    entity: ModelEntity,
    request: ModelCreateRequest | ModelUpdateRequest,
  ): void {
    entity.name = 'name' in request ? request.name : entity.name;
    entity.privacy = 'privacy' in request ? request.privacy : entity.privacy;
    entity.type = 'type' in request ? request.type : entity.type;
    entity.class = 'class' in request ? request.class : entity.class;
    entity.description =
      'description' in request ? request.description : entity.description;
    entity.website = 'website' in request ? request.website : entity.website;
    entity.webhookUrl =
      'webhookUrl' in request ? request.webhookUrl : entity.webhookUrl;
    entity.settings =
      'settings' in request ? request.settings : entity.settings;
    entity.trainingMode =
      'trainingMode' in request ? request.trainingMode : entity.trainingMode;
  }

  async updateThumbnail(entity: ModelEntity, imageCompletionId: string) {
    const imageCompletion = await this.imageCompletionProvider.get(
      imageCompletionId,
    );

    if (!imageCompletion.hasModel(entity.id)) {
      throw new NotFoundException('Image completion does use this model');
    }

    // check whether that image completion is ready
    if (imageCompletion.status !== ImageCompletionStatusEnum.READY) {
      throw new BadRequestException('Image completion is not available');
    }

    // check whether that image completion is owned by the user OR is public
    if (
      imageCompletion.user.id !== entity.user.id &&
      imageCompletion.privacy !== ImageCompletionPrivacyEnum.PUBLIC
    ) {
      throw new UnauthorizedException('Image completion is not visible');
    }

    // todo: copy image from image completion s3 bucket to model s3 bucket
    const thumbnailObject = await this.imageCompletionAssetManager.getObject(
      imageCompletion,
      imageCompletion.imagePaths[0],
    );

    const thumbnailBuffer = await this.streamToBuffer(thumbnailObject.Body);

    const thumbnailExtension = `${imageCompletion.imagePaths[0]
      .split('.')
      .pop()}`;

    entity.thumbnail =
      entity.storagePath + '/thumbnails/thumbnail.' + thumbnailExtension;

    await this.assetManager.emptyFolder(
      entity,
      entity.storagePath + '/thumbnails/',
    );

    await this.assetManager.storeObject(
      entity,
      entity.thumbnail,
      thumbnailBuffer,
      thumbnailObject.ContentType,
    );

    this.manager.update(entity);
  }

  async streamToBuffer(stream: any): Promise<Buffer> {
    return new Promise<Buffer>((resolve, reject) => {
      const chunks: any[] = [];
      stream.on('data', (chunk: any) => chunks.push(chunk));
      stream.on('error', reject);
      stream.on('end', () => resolve(Buffer.concat(chunks)));
    });
  }
}
