import { UserEntity } from '../../user/entity/user.entity';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { ImageCompletionCommentEntity } from './image-completion-comment.entity';

@Entity('image_completion_comment_like')
export class ImageCompletionCommentLikeEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => ImageCompletionCommentEntity)
  imageCompletionComment: ImageCompletionCommentEntity;

  @Column({ type: 'uuid', nullable: false })
  @Index('idx_image_completion_comment_like_image_completion_comment_id')
  imageCompletionCommentId: string;

  @ManyToOne(() => UserEntity)
  user: UserEntity;

  @Column({ type: 'uuid', nullable: false })
  @Index('idx_image_completion_comment_like_user_id')
  userId: string;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt?: Date;

  @CreateDateColumn()
  createdAt: Date;
}
