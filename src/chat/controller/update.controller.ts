import {
  Body,
  Controller,
  ForbiddenException,
  Param,
  ParseUUI<PERSON>ipe,
  <PERSON>,
  Request,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { ChatMessageDto } from '../dto/chat-message.dto';
import { ChatMessageUpdateRequest } from '../dto/chat-message.update-request';
import { ChatMessageRequestManager } from '../service/chat-message.request-manager';
import { ChatProvider } from '../service/chat.provider';
import { ChatResponseMapper } from '../service/chat.response-mapper';

@ApiTags('chat')
@Controller('chat')
export class UpdateController {
  constructor(
    private readonly chatProvider: ChatProvider,
    private readonly requestManager: ChatMessageRequestManager,
    private readonly responseMapper: ChatResponseMapper,
  ) {}

  @Patch(':conversationId/messages/:messageId')
  @ApiOperation({
    operationId: 'chat_update_message',
    summary: 'Update a chat message',
    description:
      'Updates the specified message in a chat conversation for the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- conversationId: UUID of the chat conversation\n' +
      '- messageId: UUID of the message to update\n\n' +
      'Optional Body Parameters:\n' +
      '- attachedFiles: Updated attached files associated with the message\n\n' +
      'Returns the updated message object.',
  })
  @ApiParam({
    name: 'conversationId',
    description: 'UUID of the chat conversation.',
    type: String,
  })
  @ApiParam({
    name: 'messageId',
    description: 'UUID of the message to update.',
    type: String,
  })
  @ApiBody({
    type: ChatMessageUpdateRequest,
    description: 'Message update parameters.',
  })
  @ApiOkResponse({
    type: ChatMessageDto,
    description: 'Message updated successfully.',
  })
  @ApiBadRequestResponse({
    description: `
      Bad Request. Possible reasons:\n
      - \`conversationId\`: Must be a valid UUID.
      - \`messageId\`: Must be a valid UUID.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description:
      'Forbidden. User does not have permission to update this message.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async updateMessage(
    @Body() requestBody: ChatMessageUpdateRequest,
    @Request() request,
    @Param('conversationId', new ParseUUIDPipe()) conversationId: string,
    @Param('messageId', new ParseUUIDPipe()) messageId: string,
  ): Promise<ChatMessageDto> {
    // Verify user has access to the conversation
    if (
      !(await this.chatProvider.getConversation(
        conversationId,
        request.user.id,
      ))
    ) {
      throw new ForbiddenException();
    }

    // Get the message and verify it belongs to the conversation
    const message = await this.chatProvider.getMessage(messageId);

    if (message.conversationId !== conversationId) {
      throw new ForbiddenException();
    }

    // Update the message
    const updatedMessage = await this.requestManager.updateMessage(
      message,
      requestBody,
    );

    return this.responseMapper.mapMessage(updatedMessage);
  }
}
