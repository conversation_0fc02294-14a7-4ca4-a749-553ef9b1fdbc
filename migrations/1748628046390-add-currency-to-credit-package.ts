import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCurrencyToCreditPackage1748628046390 implements MigrationInterface {
  name = 'AddCurrencyToCreditPackage1748628046390';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add currency column with default value 'eur'
    await queryRunner.query(`
      ALTER TABLE "credit_package" 
      ADD COLUMN "currency" character varying NOT NULL DEFAULT 'eur'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove currency column
    await queryRunner.query(`
      ALTER TABLE "credit_package" 
      DROP COLUMN "currency"
    `);
  }
}
