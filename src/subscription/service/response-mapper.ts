import { Injectable } from '@nestjs/common';
import { SubscriptionDto } from '../dto/subscription.dto';
import { SubscriptionEntity } from '../entity/subscription.entity';

@Injectable()
export class SubscriptionResponseMapper {
  async mapMultiple(entities: SubscriptionEntity[]): Promise<any> {
    return await Promise.all(entities.map((entity) => this.map(entity)));
  }

  async map(entity: SubscriptionEntity): Promise<SubscriptionDto> {
    const dto = new SubscriptionDto();

    dto.id = entity.id;
    dto.name = entity.name;
    dto.price = entity.price / 100;
    dto.paidAt = entity.paidAt;
    dto.status = entity.status;
    dto.stripeCheckoutUrl = entity.stripeCheckoutUrl;
    dto.expiresAt = entity.expiresAt;
    dto.createdAt = entity.createdAt;

    return dto;
  }

  async mapInternal(
    subscription: SubscriptionEntity,
  ): Promise<SubscriptionDto> {
    return this.map(subscription);
  }
}
