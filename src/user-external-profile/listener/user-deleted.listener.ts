import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CleanupAuditService } from 'src/core/service/cleanup-audit.service';
import { CleanupOperationTypeEnum } from 'src/core/enum/cleanup-status.enum';
import { UserDeletedEvent } from 'src/user/event/user-deleted.event';
import { UserExternalProfileEntity } from '../entity/user-external-profile.entity';

@Injectable()
export class UserExternalProfileDeletedListener {
  constructor(
    @InjectRepository(UserExternalProfileEntity)
    private userExternalProfileRepository: Repository<UserExternalProfileEntity>,
    private cleanupAuditService: CleanupAuditService,
  ) {}

  @OnEvent('user.deleted', { async: true })
  async handleUserDeletedEvent(event: UserDeletedEvent) {
    const operationId = this.cleanupAuditService.startOperation(
      CleanupOperationTypeEnum.EXTERNAL_PROFILES,
      event.id,
      event.email,
      { username: event.username }
    );

    try {
      let totalProcessed = 0;
      let totalDeleted = 0;

      // Clean up external profiles for this user
      const profilesResult = await this.userExternalProfileRepository.softDelete(event.id);
      totalProcessed += profilesResult.affected || 0;
      totalDeleted += profilesResult.affected || 0;

      this.cleanupAuditService.completeOperation(
        operationId,
        totalProcessed,
        totalDeleted,
        0 // No failures expected for database operations
      );

    } catch (error) {
      this.cleanupAuditService.failOperation(
        operationId,
        `Failed to cleanup external profiles: ${error.message}`
      );
      throw error;
    }
  }
}
