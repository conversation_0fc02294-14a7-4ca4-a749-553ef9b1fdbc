import { Controller, Param, Request, HttpCode, Delete } from '@nestjs/common';
import { UserNotificationConfigRequestManager } from '../../service/user-notification-config.request-manager';
import {
  ApiBadRequestResponse,
  ApiNoContentResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
  ApiNotFoundResponse,
  ApiResponse,
} from '@nestjs/swagger';

@ApiTags('user')
@Controller('users/current/notification-config')
export class DeleteController {
  constructor(private requestManager: UserNotificationConfigRequestManager) {}

  @ApiOperation({
    operationId: 'user_notification_config_delete_channel',
    summary: 'Delete notification channel configuration',
    description:
      'Deletes the notification channel configuration for the current user.\n\n' +
      'Required Parameters:\n' +
      '- channel: Notification channel to delete\n',
  })
  @ApiParam({
    name: 'channel',
    description: 'The notification channel to delete',
  })
  @ApiNoContentResponse({
    description: 'Notification channel configuration deleted successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`channel\`: Must be a valid notification channel.
  `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiNotFoundResponse({ description: 'Not Found. Channel not found.' })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. An unexpected error occurred.',
  })
  @Delete(':channel')
  @HttpCode(204)
  async delete(@Request() request, @Param() params): Promise<void> {
    await this.requestManager.delete(request.user.id, params.channel);
  }
}
