import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { AbstractProvider } from 'src/core/service/abstract.provider';
import { FindManyOptions, FindOneOptions, Repository } from 'typeorm';
import { ModelCommentEntity } from '../entity/model-comment.entity';

@Injectable()
export class ModelCommentProvider extends AbstractProvider<ModelCommentEntity> {
  constructor(
    @InjectRepository(ModelCommentEntity)
    repository: Repository<ModelCommentEntity>,
    logger: Logger,
  ) {
    super(repository, logger);
  }

  prepareFindOneOptions(criteria: any): FindOneOptions {
    return {
      where: criteria
    };
  }

  prepareFindManyOptions(
    criteria: any,
    page: number,
    limit: number,
    sortBy?: string,
    sortOrder?: 'ASC',
  ): FindManyOptions<ModelCommentEntity> {
    if (criteria.username) {
      criteria.user = {
        username: criteria.username,
      };

      delete criteria.username;
    }

    const parentOptions = super.prepareFindManyOptions(
      criteria,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    return {
      ...parentOptions,
      relations: {
        user: true,
      },
    };
  }

  async findCommentsByModelId(
      modelId: string,
    ): Promise<ModelCommentEntity[]> {
      const customQuery = await this.repository
      .createQueryBuilder('modelComments')
      .where('modelComments.modelId = :modelId', {
        modelId: modelId
      })
      .distinctOn(['modelComments.user_id'])
      .getMany()
  
      return customQuery;
    }
}
