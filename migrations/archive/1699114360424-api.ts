import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1699114360424 implements MigrationInterface {
  name = 'Api1699114360424';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE "credit_package" (
                "id" SERIAL NOT NULL,
                "name" character varying NOT NULL,
                "price" integer NOT NULL,
                "expires_after_months" integer NOT NULL,
                "expires_after_days" integer NOT NULL,
                "credit_types" json NOT NULL,
                CONSTRAINT "PK_07044d60296ffa58ab2a6e2460a" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            CREATE TABLE "subscription" (
                "id" SERIAL NOT NULL,
                "name" character varying NOT NULL,
                "price" integer NOT NULL,
                "expires_at" TIMESTAMP NOT NULL,
                "user_id" uuid,
                "credit_package_id" integer,
                CONSTRAINT "PK_8c3e00ebd02103caa1174cd5d9d" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            CREATE TABLE "transaction" (
                "id" SERIAL NOT NULL,
                "amount" integer NOT NULL,
                "type" character varying NOT NULL,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "user_id" uuid,
                CONSTRAINT "PK_89eadb93a89810556e1cbcd6ab9" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            CREATE TABLE "user_credit_balance" (
                "id" SERIAL NOT NULL,
                "credit_type" character varying NOT NULL,
                "balance" integer NOT NULL DEFAULT '0',
                "user_id" uuid,
                CONSTRAINT "PK_5320169003bbca4fa53fd184c71" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            ALTER TABLE "subscription"
            ADD CONSTRAINT "FK_940d49a105d50bbd616be540013" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "subscription"
            ADD CONSTRAINT "FK_7dd09a6b2a5612c4c04485711d0" FOREIGN KEY ("credit_package_id") REFERENCES "credit_package"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "transaction"
            ADD CONSTRAINT "FK_b4a3d92d5dde30f3ab5c34c5862" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "user_credit_balance"
            ADD CONSTRAINT "FK_a73c0a8540dbc42c293caa0f25b" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "user_credit_balance" DROP CONSTRAINT "FK_a73c0a8540dbc42c293caa0f25b"
        `);
    await queryRunner.query(`
            ALTER TABLE "transaction" DROP CONSTRAINT "FK_b4a3d92d5dde30f3ab5c34c5862"
        `);
    await queryRunner.query(`
            ALTER TABLE "subscription" DROP CONSTRAINT "FK_7dd09a6b2a5612c4c04485711d0"
        `);
    await queryRunner.query(`
            ALTER TABLE "subscription" DROP CONSTRAINT "FK_940d49a105d50bbd616be540013"
        `);
    await queryRunner.query(`
            DROP TABLE "user_credit_balance"
        `);
    await queryRunner.query(`
            DROP TABLE "transaction"
        `);
    await queryRunner.query(`
            DROP TABLE "subscription"
        `);
    await queryRunner.query(`
            DROP TABLE "credit_package"
        `);
  }
}
