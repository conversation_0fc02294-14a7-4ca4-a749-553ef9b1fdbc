import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1737575563795 implements MigrationInterface {
    name = 'Api1737575563795'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "image_edit"
            ADD "settings" json DEFAULT '{}'
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "image_edit" DROP COLUMN "settings"
        `);
    }

}
