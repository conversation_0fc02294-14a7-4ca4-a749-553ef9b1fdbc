import {
  <PERSON>,
  Get,
  Param,
  Query,
  Request,
  ParseU<PERSON><PERSON>ipe,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiBearerAuth,
  ApiBadRequestResponse,
  ApiInternalServerErrorResponse,
  ApiParam,
  ApiOkResponse,
  ApiProperty,
} from '@nestjs/swagger';
import { SubscriptionStatusEnum } from '../entity/subscription-status.enum';
import {
  PaymentInfoProvider,
  PaymentFilters,
} from '../service/payment-info.provider';

export class PaymentInfoDto {
  @ApiProperty()
  id: string;
  @ApiProperty()
  amount: number;
  @ApiProperty()
  status: SubscriptionStatusEnum;
  @ApiProperty()
  paymentDate?: Date;
  @ApiProperty()
  planName: string;
  @ApiProperty()
  paymentMethod?: string;
  @ApiProperty()
  externalReference?: string;
}

export class PaymentSummaryDto {
  @ApiProperty()
  totalPaid: number;
  @ApiProperty()
  totalPayments: number;
  @ApiProperty()
  averagePayment: number;
  @ApiProperty()
  lastPaymentDate?: Date;
}

export class PaymentListResponseDto {
  @ApiProperty({ type: () => [PaymentInfoDto] })
  payments: PaymentInfoDto[];
  @ApiProperty()
  total: number;
  @ApiProperty()
  page: number;
  @ApiProperty()
  limit: number;
  @ApiProperty()
  totalPages: number;
}

@ApiTags('payments')
@ApiBearerAuth()
@Controller('payments')
export class PaymentInfoController {
  constructor(private readonly paymentInfoProvider: PaymentInfoProvider) {}

  @Get()
  @ApiOperation({
    operationId: 'payment_list',
    summary: 'Get user payments with filtering and pagination',
    description:
      'Retrieves a paginated list of payments for the authenticated user, with optional filtering by status, amount, date, and payment method.' +
      '\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of payments per page\n' +
      '- status: Filter by status (comma-separated)\n' +
      '- minAmount: Minimum payment amount\n' +
      '- maxAmount: Maximum payment amount\n' +
      '- startDate: Start date (ISO format)\n' +
      '- endDate: End date (ISO format)\n' +
      '- paymentMethod: Filter by payment method (comma-separated)\n',
  })
  @ApiResponse({
    status: 200,
    description: 'List of payments',
    type: PaymentListResponseDto,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 10)',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    type: String,
    description: 'Filter by status (comma-separated)',
  })
  @ApiQuery({
    name: 'minAmount',
    required: false,
    type: Number,
    description: 'Minimum payment amount',
  })
  @ApiQuery({
    name: 'maxAmount',
    required: false,
    type: Number,
    description: 'Maximum payment amount',
  })
  @ApiQuery({
    name: 'startDate',
    required: false,
    type: String,
    description: 'Start date (ISO format)',
  })
  @ApiQuery({
    name: 'endDate',
    required: false,
    type: String,
    description: 'End date (ISO format)',
  })
  @ApiQuery({
    name: 'paymentMethod',
    required: false,
    type: String,
    description: 'Payment method filter (comma-separated)',
  })
  @ApiBadRequestResponse({
    description: `
      Bad Request. Possible reasons:\n
      - \`page\`: Must be a positive integer.
      - \`limit\`: Must be a positive integer between 1 and 100.
      - \`status\`: Must be a valid value from SubscriptionStatusEnum.
      - \`minAmount\`/\`maxAmount\`: Must be a valid non-negative number.
      - \`startDate\`/\`endDate\`: Must be a valid ISO date string.
      - \`paymentMethod\`: Must be a valid payment method value.
      - Invalid or unavailable filter parameters.
    `,
  })
  @ApiInternalServerErrorResponse({
    description: `
      Internal Server Error. Possible reasons:
      - Unexpected error occurred during payment retrieval.
      - Database connection failure.
      - Service unavailable or timed out.
      - Failed to process filter parameters.
    `,
  })
  async getPayments(
    @Request() req: any,
    @Query('page') page = '1',
    @Query('limit') limit = '10',
    @Query('status') status?: string,
    @Query('minAmount') minAmount?: string,
    @Query('maxAmount') maxAmount?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('paymentMethod') paymentMethod?: string,
  ): Promise<PaymentListResponseDto> {
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    if (pageNum < 1 || limitNum < 1 || limitNum > 100) {
      throw new BadRequestException('Invalid pagination parameters');
    }

    const filters: PaymentFilters = {};

    // Parse status filter
    if (status) {
      const statusArray = status.split(',').map((s) => s.trim());
      const validStatuses = Object.values(SubscriptionStatusEnum);
      filters.status = statusArray.filter((s) =>
        validStatuses.includes(s as SubscriptionStatusEnum),
      ) as SubscriptionStatusEnum[];
    }

    // Parse amount filters
    if (minAmount) {
      const min = parseFloat(minAmount);
      if (!isNaN(min) && min >= 0) {
        filters.minAmount = min;
      }
    }

    if (maxAmount) {
      const max = parseFloat(maxAmount);
      if (!isNaN(max) && max >= 0) {
        filters.maxAmount = max;
      }
    }

    // Parse date filters
    if (startDate) {
      const start = new Date(startDate);
      if (!isNaN(start.getTime())) {
        filters.startDate = start;
      }
    }

    if (endDate) {
      const end = new Date(endDate);
      if (!isNaN(end.getTime())) {
        filters.endDate = end;
      }
    }

    // Parse payment method filter
    if (paymentMethod) {
      filters.paymentMethod = paymentMethod.split(',').map((m) => m.trim());
    }

    const { payments, total } = await this.paymentInfoProvider.getPayments(
      req.user.id,
      filters,
      pageNum,
      limitNum,
    );

    const paymentDtos: PaymentInfoDto[] = payments.map((payment) => ({
      id: payment.id,
      amount: payment.price,
      status: payment.status,
      paymentDate: payment.paidAt,
      planName: payment.name,
      paymentMethod: this.extractPaymentMethod(payment.externalReference),
      externalReference: payment.externalReference,
    }));

    return {
      payments: paymentDtos,
      total,
      page: pageNum,
      limit: limitNum,
      totalPages: Math.ceil(total / limitNum),
    };
  }

  @Get('summary')
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - Invalid or unavailable parameters.
    `,
  })
  @ApiOperation({
    operationId: 'payment_summary',
    summary: 'Get payment summary for the user',
    description: 'Retrieves a summary of payments for the authenticated user.',
  })
  @ApiResponse({
    status: 200,
    description: 'Payment summary',
    type: PaymentSummaryDto,
  })
  @ApiInternalServerErrorResponse({
    description: `
      Internal Server Error. Possible reasons:
      - Unexpected error occurred during payment summary retrieval.
      - Database connection failure.
      - Service unavailable or timed out.
      - Failed to process summary request.
    `,
  })
  async getPaymentSummary(@Request() req: any): Promise<PaymentSummaryDto> {
    return await this.paymentInfoProvider.getPaymentSummary(req.user.id);
  }

  @Get(':paymentId')
  @ApiOperation({
    operationId: 'payment_get_by_id',
    summary: 'Get specific payment details',
    description:
      'Retrieves the details of a specific payment by its ID.' +
      '\n\n' +
      'Required Parameters:\n' +
      '- paymentId: UUID of the payment\n',
  })
  @ApiOkResponse({
    type: PaymentInfoDto,
    description: 'Payment details',
  })
  @ApiBadRequestResponse({
    description: `
      Bad Request. Possible reasons:\n
      - \`paymentId\`: Must be a valid UUID format.
      - Payment ID does not exist or is not associated with the authenticated user.
      - Invalid or unavailable payment referenced.
    `,
  })
  @ApiResponse({
    status: 404,
    description: `
      Not Found. Possible reasons:
      - Payment with the specified ID does not exist.
      - Payment is not associated with the authenticated user.
      - Payment record has been deleted or is unavailable.
    `,
  })
  @ApiParam({
    name: 'paymentId',
    description: 'ID of the payment',
    type: 'string',
    format: 'uuid',
  })
  @ApiInternalServerErrorResponse({
    description: `
      Internal Server Error. Possible reasons:
      - Unexpected error occurred during payment retrieval.
      - Database connection failure.
      - Service unavailable or timed out.
      - Failed to process payment ID.
    `,
  })
  async getPaymentById(
    @Request() req: any,
    @Param('paymentId', ParseUUIDPipe) paymentId: string,
  ): Promise<PaymentInfoDto> {
    const payment = await this.paymentInfoProvider.getPaymentById(
      req.user.id,
      paymentId,
    );

    if (!payment) {
      throw new NotFoundException('Payment not found');
    }

    return {
      id: payment.id,
      amount: payment.price,
      status: payment.status,
      paymentDate: payment.paidAt,
      planName: payment.name,
      paymentMethod: this.extractPaymentMethod(payment.externalReference),
      externalReference: payment.externalReference,
    };
  }

  private extractPaymentMethod(externalReference?: string): string {
    if (!externalReference) {
      return 'unknown';
    }

    if (externalReference.startsWith('sub_')) {
      return 'stripe';
    }

    if (externalReference.startsWith('paypal_')) {
      return 'paypal';
    }

    return 'other';
  }
}
