import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { Logger } from 'nestjs-pino';
import { SignupCodeFeatureEnum } from 'src/user/entity/signup-code-feature.enum';
import { UserEntity } from 'src/user/entity/user.entity';
import { UserEmailValidatedEvent } from 'src/user/event/user-email-validated.event';
import { UserManager } from 'src/user/service/manager';
import { UserProvider } from 'src/user/service/provider';
import { SignupCodeProvider } from 'src/user/service/signup-code.provider';
import { CreditTypeEnum } from '../entity/credit-type.enum';
import { TransactionTypeEnum } from '../entity/transaction-type.enum';
import { SubscriptionManager } from '../service/manager';
import { TransactionManager } from '../service/transaction.manager';
import { UserCreditBalanceProvider } from '../service/user-credit-balance.provider';

@Injectable()
export class UserEmailValidatedListener {
  constructor(
    private subscriptionManager: SubscriptionManager,
    private userProvider: UserProvider,
    private userManager: UserManager,
    private signupCodeProvider: SignupCodeProvider,
    private transactionManager: TransactionManager,
    private userCreditBalanceProvider: UserCreditBalanceProvider,
    private logger: Logger,
  ) {}

  @OnEvent('user.email_validated', { async: true })
  async handleUserEmailValidatedEvent(event: UserEmailValidatedEvent) {
    const user = await this.userProvider.get(event.id);

    this.logger.log('user.email_validated', {
      userId: user.id,
      grants: user.grants,
    });

    if (!user.grants) {
      user.grants = {};
    }

    if (user.grants.emailValidation === true) {
      return;
    }

    user.grants.emailValidation = true;

    await this.userManager.update(user);

    await this.subscriptionManager.createFreeSubscription(user);

    if (user.userSignupCodeId) {
      const signupCode = await this.signupCodeProvider.get(
        user.userSignupCodeId,
      );

      const transactionContext = {
        userSignupCodeId: signupCode.id,
        refererId: signupCode.userId,
        refererCode: signupCode.code,
        referralUserId: user.id,
      };

      for (const feature in signupCode.features) {
        const amount = signupCode.features[feature];

        switch (feature) {
          case SignupCodeFeatureEnum.RECIPIENT_FREE_IMAGE:
            // Store the transaction result
            await this.transactionManager.register(
              TransactionTypeEnum.TOP_UP,
              amount,
              CreditTypeEnum.IMAGE,
              signupCode.code,
              true,
              user.id,
              null,
              transactionContext,
            );

            // Explicitly check the balance was updated
            const balance = await this.userCreditBalanceProvider.getBalance(
              user,
              CreditTypeEnum.IMAGE,
            );
            this.logger.log('User balance after transaction', {
              userId: user.id,
              balance,
            });
            break;

          case SignupCodeFeatureEnum.REFERER_FREE_IMAGE:
            const referer = await this.userProvider.get(signupCode.userId);

            await this.transactionManager.register(
              TransactionTypeEnum.TOP_UP,
              amount,
              CreditTypeEnum.IMAGE,
              user.id,
              true,
              referer.id,
              null,
              transactionContext,
            );
            break;
        }
      }
    }
  }

  async grantUserCredits(
    user: UserEntity,
    type: CreditTypeEnum,
    amount: number,
    reference: string,
  ) {
    await this.transactionManager.register(
      TransactionTypeEnum.TOP_UP,
      amount,
      type,
      reference,
      true,
      user.id,
    );
  }
}
