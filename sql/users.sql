SELECT  u.id AS "User ID",
        u.username AS "Username",
        u.email AS "E-mail",
        u.created_at AS "Registered At",
        u.images_generated AS "Images Generated",
        u.images_available AS "Images Published",    
        u.models_available AS "Models Trained",
        ( SELECT COUNT(*) FROM image_completion_like icl WHERE icl.user_id = u.id AND icl.deleted_at IS NULL ) AS "Images Liked",
        ( SELECT COUNT(*) FROM image_completion i WHERE i.user_id = u.id AND i.regenerated_from_id IS NOT NULL AND i.deleted_at IS NULL ) AS "Images Regenerated",
        ( SELECT SUM(usc.usages) FROM user_signup_code usc WHERE usc.user_id = u.id AND usc.deleted_at IS NULL ) AS "Friends Referred",
        ( SELECT SUM(t.amount) FROM transaction t WHERE t.user_id = u.id AND t.deleted_at IS NULL AND t.type = 'spending' ) AS "Credits Used",
        ( SELECT SUM(t.amount) FROM transaction t WHERE t.user_id = u.id AND t.deleted_at IS NULL AND t.type = 'top-up' AND t.description <> 'Free' ) AS "Credits bought"
FROM    user_account u
ORDER BY u.username;
