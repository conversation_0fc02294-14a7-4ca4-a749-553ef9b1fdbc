import {
  <PERSON>,
  Get,
  Param,
  ParseU<PERSON><PERSON>ip<PERSON>,
  Query,
  Request,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiOkResponse,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiOperation,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import { BaseFindResponseHeadersDto } from 'src/core/dto/base-find-response-headers.dto';
import { AuthOptional } from 'src/core/security/public-routes';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { ImageCompletionRelatedSearchRequest } from '../dto/image-completion-related.request';
import { ImageCompletionDto } from '../dto/image-completion.dto';
import { ImageCompletionSearchRequest } from '../dto/image-completion.search-request';
import { ImageCompletionProvider } from '../service/provider';
import { ImageCompletionRequestManager } from '../service/request-manager';
import { ImageCompletionResponseMapper } from '../service/response-mapper';

@ApiTags('image_completion')
@Controller('image_completions')
export class ReadController {
  constructor(
    private provider: ImageCompletionProvider,
    private requestManager: ImageCompletionRequestManager,
    private responseMapper: ImageCompletionResponseMapper,
  ) {}

  @Get()
  @ApiOperation({
    operationId: 'image_completion_list',
    summary: 'List image completions',
    description:
      'Retrieves a paginated list of image completions.\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of items per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- status: Filter by status\n' +
      '- privacy: Filter by privacy\n' +
      '- userId: Filter by user ID\n' +
      '- username: Filter by username\n' +
      '- prompt: Filter by prompt\n' +
      '- systemVersion: Filter by system version\n' +
      '- isHot: Filter by hot images\n' +
      '- includeNsfw: Include NSFW content\n' +
      '- onlyFollowing: Show only images from followed users\n' +
      '- modelIds[]: Filter by model IDs\n' +
      '- editedImages: Filter by edited images\n' +
      '- day: Filter by items created today\n' +
      '- week: Filter by items created this week\n' +
      '- month: Filter by items created this month\n' +
      '- year: Filter by items created this year\n',
  })
  @ApiOkResponse({
    type: ImageCompletionDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated list of image completions.',
  })
  @ApiQuery({
    type: ImageCompletionSearchRequest,
    description:
      'Query parameters for searching and paginating image completions.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - Filter parameters must be valid values.
      - \`userId\`: Must be a valid UUID.
      - \`username\`: Must be a valid username.
      - \`systemVersion\`: Must be a valid system version.
      - \`isHot\`: Must be a valid boolean value.
      - \`includeNsfw\`: Must be a valid boolean value.
      - \`onlyFollowing\`: Must be a valid boolean value.
      - \`modelIds\`: Must be a valid array of UUIDs.
      - \`editedImages\`: Must be a valid boolean value.
      - \`day\`: Must be a valid boolean value.
      - \`week\`: Must be a valid boolean value.
      - \`month\`: Must be a valid boolean value.
      - \`year\`: Must be a valid boolean value.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @UsePipes(new ValidationPipe())
  async find(
    @Request() request,
    @Query() query: ImageCompletionSearchRequest,
    @Res() res: Response,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;
    const filters = await this.requestManager.sanitizeSearchFilters(
      inputFilters,
      request.user,
    );

    const entities = await this.provider.findBy(
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    const totalCount = await this.provider.countBy(filters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultiple(entities, request.user?.id));
  }

  @Get(':id')
  @ApiOperation({
    operationId: 'image_completion_get',
    summary: 'Get image completion by ID',
    description:
      'Retrieves a specific image completion by its UUID.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the image completion\n',
  })
  @ApiOkResponse({
    type: ImageCompletionDto,
    description: 'Returns the image completion with the specified ID.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the image completion to retrieve.',
    type: String,
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - Image completion does not exist.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @AuthOptional()
  async get(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<ImageCompletionDto> {
    const imageCompletion = await this.provider.get(id);

    return await this.responseMapper.map(
      imageCompletion,
      true,
      true,
      request.user?.id,
    );
  }

  @Get(':id/related-images')
  @ApiOperation({
    operationId: 'image_completion_related_list',
    summary: 'List related images for an image completion',
    description:
      'Retrieves a paginated list of images related to the specified image completion.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the image completion\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of related images per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- privacy: Filter by privacy\n' +
      '- status: Filter by status\n' +
      '- isNsfw: Filter by NSFW content\n',
  })
  @ApiOkResponse({
    type: ImageCompletionDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated list of related images.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the image completion.',
    type: String,
  })
  @ApiQuery({
    type: ImageCompletionRelatedSearchRequest,
    description:
      'Query parameters for searching and paginating related images.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - \`privacy\`: Must be a valid value from the \`PrivacyEnum\`.
      - \`status\`: Must be a valid value from the \`StatusEnum\`.
      - \`isNsfw\`: Must be a valid boolean value.
      - Filter parameters must be valid values.
      - Image completion does not exist.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @UsePipes(new ValidationPipe())
  async related(
    @Request() request,
    @Query() query: ImageCompletionRelatedSearchRequest,
    @Res() res: Response,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;
    const imageEntity = await this.provider.get(id);
    let imageId = id;

    const filter = { ...inputFilters, hideFromUserProfile: false };

    if (imageEntity.regeneratedFromId) {
      imageId = imageEntity.regeneratedFromId;
    } else if (imageEntity.originalImageCompletionId) {
      imageId = imageEntity.originalImageCompletionId;
    }

    const entities = await this.provider.findRelatedImages(
      filter,
      page,
      limit,
      sortBy,
      sortOrder,
      imageId,
      request.user.id,
      id,
    );
    const totalCount = await this.provider.countRelatedImages(
      filter,
      imageId,
      request.user.id,
      id,
    );
    setPaginationHeaders(res, totalCount, page, limit);
    res.send(await this.responseMapper.mapMultiple(entities, request.user?.id));
  }
}
