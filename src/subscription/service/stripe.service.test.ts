import { BadRequestException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from 'nestjs-pino';
import Stripe from 'stripe';
import { CreditPackageEntity } from '../entity/credit-package.entity';
import { CreditTypeEnum } from '../entity/credit-type.enum';
import { CreditPackageManager } from './credit-package.manager';
import { CreditPackageProvider } from './credit-package.provider';
import { StripeService } from './stripe.service';

describe('StripeService', () => {
  let service: StripeService;
  let stripe: Stripe;
  let creditPackageManager: CreditPackageManager;
  let creditPackageProvider: CreditPackageProvider;
  let logger: Logger;

  const mockStripe = {
    products: {
      create: jest.fn(),
    },
    prices: {
      create: jest.fn(),
      retrieve: jest.fn(),
    },
    subscriptions: {
      retrieve: jest.fn(),
      update: jest.fn(),
    },
    invoices: {
      retrieveUpcoming: jest.fn(),
    },
  };

  const mockCreditPackageManager = {
    save: jest.fn(),
  };

  const mockCreditPackageProvider = {
    findBy: jest.fn(),
  };

  const mockLogger = {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StripeService,
        {
          provide: 'Stripe',
          useValue: mockStripe,
        },
        {
          provide: CreditPackageManager,
          useValue: mockCreditPackageManager,
        },
        {
          provide: CreditPackageProvider,
          useValue: mockCreditPackageProvider,
        },
        {
          provide: Logger,
          useValue: mockLogger,
        },
      ],
    }).compile();

    service = module.get<StripeService>(StripeService);
    stripe = module.get<Stripe>('Stripe');
    creditPackageManager = module.get<CreditPackageManager>(CreditPackageManager);
    creditPackageProvider = module.get<CreditPackageProvider>(CreditPackageProvider);
    logger = module.get<Logger>(Logger);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getOrCreateStripePriceId', () => {
    it('should return existing stripe price ID if available', async () => {
      const creditPackage: CreditPackageEntity = {
        id: '1',
        stripePriceId: 'price_existing',
        name: 'Pro - Yearly',
        price: 7490,
        expiresAfterMonths: 12,
        isRecurring: true,
      } as CreditPackageEntity;

      const result = await service.getOrCreateStripePriceId(creditPackage);

      expect(result).toBe('price_existing');
      expect(mockStripe.products.create).not.toHaveBeenCalled();
      expect(mockStripe.prices.create).not.toHaveBeenCalled();
    });

    it('should create Stripe product and yearly price for yearly package', async () => {
      const creditPackage: CreditPackageEntity = {
        id: '1',
        stripePriceId: null,
        stripeProductId: null,
        name: 'Pro - Yearly',
        price: 7490,
        expiresAfterMonths: 12,
        targetEntity: 'user',
        isRecurring: true,
        creditTypes: { [CreditTypeEnum.IMAGE]: 36000, [CreditTypeEnum.MODEL]: 20 },
      } as CreditPackageEntity;

      const mockProduct = { id: 'prod_yearly' };
      const mockPrice = { id: 'price_yearly' };

      mockStripe.products.create.mockResolvedValue(mockProduct);
      mockStripe.prices.create.mockResolvedValue(mockPrice);
      mockCreditPackageManager.save.mockResolvedValue(creditPackage);

      const result = await service.getOrCreateStripePriceId(creditPackage);

      expect(mockStripe.products.create).toHaveBeenCalledWith({
        name: 'Pro - Yearly',
        metadata: {
          creditPackageId: '1',
          targetEntity: 'user',
          expiresAfterMonths: '12',
        },
      });

      expect(mockStripe.prices.create).toHaveBeenCalledWith({
        product: 'prod_yearly',
        unit_amount: 7490,
        currency: 'eur',
        recurring: {
          interval: 'year',
        },
      });

      expect(result).toBe('price_yearly');
      expect(creditPackage.stripeProductId).toBe('prod_yearly');
      expect(creditPackage.stripePriceId).toBe('price_yearly');
    });

    it('should create monthly price for monthly package', async () => {
      const creditPackage: CreditPackageEntity = {
        id: '2',
        stripePriceId: null,
        stripeProductId: 'prod_existing',
        name: 'Pro',
        price: 749,
        expiresAfterMonths: 1,
        targetEntity: 'user',
        isRecurring: true,
      } as CreditPackageEntity;

      const mockPrice = { id: 'price_monthly' };
      mockStripe.prices.create.mockResolvedValue(mockPrice);
      mockCreditPackageManager.save.mockResolvedValue(creditPackage);

      const result = await service.getOrCreateStripePriceId(creditPackage);

      expect(mockStripe.prices.create).toHaveBeenCalledWith({
        product: 'prod_existing',
        unit_amount: 749,
        currency: 'eur',
        recurring: {
          interval: 'month',
          interval_count: 1,
        },
      });

      expect(result).toBe('price_monthly');
    });

    it('should use custom currency when specified in credit package', async () => {
      const creditPackage: CreditPackageEntity = {
        id: '3',
        stripePriceId: null,
        stripeProductId: 'prod_existing',
        name: 'Pro USD',
        price: 749,
        currency: 'usd',
        expiresAfterMonths: 1,
        targetEntity: 'user',
        isRecurring: true,
      } as CreditPackageEntity;

      const mockPrice = { id: 'price_usd' };
      mockStripe.prices.create.mockResolvedValue(mockPrice);
      mockCreditPackageManager.save.mockResolvedValue(creditPackage);

      const result = await service.getOrCreateStripePriceId(creditPackage);

      expect(mockStripe.prices.create).toHaveBeenCalledWith({
        product: 'prod_existing',
        unit_amount: 749,
        currency: 'usd',
        recurring: {
          interval: 'month',
          interval_count: 1,
        },
      });

      expect(result).toBe('price_usd');
    });

    it('should handle Stripe errors gracefully', async () => {
      const creditPackage: CreditPackageEntity = {
        id: '3',
        stripePriceId: null,
        stripeProductId: null,
        name: 'Pro - Yearly',
        price: 7490,
        expiresAfterMonths: 12,
        isRecurring: true,
      } as CreditPackageEntity;

      mockStripe.products.create.mockRejectedValue(new Error('Stripe error'));

      await expect(service.getOrCreateStripePriceId(creditPackage)).rejects.toThrow(
        BadRequestException,
      );

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to create Stripe price',
        expect.objectContaining({
          error: expect.any(Error),
          creditPackageId: '3',
          creditPackageName: 'Pro - Yearly',
        }),
      );
    });
  });

  describe('createYearlyStripeProducts', () => {
    it('should create Stripe products for all yearly packages', async () => {
      const yearlyPackages: CreditPackageEntity[] = [
        {
          id: '1',
          name: 'Pro - Yearly',
          stripePriceId: null,
          expiresAfterMonths: 12,
          isRecurring: true,
          isVisible: true,
        } as CreditPackageEntity,
        {
          id: '2',
          name: 'Fun - Yearly',
          stripePriceId: 'price_existing',
          expiresAfterMonths: 12,
          isRecurring: true,
          isVisible: true,
        } as CreditPackageEntity,
      ];

      mockCreditPackageProvider.findBy.mockResolvedValue(yearlyPackages);

      // Mock the getOrCreateStripePriceId method
      jest.spyOn(service, 'getOrCreateStripePriceId').mockResolvedValue('price_new');

      await service.createYearlyStripeProducts();

      expect(mockCreditPackageProvider.findBy).toHaveBeenCalledWith(
        {
          expiresAfterMonths: 12,
          isRecurring: true,
          isVisible: true,
        },
        1,
        100,
      );

      // Should only call for packages without existing price IDs
      expect(service.getOrCreateStripePriceId).toHaveBeenCalledTimes(1);
      expect(service.getOrCreateStripePriceId).toHaveBeenCalledWith(yearlyPackages[0]);
    });
  });

  describe('calculateProration', () => {
    it('should calculate proration for subscription upgrade', async () => {
      const mockSubscription = {
        id: 'sub_123',
        customer: 'cus_123',
        items: {
          data: [{ id: 'si_123' }],
        },
      } as Stripe.Subscription;

      const mockUpcomingInvoice = {
        amount_due: 5000,
      } as Stripe.UpcomingInvoice;

      mockStripe.invoices.retrieveUpcoming.mockResolvedValue(mockUpcomingInvoice);

      const result = await service.calculateProration(mockSubscription, 'price_yearly');

      expect(mockStripe.invoices.retrieveUpcoming).toHaveBeenCalledWith({
        customer: 'cus_123',
        subscription: 'sub_123',
        subscription_items: [
          {
            id: 'si_123',
            price: 'price_yearly',
          },
        ],
        subscription_proration_behavior: 'create_prorations',
      });

      expect(result).toEqual(mockUpcomingInvoice);
    });

    it('should handle proration calculation errors', async () => {
      const mockSubscription = {
        id: 'sub_123',
        customer: 'cus_123',
        items: { data: [{ id: 'si_123' }] },
      } as Stripe.Subscription;

      mockStripe.invoices.retrieveUpcoming.mockRejectedValue(new Error('Stripe error'));

      await expect(
        service.calculateProration(mockSubscription, 'price_yearly'),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('upgradeToYearlySubscription', () => {
    it('should upgrade subscription to yearly billing', async () => {
      const mockSubscription = {
        id: 'sub_123',
        items: { data: [{ id: 'si_123' }] },
      } as Stripe.Subscription;

      const mockUpdatedSubscription = {
        id: 'sub_123',
        current_period_end: 1234567890,
      } as Stripe.Subscription;

      mockStripe.subscriptions.retrieve.mockResolvedValue(mockSubscription);
      mockStripe.subscriptions.update.mockResolvedValue(mockUpdatedSubscription);

      const result = await service.upgradeToYearlySubscription('sub_123', 'price_yearly');

      expect(mockStripe.subscriptions.update).toHaveBeenCalledWith('sub_123', {
        items: [
          {
            id: 'si_123',
            price: 'price_yearly',
          },
        ],
        proration_behavior: 'create_prorations',
      });

      expect(result).toEqual(mockUpdatedSubscription);
    });
  });

  describe('validateStripePriceId', () => {
    it('should return true for active price', async () => {
      mockStripe.prices.retrieve.mockResolvedValue({ active: true });

      const result = await service.validateStripePriceId('price_123');

      expect(result).toBe(true);
      expect(mockStripe.prices.retrieve).toHaveBeenCalledWith('price_123');
    });

    it('should return false for inactive price', async () => {
      mockStripe.prices.retrieve.mockResolvedValue({ active: false });

      const result = await service.validateStripePriceId('price_123');

      expect(result).toBe(false);
    });

    it('should return false on error', async () => {
      mockStripe.prices.retrieve.mockRejectedValue(new Error('Price not found'));

      const result = await service.validateStripePriceId('price_123');

      expect(result).toBe(false);
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });
});
