import { JwtIntegrationAuthGuard } from './jwt-integration-auth.guard';
import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Logger } from 'nestjs-pino';
import { JwtService } from '@nestjs/jwt';
import { UserProvider } from 'src/user/service/provider';
import { UserAuthTokenProvider } from './user-auth-token.provider';
import { OrganizationProvider } from 'src/organization/service/provider';
import { OrganizationAuthTokenProvider } from './organization-auth-token.provider';
import { IS_AUTH_OPTIONAL_KEY, IS_PUBLIC_KEY } from 'src/core/security/public-routes';

describe('JwtIntegrationAuthGuard', () => {
  let guard: JwtIntegrationAuthGuard;
  let jwtService: { verify: jest.Mock };
  let userProvider: { get: jest.Mock };
  let userAuthTokenProvider: { findOneBy: jest.Mock };
  let organizationProvider: { get: jest.Mock };
  let organizationAuthTokenProvider: { findOneBy: jest.Mock };
  let reflector: { getAllAndOverride: jest.Mock };
  let logger: { verbose: jest.Mock };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        JwtIntegrationAuthGuard,
        { provide: JwtService, useValue: { verify: jest.fn() } },
        { provide: UserProvider, useValue: { get: jest.fn() } },
        { provide: UserAuthTokenProvider, useValue: { findOneBy: jest.fn() } },
        { provide: OrganizationProvider, useValue: { get: jest.fn() } },
        { provide: OrganizationAuthTokenProvider, useValue: { findOneBy: jest.fn() } },
        { provide: Reflector, useValue: { getAllAndOverride: jest.fn() } },
        { provide: Logger, useValue: { verbose: jest.fn() } },
      ],
    }).compile();

    guard = module.get<JwtIntegrationAuthGuard>(JwtIntegrationAuthGuard);
    jwtService = module.get(JwtService) as any;
    userProvider = module.get(UserProvider) as any;
    userAuthTokenProvider = module.get(UserAuthTokenProvider) as any;
    organizationProvider = module.get(OrganizationProvider) as any;
    organizationAuthTokenProvider = module.get(OrganizationAuthTokenProvider) as any;
    reflector = module.get(Reflector) as any;
    logger = module.get(Logger) as any;
  });

  function createMockContext(
    token?: string,
    isPublic = false,
    isAuthOptional = false,
  ) {
    const req: any = { headers: {} };
    if (token) {
      req.headers.authorization = `Bearer ${token}`;
    }
    reflector.getAllAndOverride.mockImplementation((key) => {
      if (key === IS_PUBLIC_KEY) return isPublic;
      if (key === IS_AUTH_OPTIONAL_KEY) return isAuthOptional;
      return undefined;
    });
    const context: any = {
      switchToHttp: () => ({ getRequest: () => req }),
      getHandler: () => ({}),
      getClass: () => ({}),
    };
    return { context, req };
  }

  it('throws UnauthorizedException when no token and not optional', async () => {
    const { context } = createMockContext(undefined, false, false);
    await expect(
      guard.canActivate(context as ExecutionContext),
    ).rejects.toBeInstanceOf(UnauthorizedException);
  });

  it('authenticates organization token', async () => {
    const { context, req } = createMockContext('org-token');
    const payload = { tokenType: 'organization', organizationId: 'org1' };
    jwtService.verify.mockReturnValue(payload);
    const orgEntity = { id: 'org1' };
    organizationProvider.get.mockResolvedValue(orgEntity);
    const orgAuth = { token: 'org-token', hideFromUserProfile: false };
    organizationAuthTokenProvider.findOneBy.mockResolvedValue(orgAuth);

    const result = await guard.canActivate(context as ExecutionContext);
    expect(result).toBe(true);
    expect(req.organization).toBe(orgEntity);
    expect(req.integrationAuthToken).toBe(orgAuth);
  });

  it('throws for invalid organization token', async () => {
    const { context } = createMockContext('org-token');
    const payload = { tokenType: 'organization', organizationId: 'org1' };
    jwtService.verify.mockReturnValue(payload);
    organizationProvider.get.mockResolvedValue({ id: 'org1' });
    organizationAuthTokenProvider.findOneBy.mockResolvedValue(undefined);

    await expect(
      guard.canActivate(context as ExecutionContext),
    ).rejects.toBeInstanceOf(UnauthorizedException);
  });

  it('authenticates user token', async () => {
    const { context, req } = createMockContext('usr-token');
    const payload = { tokenType: 'access', id: 'user1' };
    jwtService.verify.mockReturnValue(payload);
    const userEntity = { id: 'user1' };
    userProvider.get.mockResolvedValue(userEntity);
    const userAuth = { token: 'usr-token', hideFromUserProfile: true };
    userAuthTokenProvider.findOneBy.mockResolvedValue(userAuth);

    const result = await guard.canActivate(context as ExecutionContext);
    expect(result).toBe(true);
    expect(req.user).toBe(userEntity);
    expect(req.integrationAuthToken).toBe(userAuth);
  });
});