import { ArgumentsHost, Catch, NotFoundException } from '@nestjs/common';
import { BaseExceptionFilter } from '@nestjs/core';
import { EntityNotFoundError } from 'typeorm';

@Catch(EntityNotFoundError)
export class EntityNotFoundErrorFilter extends BaseExceptionFilter {
  public catch(exception: EntityNotFoundError, host: ArgumentsHost): any {
    super.catch(new NotFoundException(exception.message), host);
  }
}
