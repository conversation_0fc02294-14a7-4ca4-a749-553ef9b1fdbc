import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from 'nestjs-pino';
import { DateTime } from 'luxon';
import { CreditTypeEnum } from '../entity/credit-type.enum';
import { SubscriptionStatusEnum } from '../entity/subscription-status.enum';
import { TransactionTypeEnum } from '../entity/transaction-type.enum';
import { CreditRenewalService } from './credit-renewal.service';
import { SubscriptionProvider } from './provider';
import { TransactionManager } from './transaction.manager';
import { UserCreditBalanceManager } from './user-credit-balance.manager';

describe('CreditRenewalService', () => {
  let service: CreditRenewalService;
  let subscriptionProvider: SubscriptionProvider;
  let userCreditBalanceManager: UserCreditBalanceManager;
  let transactionManager: TransactionManager;
  let logger: Logger;

  const mockSubscriptionProvider = {
    findBy: jest.fn(),
    get: jest.fn(),
  };

  const mockUserCreditBalanceManager = {
    increase: jest.fn(),
  };

  const mockTransactionManager = {
    register: jest.fn(),
  };

  const mockLogger = {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreditRenewalService,
        {
          provide: SubscriptionProvider,
          useValue: mockSubscriptionProvider,
        },
        {
          provide: UserCreditBalanceManager,
          useValue: mockUserCreditBalanceManager,
        },
        {
          provide: TransactionManager,
          useValue: mockTransactionManager,
        },
        {
          provide: Logger,
          useValue: mockLogger,
        },
      ],
    }).compile();

    service = module.get<CreditRenewalService>(CreditRenewalService);
    subscriptionProvider = module.get<SubscriptionProvider>(SubscriptionProvider);
    userCreditBalanceManager = module.get<UserCreditBalanceManager>(UserCreditBalanceManager);
    transactionManager = module.get<TransactionManager>(TransactionManager);
    logger = module.get<Logger>(Logger);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('processMonthlyRenewals', () => {
    it('should process yearly subscriptions due for renewal', async () => {
      const now = DateTime.now();
      const lastMonth = now.minus({ months: 1 });

      const mockSubscriptions = [
        {
          id: 'sub_1',
          userId: 'user_1',
          creditPackageId: 'pkg_1',
          status: SubscriptionStatusEnum.ACTIVE,
          paidAt: lastMonth.toJSDate(),
          renewedAt: null,
          creditPackage: {
            id: 'pkg_1',
            name: 'Pro - Yearly',
            expiresAfterMonths: 12,
            isRecurring: true,
            creditTypes: {
              [CreditTypeEnum.IMAGE]: 36000,
              [CreditTypeEnum.MODEL]: 20,
            },
          },
        },
        {
          id: 'sub_2',
          userId: 'user_2',
          creditPackageId: 'pkg_2',
          status: SubscriptionStatusEnum.ACTIVE,
          paidAt: now.minus({ months: 2 }).toJSDate(),
          renewedAt: null,
          creditPackage: {
            id: 'pkg_2',
            name: 'Fun - Yearly',
            expiresAfterMonths: 12,
            isRecurring: true,
            creditTypes: {
              [CreditTypeEnum.IMAGE]: 10800,
              [CreditTypeEnum.MODEL]: 10,
            },
          },
        },
      ];

      mockSubscriptionProvider.findBy.mockResolvedValue(mockSubscriptions);

      await service.processMonthlyRenewals();

      expect(mockSubscriptionProvider.findBy).toHaveBeenCalledWith(
        {
          status: SubscriptionStatusEnum.ACTIVE,
          creditPackage: {
            expiresAfterMonths: 12,
            isRecurring: true,
          },
        },
        1,
        1000,
      );

      // Should process the first subscription (due today)
      expect(mockTransactionManager.register).toHaveBeenCalledWith(
        TransactionTypeEnum.SUBSCRIPTION,
        0,
        'Pro - Yearly - Monthly Renewal',
        expect.stringContaining('sub_1|monthly|'),
        false,
        'user_1',
        null,
        expect.objectContaining({
          subscriptionId: 'sub_1',
          userId: 'user_1',
          creditPackageId: 'pkg_1',
          renewalType: 'monthly',
        }),
      );

      expect(mockUserCreditBalanceManager.increase).toHaveBeenCalledWith(
        CreditTypeEnum.IMAGE,
        36000,
        'user_1',
        null,
        expect.any(Date),
      );

      expect(mockUserCreditBalanceManager.increase).toHaveBeenCalledWith(
        CreditTypeEnum.MODEL,
        20,
        'user_1',
        null,
        expect.any(Date),
      );
    });

    it('should handle errors gracefully', async () => {
      mockSubscriptionProvider.findBy.mockRejectedValue(new Error('Database error'));

      await service.processMonthlyRenewals();

      expect(mockLogger.error).toHaveBeenCalledWith(
        'credit_renewal.process_monthly_renewals_error',
        expect.objectContaining({
          error: expect.any(Error),
        }),
      );
    });
  });

  describe('processSubscriptionRenewalManual', () => {
    it('should process manual renewal for valid yearly subscription', async () => {
      const subscription = {
        id: 'sub_123',
        userId: 'user_123',
        creditPackageId: 'pkg_123',
        status: SubscriptionStatusEnum.ACTIVE,
        creditPackage: {
          id: 'pkg_123',
          name: 'Pro - Yearly',
          expiresAfterMonths: 12,
          creditTypes: {
            [CreditTypeEnum.IMAGE]: 36000,
            [CreditTypeEnum.MODEL]: 20,
          },
        },
      };

      mockSubscriptionProvider.get.mockResolvedValue(subscription);

      await service.processSubscriptionRenewalManual('sub_123');

      expect(mockSubscriptionProvider.get).toHaveBeenCalledWith('sub_123');
      expect(mockTransactionManager.register).toHaveBeenCalled();
      expect(mockUserCreditBalanceManager.increase).toHaveBeenCalledTimes(2);
    });

    it('should throw error for non-existent subscription', async () => {
      mockSubscriptionProvider.get.mockResolvedValue(null);

      await expect(service.processSubscriptionRenewalManual('sub_123')).rejects.toThrow(
        'Subscription not found: sub_123',
      );
    });

    it('should throw error for inactive subscription', async () => {
      const subscription = {
        id: 'sub_123',
        status: SubscriptionStatusEnum.INACTIVE,
        creditPackage: { expiresAfterMonths: 12 },
      };

      mockSubscriptionProvider.get.mockResolvedValue(subscription);

      await expect(service.processSubscriptionRenewalManual('sub_123')).rejects.toThrow(
        'Subscription is not active: sub_123',
      );
    });

    it('should throw error for non-yearly subscription', async () => {
      const subscription = {
        id: 'sub_123',
        status: SubscriptionStatusEnum.ACTIVE,
        creditPackage: { expiresAfterMonths: 1 },
      };

      mockSubscriptionProvider.get.mockResolvedValue(subscription);

      await expect(service.processSubscriptionRenewalManual('sub_123')).rejects.toThrow(
        'Subscription is not yearly: sub_123',
      );
    });
  });

  describe('getRenewalStatistics', () => {
    it('should return correct statistics', async () => {
      const now = DateTime.now();
      const mockSubscriptions = [
        {
          id: 'sub_1',
          paidAt: now.minus({ months: 1 }).toJSDate(),
          renewedAt: null,
        },
        {
          id: 'sub_2',
          paidAt: now.minus({ months: 2 }).toJSDate(),
          renewedAt: null,
        },
        {
          id: 'sub_3',
          paidAt: now.minus({ days: 15 }).toJSDate(),
          renewedAt: null,
        },
      ];

      mockSubscriptionProvider.findBy.mockResolvedValue(mockSubscriptions);

      const stats = await service.getRenewalStatistics();

      expect(stats.totalYearlySubscriptions).toBe(3);
      expect(stats.subscriptionsDueToday).toBe(1); // sub_1 is due today
      expect(stats.subscriptionsOverdue).toBe(1); // sub_2 is overdue
    });
  });

  describe('findYearlySubscriptionsDueForRenewal', () => {
    it('should filter subscriptions due for renewal today', async () => {
      const now = DateTime.now();
      const mockSubscriptions = [
        {
          id: 'sub_1',
          paidAt: now.minus({ months: 1 }).toJSDate(),
          renewedAt: null,
        },
        {
          id: 'sub_2',
          paidAt: now.minus({ days: 15 }).toJSDate(),
          renewedAt: null,
        },
      ];

      mockSubscriptionProvider.findBy.mockResolvedValue(mockSubscriptions);

      const result = await service['findYearlySubscriptionsDueForRenewal']();

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('sub_1');
    });
  });
});
