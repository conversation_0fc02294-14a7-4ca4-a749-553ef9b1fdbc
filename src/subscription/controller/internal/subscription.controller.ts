import {
  <PERSON>,
  Param,
  Put,
  HttpCode,
  ParseUUIDPipe,
  Post,
  Delete,
  Get,
} from '@nestjs/common';
import { Body } from '@nestjs/common/decorators/http/route-params.decorator';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiTags,
  ApiParam,
  ApiOperation,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { SubscriptionDto } from 'src/subscription/dto/subscription.dto';
import { SubscriptionInternalRequest } from 'src/subscription/dto/subscription.internal-request';
import { SubscriptionProvider } from '../../service/provider';
import { SubscriptionRequestManager } from '../../service/request-manager';
import { SubscriptionResponseMapper } from '../../service/response-mapper';
import { SubscriptionManager } from 'src/subscription/service/manager';
import { UserProvider } from 'src/user/service/provider';
import { In } from 'typeorm';
import { SubscriptionStatusEnum } from 'src/organization/enum/subscription-status.enum';

@ApiTags('subscription / internal')
@Controller('internal/subscriptions')
export class SubscriptionController {
  constructor(
    private provider: SubscriptionProvider,
    private subscriptionManager: SubscriptionManager,
    private requestManager: SubscriptionRequestManager,
    private responseMapper: SubscriptionResponseMapper,
    private userProvider: UserProvider,
  ) {}

  @Put(':id/activation')
  @ApiOperation({
    operationId: 'internal_subscription_activate',
    summary: 'Activate a subscription (internal)',
    description:
      'Activates a subscription with internal parameters. Intended for internal use only.\n\n' +
      'Requirements:\n\n' +
      '- Subscription must be new or pending' +
      '\n\n' +
      'Required Parameters:\n\n' +
      '- id: UUID of the subscription\n' +
      'Required Body Parameters:\n' +
      '- expiresAt: Expiration date of the subscription \n\n' +
      'Optional Body Parameters: \n' +
      '- externalReference: External reference of the subscription\n',
  })
  @ApiBody({ type: SubscriptionInternalRequest })
  @ApiOkResponse({
    type: SubscriptionDto,
    description: 'Subscription activated.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - \`expiresAt\`: Must be a valid date.
      - \`externalReference\`: Must be a valid string.
      - Subscription does not exist.
      - Subscription is not in a valid state for activation.
      - Invalid or unavailable parameters.
      - Failed to analyze request body.
  `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async activation(
    @Body() requestBody: SubscriptionInternalRequest,
    @Param() params,
  ): Promise<SubscriptionDto> {
    const subscription = await this.provider.get(params.id);

    this.requestManager.handleInternalActivation(subscription, requestBody);

    return await this.responseMapper.mapInternal(subscription);
  }

  @Put(':id/stripe-status')
  @ApiOperation({
    operationId: 'internal_subscription_update_stripe_status',
    summary: 'Update Stripe status for subscription (internal)',
    description:
      'Updates the Stripe checkout or subscription status for a specific subscription. Intended for internal use only.' +
      '\n\nRequired Parameters:\n' +
      '- id: UUID of the subscription\n',
  })
  @ApiOkResponse({
    type: SubscriptionDto,
    description: 'Subscription Stripe status updated.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - Subscription does not exist.
      - Invalid or unavailable parameters.
  `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async stripeStatusUpdate(@Param() params): Promise<SubscriptionDto> {
    const subscription = await this.provider.get(params.id);

    if (subscription.status == 'pending') {
      await this.subscriptionManager.updateStripeCheckoutStatus(subscription);
    } else {
      await this.subscriptionManager.updateStripeSubscriptionStatus(
        subscription,
      );
    }

    return await this.responseMapper.mapInternal(subscription);
  }

  @Put(':id/checkout-session-status')
  @ApiOperation({
    operationId: 'internal_subscription_update_checkout_session_status',
    summary: 'Update Stripe checkout session status (internal)',
    description:
      'Updates the Stripe checkout session status for a specific subscription. Intended for internal use only.' +
      '\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the subscription\n',
  })
  @ApiOkResponse({
    type: SubscriptionDto,
    description: 'Subscription checkout session status updated.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - Subscription does not exist.
      - Invalid or unavailable parameters.
  `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async stripeCheckoutSessionStatusUpdate(
    @Param() params,
  ): Promise<SubscriptionDto> {
    const subscription = await this.provider.get(params.id);

    this.subscriptionManager.updateStripeCheckoutStatus(subscription);

    return await this.responseMapper.mapInternal(subscription);
  }

  @Put('refresh-pending')
  @ApiOperation({
    operationId: 'internal_subscription_refresh_pending',
    summary: 'Refresh all pending subscriptions (internal)',
    description:
      'Refreshes the status of all pending subscriptions. Intended for internal use only.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - Invalid or unavailable parameters.
  `,
  })
  @ApiNoContentResponse({ description: 'Pending subscriptions refreshed.' })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async refreshPendingSubscriptions(): Promise<void> {
    await this.requestManager.refreshPendingSubscriptions();
  }

  @Get(':userId')
  @ApiOperation({
    operationId: 'internal_subscription_get_by_user',
    summary: 'Get current active subscription for user (internal)',
    description:
      'Retrieves the current active subscription for the specified user. Intended for internal use only.' +
      '\n\n' +
      'Required Parameters:\n' +
      '- userId: UUID of the user\n',
  })
  @ApiOkResponse({
    type: SubscriptionDto,
    description: 'Current active subscription.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`userId\`: Must be a valid UUID.
      - User does not have an active subscription.
  `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async find(
    @Param('userId', new ParseUUIDPipe()) userId: string,
  ): Promise<SubscriptionDto> {
    const subscription =
      await this.provider.getCurrentlyActiveSubscriptionForUser(userId);

    return this.responseMapper.map(subscription);
  }

  @Post('trial/:userId')
  @ApiOperation({
    operationId: 'internal_subscription_activate_trial',
    summary: 'Activate trial subscription for user (internal)',
    description:
      'Activates a trial subscription for the specified user. Intended for internal use only.' +
      '\n\n' +
      'Required Parameters:\n' +
      '- userId: UUID of the user\n',
  })
  @ApiOkResponse({
    type: SubscriptionDto,
    description: 'Trial subscription activated.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`userId\`: Must be a valid UUID.
      - User does not exist.
 `,
  })
  @ApiParam({ name: 'userId', description: 'ID of the user' })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async activateTrialSubscription(
    @Param('userId', new ParseUUIDPipe()) userId: string,
  ): Promise<SubscriptionDto> {
    const user = await this.userProvider.get(userId);

    const subscription = await this.subscriptionManager.createTrialSubscription(
      user,
      'Beginner',
      30,
    );

    return await this.responseMapper.mapInternal(subscription);
  }

  @Delete(':id')
  @ApiOperation({
    operationId: 'internal_subscription_delete',
    summary: 'Delete a subscription (internal)',
    description:
      'Deletes a specific subscription. Intended for internal use only.\n\n' +
      'Requirements:\n' +
      '- Subscription must be active, pending, or new\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the subscription\n',
  })
  @ApiNoContentResponse({ description: 'Subscription deleted successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - Subscription does not exist.
      - Subscription is not in a valid state for deletion.
  `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @HttpCode(204)
  async delete(@Param('id', new ParseUUIDPipe()) id: string): Promise<void> {
    const subscription = await this.provider.getBy({
      id: id,
      status: In([
        SubscriptionStatusEnum.ACTIVE,
        SubscriptionStatusEnum.PENDING,
        SubscriptionStatusEnum.NEW,
      ]),
    });

    await this.subscriptionManager.deactivateSubscription(subscription);
  }
}
