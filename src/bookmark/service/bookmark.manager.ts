import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BookmarkEntity } from '../entity/bookmark.entity';

@Injectable()
export class BookmarkManager {
  constructor(
    @InjectRepository(BookmarkEntity)
    private repository: Repository<BookmarkEntity>,
  ) {}

  async create(entity: BookmarkEntity): Promise<BookmarkEntity> {
    return this.save(entity);
  }

  async delete(entity: BookmarkEntity): Promise<void> {
    await this.repository.delete(entity.id);
  }

  async save(entity: BookmarkEntity): Promise<BookmarkEntity> {
    return this.repository.save(entity);
  }
}
