import { Controller, Get, Query, Res } from '@nestjs/common';
import {
  ApiOkResponse,
  ApiQuery,
  ApiTags,
  ApiOperation,
  ApiBadRequestResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { CreditPackageDto } from '../dto/credit-package.dto';
import { CreditPackageSearchRequest } from '../dto/credit-package.search-request';
import { CreditPackageProvider } from '../service/credit-package.provider';
import { CreditPackageResponseMapper } from '../service/credit-package.response-mapper';

@ApiTags('credit-packages')
@Controller('credit-packages')
export class CreditPackageController {
  constructor(
    private responseMapper: CreditPackageResponseMapper,
    private provider: CreditPackageProvider,
  ) {}

  @Get()
  @ApiOperation({
    operationId: 'credit_package_list',
    summary: 'List available credit packages',
    description:
      'Retrieves a paginated list of available credit packages for purchase.\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of packages per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- targetEntity: Filter by target entity (user, organization)\n',
  })
  @ApiOkResponse({
    type: CreditPackageDto,
    isArray: true,
    description: 'Paginated list of credit packages.',
  })
  @ApiQuery({
    type: CreditPackageSearchRequest,
    description:
      'Query parameters for searching and paginating credit packages.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - Filter parameters must be valid values.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async find(
    @Query() query: CreditPackageSearchRequest,
    @Res() res: Response,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;
    const filters = { ...inputFilters, isVisible: true };

    const entities = await this.provider.findBy(
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    const totalCount = await this.provider.countBy(filters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultiple(entities));
  }
}
