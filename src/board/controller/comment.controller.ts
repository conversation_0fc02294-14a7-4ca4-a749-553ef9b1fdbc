import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Param,
  ParseUUIDPipe,
  Post,
  Query,
  Request,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { Response } from 'express';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiQuery,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiForbiddenResponse,
  ApiUnauthorizedResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { BoardProvider } from '../service/provider';
import { BoardCommentProvider } from '../service/board-comment.provider';
import { BoardCommentRequestManager } from '../service/board-comment.request-manager';
import { BoardCommentResponseMapper } from '../service/board-comment.response-mapper';
import { BoardCommentDTO } from '../dto/board-comment.dto';
import { BoardCommentSearchRequest } from '../dto/board-comment.search-request';
import { BaseFindResponseHeadersDto } from 'src/core/dto/base-find-response-headers.dto';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { BoardCommentRequest } from '../dto/board-comment.request';

@ApiTags('board / comments')
@Controller('boards')
export class CommentController {
  constructor(
    private boardProvider: BoardProvider,
    private boardCommentProvider: BoardCommentProvider,
    private requestManager: BoardCommentRequestManager,
    private responseMapper: BoardCommentResponseMapper,
  ) {}

  @Get(':id/comments')
  @ApiOperation({
    operationId: 'board_comment_list',
    summary: 'List comments for a board',
    description:
      'Retrieves a paginated list of comments for the specified board.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the board\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of comments per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- userId: UUID of the user\n' +
      '- boardId: UUID of the board\n' +
      '- username: Username of the user\n',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the board to retrieve comments for.',
    type: String,
  })
  @ApiQuery({
    type: BoardCommentSearchRequest,
    description: 'Query parameters for searching and paginating comments.',
  })
  @ApiOkResponse({
    type: BoardCommentDTO,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated list of comments for the board.',
  })
  @ApiBadRequestResponse({
    description: `
      Bad Request. Possible reasons:\n
      - \`id\`, \`boardId\`, \`commentId\`: Must be valid UUIDs.
      - \`comment\`: Must be a non-empty string.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User does not have access to this board.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @UsePipes(new ValidationPipe())
  async find(
    @Query() query: BoardCommentSearchRequest,
    @Res() res: Response,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;
    const filters = this.validateSearchFilters(inputFilters, id);

    const entities = await this.boardCommentProvider.findBy(
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    const totalCount = await this.boardCommentProvider.countBy(filters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultiple(entities));
  }

  @Post(':id/comments')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'board_comment_create',
    summary: 'Add a comment to a board',
    description:
      'Adds a new comment to the specified board.\n\n' +
      'Required Query Parameters:\n' +
      '- id: UUID of the board\n' +
      'Required Body Parameters:\n' +
      '- comment: Content of the comment\n',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the board to add a comment to.',
    type: String,
  })
  @ApiBody({
    type: BoardCommentRequest,
    description: 'Comment creation parameters.',
  })
  @ApiNoContentResponse({
    description: 'Comment added successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - \`comment\`: Must be a non-empty string.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description:
      'Forbidden. User does not have permission to comment on this board.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async coment(
    @Body() requestBody: BoardCommentRequest,
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ) {
    const entity = await this.boardProvider.get(id);
    await this.requestManager.comment(entity, request.user.id, requestBody);
  }

  @Delete(':boardId/comments/:commentId')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'board_comment_delete',
    summary: 'Delete a comment from a board',
    description:
      'Deletes the specified comment from the board if the user is the author.\n\n' +
      'Required Parameters:\n' +
      '- boardId: UUID of the board\n' +
      '- commentId: UUID of the comment to delete\n',
  })
  @ApiParam({
    name: 'boardId',
    description: 'UUID of the board.',
    type: String,
  })
  @ApiParam({
    name: 'commentId',
    description: 'UUID of the comment to delete.',
    type: String,
  })
  @ApiNoContentResponse({
    description: 'Comment deleted successfully.',
  })
  @ApiBadRequestResponse({
    description: `
      Bad Request. Possible reasons:\n
      - \`id\`, \`boardId\`, \`commentId\`: Must be valid UUIDs.
      - \`comment\`: Must be a non-empty string.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description:
      'Forbidden. User does not have permission to delete this comment.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async delete(
    @Request() request,
    @Param('boardId', new ParseUUIDPipe()) boardId: string,
    @Param('commentId', new ParseUUIDPipe()) commentId: string,
  ) {
    const entity = await this.boardCommentProvider.getBy({
      id: commentId,
      boardId: boardId,
      userId: request.user.id,
    });
    const entityBoard = await this.boardProvider.get(boardId);

    await this.requestManager.delete(entity, entityBoard);
  }

  validateSearchFilters(
    filters: BoardCommentSearchRequest,
    boardId: string,
  ): any {
    return { ...filters, boardId: boardId };
  }
}
