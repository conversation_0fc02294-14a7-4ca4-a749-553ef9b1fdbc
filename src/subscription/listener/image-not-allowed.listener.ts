import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { ImageNotAllowedEvent } from 'src/image-completion/event/image-not-allowed.event';
import { UserManager } from 'src/user/service/manager';
import { UserProvider } from 'src/user/service/provider';
import { CreditTypeEnum } from '../entity/credit-type.enum';
import { TransactionTypeEnum } from '../entity/transaction-type.enum';
import { TransactionManager } from '../service/transaction.manager';

@Injectable()
export class ImageNotAllowedListener {
  constructor(
    private userManager: UserManager,
    private userProvider: UserProvider,
    private transactionManager: TransactionManager,
  ) {}

  @OnEvent('image.not_allowed', { async: true })
  async handleImageNotAllowedEvent(event: ImageNotAllowedEvent) {
    const user = await this.userProvider.get(event.userId);

    await this.transactionManager.register(
      TransactionTypeEnum.SPENDING,
      1,
      CreditTypeEnum.IMAGE,
      event.id,
      true,
      user.id,
    );
  }
}
