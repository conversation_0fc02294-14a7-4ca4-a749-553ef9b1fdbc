import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1742138443587 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            UPDATE "public"."model" SET storage_bucket = 'letzai-prod-models-v3' WHERE storage_bucket = 'letzai-prod-models-v2'
        `);
    await queryRunner.query(`
            UPDATE "public"."model" SET storage_bucket = 'letzai-dev-models-v3' WHERE storage_bucket = 'letzai-dev-models-v2'
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
