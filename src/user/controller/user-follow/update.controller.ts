import {
  Controller,
  HttpCode,
  Param,
  ParseUUIDPipe,
  Put,
  Request,
} from '@nestjs/common';
import {
  ApiNoContentResponse,
  ApiParam,
  ApiTags,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiNotFoundResponse,
  ApiBadRequestResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { UserFollowManager } from '../../service/user-follow.manager';
import { UserFollowProvider } from 'src/user/service/user-follow.provider';

@ApiTags('user-follow')
@Controller('/users/current/follows')
export class UpdateController {
  constructor(
    private provider: UserFollowProvider,
    private manager: UserFollowManager,
  ) {}

  @ApiOperation({
    operationId: 'user_follow_approve',
    summary: 'Approve a follow request',
    description:
      'Approves a follow request for the current user.\n\n' +
      'Requirements:\n' +
      '- User must be authenticated\n' +
      '- Follow request must exist and belong to the user\n\n' +
      'Required Parameters:\n' +
      '- userFollowId: The unique identifier of the follow request',
  })
  @ApiNoContentResponse({ description: 'Follow request approved.' })
  @ApiParam({
    name: 'userFollowId',
    description: 'The unique identifier of the follow request',
    type: 'string',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiNotFoundResponse({
    description: 'Not Found. The follow request could not be found.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`userFollowId\`: Must be a valid UUID.
  `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Put(':userFollowId/approval')
  @HttpCode(204)
  async approve(
    @Request() request,
    @Param('userFollowId', new ParseUUIDPipe()) userFollowId: string,
  ): Promise<void> {
    const userFollow = await this.provider.getByFollowingAndId(
      request.user,
      userFollowId,
    );

    await this.manager.approve(userFollow);
  }

  @ApiOperation({
    operationId: 'user_follow_refuse',
    summary: 'Refuse a follow request',
    description:
      'Refuses a follow request for the current user.\n\n' +
      'Requirements:\n' +
      '- User must be authenticated\n' +
      '- Follow request must exist and belong to the user\n\n' +
      'Required Parameters:\n' +
      '- userFollowId: The unique identifier of the follow request',
  })
  @ApiNoContentResponse({ description: 'Follow request refused.' })
  @ApiParam({
    name: 'userFollowId',
    description: 'The unique identifier of the follow request',
    type: 'string',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiNotFoundResponse({
    description: 'Not Found. The follow request could not be found.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`userFollowId\`: Must be a valid UUID.
  `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Put(':userFollowId/refusal')
  @HttpCode(204)
  async refuse(
    @Request() request,
    @Param('userFollowId', new ParseUUIDPipe()) userFollowId: string,
  ): Promise<void> {
    const userFollow = await this.provider.getByFollowingAndId(
      request.user,
      userFollowId,
    );

    await this.manager.refuse(userFollow);
  }
}
