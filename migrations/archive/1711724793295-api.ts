import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1711724793295 implements MigrationInterface {
  name = 'Api1711724793295';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE "image_completion"
        ADD "queue" character varying DEFAULT NULL
    `);

    await queryRunner.query(`UPDATE "image_completion" SET queue = 'fast'`);

    await queryRunner.query(`
        ALTER TABLE "image_completion"
        ALTER COLUMN "queue" SET NOT NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "image_completion" DROP COLUMN "queue"
        `);
  }
}
