import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { UserCreatedEvent } from 'src/user/event/user-created.event';
import { UserProvider } from 'src/user/service/provider';
import { SubscriptionManager } from '../service/manager';

@Injectable()
export class UserCreatedListener {
  constructor(
    private subscriptionManager: SubscriptionManager,
    private userProvider: UserProvider,
  ) {}

  @OnEvent('user.created', { async: true })
  async handleUserCreatedEvent(event: UserCreatedEvent) {
    // const user = await this.userProvider.get(event.id);
    // this.subscriptionManager.createFreeSubscription(user);
  }
}
