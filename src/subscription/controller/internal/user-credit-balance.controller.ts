import { <PERSON>, Param, Post } from '@nestjs/common';
import { Body } from '@nestjs/common/decorators/http/route-params.decorator';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { UserCreditBalanceDto } from 'src/subscription/dto/user-credit-balance.dto';
import { UserCreditBalanceInternalRequest } from 'src/subscription/dto/user-credit-balance.internal-request';
import { UserCreditBalanceProvider } from 'src/subscription/service/user-credit-balance.provider';
import { UserCreditBalanceRequestManager } from 'src/subscription/service/user-credit-balance.request-manager';
import { UserCreditBalanceResponseMapper } from 'src/subscription/service/user-credit-balance.response-mapper';
import { UserProvider } from 'src/user/service/provider';
import { Raw } from 'typeorm';

@ApiTags('user credits / internal')
@Controller('internal/user-credit-balance')
export class InternalUserCreditBalanceController {
  constructor(
    private userProvider: UserProvider,
    private userCreditBalanceProvider: UserCreditBalanceProvider,
    private requestManager: UserCreditBalanceRequestManager,
    private responseMapper: UserCreditBalanceResponseMapper,
  ) {}

  @Post(':userId')
  @ApiOperation({
    operationId: 'internal_user_credit_balance_transaction_by_id',
    summary: 'Handle internal credit transaction by user ID',
    description:
      'Handles an internal credit transaction for a user by user ID. Intended for internal use only.' +
      '\n\n' +
      'Required Parameters: \n' +
      '- username: Username of the user\n' +
      '- organizationId: UUID of the organization\n' +
      '- creditType: Type of credit\n' +
      '- amount: Amount of credit\n' +
      '- expiresAt: Expiration date of the credit\n',
  })
  @ApiBody({ type: UserCreditBalanceInternalRequest })
  @ApiOkResponse({
    type: UserCreditBalanceDto,
    isArray: true,
    description: 'Updated list of user credit balances.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`userId\`: Must be a valid UUID.
      - \`creditType\`: Must be a valid credit type.
      - \`amount\`: Must be a valid number.
      - \`expiresAt\`: Must be a valid date.
  `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async handleInternalTransaction(
    @Body() requestBody: UserCreditBalanceInternalRequest,
    @Param() params,
  ): Promise<UserCreditBalanceDto[]> {
    const user = await this.userProvider.get(params.userId);

    await this.requestManager.handleInternalTransaction(requestBody, user);

    const now = new Date();

    const entities = await this.userCreditBalanceProvider.findBy(
      {
        user: { id: params.userId },
        expiresAt: Raw((alias) => `( ${alias} IS NULL OR ${alias} > :now )`, {
          now,
        }),
      },
      1,
      10,
    );

    return await this.responseMapper.mapMultiple(entities);
  }

  @Post()
  @ApiOperation({
    operationId: 'internal_user_credit_balance_transaction_by_username_or_org',
    summary: 'Handle internal credit transaction by username or organization',
    description:
      'Handles an internal credit transaction for a user by username or for an organization. Intended for internal use only.' +
      '\n\n' +
      'Required Parameters: \n' +
      '- username: Username of the user\n' +
      '- organizationId: UUID of the organization\n' +
      '- creditType: Type of credit\n' +
      '- amount: Amount of credit\n' +
      '- expiresAt: Expiration date of the credit\n',
  })
  @ApiBody({ type: UserCreditBalanceInternalRequest })
  @ApiOkResponse({
    type: UserCreditBalanceDto,
    isArray: true,
    description: 'Updated list of user or organization credit balances.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`username\`: Must be a valid username.
      - \`organizationId\`: Must be a valid UUID.
      - \`creditType\`: Must be a valid credit type.
      - \`amount\`: Must be a valid number.
      - \`expiresAt\`: Must be a valid date.
  `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async handleInternalTransactionByUsername(
    @Body() requestBody: UserCreditBalanceInternalRequest,
  ): Promise<UserCreditBalanceDto[]> {
    let entities;
    const now = new Date();

    if (requestBody.username) {
      const user = await this.userProvider.getBy({
        username: requestBody.username,
      });

      await this.requestManager.handleInternalTransaction(requestBody, user);

      entities = await this.userCreditBalanceProvider.findBy(
        {
          user: { id: user.id },
          expiresAt: Raw((alias) => `( ${alias} IS NULL OR ${alias} > :now )`, {
            now,
          }),
        },
        1,
        10,
      );
    } else if (requestBody.organizationId) {
      await this.requestManager.handleInternalTransaction(
        requestBody,
        null,
        requestBody.organizationId,
      );

      entities = await this.userCreditBalanceProvider.findBy(
        {
          organizationId: requestBody.organizationId,
          expiresAt: Raw((alias) => `( ${alias} IS NULL OR ${alias} > :now )`, {
            now,
          }),
        },
        1,
        10,
      );
    }

    return await this.responseMapper.mapMultiple(entities);
  }
}
