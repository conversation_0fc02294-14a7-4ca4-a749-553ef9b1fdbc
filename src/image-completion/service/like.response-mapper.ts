import { Injectable } from '@nestjs/common';
import { UserProvider } from 'src/user/service/provider';
import { ImageCompletionLikeDto } from '../dto/image-completion-like.dto';
import { ImageCompletionLikeEntity } from '../entity/image-completion-like.entity';
import { ImageCompletionResponseMapper } from './response-mapper';
import { LikesRankDto } from '../dto/likes-rank.dto';
import { UserResponseMapper } from 'src/user/service/response-mapper';

@Injectable()
export class ImageCompletionLikeResponseMapper {
  constructor(
    private userProvider: UserProvider,
    private imageCompletionResponseMapper: ImageCompletionResponseMapper,
    private userResponseMapper: UserResponseMapper,
  ) {}

  async mapMultiple(
    entities: ImageCompletionLikeEntity[],
    mapImages = false,
    userId: string = null,
  ): Promise<any> {
    const dtos = [];

    for (const entity of entities) {
      if (entity.imageCompletion) {
        dtos.push(await this.map(entity, mapImages, userId));
      }
    }

    return dtos;
  }

  async map(
    entity: ImageCompletionLikeEntity,
    mapImage = false,
    userId: string = null,
  ): Promise<ImageCompletionLikeDto> {
    const dto = new ImageCompletionLikeDto();

    const user = await this.userProvider.get(entity.userId);

    dto.id = entity.id;
    dto.userId = entity.userId;
    dto.username = user.username;

    if (mapImage) {
      dto.imageCompletion = await this.imageCompletionResponseMapper.map(
        entity.imageCompletion,
        true,
        true,
        userId,
      );
    }

    return dto;
  }

  async mapLikesRank(entity: any, mapRank = false) {
    const user = await this.userProvider.get(entity.user_id);

    const dto = new LikesRankDto();

    if (mapRank) {
      dto.rank = entity.rank;
    }

    dto.user = this.userResponseMapper.mapPublic(user);
    dto.userId = user.id;
    dto.likes = entity.likes_count;

    return dto;
  }

  async mapLikesRankMultiple(entities: any): Promise<LikesRankDto[]> {
    return Promise.all(
      entities.map((entity) => this.mapLikesRank(entity, true)),
    );
  }
}
