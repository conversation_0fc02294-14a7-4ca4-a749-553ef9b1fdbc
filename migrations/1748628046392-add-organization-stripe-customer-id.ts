import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddOrganizationStripeCustomerId1748628046392 implements MigrationInterface {
  name = 'AddOrganizationStripeCustomerId1748628046392';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "organization"
      ADD "stripe_customer_id" character varying
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "organization" DROP COLUMN "stripe_customer_id"
    `);
  }
}
