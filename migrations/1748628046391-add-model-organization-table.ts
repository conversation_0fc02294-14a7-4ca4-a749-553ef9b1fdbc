import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddModelOrganizationTable1748628046391 implements MigrationInterface {
  name = 'AddModelOrganizationTable1748628046391';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE "model_organization" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "model_id" uuid NOT NULL,
        "organization_id" uuid NOT NULL,
        "shared_by_user_id" uuid,
        "deleted_at" TIMESTAMP,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_model_organization" PRIMARY KEY ("id")
      )
    `);

    await queryRunner.query(`
      CREATE INDEX "idx_model_organization_model_id" ON "model_organization" ("model_id")
    `);

    await queryRunner.query(`
      CREATE INDEX "idx_model_organization_organization_id" ON "model_organization" ("organization_id")
    `);

    await queryRunner.query(`
      CREATE INDEX "idx_model_organization_shared_by_user_id" ON "model_organization" ("shared_by_user_id")
    `);

    await queryRunner.query(`
      ALTER TABLE "model_organization"
      ADD CONSTRAINT "FK_model_organization_model_id" 
      FOREIGN KEY ("model_id") REFERENCES "model"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "model_organization"
      ADD CONSTRAINT "FK_model_organization_organization_id" 
      FOREIGN KEY ("organization_id") REFERENCES "organization"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "model_organization"
      ADD CONSTRAINT "FK_model_organization_shared_by_user_id" 
      FOREIGN KEY ("shared_by_user_id") REFERENCES "user_account"("id") ON DELETE SET NULL ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      CREATE UNIQUE INDEX "idx_model_organization_unique" 
      ON "model_organization" ("model_id", "organization_id") 
      WHERE "deleted_at" IS NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DROP INDEX "idx_model_organization_unique"
    `);

    await queryRunner.query(`
      ALTER TABLE "model_organization" DROP CONSTRAINT "FK_model_organization_shared_by_user_id"
    `);

    await queryRunner.query(`
      ALTER TABLE "model_organization" DROP CONSTRAINT "FK_model_organization_organization_id"
    `);

    await queryRunner.query(`
      ALTER TABLE "model_organization" DROP CONSTRAINT "FK_model_organization_model_id"
    `);

    await queryRunner.query(`
      DROP INDEX "idx_model_organization_shared_by_user_id"
    `);

    await queryRunner.query(`
      DROP INDEX "idx_model_organization_organization_id"
    `);

    await queryRunner.query(`
      DROP INDEX "idx_model_organization_model_id"
    `);

    await queryRunner.query(`
      DROP TABLE "model_organization"
    `);
  }
}
