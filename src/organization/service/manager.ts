import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { OrganizationEntity } from '../entity/organization.entity';
import { UserEntity } from '../../user/entity/user.entity';
import { OrganizationUserEntity } from '../entity/organization-user.entity';

@Injectable()
export class OrganizationManager {
  constructor(
    @InjectRepository(OrganizationEntity)
    private organizationRepository: Repository<OrganizationEntity>,
    @InjectRepository(OrganizationUserEntity)
    private organizationUserRepository: Repository<OrganizationUserEntity>,
  ) {}

  async create(
    organization: OrganizationEntity,
    owner: UserEntity,
  ): Promise<OrganizationEntity> {
    await this.organizationRepository.save(organization);

    const organizationUser = new OrganizationUserEntity();
    organizationUser.user = owner;
    organizationUser.userId = owner.id;
    organizationUser.organization = organization;
    organizationUser.organizationId = organization.id;
    organizationUser.isOwner = true;
    organizationUser.isAdmin = true;

    await this.organizationUserRepository.save(organizationUser);

    return organization;
  }

  async update(organization: OrganizationEntity): Promise<OrganizationEntity> {
    return await this.organizationRepository.save(organization);
  }

  async delete(organization: OrganizationEntity): Promise<void> {
    await this.organizationRepository.softDelete(organization.id);
  }

  async updateSeats(
    organization: OrganizationEntity,
    seats: number,
  ): Promise<OrganizationEntity> {
    organization.seatsPurchased = seats;

    return await this.organizationRepository.save(organization);
  }
}
