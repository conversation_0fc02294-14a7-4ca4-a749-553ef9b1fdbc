import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1692140959001 implements MigrationInterface {
    name = 'Api1692140959001'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "signup_code" character varying
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "signup_code"
        `);
    }

}
