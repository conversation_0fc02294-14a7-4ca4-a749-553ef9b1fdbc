import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1746558948065 implements MigrationInterface {
  name = 'Api1746558948065';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "credit_package"
            ADD "seats" integer
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "credit_package" DROP COLUMN "seats"
        `);
  }
}
