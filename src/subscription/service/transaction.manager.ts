import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { Repository } from 'typeorm';
import { CreditTypeEnum } from '../entity/credit-type.enum';
import { TransactionTypeEnum } from '../entity/transaction-type.enum';
import { TransactionEntity } from '../entity/transaction.entity';
import { UserCreditBalanceManager } from './user-credit-balance.manager';

@Injectable()
export class TransactionManager {
  constructor(
    @InjectRepository(TransactionEntity)
    private repository: Repository<TransactionEntity>,
    private userCreditBalanceManager: UserCreditBalanceManager,
    private logger: Logger,
  ) {}

  async create(entity: TransactionEntity): Promise<TransactionEntity> {
    await this.repository.save(entity);

    return entity;
  }

  async register(
    type: TransactionTypeEnum,
    price: number,
    description: string,
    reference: string,
    updateBalance = true,
    userId?: string,
    organizationId?: string,
    context?: any,
  ) {
    const transaction = await this.getOrCreate(
      type,
      description,
      reference,
      userId,
      organizationId,
      context,
    );

    const previousAmount = transaction.amount;
    transaction.amount = price;

    this.logger.log('transaction.register', {
      type,
      price,
      description,
      reference,
      updateBalance,
      userId,
      organizationId,
    });

    await this.create(transaction);

    if (!updateBalance || previousAmount === price) {
      return transaction;
    }

    if (transaction.type === TransactionTypeEnum.TOP_UP) {
      if (previousAmount) {
        await this.userCreditBalanceManager.deduct(
          description.toLowerCase() as CreditTypeEnum,
          previousAmount,
          userId,
          organizationId,
        );
      }

      await this.userCreditBalanceManager.increase(
        description.toLowerCase() as CreditTypeEnum,
        transaction.amount,
        userId,
        organizationId,
      );
    }

    if (transaction.type === TransactionTypeEnum.SPENDING) {
      if (previousAmount) {
        await this.userCreditBalanceManager.increase(
          description.toLowerCase() as CreditTypeEnum,
          previousAmount,
          userId,
          organizationId,
        );
      }

      await this.userCreditBalanceManager.deduct(
        description.toLowerCase() as CreditTypeEnum,
        transaction.amount,
        userId,
        organizationId,
      );
    }

    return transaction;
  }

  async getOrCreate(
    type: TransactionTypeEnum,
    description: string,
    reference: string,
    userId?: string,
    organizationId?: string,
    context?: any,
  ): Promise<TransactionEntity> {
    let transaction = await this.repository.findOne({
      where: {
        type,
        description,
        reference,
        userId,
        organizationId,
      },
    });

    if (!transaction) {
      transaction = new TransactionEntity();

      transaction.type = type;
      transaction.description = description;
      transaction.reference = reference;
      transaction.context = context;
      transaction.userId = userId;
      transaction.organizationId = organizationId;
    }

    return transaction;
  }
}
