import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { ImageDeletedEvent } from 'src/image-completion/event/image-deleted.event';
import { BookmarkProvider } from '../service/bookmark.provider';
import { BookmarkRequestManager } from '../service/bookmark.request-manager';

@Injectable()
export class ImageDeletedListener {
  constructor(
    private bookmarkProvider: BookmarkProvider,
    private bookmarkRequestManager: BookmarkRequestManager,
  ) {}

  @OnEvent('image.deleted', { async: true })
  async handleImageDeletedEvent(event: ImageDeletedEvent) {
    if ((await this.bookmarkProvider.countBy({ referenceId: event.id })) > 0) {
      const entities = await this.bookmarkProvider.findBy(
        {
          referenceId: event.id,
        },
        1,
        999,
      );
      for (const entity of entities) {
        await this.bookmarkRequestManager.delete(
          entity.referenceId,
          entity.userId,
        );
      }
    }
  }
}
