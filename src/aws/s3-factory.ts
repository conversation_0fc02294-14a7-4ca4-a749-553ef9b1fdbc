import { S3Client, S3ClientConfig } from '@aws-sdk/client-s3';
import { fromEnv } from '@aws-sdk/credential-provider-env';
import { defaultProvider } from '@aws-sdk/credential-provider-node';
import { Provider } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export const s3Factory: Provider = {
  provide: 'S3',
  useFactory: (configService: ConfigService) => {
    const awsAccessKeyId = configService.get<string>('AWS_ACCESS_KEY_ID');
    const awsSecretAccessKey = configService.get<string>(
      'AWS_SECRET_ACCESS_KEY',
    );
    const localstackUrl = configService.get<string>('LOCALSTACK_S3_URL');

    let s3config: S3ClientConfig = {
      region: configService.get<string>('AWS_REGION'),
      credentials: defaultProvider(),
    };

    if (awsAccessKeyId && awsSecretAccessKey) {
      s3config.credentials = fromEnv();
    } else if (localstackUrl) {
      s3config = {
        ...s3config,
        forcePathStyle: true,
        endpoint: localstackUrl,
        credentials: {
          accessKeyId: 'dummy',
          secretAccessKey: 'dummy',
        },
      };
    }

    return new S3Client(s3config);
  },
  inject: [ConfigService],
};
