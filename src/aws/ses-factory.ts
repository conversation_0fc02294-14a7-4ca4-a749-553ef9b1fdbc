import { SES } from '@aws-sdk/client-ses';
import { fromEnv } from '@aws-sdk/credential-provider-env';
import { defaultProvider } from '@aws-sdk/credential-provider-node';
import { Provider } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export const sesFactory: Provider = {
  provide: 'SES',
  useFactory: (configService: ConfigService) => {
    const awsAccessKeyId = configService.get<string>('AWS_ACCESS_KEY_ID');
    const awsSecretAccessKey = configService.get<string>(
      'AWS_SECRET_ACCESS_KEY',
    );

    const sesConfig = {
      apiVersion: '2010-12-01',
      region: configService.get<string>('AWS_REGION'),
      credentials: defaultProvider(),
    };

    if (awsAccessKeyId && awsSecretAccessKey) {
      sesConfig.credentials = fromEnv();
    }

    return new SES(sesConfig);
  },
  inject: [ConfigService],
};
