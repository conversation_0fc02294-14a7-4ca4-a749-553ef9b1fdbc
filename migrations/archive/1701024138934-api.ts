import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1701024138934 implements MigrationInterface {
    name = 'Api1701024138934'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "model"
            ADD "class" character varying
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "model" DROP COLUMN "class"
        `);
    }

}
