import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { UserEntity } from './user.entity';

@Entity('user_follow')
export class UserFollowEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;
  
  @ManyToOne(() => UserEntity, { eager: true })
  follower: UserEntity;

  @Column({ type: 'uuid', nullable: true })
  @Index()
  followerId: string;

  @ManyToOne(() => UserEntity, { eager: true })
  following: UserEntity;

  @Column({ type: 'uuid', nullable: true })
  @Index()
  followingId: string;

  @Column({ type: 'boolean', nullable: true })
  isApproved?: boolean = null;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt?: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
