import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1691951006408 implements MigrationInterface {
    name = 'Api1691951006408'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TYPE "public"."model_status_enum"
            RENAME TO "model_status_enum_old"
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."model_status_enum" AS ENUM(
                'new',
                'pending',
                'training',
                'finished',
                'available',
                'failed'
            )
        `);
        await queryRunner.query(`
            ALTER TABLE "model"
            ALTER COLUMN "status" DROP DEFAULT
        `);
        await queryRunner.query(`
            ALTER TABLE "model"
            ALTER COLUMN "status" TYPE "public"."model_status_enum" USING "status"::"text"::"public"."model_status_enum"
        `);
        await queryRunner.query(`
            ALTER TABLE "model"
            ALTER COLUMN "status"
            SET DEFAULT 'new'
        `);
        await queryRunner.query(`
            DROP TYPE "public"."model_status_enum_old"
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TYPE "public"."model_status_enum_old" AS ENUM('new', 'pending', 'training', 'ready', 'failed')
        `);
        await queryRunner.query(`
            ALTER TABLE "model"
            ALTER COLUMN "status" DROP DEFAULT
        `);
        await queryRunner.query(`
            ALTER TABLE "model"
            ALTER COLUMN "status" TYPE "public"."model_status_enum_old" USING "status"::"text"::"public"."model_status_enum_old"
        `);
        await queryRunner.query(`
            ALTER TABLE "model"
            ALTER COLUMN "status"
            SET DEFAULT 'new'
        `);
        await queryRunner.query(`
            DROP TYPE "public"."model_status_enum"
        `);
        await queryRunner.query(`
            ALTER TYPE "public"."model_status_enum_old"
            RENAME TO "model_status_enum"
        `);
    }

}
