import { ApiProperty } from '@nestjs/swagger';
import { IsUUID, IsNotEmpty, IsOptional, IsBoolean, IsString } from 'class-validator';

export class SubscriptionUpgradeRequest {
  @ApiProperty({
    description: 'ID of the target credit package to upgrade to',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  @IsUUID()
  @IsNotEmpty()
  targetCreditPackageId: string;

  @ApiProperty({
    description: 'Whether to confirm the upgrade with proration charges',
    example: true,
    required: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  confirmProration?: boolean = false;

  @ApiProperty({
    description: 'Optional voucher/discount code to apply to the upgrade',
    example: 'SAVE20',
    required: false,
  })
  @IsOptional()
  @IsString()
  voucherCode?: string;
}
