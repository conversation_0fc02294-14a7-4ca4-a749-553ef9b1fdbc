import { DateTime } from 'luxon';
import {
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    Index,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { UserEntity } from '../../user/entity/user.entity';
import { CreditPackageEntity } from './credit-package.entity';
import { SubscriptionStatusEnum } from './subscription-status.enum';

@Entity('subscription')
export class SubscriptionEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => UserEntity, { eager: true })
  user: UserEntity;

  @Column({ type: 'uuid', nullable: false })
  @Index()
  userId: string;

  @Column({ type: 'uuid', nullable: true })
  previousSubscriptionId?: string;

  @Column()
  name: string;

  @Column()
  status: SubscriptionStatusEnum;

  @Column()
  price: number;

  @Column({ type: 'timestamp', nullable: true })
  paidAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  renewedAt?: Date;

  @Column({ type: 'timestamp', nullable: true })
  expiresAt?: Date;

  @ManyToOne(() => CreditPackageEntity, { eager: true })
  creditPackage: CreditPackageEntity;

  @Column({ type: 'uuid' })
  @Index()
  creditPackageId: string;

  @Column({ type: 'text', nullable: true })
  externalReference?: string;

  @Column({ nullable: true })
  stripeCheckoutSessionId?: string;

  @Column({ type: 'text', nullable: true })
  stripeCheckoutUrl?: string;

  @Column({ nullable: true })
  stripeLatestInvoice?: string;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt?: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  public getExpiresAt(): DateTime | null {
    return this.expiresAt ? DateTime.fromJSDate(this.expiresAt) : null;
  }
}
