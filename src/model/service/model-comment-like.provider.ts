import { Injectable } from '@nestjs/common';
import { AbstractProvider } from 'src/core/service/abstract.provider';
import { ModelCommentLikeEntity } from '../entity/model-comment-like.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, FindOneOptions, Repository } from 'typeorm';
import { Logger } from 'nestjs-pino';

@Injectable()
export class ModelCommentLikeProvider extends AbstractProvider<ModelCommentLikeEntity> {
  constructor(
    @InjectRepository(ModelCommentLikeEntity)
    repository: Repository<ModelCommentLikeEntity>,
    logger: Logger,
  ) {
    super(repository, logger);
  }

  prepareFindOneOptions(criteria: any): FindOneOptions {
    return {
      where: criteria,
    };
  }

  prepareFindManyOptions(
    criteria: any,
    page: number,
    limit: number,
    sortBy?: string,
    sortOrder = 'ASC',
  ): FindManyOptions<ModelCommentLikeEntity> {
    if (criteria.username) {
      criteria.user = {
        username: criteria.username,
      };

      delete criteria.username;
    }

    const parentOptions = super.prepareFindManyOptions(
      criteria,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    return {
      ...parentOptions,
      relations: {
        user: true,
      },
    };
  }
}
