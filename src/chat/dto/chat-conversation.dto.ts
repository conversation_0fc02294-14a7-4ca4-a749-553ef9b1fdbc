import { ApiProperty } from '@nestjs/swagger';

export class ChatConversationDto {
  @ApiProperty({ description: 'Unique identifier for the conversation' })
  id: string;

  @ApiProperty({ description: 'Title of the conversation' })
  title: string;

  @ApiProperty({ description: 'User ID who owns the conversation' })
  userId: string;

  @ApiProperty({ description: 'Creation date of the conversation' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update date of the conversation' })
  updatedAt: Date;
}
