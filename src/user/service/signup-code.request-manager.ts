import { Injectable } from '@nestjs/common';
import { SignupCodeRequest } from '../dto/signup-code.request';
import { SignupCodeEntity } from '../entity/signup-code.entity';
import { SignupCodeManager } from './signup-code.manager';

@Injectable()
export class SignupCodeRequestManager {
  constructor(private manager: SignupCodeManager) {}

  async create(request: SignupCodeRequest): Promise<SignupCodeEntity> {
    const signupCode = new SignupCodeEntity();

    this.mapRequestData(signupCode, request);

    signupCode.code =
      this.generateRandomSignupCode(4) + '-' + this.generateRandomSignupCode(4);
    signupCode.features = {
      RECIPIENT_FREE_IMAGE: 240,
      RECIPIENT_FREE_MODEL: 0,
      REFERER_FREE_IMAGE: 240,
      REFERER_FREE_MODEL: 0,
    };

    await this.manager.create(signupCode);

    return signupCode;
  }

  private generateRandomSignupCode(length: number): string {
    const characters =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let code = '';

    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * characters.length);
      code += characters.charAt(randomIndex);
    }

    return code.toUpperCase();
  }

  update(
    signupCode: SignupCodeEntity,
    request: SignupCodeRequest,
  ): Promise<SignupCodeEntity> {
    this.mapRequestData(signupCode, request);

    return this.manager.update(signupCode);
  }

  mapRequestData(
    signupCode: SignupCodeEntity,
    request: SignupCodeRequest,
  ): void {
    signupCode.userId = request.userId ?? signupCode.userId;
    signupCode.notes = request.notes ?? signupCode.notes;
  }
}
