import {
  Controller,
  Delete,
  ForbiddenException,
  Get,
  HttpCode,
  ParseUUIDPipe,
  Put,
  Request,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  Param,
  Query,
  Res,
} from '@nestjs/common/decorators/http/route-params.decorator';
import {
  ApiBadRequestResponse,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiOperation,
  ApiForbiddenResponse,
  ApiUnauthorizedResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import { BaseFindResponseHeadersDto } from 'src/core/dto/base-find-response-headers.dto';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { ImageCompletionDto } from 'src/image-completion/dto/image-completion.dto';
import { ImageCompletionSearchRequest } from 'src/image-completion/dto/image-completion.search-request';
import { ImageCompletionProvider } from 'src/image-completion/service/provider';
import { ImageCompletionResponseMapper } from 'src/image-completion/service/response-mapper';
import { PublicBoardDto } from '../dto/public.board.dto';
import { VisibilityEnum } from '../enum/board-visibility.enum';
import { BoardImageCompletionManager } from '../service/board-image-completion.manager';
import { BoardImageCompletionProvider } from '../service/board-image-completion.provider';
import { BoardImageCompletionRequestManager } from '../service/board-image-completion.request-manager';
import { BoardUserProvider } from '../service/board-user.provider';
import { BoardProvider } from '../service/provider';
import { BoardRequestManager } from '../service/request-manager';
import { BoardResponseMapper } from '../service/response-mapper';

@ApiTags('board')
@Controller()
export class ImageController {
  constructor(
    private readonly boardProvider: BoardProvider,
    private readonly boardRequestManager: BoardRequestManager,
    private readonly boardUserProvider: BoardUserProvider,
    private readonly imageCompletionProvider: ImageCompletionProvider,
    private readonly imageCompletionResponseMapper: ImageCompletionResponseMapper,
    private readonly boardImageCompletionManager: BoardImageCompletionManager,
    private readonly boardImageCompletionProvider: BoardImageCompletionProvider,
    private readonly boardImageCompletionRequestManager: BoardImageCompletionRequestManager,
    private readonly boardResponseMapper: BoardResponseMapper,
  ) {}

  @Get('boards/:boardId/images')
  @ApiOperation({
    operationId: 'board_image_list',
    summary: 'List images for a board',
    description:
      'Retrieves a paginated list of images for the specified board.\n\n' +
      'Required Parameters:\n' +
      '- boardId: UUID of the board\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of images per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- userId: UUID of the user\n' +
      '- username: Username of the user\n' +
      '- prompt: Prompt of the image\n' +
      '- privacy: Privacy of the image\n' +
      '- status: Status of the image\n' +
      '- systemVersion: System version of the image\n' +
      '- isHot: Whether the image is hot\n' +
      '- includeNsfw: Whether the image is NSFW\n' +
      '- includeFollowing: Whether the image is from a followed user\n' +
      '- modelIds[]: IDs of the models\n' +
      '- editedImages: Whether the image is an edited image\n' +
      '- day: Whether the image was created today\n' +
      '- week: Whether the image was created this week\n' +
      '- month: Whether the image was created this month\n' +
      '- year: Whether the image was created this year\n',
  })
  @ApiOkResponse({
    type: ImageCompletionDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated list of images for the board.',
  })
  @ApiQuery({
    type: ImageCompletionSearchRequest,
    description: 'Query parameters for searching and paginating images.',
  })
  @ApiParam({
    name: 'boardId',
    description: 'UUID of the board to retrieve images for.',
    type: String,
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - Filter parameters must be valid values.
      - \`boardId\`: Must be a valid UUID.
      - \`userId\`: Must be a valid UUID.
      - \`username\`: Must be a valid username.
      - \`prompt\`: Must be a valid string.
      - \`privacy\`: Must be a valid value from the \`PrivacyEnum\`.
      - \`status\`: Must be a valid value from the \`StatusEnum\`.
      - \`systemVersion\`: Must be a valid system version.
      - \`isHot\`: Must be a valid boolean value.
      - \`includeNsfw\`: Must be a valid boolean value.
      - \`includeFollowing\`: Must be a valid boolean value.
      - \`modelIds\`: Must be a valid array of UUIDs.
      - \`editedImages\`: Must be a valid boolean value.
      - \`day\`: Must be a valid boolean value.
      - \`week\`: Must be a valid boolean value.
      - \`month\`: Must be a valid boolean value.
      - \`year\`: Must be a valid boolean value.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User does not have access to this board.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @UsePipes(new ValidationPipe())
  async findImages(
    @Request() request,
    @Query() query: ImageCompletionSearchRequest,
    @Res() res: Response,
    @Param('boardId', new ParseUUIDPipe()) boardId: string,
  ): Promise<void> {
    const board = await this.boardProvider.get(boardId);

    if (!(await this.boardUserProvider.isMember(request.user.id, board.id))) {
      if (board.visibility != VisibilityEnum.PUBLIC) {
        throw new ForbiddenException();
      }
    }

    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;
    const filters =
      await this.boardImageCompletionRequestManager.sanitizeSearchFilters(
        inputFilters,
        request.user,
        board,
      );

    const entities = await this.imageCompletionProvider.findBy(
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    const totalCount = await this.imageCompletionProvider.countBy(filters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(
      await this.imageCompletionResponseMapper.mapMultiple(
        entities,
        request.user?.id,
      ),
    );
  }

  @Put('boards/:boardId/thumbnail/:imageCompletionId')
  @ApiOperation({
    operationId: 'board_set_thumbnail',
    summary: 'Set board thumbnail image',
    description:
      'Sets the thumbnail image for the specified board.\n\n' +
      'Required Parameters:\n' +
      '- boardId: UUID of the board\n' +
      '- imageCompletionId: UUID of the image to set as thumbnail\n',
  })
  @ApiParam({
    name: 'boardId',
    description: 'UUID of the board.',
    type: String,
  })
  @ApiParam({
    name: 'imageCompletionId',
    description: 'UUID of the image to set as thumbnail.',
    type: String,
  })
  @ApiNoContentResponse({ description: 'Thumbnail set successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`boardId\`: Must be a valid UUID.
      - \`imageCompletionId\`: Must be a valid UUID.
      - Image completion does not exist.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. Only managers can set the thumbnail.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @HttpCode(204)
  async setThumbnail(
    @Request() request,
    @Param('boardId', new ParseUUIDPipe()) boardId: string,
    @Param('imageCompletionId', new ParseUUIDPipe()) imageCompletionId: string,
  ): Promise<void> {
    const board = await this.boardProvider.get(boardId);

    if (!(await this.boardUserProvider.isManager(request.user.id, board.id))) {
      throw new ForbiddenException();
    }

    const imageCompletion = await this.imageCompletionProvider.getBy({
      id: imageCompletionId,
    });

    await this.boardRequestManager.updateThumbnail(board, imageCompletion);
  }

  @Put('boards/:boardId/images/:imageCompletionId')
  @ApiOperation({
    operationId: 'board_link_image',
    summary: 'Link image to board',
    description:
      'Links an image to the specified board.\n\n' +
      'Required Parameters:\n' +
      '- boardId: UUID of the board\n' +
      '- imageCompletionId: UUID of the image to link\n',
  })
  @ApiParam({
    name: 'boardId',
    description: 'UUID of the board.',
    type: String,
  })
  @ApiParam({
    name: 'imageCompletionId',
    description: 'UUID of the image to link.',
    type: String,
  })
  @ApiNoContentResponse({ description: 'Image linked successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`boardId\`: Must be a valid UUID.
      - \`imageCompletionId\`: Must be a valid UUID.
      - Image completion does not exist.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. Only members can link images.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @HttpCode(204)
  async linkImage(
    @Request() request,
    @Param('boardId', new ParseUUIDPipe()) boardId: string,
    @Param('imageCompletionId', new ParseUUIDPipe()) imageCompletionId: string,
  ): Promise<void> {
    const board = await this.boardProvider.get(boardId);

    if (!(await this.boardUserProvider.isMember(request.user.id, board.id))) {
      throw new ForbiddenException();
    }

    const imageCompletion = await this.imageCompletionProvider.getBy({
      userId: request.user.id,
      id: imageCompletionId,
    });

    await this.boardImageCompletionManager.linkImage(
      board.id,
      imageCompletion.id,
    );
  }

  @Delete('boards/:boardId/images/:imageCompletionId')
  @ApiOperation({
    operationId: 'board_unlink_image',
    summary: 'Unlink image from board',
    description:
      'Removes the link between an image and the specified board.\n\n' +
      'Required Parameters:\n' +
      '- boardId: UUID of the board\n' +
      '- imageCompletionId: UUID of the image to unlink\n',
  })
  @ApiParam({
    name: 'boardId',
    description: 'UUID of the board.',
    type: String,
  })
  @ApiParam({
    name: 'imageCompletionId',
    description: 'UUID of the image to unlink.',
    type: String,
  })
  @ApiNoContentResponse({ description: 'Image unlinked successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`boardId\`: Must be a valid UUID.
      - \`imageCompletionId\`: Must be a valid UUID.
      - Image completion does not exist.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. Only members can unlink images.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async unlinkImage(
    @Request() request,
    @Param('boardId', new ParseUUIDPipe()) boardId: string,
    @Param('imageCompletionId', new ParseUUIDPipe()) imageCompletionId: string,
  ): Promise<void> {
    const board = await this.boardProvider.get(boardId);

    if (!(await this.boardUserProvider.isMember(request.user.id, board.id))) {
      throw new ForbiddenException();
    }

    const imageCompletion = await this.imageCompletionProvider.getBy({
      userId: request.user.id,
      id: imageCompletionId,
    });

    await this.boardImageCompletionManager.unlinkImage(
      board.id,
      imageCompletion.id,
    );
  }

  @Get('image_completions/:imageCompletionId/boards')
  @ApiOperation({
    operationId: 'image_completion_boards',
    summary: 'List boards for an image',
    description:
      'Retrieves all boards that the specified image is linked to.\n\n' +
      'Required Parameters:\n' +
      '- imageCompletionId: UUID of the image\n',
  })
  @ApiParam({
    name: 'imageCompletionId',
    description: 'UUID of the image.',
    type: String,
  })
  @ApiOkResponse({
    type: PublicBoardDto,
    isArray: true,
    description: 'List of boards the image is linked to.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`imageCompletionId\`: Must be a valid UUID.
      - Image completion does not exist.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User does not have access to this image.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async getBoardsByImage(
    @Request() request,
    @Param('imageCompletionId', new ParseUUIDPipe()) imageCompletionId: string,
  ): Promise<PublicBoardDto[]> {
    const imageCompletion = await this.imageCompletionProvider.getBy({
      id: imageCompletionId,
      user: { id: request.user.id },
    });

    const boardImageCompletions =
      await this.boardImageCompletionProvider.findBoardsByImage(
        imageCompletion.id,
      );

    return this.boardResponseMapper.mapMultiplePublic(
      boardImageCompletions,
      request.user.id,
    );
  }
}
