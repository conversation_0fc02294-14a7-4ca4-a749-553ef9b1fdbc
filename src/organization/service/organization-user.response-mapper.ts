import { Injectable } from '@nestjs/common';
import { UserResponseMapper } from 'src/user/service/response-mapper';
import { UserProvider } from 'src/user/service/provider';
import { OrganizationResponseMapper } from './response-mapper';
import { OrganizationProvider } from './provider';
import { OrganizationUserEntity } from '../entity/organization-user.entity';
import { OrganizationUserDto } from '../dto/organization-user.dto';

@Injectable()
export class OrganizationUserResponseMapper {
  constructor(
    private userResponseMapper: UserResponseMapper,
    private userProvider: UserProvider,
    private organizationResponseMapper: OrganizationResponseMapper,
    private organizationProvider: OrganizationProvider,
  ) {}

  async map(
    organizationUser: OrganizationUserEntity,
  ): Promise<OrganizationUserDto> {
    const organization =
      organizationUser.organization ??
      (await this.organizationProvider.get(organizationUser.organizationId));

    const dto = {
      id: organization.id,
      organization: await this.organizationResponseMapper.mapPublic(
        organization,
      ),
      isAdmin: organizationUser.isAdmin,
      user: this.userResponseMapper.mapPublic(organizationUser.user),
    } as Partial<OrganizationUserDto>;

    return dto as OrganizationUserDto;
  }

  async mapMultiple(
    organizationUserEntities: OrganizationUserEntity[],
  ): Promise<OrganizationUserDto[]> {
    return Promise.all(
      organizationUserEntities.map((organizationUserEntity) =>
        this.map(organizationUserEntity),
      ),
    );
  }
}
