import {
  Controller,
  Delete,
  HttpCode,
  Param,
  ParseUUI<PERSON>ipe,
  Request,
} from '@nestjs/common';
import {
  ApiNoContentResponse,
  ApiParam,
  ApiTags,
  ApiOperation,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { ImageEditRequestManager } from '../service/request-manager';

@ApiTags('image-edit')
@Controller('image-edits')
export class DeleteController {
  constructor(private readonly requestManager: ImageEditRequestManager) {}

  @Delete(':id')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'image_edit_delete',
    summary: 'Delete image edit',
    description:
      'Deletes the specified image edit for the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- id: ID of the image edit to delete\n',
  })
  @ApiNoContentResponse({ description: 'Image edit deleted successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - Image edit does not exist.
   `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description:
      'Forbidden. User does not have permission to delete this image edit.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiParam({ name: 'id', description: 'ID of the image edit to delete.' })
  async delete(
    @Param('id', new ParseUUIDPipe()) id: string,
    @Request() request,
  ) {
    await this.requestManager.delete(id, request.user);
  }
}
