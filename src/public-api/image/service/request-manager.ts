import {
    BadRequestException,
    Injectable,
    UnauthorizedException,
} from '@nestjs/common';
import { Logger } from 'nestjs-pino';
import { ImageCompletionSearchRequest } from 'src/image-completion/dto/image-completion.search-request';
import { ImageCompletionModelEntity } from 'src/image-completion/entity/image-completion-model.entity';
import {
    ImageCompletionEntity,
    PrivacyEnum as ImageCompletionPrivacyEnum,
    QueueEnum,
    StatusEnum,
} from 'src/image-completion/entity/image-completion.entity';
import { ImageCompletionManager } from 'src/image-completion/service/manager';
import {
    ModelEntity,
    PrivacyEnum as ModelPrivacyEnum,
} from 'src/model/entity/model.entity';
import { StatusEnum as ModelStatusEnum } from 'src/model/enum/status.enum';
import { ModelProvider } from 'src/model/service/provider';
import { OrganizationUserProvider } from 'src/organization/service/organization-user.provider';
import { PaymentRequiredException } from 'src/subscription/exception/payment-required.exception';
import { UserEntity } from 'src/user/entity/user.entity';
import { ImageCompletionRequestManager as PrivateImageCompletionRequestManager } from '../../../image-completion/service/request-manager';
import { ImagePrivacyRequest } from '../dto/image.privacy-request';
import { ImagePromptPrivacyRequest } from '../dto/image.prompt-privacy-request';
import { ImageRequest } from '../dto/image.request';
import { ImageSearchRequest } from '../dto/image.search-request';

@Injectable()
export class ImageRequestManager {
  constructor(
    private manager: ImageCompletionManager,
    private privateRequestManager: PrivateImageCompletionRequestManager,
    private modelProvider: ModelProvider,
    private organizationUserProvider: OrganizationUserProvider,
    private logger: Logger,
  ) {}

  async create(
    request: ImageRequest,
    user: UserEntity,
  ): Promise<ImageCompletionEntity> {
    // Validate organization membership if organizationId is provided
    if (request.organizationId) {
      const isMember = await this.organizationUserProvider.isMember(
        user.id,
        request.organizationId,
      );
      if (!isMember) {
        throw new UnauthorizedException(
          'User is not a member of the specified organization',
        );
      }
    }

    const entity = new ImageCompletionEntity();

    const modelNames = this.extractModelNamesFromPrompt(request.prompt);
    if (modelNames.length > 0) {
      const models = await this.modelProvider.findByNames(modelNames, user);

      // Check if all requested models were found (findByNames already handles access control)
      if (models.length < modelNames.length) {
        const foundModelNames = models.map(m => m.name);
        const missingModels = modelNames.filter(name => !foundModelNames.includes(name));
        throw new UnauthorizedException(
          `You do not have permission to view the model @${missingModels[0]}.`,
        );
      }

      models.forEach((model) => {
        if (model.status !== ModelStatusEnum.AVAILABLE) {
          throw new BadRequestException(
            `Model "${model.name}" is not available for image completion.`,
          );
        }
      });

      entity.models = models.map((model) => {
        const imageCompletionModel = new ImageCompletionModelEntity();
        imageCompletionModel.model = model;
        imageCompletionModel.modelId = model.id;

        return imageCompletionModel;
      });

      entity.hasPrivateModel = this.containsPrivateModel(models);
    }

    entity.privacy = ImageCompletionPrivacyEnum.PRIVATE;
    entity.user = user;
    entity.prompt = request.prompt;
    entity.hasWatermark = request.hasWatermark;
    entity.queue = QueueEnum.SLOW;
    entity.hideFromUserProfile = request.hideFromUserProfile;
    entity.systemVersion =
      'systemVersion' in request ? request.systemVersion : user.systemVersion;
    entity.webhookUrl = request.webhookUrl ?? entity.webhookUrl;
    entity.generationSettings = {
      width: request.width,
      height: request.height,
      creativity: request.creativity,
      quality: request.quality,
      mode: request.mode,
    };

    if (user.hidePrompt) {
      entity.hidePrompt = user.hidePrompt;
    }

    // Set organizationId if provided for organization credit deduction
    if (request.organizationId) {
      entity.organizationId = request.organizationId;
    }

    try {
      return await this.manager.create(entity);
    } catch (e) {
      if ('user_credit_balance.not_found' === e.message) {
        throw new PaymentRequiredException();
      }

      throw new BadRequestException(e.message);
    }
  }

  async interrupt(entity: ImageCompletionEntity) {
    if (
      entity.status != StatusEnum.GENERATING &&
      entity.status != StatusEnum.NEW
    ) {
      throw new BadRequestException('Image is not being generated');
    }

    entity.status = StatusEnum.INTERRUPTED;

    try {
      await this.manager.update(entity);
    } catch (e) {
      if ('user_credit_balance.not_found' === e.message) {
        throw new PaymentRequiredException();
      }

      this.logger.error('Error interrupting image generation', {
        imageCompletion: entity,
        error: e.message,
      });

      throw new BadRequestException(e.message);
    }
  }

  async updatePromptPrivacy(
    entity: ImageCompletionEntity,
    request: ImagePromptPrivacyRequest,
  ): Promise<ImageCompletionEntity> {
    entity.hidePrompt = request.hidePrompt;
    return await this.manager.update(entity);
  }

  async updatePrivacy(
    entity: ImageCompletionEntity,
    request: ImagePrivacyRequest,
  ): Promise<ImageCompletionEntity> {
    if (entity.status != StatusEnum.READY) {
      throw new BadRequestException('Image not ready for publication');
    }

    if (entity.isNsfw || entity.hasPrivateModel) {
      throw new BadRequestException('Image publication not allowed');
    }

    entity.privacy = request.privacy;

    return await this.manager.update(entity);
  }

  extractModelNamesFromPrompt(prompt: string): string[] {
    return (prompt.match(/@(\w+)/g) || []).map((name) => name.slice(1));
  }



  containsPrivateModel(models: ModelEntity[]): boolean {
    const privacyLevels = models.map((model) => model.privacy);

    return (
      privacyLevels.includes(ModelPrivacyEnum.LICENSED) ||
      privacyLevels.includes(ModelPrivacyEnum.PRIVATE)
    );
  }

  extractHighestPrivacy(models: ModelEntity[]): string {
    const privacyLevels = models.map((model) => model.privacy);

    if (privacyLevels.includes(ModelPrivacyEnum.LICENSED)) {
      return ModelPrivacyEnum.LICENSED;
    }

    if (privacyLevels.includes(ModelPrivacyEnum.PRIVATE)) {
      return ModelPrivacyEnum.PRIVATE;
    }

    return ModelPrivacyEnum.PUBLIC;
  }

  async sanitizeSearchFilters(
    filters: ImageSearchRequest,
    currentUser: UserEntity,
  ): Promise<any> {
    return await this.privateRequestManager.sanitizeSearchFilters(
      { ...filters } as unknown as ImageCompletionSearchRequest,
      currentUser,
    );
  }
}
