import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1703706948732 implements MigrationInterface {
  name = 'Api1703706948732';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "images_generated" integer DEFAULT '0'
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "tutorial_steps" json
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "tutorial_steps"
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "images_generated"
        `);
  }
}
