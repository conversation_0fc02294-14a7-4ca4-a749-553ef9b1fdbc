import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1694380058648 implements MigrationInterface {
    name = 'Api1694380058648'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "image_completion"
            ADD "generation_data" text
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "image_completion" DROP COLUMN "generation_data"
        `);
    }

}
