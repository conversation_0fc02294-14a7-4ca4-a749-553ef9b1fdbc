import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1734719920903 implements MigrationInterface {
  name = 'Api1734719920903';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "user_credit_balance"
            ADD "organization_id" uuid
        `);
    await queryRunner.query(`
            ALTER TABLE "user_credit_balance" DROP CONSTRAINT "FK_a73c0a8540dbc42c293caa0f25b"
        `);
    await queryRunner.query(`
            ALTER TABLE "user_credit_balance"
            ALTER COLUMN "user_id" DROP NOT NULL
        `);
    await queryRunner.query(`
            CREATE INDEX "IDX_40354d078bfc1e63973f643cf7" ON "user_credit_balance" ("organization_id")
        `);
    await queryRunner.query(`
            ALTER TABLE "user_credit_balance"
            ADD CONSTRAINT "FK_a73c0a8540dbc42c293caa0f25b" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "user_credit_balance"
            ADD CONSTRAINT "FK_40354d078bfc1e63973f643cf7c" FOREIGN KEY ("organization_id") REFERENCES "organization"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "user_credit_balance" DROP CONSTRAINT "FK_40354d078bfc1e63973f643cf7c"
        `);
    await queryRunner.query(`
            ALTER TABLE "user_credit_balance" DROP CONSTRAINT "FK_a73c0a8540dbc42c293caa0f25b"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."IDX_40354d078bfc1e63973f643cf7"
        `);
    await queryRunner.query(`
            ALTER TABLE "user_credit_balance"
            ALTER COLUMN "user_id"
            SET NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "user_credit_balance"
            ADD CONSTRAINT "FK_a73c0a8540dbc42c293caa0f25b" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "user_credit_balance" DROP COLUMN "organization_id"
        `);
  }
}
