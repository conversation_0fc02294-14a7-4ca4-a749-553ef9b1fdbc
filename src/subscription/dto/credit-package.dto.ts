import { ApiProperty } from '@nestjs/swagger';
import { CreditTypeEnum } from '../entity/credit-type.enum';

export class CreditPackageDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  price: number;

  @ApiProperty()
  currency: string;

  @ApiProperty()
  expiresAfterMonths?: number;

  @ApiProperty()
  creditTypes: { [key in CreditTypeEnum]: number };

  @ApiProperty()
  isRecurring: boolean;
}
