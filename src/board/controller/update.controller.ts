import {
  Controller,
  ForbiddenException,
  ParseUUIDPipe,
  Patch,
  Request,
} from '@nestjs/common';
import {
  Body,
  Param,
} from '@nestjs/common/decorators/http/route-params.decorator';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { BoardDto } from '../dto/board.dto';
import { BoardRequest } from '../dto/board.request';
import { BoardUserProvider } from '../service/board-user.provider';
import { BoardProvider } from '../service/provider';
import { BoardRequestManager } from '../service/request-manager';
import { BoardResponseMapper } from '../service/response-mapper';

@ApiTags('board')
@Controller('boards')
export class UpdateController {
  constructor(
    private boardProvider: BoardProvider,
    private boardMapper: BoardResponseMapper,
    private boardRequestManager: BoardRequestManager,
    private boardUserProvider: BoardUserProvider,
  ) {}

  @Patch(':id')
  @ApiOperation({
    operationId: 'board_update',
    summary: 'Update a board',
    description:
      'Updates the specified board for which the user is a manager.\n\n' +
      'Required Query Parameters:\n' +
      '- id: UUID of the board to update\n\n' +
      'Optional Body Parameters:\n' +
      '- name: New name of the board\n' +
      '- description: New description of the board\n' +
      '- visibility: New visibility of the board\n' +
      '- safety: New safety level of the board\n' +
      '- challengeDeadline: New challenge deadline of the board\n\n' +
      'Returns the updated board object.',
  })
  @ApiBody({
    type: BoardRequest,
    description: 'Board update parameters.',
  })
  @ApiOkResponse({
    type: BoardDto,
    description: 'Board updated successfully.',
  })
  @ApiBadRequestResponse({
    description: `
      Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - \`name\`: If provided, must be a non-empty string.
      - \`description\`: If provided, must be a string.
      - \`visibility\`: If provided, must be one of [PUBLIC, PRIVATE, HIDDEN].
      - \`safety\`: If provided, must be one of [NSFW, SFW].
      - \`challengeDeadline\`: If provided, must be a valid ISO date string.
      - Invalid or unavailable parameters.
      - Failed to analyze request body.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description:
      'Forbidden. User does not have permission to update this board.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the board to update.',
    type: String,
  })
  async update(
    @Body() boardRequest: BoardRequest,
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<BoardDto> {
    const board = await this.boardProvider.get(id);

    if (!(await this.boardUserProvider.isManager(request.user.id, board.id))) {
      throw new ForbiddenException();
    }

    await this.boardRequestManager.update(board, boardRequest);

    return await this.boardMapper.map(board);
  }
}
