import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { FindManyOptions, FindOneOptions, Repository } from 'typeorm';
import { AbstractProvider } from '../../core/service/abstract.provider';
import { OrganizationUserEntity } from '../entity/organization-user.entity';

@Injectable()
export class OrganizationUserProvider extends AbstractProvider<OrganizationUserEntity> {
  constructor(
    @InjectRepository(OrganizationUserEntity)
    protected readonly repository: Repository<OrganizationUserEntity>,
    protected readonly logger: Logger,
  ) {
    super(repository, logger);
  }

  async getUserOrganizations(
    userId: string,
  ): Promise<OrganizationUserEntity[]> {
    return await this.repository.find({ where: { userId } });
  }

  async isMember(userId: string, organizationId: string): Promise<boolean> {
    return (
      (await this.repository.count({
        where: { userId, organizationId },
      })) > 0
    );
  }

  async getMember(
    userId: string,
    organizationId: string,
  ): Promise<OrganizationUserEntity> {
    return this.getBy({ userId, organizationId });
  }

  async getOwner(organizationId: string): Promise<OrganizationUserEntity> {
    return this.getBy({ organizationId, isOwner: true });
  }

  async isAdmin(userId: string, organizationId: string): Promise<boolean> {
    return (
      (await this.repository.count({
        where: { userId, organizationId, isAdmin: true },
      })) > 0
    );
  }

  async isOwner(userId: string, organizationId: string): Promise<boolean> {
    return (
      (await this.repository.count({
        where: { userId, organizationId, isOwner: true },
      })) > 0
    );
  }

  async isManager(userId: string, organizationId: string): Promise<boolean> {
    return (
      (await this.repository.count({
        where: { userId, organizationId, isOwner: true },
      })) > 0 ||
      (await this.repository.count({
        where: { userId, organizationId, isAdmin: true },
      })) > 0
    );
  }

  prepareFindOneOptions(criteria: any): FindOneOptions<OrganizationUserEntity> {
    return {
      where: criteria,
      relations: {
        user: true,
        organization: true,
      },
    };
  }

  prepareFindManyOptions(
    criteria: any,
    page: number,
    limit: number,
    sortBy?: string,
    sortOrder = 'ASC',
  ): FindManyOptions<OrganizationUserEntity> {
    const parentOptions = super.prepareFindManyOptions(
      criteria,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    return {
      ...parentOptions,
      relations: {
        user: true,
        organization: true,
      },
    };
  }
}
