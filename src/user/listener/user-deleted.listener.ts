import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserDeletedEvent } from '../event/user-deleted.event';
import { UserFollowEntity } from '../entity/user-follow.entity';

@Injectable()
export class UserDeletedListener {
  constructor(
    @InjectRepository(UserFollowEntity)
    private userFollowRepository: Repository<UserFollowEntity>,
  ) {}

  @OnEvent('user.deleted')
  async handleUserDeletedEvent(event: UserDeletedEvent) {
    await this.userFollowRepository.softDelete({
      followerId: event.id,
    });

    await this.userFollowRepository.softDelete({
      followingId: event.id,
    });
  }
}
