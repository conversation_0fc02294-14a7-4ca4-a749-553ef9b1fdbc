import { Controller, Delete, HttpCode, Param, Request } from '@nestjs/common';
import { UserExternalProfileDto } from '../dto/user-external-profile.dto';
import { UserExternalProfileManager } from '../service/manager';
import { UserExternalProfileProvider } from '../service/provider';
import {
  ApiOkResponse,
  ApiParam,
  ApiTags,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiNotFoundResponse,
  ApiBadRequestResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';

@ApiTags('user')
@Controller('users/current/external-profiles')
export class DeleteController {
  constructor(
    private provider: UserExternalProfileProvider,
    private manager: UserExternalProfileManager,
  ) {}

  @Delete(':id')
  @ApiOperation({
    operationId: 'user_external_profile_delete',
    summary: 'Delete external profile for current user',
    description:
      'Deletes an external profile for the current authenticated user.\n\n' +
      'Required Parameter:\n' +
      '- id: External Profile ID\n',
  })
  @ApiOkResponse({
    type: UserExternalProfileDto,
    description: 'External profile deleted successfully.',
  })
  @ApiParam({ name: 'id', description: 'External Profile ID' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - External profile does not exist.
  `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiNotFoundResponse({
    description: 'Not Found. The external profile could not be found.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @HttpCode(204)
  async get(@Request() request, @Param() params): Promise<void> {
    await this.provider
      .getBy({ user: { id: request.user.id }, id: params.id })
      .then((entity) => this.manager.delete(entity));
  }
}
