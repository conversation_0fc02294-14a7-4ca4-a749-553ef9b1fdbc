import {
  DeleteObjectCommand,
  GetObjectCommand,
  PutObjectCommand,
  S3Client,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { UserImageEntity } from '../entity/user-image.entity';

@Injectable()
export class UserImageManager {
  constructor(
    @InjectRepository(UserImageEntity)
    private repository: Repository<UserImageEntity>,
    private configService: ConfigService,
    @Inject('S3') private s3: S3Client,
    private logger: Logger,
  ) {}

  async create(
    entity: UserImageEntity,
    extension: string,
    originalFilename?: string,
  ): Promise<UserImageEntity> {
    entity.id = uuidv4();

    // Generate storage paths
    this.generateStoragePath(entity);
    
    entity.filename = `${uuidv4()}.${extension}`;
    entity.originalFilename = originalFilename;
    entity.imagePath = `${entity.storagePath}/${entity.filename}`;
    
    await this.save(entity);
    
    return entity;
  }

  async save(entity: UserImageEntity) {
    await this.repository.save(entity);
  }

  async delete(entity: UserImageEntity): Promise<void> {
    try {
      // Delete the file from S3
      if (entity.storageBucket && entity.imagePath) {
        const command = new DeleteObjectCommand({
          Bucket: entity.storageBucket,
          Key: entity.imagePath,
        });
        await this.s3.send(command);
      }

      // Soft delete the entity
      await this.repository.softDelete(entity.id);
    } catch (error) {
      this.logger.error('Error deleting user image', {
        imageId: entity.id,
        error: error,
      });
      throw error;
    }
  }

  generateStorageBucket(entity: UserImageEntity): void {
    entity.storageBucket = this.configService.get<string>(
      'USER_IMAGE_S3_BUCKET_NAME',
      this.configService.get<string>('IMAGE_COMPLETION_S3_BUCKET_NAME'),
    );
  }

  generateStoragePath(entity: UserImageEntity): void {
    if (!entity.storageBucket) {
      this.generateStorageBucket(entity);
    }
    entity.storagePath = `user-images/${entity.userId}`;
  }

  async generateSignedUrl(
    entity: UserImageEntity,
    expiresIn: number = 60 * 15,
  ): Promise<string> {
    const command = new GetObjectCommand({
      Bucket: entity.storageBucket,
      Key: entity.imagePath,
    });

    return getSignedUrl(this.s3, command, { expiresIn });
  }

  async generateImageUploadUrl(
    entity: UserImageEntity,
    extension: string,
  ): Promise<string> {
    try {
      const command = new PutObjectCommand({
        Bucket: entity.storageBucket,
        Key: entity.imagePath,
      });

      return await getSignedUrl(this.s3, command, {
        expiresIn: 60 * 15, // URL expires in 15 minutes
      });
    } catch (error) {
      this.logger.error('Error generating user image upload url', {
        userId: entity.userId,
        error: error,
      });

      throw error;
    }
  }

  async getObject(entity: UserImageEntity): Promise<any> {
    const command = new GetObjectCommand({
      Bucket: entity.storageBucket,
      Key: entity.imagePath,
    });

    return await this.s3.send(command);
  }
}
