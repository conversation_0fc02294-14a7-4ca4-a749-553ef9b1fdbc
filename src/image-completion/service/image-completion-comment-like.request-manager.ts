import { BadRequestException, Injectable } from '@nestjs/common';
import { ImageCompletionCommentLikeManager } from './image-completion-comment-like.manager';
import { ImageCompletionCommentLikeProvider } from './image-completion-comment-like.provider';
import { ImageCompletionCommentEntity } from '../entity/image-completion-comment.entity';

@Injectable()
export class ImageCompletionCommentLikeRequestManager {
  constructor(
    private manager: ImageCompletionCommentLikeManager,
    private provider: ImageCompletionCommentLikeProvider,
  ) {}

  async like(
    imageCompletionComment: ImageCompletionCommentEntity,
    userId: string,
  ): Promise<void> {
    const existingLikes = await this.provider.countBy({
      imageCompletionCommentId: imageCompletionComment.id,
      userId: userId,
    });
    if (existingLikes > 0) {
      throw new BadRequestException('Already liked');
    }
    await this.manager.like(imageCompletionComment, userId);
  }

  async unlike(
    imageCompletionComment: ImageCompletionCommentEntity,
    userId: string,
  ) {
    const imageCompletionCommentLike = await this.provider.getBy({
      imageCompletionCommentId: imageCompletionComment.id,
      userId: userId,
    });

    await this.manager.unlike(imageCompletionCommentLike);
  }
}
