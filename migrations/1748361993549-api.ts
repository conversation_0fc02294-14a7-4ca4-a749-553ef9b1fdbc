import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1748361993549 implements MigrationInterface {
  name = 'Api1748361993549';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Set all existing NULL limits to 10
    await queryRunner.query(`
      UPDATE "user_signup_code"
      SET "limit" = 10
      WHERE "limit" IS NULL
    `);

    // Set NOT NULL and default
    await queryRunner.query(`
      ALTER TABLE "user_signup_code"
      ALTER COLUMN "limit"
      SET NOT NULL
    `);
    await queryRunner.query(`
      ALTER TABLE "user_signup_code"
      ALTER COLUMN "limit"
      SET DEFAULT 10
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "user_signup_code"
      ALTER COLUMN "limit" DROP DEFAULT
    `);
    await queryRunner.query(`
      ALTER TABLE "user_signup_code"
      ALTER COLUMN "limit" DROP NOT NULL
    `);
  }
}
