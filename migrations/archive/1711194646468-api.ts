import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1711194646468 implements MigrationInterface {
    name = 'Api1711194646468'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "model"
            ADD "images_available" integer NOT NULL DEFAULT '0'
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "model" DROP COLUMN "images_available"
        `);
    }

}
