export class ImageCompletionCommentLikedEvent {
  id: string;
  userId: string;
  ownerId: string;
  imageCompletionCommentId: string;

  constructor(data: {
    id: string;
    imageCompletionCommentId: string;
    ownerId: string;
    userId: string;
  }) {
    this.id = data.id;
    this.userId = data.userId;
    this.ownerId = data.ownerId;
    this.imageCompletionCommentId = data.imageCompletionCommentId;
  }
}
