import { Injectable } from '@nestjs/common';
import { SignupCodeDto } from '../dto/signup-code.dto';
import { SignupCodeEntity } from '../entity/signup-code.entity';

@Injectable()
export class SignupCodeResponseMapper {
  map(signupCode: SignupCodeEntity): SignupCodeDto {
    const signupCodeDto = new SignupCodeDto();

    signupCodeDto.id = signupCode.id;
    signupCodeDto.code = signupCode.code;
    signupCodeDto.notes = signupCode.notes;
    signupCodeDto.usages = signupCode.usages;
    signupCodeDto.limit = signupCode.limit;
    signupCodeDto.features = signupCode.features;

    return signupCodeDto;
  }

  mapMultiple(signupCodes: SignupCodeEntity[]): SignupCodeDto[] {
    return signupCodes.map((signupCode) => this.map(signupCode));
  }
}
