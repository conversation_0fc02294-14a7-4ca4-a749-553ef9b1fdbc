import { Injectable } from '@nestjs/common';

@Injectable()
export class BlockedEmailDomainsService {
  private readonly blockedDomains = new Set([
    // User-specified domains
    "mechanicspedia.com",

    // Common throwaway email domains
    "10minutemail.com",
    "10minutemail.net",
    "20minutemail.com",
    "2prong.com",
    "30minutemail.com",
    "33mail.com",
    "3d-painting.com",
    "7tags.com",
    "9ox.net",
    "aaathats3as.com",
    "abcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghijk.com",
    "agedmail.com",
    "amilegit.com",
    "anonbox.net",
    "anonymbox.com",
    "antichef.com",
    "antispam.de",
    "armyspy.com",
    "banit.me",
    "binkmail.com",
    "bit-degree.com",
    "bobmail.info",
    "brennendesreich.de",
    "bugmenot.com",
    "bumpymail.com",
    "bunnymail.info",
    "buymoreplays.com",
    "byom.de",
    "c2dm.com",
    "card.zp.ua",
    "casualdx.com",
    "cefcef.com",
    "centermail.com",
    "centermail.net",
    "chammy.info",
    "childsavetrust.org",
    "chogmail.com",
    "choicemail1.com",
    "clrmail.com",
    "cmail.net",
    "cmail.org",
    "coldemail.info",
    "cool.fr.nf",
    "correo.blogos.net",
    "crankmail.com",
    "crazymailing.com",
    "cubiclink.com",
    "curryworld.de",
    "cust.in",
    "cuvox.de",
    "d3p.dk",
    "dacoolest.com",
    "dandikmail.com",
    "dayrep.com",
    "dcemail.com",
    "deadaddress.com",
    "deadspam.com",
    "despam.it",
    "devnullmail.com",
    "dfgh.net",
    "digitalsanctuary.com",
    "discardmail.com",
    "discardmail.de",
    "disposableaddress.com",
    "disposableemailaddresses.com",
    "disposableinbox.com",
    "dispose.it",
    "disposeamail.com",
    "disposemail.com",
    "dispostable.com",
    "dodgeit.com",
    "dodgit.com",
    "donemail.ru",
    "dontreg.com",
    "dontsendmespam.de",
    "drdrb.com",
    "drdrb.net",
    "droplar.com",
    "dropmail.me",
    "dudmail.com",
    "dump-email.info",
    "dumpandjunk.com",
    "dumpmail.de",
    "dumpyemail.com",
    "e-mail.com",
    "e-mail.org",
    "e4ward.com",
    "easytrashmail.com",
    "einrot.com",
    "email60.com",
    "emaildienst.de",
    "emailgo.de",
    "emailias.com",
    "emailinfive.com",
    "emailisvalid.com",
    "emailmiser.com",
    "emailsensei.com",
    "emailtemporar.ro",
    "emailtemporario.com.br",
    "emailto.de",
    "emailwarden.com",
    "emailx.at.hm",
    "emailxfer.com",
    "emeil.in",
    "emeil.ir",
    "emz.net",
    "enterto.com",
    "ephemail.net",
    "ero-tube.org",
    "etranquil.com",
    "etranquil.net",
    "etranquil.org",
    "explodemail.com",
    "fakeinbox.com",
    "fakemailz.com",
    "fakemail.fr",
    "fakemailgenerator.com",
    "faketempmail.com",
    "fansworldwide.de",
    "fantasymail.de",
    "fastacura.com",
    "fastchevy.com",
    "fastchrysler.com",
    "fastkawasaki.com",
    "fastmazda.com",
    "fastmitsubishi.com",
    "fastnissan.com",
    "fastsubaru.com",
    "fastsuzuki.com",
    "fasttoyota.com",
    "fastyamaha.com",
    "fatflap.com",
    "fdfdsfds.com",
    "fightmail.com",
    "filzmail.com",
    "fizmail.com",
    "fleckens.hu",
    "flying-mail.com",
    "frapmail.com",
    "freemails.cf",
    "freemails.ga",
    "freemails.ml",
    "freundin.ru",
    "fux0ringduh.com",
    "garliclife.com",
    "gawab.com",
    "get-mail.cf",
    "get-mail.ga",
    "get-mail.ml",
    "get-mail.tk",
    "get1mail.com",
    "get2mail.fr",
    "getairmail.com",
    "getmails.eu",
    "getonemail.com",
    "getonemail.net",
    "ghosttexter.de",
    "giantmail.de",
    "girlsundertheinfluence.com",
    "gishpuppy.com",
    "gmial.com",
    "gohome.org",
    "gorillaswithdirtyarmpits.com",
    "gotmail.com",
    "gotmail.net",
    "gotmail.org",
    "gotti.otherinbox.com",
    "great-host.in",
    "greensloth.com",
    "grr.la",
    "gsrv.co.uk",
    "guerillamail.biz",
    "guerillamail.com",
    "guerillamail.net",
    "guerillamail.org",
    "guerillamailblock.com",
    "guerrillamail.biz",
    "guerrillamail.com",
    "guerrillamail.de",
    "guerrillamail.net",
    "guerrillamail.org",
    "guerrillamailblock.com",
    "h.mintemail.com",
    "hacccc.com",
    "haltospam.com",
    "harakirimail.com",
    "hatespam.org",
    "herp.in",
    "hidemail.de",
    "hidzz.com",
    "hmamail.com",
    "hopemail.biz",
    "hotpop.com",
    "hulapla.de",
    "ieatspam.eu",
    "ieatspam.info",
    "ieh-mail.de",
    "ikbenspamvrij.nl",
    "imails.info",
    "inboxalias.com",
    "inboxclean.com",
    "inboxclean.org",
    "incognitomail.com",
    "incognitomail.net",
    "incognitomail.org",
    "insorg-mail.info",
    "instant-mail.de",
    "instantemailaddress.com",
    "ipoo.org",
    "irish2me.com",
    "iwi.net",
    "jnxjn.com",
    "jobbikszimpatizans.hu",
    "junk1e.com",
    "junkmail.ga",
    "junkmail.gq",
    "kasmail.com",
    "kaspop.com",
    "keepmymail.com",
    "killmail.com",
    "killmail.net",
    "klassmaster.com",
    "klzlk.com",
    "koszmail.pl",
    "kurzepost.de",
    "l33r.eu",
    "lackmail.net",
    "lags.us",
    "landmail.co",
    "lastmail.co",
    "lavabit.com",
    "lawlita.com",
    "letthemeatspam.com",
    "lhsdv.com",
    "lifebyfood.com",
    "link2mail.net",
    "litedrop.com",
    "lkgn.se",
    "lookugly.com",
    "lopl.co.cc",
    "lortemail.dk",
    "lr78.com",
    "lroid.com",
    "lukop.dk",
    "m21.cc",
    "mail-filter.com",
    "mail-temporaire.fr",
    "mail.by",
    "mail.mezimages.net",
    "mail.zp.ua",
    "mail1a.de",
    "mail21.cc",
    "mail2rss.org",
    "mail333.com",
    "mail4trash.com",
    "mailbidon.com",
    "mailbiz.biz",
    "mailblocks.com",
    "mailcatch.com",
    "mailde.de",
    "mailde.info",
    "maildrop.cc",
    "maildu.de",
    "maildx.com",
    "maileater.com",
    "mailed.ro",
    "mailexpire.com",
    "mailfa.tk",
    "mailforspam.com",
    "mailfreeonline.com",
    "mailguard.me",
    "mailhz.me",
    "mailimate.com",
    "mailin8r.com",
    "mailinater.com",
    "mailinator.com",
    "mailinator.net",
    "mailinator.org",
    "mailinator2.com",
    "mailincubator.com",
    "mailismagic.com",
    "mailme.lv",
    "mailme24.com",
    "mailmetrash.com",
    "mailmoat.com",
    "mailnator.com",
    "mailnesia.com",
    "mailnull.com",
    "mailpick.biz",
    "mailrock.biz",
    "mailscrap.com",
    "mailshell.com",
    "mailsiphon.com",
    "mailtemp.info",
    "mailtome.de",
    "mailtothis.com",
    "mailtrash.net",
    "mailtv.net",
    "mailtv.tv",
    "mailzilla.com",
    "mailzilla.org",
    "makemetheking.com",
    "manifestgenerator.com",
    "manybrain.com",
    "mbx.cc",
    "mciek.com",
    "megago.tk",
    "meltmail.com",
    "messagebeamer.de",
    "mezimages.net",
    "mierdamail.com",
    "mintemail.com",
    "misterpinball.de",
    "moncourrier.fr.nf",
    "monemail.fr.nf",
    "monmail.fr.nf",
    "monumentmail.com",
    "mt2009.com",
    "mt2014.com",
    "mycard.net.ua",
    "mycleaninbox.net",
    "myemailboxy.com",
    "mymail-in.net",
    "mymailoasis.com",
    "mynetstore.de",
    "mypacks.net",
    "mypartyclip.de",
    "myphantomemail.com",
    "mysamp.de",
    "mytempemail.com",
    "mytempmail.com",
    "mytrashmail.com",
    "nabuma.com",
    "neomailbox.com",
    "nepwk.com",
    "nervmich.net",
    "nervtmich.net",
    "netmails.com",
    "netmails.net",
    "netzidiot.de",
    "neverbox.com",
    "nice-4u.com",
    "nincsmail.com",
    "nincsmail.hu",
    "nnh.com",
    "no-spam.ws",
    "noblepioneer.com",
    "nobugmail.com",
    "noclickemail.com",
    "nogmailspam.info",
    "nomail.xl.cx",
    "nomail2me.com",
    "nomorespamemails.com",
    "nonspam.eu",
    "nonspammer.de",
    "noref.in",
    "nospam.ze.tc",
    "nospam4.us",
    "nospamfor.us",
    "nospammail.net",
    "nospamthanks.info",
    "notmailinator.com",
    "notsharingmy.info",
    "nowhere.org",
    "nowmymail.com",
    "ntlhelp.net",
    "nullbox.info",
    "nurfuerspam.de",
    "nwldx.com",
    "objectmail.com",
    "obobbo.com",
    "odaymail.com",
    "oneoffemail.com",
    "onewaymail.com",
    "onlatedotcom.info",
    "online.ms",
    "oopi.org",
    "opayq.com",
    "ordinaryamerican.net",
    "otherinbox.com",
    "ovpn.to",
    "owlpic.com",
    "pancakemail.com",
    "paplease.com",
    "pcusers.otherinbox.com",
    "pepbot.com",
    "pimpedupmyspace.com",
    "pjkl.com",
    "plexolan.de",
    "poczta.onet.pl",
    "politikerclub.de",
    "pooae.com",
    "pookmail.com",
    "privacy.net",
    "privy-mail.com",
    "privymail.de",
    "proxymail.eu",
    "prtnx.com",
    "punkass.com",
    "put2.net",
    "putthisinyourspamdatabase.com",
    "pwrby.com",
    "qq.com",
    "quickinbox.com",
    "rcpt.at",
    "reallymymail.com",
    "realtyalerts.ca",
    "recode.me",
    "reconmail.com",
    "recursor.net",
    "recyclebin.org",
    "regbypass.com",
    "regbypass.comsafe-mail.net",
    "rejectmail.com",
    "rhyta.com",
    "rmqkr.net",
    "rppkn.com",
    "rtrtr.com",
    "s0ny.net",
    "safe-mail.net",
    "safersignup.de",
    "safetymail.info",
    "safetypost.de",
    "sandelf.de",
    "saynotospams.com",
    "schafmail.de",
    "schrott-email.de",
    "secretemail.de",
    "secure-mail.biz",
    "secure-mail.cc",
    "selfdestructingmail.com",
    "selfdestructingmail.org",
    "sendspamhere.de",
    "sharklasers.com",
    "shieldedmail.com",
    "shieldemail.com",
    "shitmail.me",
    "shitmail.org",
    "shitware.nl",
    "shortmail.net",
    "sibmail.com",
    "sinnlos-mail.de",
    "skeefmail.com",
    "slapsfromlastnight.com",
    "slaskpost.se",
    "smashmail.de",
    "smellfear.com",
    "snakemail.com",
    "sneakemail.com",
    "snkmail.com",
    "sofimail.com",
    "sofort-mail.de",
    "sogetthis.com",
    "soisz.com",
    "solvemail.info",
    "spam.la",
    "spam.su",
    "spam4.me",
    "spamail.de",
    "spambob.com",
    "spambob.net",
    "spambob.org",
    "spambog.com",
    "spambog.de",
    "spambog.net",
    "spambog.ru",
    "spambox.info",
    "spambox.irishspringtours.com",
    "spambox.us",
    "spamcannon.com",
    "spamcannon.net",
    "spamcero.com",
    "spamcon.org",
    "spamcorptastic.com",
    "spamcowboy.com",
    "spamcowboy.net",
    "spamcowboy.org",
    "spamday.com",
    "spamex.com",
    "spamfree.eu",
    "spamfree24.com",
    "spamfree24.de",
    "spamfree24.eu",
    "spamfree24.net",
    "spamfree24.org",
    "spamgoes.com",
    "spamgourmet.com",
    "spamgourmet.net",
    "spamgourmet.org",
    "spamherelots.com",
    "spamhereplease.com",
    "spamhippo.com",
    "spamhole.com",
    "spamify.com",
    "spaminator.de",
    "spamkill.info",
    "spaml.com",
    "spaml.de",
    "spammotel.com",
    "spamobox.com",
    "spamoff.de",
    "spamslicer.com",
    "spamspot.com",
    "spamthis.co.uk",
    "spamthisplease.com",
    "spamtrail.com",
    "spamtroll.net",
    "speed.1s.fr",
    "spoofmail.de",
    "stuffmail.de",
    "super-auswahl.de",
    "supergreatmail.com",
    "supermailer.jp",
    "superrito.com",
    "superstachel.de",
    "suremail.info",
    "talkinator.com",
    "teewars.org",
    "teleworm.com",
    "teleworm.us",
    "temp-mail.org",
    "temp-mail.ru",
    "tempalias.com",
    "tempe-mail.com",
    "tempemail.biz",
    "tempemail.com",
    "tempemail.net",
    "tempinbox.co.uk",
    "tempinbox.com",
    "tempmail.eu",
    "tempmail.it",
    "tempmail2.com",
    "tempmaildrop.com",
    "tempomail.fr",
    "temporarily.de",
    "temporarioemail.com.br",
    "temporaryemail.net",
    "temporaryinbox.com",
    "temporarymailaddress.com",
    "tempthe.net",
    "thanksnospam.info",
    "thankyou2010.com",
    "thc.st",
    "thecloudindex.com",
    "thisisnotmyrealemail.com",
    "thismail.net",
    "throam.com",
    "throwawayemailaddress.com",
    "tilien.com",
    "tittbit.in",
    "tizi.com",
    "tmail.ws",
    "tmailinator.com",
    "toiea.com",
    "toomail.biz",
    "topranklist.de",
    "tradermail.info",
    "trash-amil.com",
    "trash-mail.at",
    "trash-mail.com",
    "trash-mail.de",
    "trash2009.com",
    "trash2010.com",
    "trash2011.com",
    "trashdevil.com",
    "trashdevil.de",
    "trashemail.de",
    "trashmail.at",
    "trashmail.com",
    "trashmail.de",
    "trashmail.me",
    "trashmail.net",
    "trashmail.org",
    "trashmail.ws",
    "trashmailer.com",
    "trashymail.com",
    "trashymail.net",
    "trialmail.de",
    "turual.com",
    "twinmail.de",
    "tyldd.com",
    "uggsrock.com",
    "umail.net",
    "unlimit.com",
    "unmail.ru",
    "upliftnow.com",
    "uplipht.com",
    "uroid.com",
    "us.af",
    "venompen.com",
    "veryrealemail.com",
    "vidchart.com",
    "viditag.com",
    "viewcastmedia.com",
    "viewcastmedia.net",
    "viewcastmedia.org",
    "vomoto.com",
    "vubby.com",
    "walala.org",
    "walkmail.net",
    "webemail.me",
    "webm4il.info",
    "webuser.in",
    "wh4f.org",
    "whopy.com",
    "willhackforfood.biz",
    "willselfdestruct.com",
    "winemaven.info",
    "wronghead.com",
    "wuzup.net",
    "wuzupmail.net",
    "www.e4ward.com",
    "www.gishpuppy.com",
    "www.mailinator.com",
    "wwwnew.eu",
    "xagloo.com",
    "xemaps.com",
    "xents.com",
    "xmaily.com",
    "xoxy.net",
    "yapped.net",
    "yeah.net",
    "yesey.net",
    "yogamaven.com",
    "yomail.info",
    "yopmail.com",
    "yopmail.fr",
    "yopmail.net",
    "yourdomain.com",
    "yourmailbox.info",
    "ypmail.webredirect.org",
    "yuurok.com",
    "zehnminutenmail.de",
    "zippymail.info",
    "zoemail.org",
    "zoemail.net",
    "zomg.info",
  ]);

  /**
   * Check if an email domain is blocked
   * @param email The email address to check
   * @returns true if the domain is blocked, false otherwise
   */
  isEmailDomainBlocked(email: string): boolean {
    if (!email || typeof email !== 'string') {
      return false;
    }

    const domain = this.extractDomain(email);
    if (!domain) {
      return false;
    }

    return this.blockedDomains.has(domain.toLowerCase());
  }

  /**
   * Extract domain from email address
   * @param email The email address
   * @returns The domain part of the email
   */
  private extractDomain(email: string): string | null {
    const emailRegex = /^[^\s@]+@([^\s@]+)$/;
    const match = email.match(emailRegex);
    return match ? match[1] : null;
  }

  /**
   * Get all blocked domains (for testing or admin purposes)
   * @returns Array of blocked domains
   */
  getBlockedDomains(): string[] {
    return Array.from(this.blockedDomains);
  }

  /**
   * Add a domain to the blocklist
   * @param domain The domain to block
   */
  addBlockedDomain(domain: string): void {
    if (domain && typeof domain === 'string') {
      this.blockedDomains.add(domain.toLowerCase());
    }
  }

  /**
   * Remove a domain from the blocklist
   * @param domain The domain to unblock
   */
  removeBlockedDomain(domain: string): void {
    if (domain && typeof domain === 'string') {
      this.blockedDomains.delete(domain.toLowerCase());
    }
  }
}
