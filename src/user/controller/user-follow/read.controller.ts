import {
  <PERSON>,
  Get,
  Param,
  ParseUUIDPipe,
  Query,
  Request,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiOkResponse,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiOperation,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiNotFoundResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import { BaseFindResponseHeadersDto } from 'src/core/dto/base-find-response-headers.dto';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { UserProvider } from 'src/user/service/provider';
import { UserFollowDto } from '../../dto/user-follow.dto';
import { UserFollowSearchRequest } from '../../dto/user-follow.search-request';
import { UserFollowProvider } from '../../service/user-follow.provider';
import { UserFollowResponseMapper } from '../../service/user-follow.response-mapper';
import { UserEntity } from 'src/user/entity/user.entity';

@ApiTags('user-follow')
@Controller()
export class ReadController {
  constructor(
    private provider: UserFollowProvider,
    private responseMapper: UserFollowResponseMapper,
    private userProvider: UserProvider,
  ) {}

  @ApiOperation({
    operationId: 'user_follow_list_following',
    summary: 'List users the specified user is following',
    description:
      'Retrieves a paginated list of users that the specified user is following.\n\n' +
      'Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of users per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- followerId: The unique identifier of the user to list follows for\n' +
      '- followingId: The unique identifier of the user being followed\n' +
      '- isPending: Whether the follow request is pending\n' +
      '- isApproved: Whether the follow request is approved\n\n' +
      'Returns the list of users the specified user is following.',
  })
  @ApiOkResponse({
    type: UserFollowDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated list of users the specified user is following.',
  })
  @ApiParam({
    name: 'userId',
    description: 'The unique identifier of the user',
    type: 'string',
  })
  @ApiQuery({
    type: UserFollowSearchRequest,
    description: 'Query parameters for searching and paginating follows.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - \`followerId\`: Must be a valid UUID.
      - \`followingId\`: Must be a valid UUID.
      - \`isPending\`: Must be a valid boolean value.
      - \`isApproved\`: Must be a valid boolean value.
      - Filter parameters must be valid values.
      - Invalid or unavailable parameters.
  `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Get('users/:userId/follows')
  @UsePipes(new ValidationPipe())
  async findFollowing(
    @Request() request,
    @Query() query: UserFollowSearchRequest,
    @Res() res: Response,
    @Param('userId') userId: string,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;

    let follower: UserEntity;

    if ('current' == userId) {
      follower = request.user;
    } else {
      follower = await this.userProvider.get(userId);
    }

    inputFilters.followerId = follower.id;

    const entities = await this.provider.findBy(
      inputFilters,
      page,
      limit,
      sortBy,
      sortOrder,
    );
    const totalCount = await this.provider.countBy(inputFilters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultiple(entities));
  }

  @ApiOperation({
    operationId: 'user_follow_list_followers',
    summary: 'List followers of the specified user',
    description:
      'Retrieves a paginated list of users who are following the specified user.\n\n' +
      'Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of users per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- followerId: The unique identifier of the user to list followers for\n' +
      '- followingId: The unique identifier of the user being followed\n' +
      '- isPending: Whether the follow request is pending\n' +
      '- isApproved: Whether the follow request is approved\n\n' +
      'Returns the list of followers.',
  })
  @ApiOkResponse({
    type: UserFollowDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated list of followers.',
  })
  @ApiParam({
    name: 'userId',
    description: 'The unique identifier of the user',
    type: 'string',
  })
  @ApiQuery({
    type: UserFollowSearchRequest,
    description: 'Query parameters for searching and paginating followers.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - \`followerId\`: Must be a valid UUID.
      - \`followingId\`: Must be a valid UUID.
      - \`isPending\`: Must be a valid boolean value.
      - \`isApproved\`: Must be a valid boolean value.
      - Filter parameters must be valid values.
      - Invalid or unavailable parameters.
  `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Get('users/:userId/followers')
  @UsePipes(new ValidationPipe())
  async findFollowers(
    @Request() request,
    @Query() query: UserFollowSearchRequest,
    @Res() res: Response,
    @Param('userId') userId: string,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;

    let following: UserEntity;

    if ('current' == userId) {
      following = request.user;
    } else {
      following = await this.userProvider.get(userId);
    }

    inputFilters.followingId = following.id;

    const entities = await this.provider.findBy(
      inputFilters,
      page,
      limit,
      sortBy,
      sortOrder,
    );
    const totalCount = await this.provider.countBy(inputFilters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultiple(entities));
  }

  @ApiOperation({
    operationId: 'user_follow_check',
    summary: 'Check if a user is following another user',
    description:
      'Checks if the follower is following the specified user.\n\n' +
      'Required Parameters:\n' +
      '- followerId: The unique identifier of the follower\n' +
      '- followingId: The unique identifier of the user being followed\n\n' +
      'Returns the follow relationship details.',
  })
  @ApiOkResponse({
    type: UserFollowDto,
    description: 'Follow relationship details.',
  })
  @ApiParam({
    name: 'followerId',
    description: 'The unique identifier of the follower',
    type: 'string',
  })
  @ApiParam({
    name: 'followingId',
    description: 'The unique identifier of the user being followed',
    type: 'string',
    format: 'uuid',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`followerId\`: Must be a valid UUID.
      - \`followingId\`: Must be a valid UUID.
 `,
  })
  @ApiNotFoundResponse({
    description: 'Not Found. The follow relationship could not be found.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Get('users/:followerId/follows/:followingId')
  @Get('users/:followerId/following/:followingId')
  async checkFollow(
    @Request() request,
    @Param('followerId') followerId: string,
    @Param('followingId', new ParseUUIDPipe()) followingId: string,
  ): Promise<UserFollowDto> {
    if ('current' == followerId) {
      followerId = request.user.id;
    }

    const userFollow = await this.provider.getByFollowerAndFollowing(
      followerId,
      followingId,
    );

    return await this.responseMapper.map(userFollow);
  }
}
