import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { UserProvider } from 'src/user/service/provider';
import { UserManager } from '../service/manager';
import { SubscriptionActivatedEvent } from 'src/subscription/event/subscription-activated.event';

@Injectable()
export class SubscriptionActivatedListener {
  constructor(
    private userManager: UserManager,
    private userProvider: UserProvider,
  ) {}

  @OnEvent('subscription.activated', { async: true })
  async handleSubscriptionActivatedEvent(event: SubscriptionActivatedEvent) {
    const user = await this.userProvider.get(event.userId);

    user.isVerified = true;

    this.userManager.update(user);
  }
}
