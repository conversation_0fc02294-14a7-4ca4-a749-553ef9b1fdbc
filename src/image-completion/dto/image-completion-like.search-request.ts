import { ApiProperty } from '@nestjs/swagger';
import { <PERSON>Array, IsOptional, IsString, IsUUID } from 'class-validator';
import { BaseSearchRequest } from 'src/core/dto/base.search-request';

export class ImageCompletionLikeSearchRequest extends BaseSearchRequest {
  @IsOptional()
  @IsUUID()
  @ApiProperty()
  userId: string;

  @IsOptional()
  @IsString()
  @ApiProperty()
  username: string;

  @IsOptional()
  @IsArray()
  @ApiProperty()
  modelIds: string[];
}
