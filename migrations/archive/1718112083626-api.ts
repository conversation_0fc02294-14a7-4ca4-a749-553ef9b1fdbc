import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1718112083626 implements MigrationInterface {
    name = 'Api1718112083626'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "image_completion_comment" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "image_completion_id" uuid NOT NULL,
                "user_id" uuid NOT NULL,
                "comment" text NOT NULL,
                "deleted_at" TIMESTAMP,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_19f1500a82fae75499383f3d4a6" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE INDEX "idx_image_completion_comment_image_completion_id" ON "image_completion_comment" ("image_completion_id")
        `);
        await queryRunner.query(`
            CREATE INDEX "idx_image_completion_comment_user_id" ON "image_completion_comment" ("user_id")
        `);
        await queryRunner.query(`
            ALTER TABLE "image_completion"
            ADD "comments" integer NOT NULL DEFAULT '0'
        `);
        await queryRunner.query(`
            ALTER TABLE "image_completion_comment"
            ADD CONSTRAINT "FK_442de77783b4af74c88248bb5ac" FOREIGN KEY ("image_completion_id") REFERENCES "image_completion"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "image_completion_comment"
            ADD CONSTRAINT "FK_b0d90e21352883e4d43a4be5285" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "image_completion_comment" DROP CONSTRAINT "FK_b0d90e21352883e4d43a4be5285"
        `);
        await queryRunner.query(`
            ALTER TABLE "image_completion_comment" DROP CONSTRAINT "FK_442de77783b4af74c88248bb5ac"
        `);
        await queryRunner.query(`
            ALTER TABLE "image_completion" DROP COLUMN "comments"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."idx_image_completion_comment_user_id"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."idx_image_completion_comment_image_completion_id"
        `);
        await queryRunner.query(`
            DROP TABLE "image_completion_comment"
        `);
    }

}
