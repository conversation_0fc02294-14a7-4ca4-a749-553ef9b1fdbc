import { IsNotEmpty, IsEmail, IsString, ValidateIf } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class UserAuthRequest {
  @ValidateIf(o => !o.username)
  @IsEmail()
  @ApiProperty()
  @Transform(({ value }) => value.toLowerCase())
  email: string;

  @ValidateIf(o => !o.email)
  @IsString()
  @ApiProperty()
  @Transform(({ value }) => value.toLowerCase())
  username: string;

  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  password: string;
}
