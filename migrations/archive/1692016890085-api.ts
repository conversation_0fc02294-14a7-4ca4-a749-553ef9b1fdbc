import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1692016890085 implements MigrationInterface {
    name = 'Api1692016890085'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "image_completion"
            ADD "preview_image" text
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "image_completion" DROP COLUMN "preview_image"
        `);
    }

}
