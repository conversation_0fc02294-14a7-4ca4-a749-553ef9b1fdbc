import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserExternalProfileEntity } from '../entity/user-external-profile.entity';

@Injectable()
export class UserExternalProfileManager {
  constructor(
    @InjectRepository(UserExternalProfileEntity)
    private repository: Repository<UserExternalProfileEntity>,
  ) {}

  async create(
    entity: UserExternalProfileEntity,
  ): Promise<UserExternalProfileEntity> {
    return await this.repository.save(entity);
  }

  async update(
    entity: UserExternalProfileEntity,
  ): Promise<UserExternalProfileEntity> {
    return await this.repository.save(entity);
  }

  async delete(entity: UserExternalProfileEntity): Promise<void> {
    await this.repository.softDelete(entity.id);
  }
}
