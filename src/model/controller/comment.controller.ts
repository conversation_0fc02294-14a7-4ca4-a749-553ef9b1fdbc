import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Param,
  ParseUUIDPipe,
  Post,
  Query,
  Request,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { Response } from 'express';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiQuery,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { ModelProvider } from '../service/provider';
import { ModelCommentProvider } from '../service/model-comment.provider';
import { ModelCommentRequestManager } from '../service/model-comment.request-manager';
import { ModelCommentResponseMapper } from '../service/model-comment.response-mapper';
import { ModelCommentDto } from '../dto/model-comment.dto';
import { BaseFindResponseHeadersDto } from 'src/core/dto/base-find-response-headers.dto';
import { ModelCommentSearchRequest } from '../dto/model-comment.search-request';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { ModelCommentRequest } from '../dto/model-comment.request';

@ApiTags('model / comments')
@Controller('models')
export class CommentController {
  constructor(
    private modelProvider: ModelProvider,
    private modelCommentProvider: ModelCommentProvider,
    private requestManager: ModelCommentRequestManager,
    private responseMapper: ModelCommentResponseMapper,
  ) {}

  @Get(':id/comments')
  @ApiOperation({
    operationId: 'model_comment_list',
    summary: 'List comments for a model',
    description:
      'Retrieves a paginated list of comments for the specified model.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the model\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of comments per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- userId: UUID of the user\n' +
      '- username: Username of the user\n' +
      '- modelId: UUID of the model\n',
  })
  @ApiOkResponse({
    type: ModelCommentDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated list of comments for the model.',
  })
  @ApiQuery({
    type: ModelCommentSearchRequest,
    description: 'Query parameters for searching and paginating comments.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the model to retrieve comments for.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - Filter parameters must be valid values.
      - \`userId\`: Must be a valid UUID.
      - \`username\`: Must be a valid username.
      - \`modelId\`: Must be a valid UUID.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User does not have access to this model.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @UsePipes(new ValidationPipe())
  async find(
    @Query() query: ModelCommentSearchRequest,
    @Res() res: Response,
    @Param('id', new ParseUUIDPipe()) id: string,
    @Request() request,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;
    const filters = this.validateSearchFilters(inputFilters, id);

    const entities = await this.modelCommentProvider.findBy(
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    const totalCount = await this.modelCommentProvider.countBy(filters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultiple(entities, request.user?.id));
  }

  @Post(':id/comments')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'model_comment_create',
    summary: 'Add a comment to a model',
    description:
      'Adds a new comment to the specified model.\n\n' +
      'Required Query Parameters:\n' +
      '- id: UUID of the model\n' +
      'Required Body Parameters:\n' +
      '- comment: Content of the comment\n',
  })
  @ApiBody({
    type: ModelCommentRequest,
    description: 'Comment creation parameters.',
  })
  @ApiNoContentResponse({ description: 'Comment added successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - \`comment\`: Must be a non-empty string.
      - Invalid or unavailable parameters.
      - Failed to analyze request body.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User does not have permission to comment.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiParam({ name: 'id', description: 'UUID of the model to comment on.' })
  async comment(
    @Body() requestBody: ModelCommentRequest,
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ) {
    const entity = await this.modelProvider.getBy({ id: id });
    await this.requestManager.comment(entity, request.user.id, requestBody);
  }

  @Delete(':modelId/comments/:commentId')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'model_comment_delete',
    summary: 'Delete a comment from a model',
    description:
      'Deletes the specified comment from the model if the user is the author.\n\n' +
      'Required Parameters:\n' +
      '- modelId: UUID of the model\n' +
      '- commentId: UUID of the comment to delete\n',
  })
  @ApiNoContentResponse({ description: 'Comment deleted successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`modelId\`: Must be a valid UUID.
      - \`commentId\`: Must be a valid UUID.
      - Invalid or unavailable parameters.
      - Failed to analyze request.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description:
      'Forbidden. User does not have permission to delete this comment.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiParam({ name: 'modelId', description: 'UUID of the model.' })
  @ApiParam({
    name: 'commentId',
    description: 'UUID of the comment to delete.',
  })
  async delete(
    @Request() request,
    @Param('modelId', new ParseUUIDPipe()) modelId: string,
    @Param('commentId', new ParseUUIDPipe()) commentId: string,
  ) {
    const entity = await this.modelCommentProvider.getBy({
      id: commentId,
      modelId: modelId,
      userId: request.user.id,
    });
    const entityModel = await this.modelProvider.getBy({ id: modelId });

    await this.requestManager.delete(entity, entityModel);
  }

  validateSearchFilters(
    filters: ModelCommentSearchRequest,
    modelId: string,
  ): any {
    return { ...filters, modelId: modelId };
  }
}
