import {
  Controller,
  Get,
  Param,
  Query,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiOkResponse,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiOperation,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { UpscaleDto } from '../../dt../../dto/upscale.dto';
import { UpscaleSearchRequest } from '../../dto/upscale.search-request';
import { UpscaleProvider } from '../../service/provider';
import { UpscaleResponseMapper } from '../../service/response-mapper';

@ApiTags('upscale / internal')
@Controller('internal/upscales')
export class ReadController {
  constructor(
    private provider: UpscaleProvider,
    private responseMapper: UpscaleResponseMapper,
  ) {}

  @ApiOperation({
    operationId: 'upscale_internal_list',
    summary: 'List upscales (internal)',
    description:
      'Retrieves a paginated list of upscales for internal use.' +
      '\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of upscales per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- status: Filter by status\n' +
      '- userId: Filter by user ID\n' +
      '- username: Filter by username\n' +
      '- imageCompletionId: Filter by source image ID\n' +
      '- onlyFollowing: Show only upscales from followed users\n',
  })
  @ApiOkResponse({
    type: Array<UpscaleDto>,
    description: 'Paginated list of upscales.',
  })
  @ApiQuery({
    type: UpscaleSearchRequest,
    description: 'Query parameters for searching and paginating upscales.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - \`status\`: Must be a valid value from the \`StatusEnum\`.
      - \`userId\`: Must be a valid UUID.
      - \`username\`: Must be a valid username.
      - \`imageCompletionId\`: Must be a valid UUID.
      - \`onlyFollowing\`: Must be a valid boolean value.
      - Filter parameters must be valid values.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Get()
  @UsePipes(new ValidationPipe())
  async find(
    @Query() query: UpscaleSearchRequest,
    @Res() res: Response,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;

    const entities = await this.provider.findBy(
      inputFilters,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    const totalCount = await this.provider.countBy(inputFilters);
    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultipleInternal(entities));
  }

  @ApiOperation({
    operationId: 'upscale_internal_get',
    summary: 'Get upscale by ID (internal)',
    description:
      'Retrieves the details of a specific upscale by its unique identifier for internal use.' +
      '\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the upscale\n',
  })
  @ApiOkResponse({ type: UpscaleDto, description: 'Upscale details.' })
  @ApiParam({
    name: 'id',
    description: 'The unique identifier of the upscale',
    type: 'string',
    format: 'uuid',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - Upscale does not exist.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiNotFoundResponse({
    description: 'Not Found. The upscale could not be found.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Get(':id')
  async get(@Param() params): Promise<UpscaleDto> {
    return this.provider
      .get(params.id)
      .then((entity) => this.responseMapper.mapInternal(entity));
  }
}
