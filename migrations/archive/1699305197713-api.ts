import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1699305197713 implements MigrationInterface {
    name = 'Api1699305197713'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "stripe_customer_id" character varying
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "stripe_customer_id"
        `);
    }

}
