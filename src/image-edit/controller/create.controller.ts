import { Body, Controller, Post, Request } from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { ImageEditDto } from '../dto/image-edit.dto';
import { ImageEditRequest } from '../dto/image-edit.request';
import { ImageEditRequestManager } from '../service/request-manager';
import { ImageEditResponseMapper } from '../service/response-mapper';

@ApiTags('image-edit')
@Controller('image-edits')
export class CreateController {
  constructor(
    private readonly requestManager: ImageEditRequestManager,
    private readonly responseMapper: ImageEditResponseMapper,
  ) {}

  @Post()
  @ApiOperation({
    operationId: 'image_edit_create',
    summary: 'Create a new image edit',
    description:
      'Creates a new image edit for the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- mode: Edit mode, either "in", "out", "skin", or "context"\n\n' +
      'Optional Parameters:\n' +
      '- originalImageCompletionId: UUID of the original image completion\n' +
      '- width: Width of the edited image\n' +
      '- height: Height of the edited image\n' +
      '- imageCompletionsCount: Number of image completions to generate\n' +
      '- prompt: Prompt for the image edit\n' +
      '- mask: Mask for the image edit\n' +
      '- inputImageUrl: Input image URL for the image edit\n' +
      '- settings: Settings for the image edit\n' +
      '- webhookUrl: Webhook URL for the image edit\n' +
      '- hidePrompt: Hide the prompt from the image edit\n' +
      '- organizationId: UUID of the organization to deduct credits from\n\n' +
      'Credit Deduction:\n' +
      '- If organizationId is provided, credits will be deducted from the organization account\n' +
      '- If organizationId is not provided, credits will be deducted from the user account\n' +
      '- User must be a member of the organization to use organization credits\n\n' +
      'Monitor the edit status using the GET /image-edits/{id} endpoint.',
  })
  @ApiBody({
    type: ImageEditRequest,
    description: 'Image edit creation parameters.',
  })
  @ApiOkResponse({
    type: ImageEditDto,
    description: 'Image edit created successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`mode\`: Must be a valid value from the \`EditMode\` enum.
      - \`originalImageCompletionId\`: Must be a valid UUID.
      - \`width\`: Must be a valid number between 480 and 2160.
      - \`height\`: Must be a valid number between 480 and 2160.
      - \`imageCompletionsCount\`: Must be a valid number between 1 and 5.
      - \`prompt\`: Must be a valid string.
      - \`mask\`: Must be a valid string.
      - \`inputImageUrl\`: Must be a valid URL.
      - \`settings\`: Must be a valid object.
      - \`webhookUrl\`: Must be a valid URL.
      - \`hidePrompt\`: Must be a valid boolean.
      - \`organizationId\`: Must be a valid UUID.
      - Invalid or unavailable parameters.
      - Failed to analyze request body.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiInternalServerErrorResponse({
    description:
      'Internal Server Error. Unexpected error occurred during image edit creation.',
  })
  async create(
    @Body() requestBody: ImageEditRequest,
    @Request() request,
  ): Promise<ImageEditDto> {
    const user = request.user;
    const entity = await this.requestManager.create(requestBody, user);

    return await this.responseMapper.map(entity);
  }
}
