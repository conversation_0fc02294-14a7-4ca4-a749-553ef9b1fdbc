import { Injectable } from '@nestjs/common';
import { ImageCompletionProvider } from 'src/image-completion/service/provider';
import { ModelProvider } from 'src/model/service/provider';
import { UserProvider } from 'src/user/service/provider';

@Injectable()
export class StatisticsProvider {
  constructor(
    private imageCompletionProvider: ImageCompletionProvider,
    private userProvider: UserProvider,
    private modelProvider: ModelProvider,
  ) {}

  async findStatistics(initialDate: Date, endDate: Date): Promise<any> {
    const statistics = {
      images: await this.imageCompletionProvider.countImageStatistics(
        initialDate,
        endDate,
      ),
      models: await this.modelProvider.countModelStatistics(
        initialDate,
        endDate,
      ),
      users: await this.userProvider.countUserStatistics(initialDate, endDate),
      activeUsers: await this.imageCompletionProvider.countActiveUserStatistics(
        initialDate,
        endDate,
      ),
    };

    return statistics;
  }

  async findYearlyStatistics(year: number): Promise<any> {
    const images = await this.imageCompletionProvider.countYearlyStatistics(
      year,
    );
    const models = await this.modelProvider.countYearlyStatistics(year);
    const users = await this.userProvider.countYearlyStatistics(year);
    const activeUsers =
      await this.imageCompletionProvider.countYearlyActiveUsers(year);

    const statistics = {
      images,
      models,
      users,
      activeUsers,
    };

    return statistics;
  }

  async findMonthlyStatistics(year: number, month: number): Promise<any> {
    const images = await this.imageCompletionProvider.countMonthlyStatistics(
      year,
      month,
    );
    const models = await this.modelProvider.countMonthlyStatistics(year, month);
    const users = await this.userProvider.countMonthlyStatistics(year, month);
    const activeUsers =
      await this.imageCompletionProvider.countMonthlyActiveUsers(year, month);

    return { images, models, users, activeUsers };
  }
}
