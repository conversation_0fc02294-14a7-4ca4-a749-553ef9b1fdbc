import { Injectable } from '@nestjs/common';
import { TopModelUserDto } from '../dto/top-model-users.dto';

@Injectable()
export class TopModelUsersResponseMapper {
  mapMultiple(data: any[]): TopModelUserDto[] {
    return data.map((item) => this.map(item));
  }

  map(data: any): TopModelUserDto {
    const dto = new TopModelUserDto();
    dto.userId = data.userId;
    dto.username = data.username;
    dto.email = data.email;
    dto.modelCount = data.modelCount;
    dto.publicModels = data.publicModels;
    dto.privateModels = data.privateModels;

    return dto;
  }
}
