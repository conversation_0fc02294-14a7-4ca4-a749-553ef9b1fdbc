import { <PERSON>, <PERSON>, Param, Request } from '@nestjs/common';
import { Body } from '@nestjs/common/decorators/http/route-params.decorator';
import { UserExternalProfileProvider } from '../service/provider';
import { UserExternalProfileDto } from '../dto/user-external-profile.dto';
import { UserExternalProfileRequestManager } from '../service/request-manager';
import { UserExternalProfileResponseMapper } from '../service/response-mapper';
import { UserExternalProfileRequest } from '../request/user-external-profile.request';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiNotFoundResponse,
  ApiInternalServerErrorResponse,
  ApiParam,
} from '@nestjs/swagger';

@ApiTags('user')
@Controller('users/current/external-profiles')
export class UpdateController {
  constructor(
    private provider: UserExternalProfileProvider,
    private requestManager: UserExternalProfileRequestManager,
    private responseMapper: UserExternalProfileResponseMapper,
  ) {}

  @Patch(':id')
  @ApiOperation({
    operationId: 'user_external_profile_update',
    summary: 'Update external profile for current user',
    description:
      'Updates an external profile for the current authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- id: External profile ID\n' +
      'Optional Body Parameters:\n' +
      '- externalId: External profile ID\n' +
      '- platform: External provider name\n' +
      '- username: External profile username\n' +
      '- profileData: External profile data\n',
  })
  @ApiBody({ type: UserExternalProfileRequest })
  @ApiOkResponse({
    type: UserExternalProfileDto,
    description: 'External profile updated successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - \`externalId\`: Must be a valid string.
      - \`platform\`: Must be a valid string.
      - \`username\`: Must be a valid string.
      - \`profileData\`: Must be a valid object.
      - External profile does not exist.
      - Invalid or unavailable parameters.
  `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiNotFoundResponse({
    description: 'Not Found. The external profile could not be found.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiParam({ name: 'id', description: 'External Profile ID' })
  async update(
    @Body() requestBody: UserExternalProfileRequest,
    @Request() request,
    @Param() params,
  ): Promise<UserExternalProfileDto> {
    return this.provider
      .getBy({ user: { id: request.user.id }, id: params.id })
      .then((entity) => this.requestManager.update(entity, requestBody))
      .then((entity) => this.responseMapper.map(entity));
  }
}
