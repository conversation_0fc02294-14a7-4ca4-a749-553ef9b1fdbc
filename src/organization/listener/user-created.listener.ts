import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { UserCreatedEvent } from 'src/user/event/user-created.event';
import { UserProvider } from 'src/user/service/provider';
import { OrganizationUserEntity } from '../entity/organization-user.entity';
import { OrganizationUserManager } from '../service/organization-user.manager';
import { OrganizationUserProvider } from '../service/organization-user.provider';
import { OrganizationProvider } from '../service/provider';
import { OrganizationInviteProvider } from '../service/organization-invite.provider';
import { OrganizationInviteManager } from '../service/organization-invite.manager';
import { StatusEnum } from '../enum/organization-user.enum';

@Injectable()
export class UserCreatedListener {
  constructor(
    private organizationUserManager: OrganizationUserManager,
    private organizationInviteManager: OrganizationInviteManager,
    private organizationInviteProvider: OrganizationInviteProvider,
    private organizationUserProvider: OrganizationUserProvider,
    private organizationProvider: OrganizationProvider,
    private userProvider: UserProvider,
  ) {}

  @OnEvent('user.created')
  async handleUserCreatedEvent(event: UserCreatedEvent) {
    const user = await this.userProvider.get(event.id);

    if (
      (await this.organizationInviteProvider.countBy({
        email: user.email,
        status: StatusEnum.PENDING,
      })) > 0
    ) {
      const organizationInvites = await this.organizationInviteProvider.findBy(
        {
          email: user.email,
          status: StatusEnum.PENDING,
        },
        1,
        999,
      );

      for (const organizationInvite of organizationInvites) {
        organizationInvite.user = user;
        organizationInvite.userId = user.id;
        await this.organizationInviteManager.update(organizationInvite);
      }
    }

    const organizations = await this.organizationProvider.findBy(
      {
        isDefault: true,
      },
      1,
      999,
    );

    for (const organization of organizations) {
      const isMember = await this.organizationUserProvider.isMember(
        user.id,
        organization.id,
      );

      if (isMember) {
        continue;
      }

      const organizationUser = new OrganizationUserEntity();
      organizationUser.organizationId = organization.id;
      organizationUser.userId = user.id;

      await this.organizationUserManager.create(organizationUser);
    }
  }
}
