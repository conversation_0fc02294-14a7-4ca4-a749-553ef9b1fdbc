import {
  <PERSON>,
  Get,
  Param,
  ParseUUIDPipe,
  Query,
  Request,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiOkResponse,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import { BaseFindResponseHeadersDto } from 'src/core/dto/base-find-response-headers.dto';
import { AuthOptional } from 'src/core/security/public-routes';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { UpscaleDto } from '../dto/upscale.dto';
import { UpscaleSearchRequest } from '../dto/upscale.search-request';
import { UpscaleProvider } from '../service/provider';
import { UpscaleResponseMapper } from '../service/response-mapper';

@ApiTags('upscale')
@Controller('upscales')
export class ReadController {
  constructor(
    private provider: UpscaleProvider,
    private responseMapper: UpscaleResponseMapper,
  ) {}

  @ApiOperation({
    operationId: 'upscale_list',
    summary: 'List upscales',
    description:
      'Retrieves a paginated list of upscales for the current user.\n\n' +
      'Requirements:\n' +
      '- User must be authenticated' +
      '\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of upscales per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- status: Filter by status\n' +
      '- imageCompletionId: Filter by source image ID\n' +
      '- onlyFollowing: Show only upscales from followed users\n' +
      '- username: Filter by username\n' +
      '- userId: Filter by user ID\n',
  })
  @ApiOkResponse({
    type: UpscaleDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated list of upscales.',
  })
  @ApiQuery({
    type: UpscaleSearchRequest,
    description: 'Query parameters for searching and paginating upscales.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - Filter parameters must be valid values.
      - \`status\`: Must be a valid value from the \`StatusEnum\`.
      - \`imageCompletionId\`: Must be a valid UUID.
      - \`onlyFollowing\`: Must be a valid boolean value.
      - \`username\`: Must be a valid username.
      - \`userId\`: Must be a valid UUID.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Get()
  @UsePipes(new ValidationPipe())
  async find(
    @Request() request,
    @Query() query: UpscaleSearchRequest,
    @Res() res: Response,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;
    const filters = this.validateSearchFilters(inputFilters, request.user.id);

    const entities = await this.provider.findBy(
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    const totalCount = await this.provider.countBy(filters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultiple(entities));
  }

  @ApiOperation({
    operationId: 'upscale_get',
    summary: 'Get upscale by ID',
    description:
      'Retrieves the details of a specific upscale by its unique identifier.',
  })
  @ApiOkResponse({ type: UpscaleDto, description: 'Upscale details.' })
  @ApiParam({
    name: 'id',
    description: 'The unique identifier of the upscale',
    type: 'string',
    format: 'uuid',
  })
  @ApiBadRequestResponse({ description: 'Bad Request. Invalid upscale ID.' })
  @ApiNotFoundResponse({
    description: 'Not Found. The upscale could not be found.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Get(':id')
  @AuthOptional()
  async get(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<UpscaleDto> {
    return this.provider
      .getBy({ id: id })
      .then((entity) => this.responseMapper.map(entity, true));
  }

  validateSearchFilters(
    filters: UpscaleSearchRequest,
    currentUserId: string,
  ): any {
    const criteria = {
      followerId: null,
      ...filters,
    };

    if (filters.onlyFollowing) {
      criteria.followerId = currentUserId;
    }

    return criteria;
  }
}
