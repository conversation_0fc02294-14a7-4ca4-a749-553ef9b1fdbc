import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ImageCompletionLikeEntity } from '../entity/image-completion-like.entity';
import { ImageCompletionEntity } from '../entity/image-completion.entity';
import { ImageLikedEvent } from '../event/image-liked.event';
import { ImageCompletionManager } from './manager';
import { Notifier } from 'src/notification/service/notifier';
import { ImageLikedNotification } from '../notification/image-liked.notification';
import { UserResponseMapper } from 'src/user/service/response-mapper';
import { ImageCompletionResponseMapper } from './response-mapper';
import { UserProvider } from 'src/user/service/provider';
import { LocalCacheService } from 'src/core/service/local-cache.service';

@Injectable()
export class ImageCompletionLikeManager {
  constructor(
    @InjectRepository(ImageCompletionLikeEntity)
    private repository: Repository<ImageCompletionLikeEntity>,
    private imageCompletionManager: ImageCompletionManager,
    private eventEmitter: EventEmitter2,
    private notifier: Notifier,
    private userResponseMapper: UserResponseMapper,
    private imageResponseMapper: ImageCompletionResponseMapper,
    private userProvider: UserProvider,
  ) {}

  async like(
    imageCompletion: ImageCompletionEntity,
    userId: string,
  ): Promise<void> {
    const imageCompletionLike = new ImageCompletionLikeEntity();
    imageCompletionLike.imageCompletion = imageCompletion;
    imageCompletionLike.imageCompletionId = imageCompletion.id;
    imageCompletionLike.userId = userId;

    await this.repository.save(imageCompletionLike);

    // Clear the cache for this specific count
    const cacheKey = `count:${JSON.stringify({
      imageCompletionId: imageCompletion.id,
      userId: userId,
    })}`;
    LocalCacheService.setCache(cacheKey, null); // Invalidate specific cache key

    imageCompletion.likes++;
    await this.imageCompletionManager.save(imageCompletion);

    const user = await this.userProvider.get(userId);

    if (imageCompletion.userId != imageCompletionLike.userId) {
      await this.notifier.dispatch(
        new ImageLikedNotification(imageCompletion.userId, {
          id: imageCompletion.id,
          thumbnail:
            this.imageResponseMapper.generateThumbnailUrl(imageCompletion),
          userId: imageCompletion.userId,
          likedById: imageCompletionLike.userId,
          likedByUsername: user.username,
          likedByThumbnail: this.userResponseMapper.mapProfilePicture(user),
          likedAt: imageCompletionLike.createdAt,
        }),
      );
    }

    await this.eventEmitter.emit(
      'image.liked',
      new ImageLikedEvent({
        id: imageCompletionLike.id,
        imageCompletionId: imageCompletion.id,
        userId: userId,
        ownerId: imageCompletion.userId,
      }),
    );
  }

  async unlike(imageCompletionLike: ImageCompletionLikeEntity): Promise<void> {
    const imageCompletion = imageCompletionLike.imageCompletion;

    await this.repository.softRemove(imageCompletionLike);

    const cacheKey = `count:${JSON.stringify({
      imageCompletionId: imageCompletion.id,
      userId: imageCompletionLike.userId,
    })}`;
    LocalCacheService.setCache(cacheKey, null); // Invalidate specific cache key

    imageCompletion.likes--;
    await this.imageCompletionManager.save(imageCompletion);
  }
}
