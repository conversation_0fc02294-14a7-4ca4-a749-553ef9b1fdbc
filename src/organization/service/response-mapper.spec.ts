import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { UserProvider } from 'src/user/service/provider';
import { UserResponseMapper } from 'src/user/service/response-mapper';
import { OrganizationDto } from '../dto/organization.dto';
import { PublicOrganizationDto } from '../dto/public.organization.dto';
import { OrganizationEntity } from '../entity/organization.entity';
import { OrganizationUserProvider } from './organization-user.provider';
import { OrganizationResponseMapper } from './response-mapper';

describe('OrganizationResponseMapper', () => {
  let responseMapper: OrganizationResponseMapper;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrganizationResponseMapper,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue('https://cdn.example.com'),
          },
        },
        {
          provide: OrganizationUserProvider,
          useValue: {
            isManager: jest.fn().mockResolvedValue(false),
          },
        },
        {
          provide: UserProvider,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: UserResponseMapper,
          useValue: {
            mapPublic: jest.fn(),
          },
        },
      ],
    }).compile();

    responseMapper = module.get<OrganizationResponseMapper>(
      OrganizationResponseMapper,
    );
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(responseMapper).toBeDefined();
  });

  describe('map', () => {
    it('should map OrganizationEntity to OrganizationDto', async () => {
      const organization = new OrganizationEntity();
      organization.id = '1';
      organization.name = 'Test Org';
      organization.description = 'Test Description';

      const result = await responseMapper.map(organization);

      expect(result).toBeInstanceOf(OrganizationDto);
      expect(result.id).toBe(organization.id);
      expect(result.name).toBe(organization.name);
      expect(result.description).toBe(organization.description);
    });
  });

  describe('mapPublic', () => {
    it('should map OrganizationEntity to PublicOrganizationDto', () => {
      const organization = new OrganizationEntity();
      organization.id = '1';
      organization.name = 'Test Org';
      organization.description = 'Test Description';

      const result = responseMapper.mapPublic(organization);

      expect(result).toBeInstanceOf(PublicOrganizationDto);
      expect(result.id).toBe(organization.id);
      expect(result.name).toBe(organization.name);
      expect(result.description).toBe(organization.description);
    });
  });

  // Add more test cases for other methods in OrganizationResponseMapper
});
