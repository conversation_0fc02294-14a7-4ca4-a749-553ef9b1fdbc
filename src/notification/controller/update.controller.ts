import {
  Controller,
  Delete,
  HttpCode,
  Param,
  ParseUUI<PERSON>ipe,
  Put,
  Request,
} from '@nestjs/common';
import {
  ApiNoContentResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
  ApiBadRequestResponse,
} from '@nestjs/swagger';
import { NotificationProvider } from '../service/provider';
import { NotificationManager } from '../service/manager';
import { IsNull } from 'typeorm';

@ApiTags('notifications')
@Controller('users/current/notifications')
export class UpdateController {
  constructor(
    private provider: NotificationProvider,
    private manager: NotificationManager,
  ) {}

  @ApiOperation({
    operationId: 'notifications_mark_all_as_read',
    summary: 'Mark all notifications as read',
    description:
      'Marks all unread notifications as read for the current user.\n\n' +
      'Requirements:\n' +
      '- User must be authenticated\n' +
      '- Only unread notifications will be affected',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - Invalid or unavailable parameters.
    `,
  })
  @ApiNoContentResponse({ description: 'All notifications marked as read.' })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiNotFoundResponse({ description: 'No unread notifications found.' })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. An unexpected error occurred.',
  })
  @Put('all/read')
  @HttpCode(204)
  async markAllAsRead(@Request() request) {
    const entities = await this.provider.findBy(
      {
        user: { id: request.user.id },
        readAt: IsNull(),
      },
      1,
      99999,
    );

    await Promise.all(
      entities.map((entity) => this.manager.markAsRead(entity)),
    );
  }

  @ApiOperation({
    operationId: 'notifications_mark_as_read',
    summary: 'Mark a notification as read',
    description:
      'Marks a specific notification as read for the current user.\n\n' +
      'Requirements:\n' +
      '- User must be authenticated\n' +
      '- Notification must exist and belong to the user',
  })
  @ApiParam({
    name: 'id',
    description: 'The unique identifier of the notification to mark as read',
    type: 'string',
    format: 'uuid',
  })
  @ApiNoContentResponse({ description: 'Notification marked as read.' })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiNotFoundResponse({ description: 'Notification not found.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
    `,
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. An unexpected error occurred.',
  })
  @Put(':id/read')
  @HttpCode(204)
  async markAsRead(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ) {
    const entity = await this.provider.getBy({
      user: { id: request.user.id },
      id: id,
    });

    await this.manager.markAsRead(entity);
  }

  @ApiOperation({
    operationId: 'notifications_mark_as_unread',
    summary: 'Mark a notification as unread',
    description:
      'Marks a specific notification as unread for the current user.\n\n' +
      'Requirements:\n' +
      '- User must be authenticated\n' +
      '- Notification must exist and belong to the user',
  })
  @ApiParam({
    name: 'id',
    description: 'The unique identifier of the notification to mark as unread',
    type: 'string',
    format: 'uuid',
  })
  @ApiNoContentResponse({ description: 'Notification marked as unread.' })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiNotFoundResponse({ description: 'Notification not found.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
    `,
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. An unexpected error occurred.',
  })
  @Delete(':id/read')
  @HttpCode(204)
  async markAsUnread(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ) {
    const entity = await this.provider.getBy({
      user: { id: request.user.id },
      id: id,
    });

    await this.manager.markAsUnread(entity);
  }
}
