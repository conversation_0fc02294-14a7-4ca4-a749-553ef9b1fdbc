import { GetObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { UpscaleEntity } from '../entity/upscale.entity';

@Injectable()
export class AssetManager {
  constructor(
    private configService: ConfigService,
    @Inject('S3') private s3: S3Client,
  ) {}

  generateStorageBucket(entity: UpscaleEntity): void {
    entity.storageBucket = this.configService.get<string>(
      'UPSCALE_S3_BUCKET_NAME',
    );
  }

  generateStoragePath(entity: UpscaleEntity): void {
    if (!entity.storageBucket) {
      this.generateStorageBucket(entity);
    }
    entity.storagePath = `${entity.user.id}/${entity.id}`;
  }

  async getObject(entity: UpscaleEntity, key: string): Promise<any> {
    const command = new GetObjectCommand({
      Bucket: entity.storageBucket,
      Key: key,
    });

    return await this.s3.send(command);
  }
}
