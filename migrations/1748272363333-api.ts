import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1748272363333 implements MigrationInterface {
  name = 'Api1748272363333';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "hide_prompt" boolean NOT NULL DEFAULT false
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion"
            ADD "hide_prompt" boolean NOT NULL DEFAULT false
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "image_completion" DROP COLUMN "hide_prompt"
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "hide_prompt"
        `);
  }
}
