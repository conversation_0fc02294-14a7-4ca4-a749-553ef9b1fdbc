import { SQSClient, SendMessageCommand } from '@aws-sdk/client-sqs';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { ImageCompletionEntity } from 'src/image-completion/entity/image-completion.entity';
import { ImageCompletionResponseMapper } from 'src/image-completion/service/response-mapper';
import { Notifier } from 'src/notification/service/notifier';
import { CreditTypeEnum } from 'src/subscription/entity/credit-type.enum';
import { TransactionTypeEnum } from 'src/subscription/entity/transaction-type.enum';
import { TransactionManager } from 'src/subscription/service/transaction.manager';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { StatusEnum, UpscaleEntity } from '../entity/upscale.entity';
import { UpscaleGeneratedEvent } from '../event/upscale-generated.event';
import { UpscaleGeneratedNotification } from '../notification/upscale-generated.notification';
import { AssetManager } from './asset.manager';
import { UpscaleResponseMapper } from './response-mapper';
import axios from 'axios';
import { Logger } from 'nestjs-pino';

@Injectable()
export class UpscaleManager {
  private CREDITS_PRICE = 20;

  constructor(
    @InjectRepository(UpscaleEntity)
    private repository: Repository<UpscaleEntity>,
    private configService: ConfigService,
    private assetManager: AssetManager,
    private transactionManager: TransactionManager,
    private responseMapper: UpscaleResponseMapper,
    private eventEmitter: EventEmitter2,
    private notifier: Notifier,
    private imageCompletionResponseMapper: ImageCompletionResponseMapper,
    private logger: Logger,
    @Inject('SQS') private sqs: SQSClient,
  ) {}

  async create(entity: UpscaleEntity): Promise<UpscaleEntity> {
    entity.id = uuidv4();
    entity.generationSeconds = this.CREDITS_PRICE;

    this.assetManager.generateStoragePath(entity);

    await this.save(entity);

    if (!entity.user.isBot) {
      try {
        await this.registerTransaction(entity);
      } catch (e) {
        entity.status = StatusEnum.FAILED;
        await this.save(entity);

        throw e;
      }
    }

    await this.writeToQueue(entity);

    return entity;
  }

  async registerTransaction(entity: UpscaleEntity) {
    const billedSeconds = this.CREDITS_PRICE;

    await this.transactionManager.register(
      TransactionTypeEnum.SPENDING,
      billedSeconds,
      CreditTypeEnum.IMAGE,
      entity.id,
      true,
      entity.userId,
      entity.organizationId,
    );
  }

  getQueueUrl(entity: UpscaleEntity): string {
    return this.configService.get<string>('UPSCALE_SQS_QUEUE_URL');
  }

  async writeToQueue(entity: UpscaleEntity) {
    const queueUrl = this.getQueueUrl(entity);

    const sendMessageCommand = new SendMessageCommand({
      QueueUrl: queueUrl,
      MessageBody: JSON.stringify(
        await this.responseMapper.mapInternal(entity),
      ),
    });

    await this.sqs.send(sendMessageCommand);
  }

  async update(entity: UpscaleEntity): Promise<UpscaleEntity> {
    let thumbnailUrl: string = null;
    let imageCompletion: ImageCompletionEntity = null;

    if (entity.imageCompletionId) {
      imageCompletion = await entity.imageCompletion;

      thumbnailUrl =
        this.imageCompletionResponseMapper.generateThumbnailUrl(
          imageCompletion,
        );
    } else {
      thumbnailUrl = entity.imageUrl;
    }

    if (entity.status === StatusEnum.READY) {
      const notificationContext = {
        id: entity.id,
        userId: entity.userId,
        username: entity.user.username,
        imageCompletionId: entity.imageCompletionId,
        originalImageThumbnail: thumbnailUrl,
        generatedAt: new Date(),
        imageVersions: imageCompletion
          ? this.responseMapper.generateImageVersions(
              imageCompletion.imagePaths[0],
            )
          : { original: entity.imageUrl },
        upscaledImageUrl: this.responseMapper.generateImageVersions(
          entity.imagePath,
        ).original,
      };

      await this.notifier.dispatch(
        new UpscaleGeneratedNotification(entity.userId, notificationContext),
      );

      if (imageCompletion && entity.userId != imageCompletion.userId) {
        await this.notifier.dispatch(
          new UpscaleGeneratedNotification(
            imageCompletion.userId,
            notificationContext,
          ),
        );
      }

      this.eventEmitter.emit(
        'upscale.generated',
        new UpscaleGeneratedEvent({
          id: entity.id,
          userId: entity.userId,
          imageCompletionId: entity.imageCompletionId,
        }),
      );

      if (entity.webhookUrl) {
        try {
          const upscaleDto = await this.responseMapper.map(entity);
          await axios.post(entity.webhookUrl, upscaleDto, {
            headers: {
              'Content-Type': 'application/json',
            },
            timeout: 5000, // 5 second timeout
          });

          this.logger.log('Webhook notification sent successfully', {
            upscaleId: entity.id,
            webhookUrl: entity.webhookUrl,
          });
        } catch (error) {
          this.logger.error('Failed to send webhook notification', {
            upscaleId: entity.id,
            webhookUrl: entity.webhookUrl,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }
    }

    await this.save(entity);

    if (!entity.user.isBot) {
      await this.registerTransaction(entity);
    }

    return entity;
  }

  async markAsSelected(upscale: UpscaleEntity) {
    upscale.isSelected = true;

    await this.save(upscale);

    const entities = await this.repository.findBy({
      imageCompletionId: upscale.imageCompletionId,
    });

    for (const entity of entities) {
      if (entity.id !== upscale.id) {
        entity.isSelected = false;

        await this.save(entity);
      }
    }
  }

  async save(entity: UpscaleEntity) {
    await this.repository.save(entity);
  }

  async delete(entity: UpscaleEntity): Promise<void> {
    await this.repository.softDelete(entity.id);
  }
}
