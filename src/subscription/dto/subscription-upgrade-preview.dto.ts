import { ApiProperty } from '@nestjs/swagger';

export class SubscriptionUpgradePreviewDto {
  @ApiProperty({
    description: 'Current subscription ID',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  currentSubscriptionId: string;

  @ApiProperty({
    description: 'Current credit package name',
    example: 'Pro',
  })
  currentPackageName: string;

  @ApiProperty({
    description: 'Current monthly price in cents',
    example: 749,
  })
  currentPrice: number;

  @ApiProperty({
    description: 'Target credit package name',
    example: 'Pro - Yearly',
  })
  targetPackageName: string;

  @ApiProperty({
    description: 'Target yearly price in cents',
    example: 7490,
  })
  targetPrice: number;

  @ApiProperty({
    description: 'Prorated amount to be charged immediately in cents',
    example: 6741,
  })
  proratedAmount: number;

  @ApiProperty({
    description: 'Amount credited for unused time in current subscription in cents',
    example: 374,
  })
  creditedAmount: number;

  @ApiProperty({
    description: 'Net amount to be charged in cents',
    example: 6367,
  })
  netAmount: number;

  @ApiProperty({
    description: 'Whether this is an upgrade from monthly to yearly',
    example: true,
  })
  isYearlyUpgrade: boolean;

  @ApiProperty({
    description: 'Next billing date for the yearly subscription',
    example: '2025-01-01T00:00:00.000Z',
  })
  nextBillingDate: Date;

  @ApiProperty({
    description: 'Savings compared to 12 monthly payments in cents',
    example: 498,
  })
  yearlySavings: number;

  @ApiProperty({
    description: 'Whether the upgrade is allowed',
    example: true,
  })
  upgradeAllowed: boolean;

  @ApiProperty({
    description: 'Reason if upgrade is not allowed',
    example: null,
    required: false,
  })
  upgradeBlockedReason?: string;

  @ApiProperty({
    description: 'Applied voucher/discount code',
    example: 'SAVE20',
    required: false,
  })
  appliedVoucherCode?: string;

  @ApiProperty({
    description: 'Discount amount from voucher in cents',
    example: 1498,
    required: false,
  })
  voucherDiscount?: number;

  @ApiProperty({
    description: 'Net amount after all discounts (proration + voucher) in cents',
    example: 4869,
  })
  finalAmount: number;
}
