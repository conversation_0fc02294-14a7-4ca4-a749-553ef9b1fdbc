import { Injectable } from '@nestjs/common';
import { OrganizationInviteEntity } from '../entity/organization-invite.entity';
import { UserResponseMapper } from 'src/user/service/response-mapper';
import { OrganizationResponseMapper } from './response-mapper';
import { OrganizationProvider } from './provider';
import { UserProvider } from 'src/user/service/provider';
import { OrganizationInviteDto } from '../dto/organization-invite.dto';

@Injectable()
export class OrganizationInviteResponseMapper {
  constructor(
    private userResponseMapper: UserResponseMapper,
    private organizationResponseMapper: OrganizationResponseMapper,
    private organizationProvider: OrganizationProvider,
    private userProvider: UserProvider,
  ) {}
  async map(organizationInvite: OrganizationInviteEntity) {
    const organization =
      organizationInvite.organization ??
      (await this.organizationProvider.get(organizationInvite.organizationId));
    const invitedBy = organizationInvite.invitedBy
      ? this.userResponseMapper.mapPublic(
          await this.userProvider.get(organizationInvite.invitedBy),
        )
      : null;

    const dto = {
      id: organizationInvite.id,
      organization: await this.organizationResponseMapper.map(organization),
      isAdmin: organizationInvite.isAdmin,
      status: organizationInvite.status,
      invitedBy,
      invitedAt: organizationInvite.invitedAt,
      acceptedAt: organizationInvite.acceptedAt,
      createdAt: organizationInvite.createdAt,
      user: this.userResponseMapper.mapPublic(organizationInvite.user),
    } as Partial<OrganizationInviteDto>;

    return dto as OrganizationInviteDto;
  }

  async mapMultiple(
    organizationInviteEntities: OrganizationInviteEntity[],
  ): Promise<OrganizationInviteDto[]> {
    return Promise.all(
      organizationInviteEntities.map((organizationInviteEntity) =>
        this.map(organizationInviteEntity),
      ),
    );
  }
}
