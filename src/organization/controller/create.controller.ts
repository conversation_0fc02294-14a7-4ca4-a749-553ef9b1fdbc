import { Controller, Post, Request } from '@nestjs/common';
import { Body } from '@nestjs/common/decorators/http/route-params.decorator';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiOkResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { UserProvider } from '../../user/service/provider';
import { OrganizationDto } from '../dto/organization.dto';
import { OrganizationRequest } from '../dto/organization.request';
import { OrganizationRequestManager } from '../service/request-manager';
import { OrganizationResponseMapper } from '../service/response-mapper';

@ApiTags('organization')
@Controller('organizations')
export class CreateController {
  constructor(
    private organizationMapper: OrganizationResponseMapper,
    private organizationRequestManager: OrganizationRequestManager,
    private userProvider: UserProvider,
  ) {}

  @ApiOperation({
    operationId: 'organization_create',
    summary: 'Create a new organization',
    description:
      'Creates a new organization and assigns the current user as the owner.\n\n' +
      'Requirements:\n' +
      '- User must be authenticated\n' +
      '- Organization name must be unique' +
      'Required Parameters:\n' +
      '- name: Name of the organization\n\n' +
      'Optional Parameters:\n' +
      '- description: Description of the organization\n' +
      '- website: Website of the organization\n' +
      '- handle: Unique handle for the organization\n' +
      '- currency: Currency used by the organization\n' +
      '- locale: Locale used by the organization\n' +
      '- includeWatermarks: Whether to include watermarks\n' +
      '- imageQueue: Image queue used by the organization\n' +
      '- modelQueue: Model queue used by the organization\n' +
      '- systemVersion: System version used by the organization\n\n' +
      'Returns the created organization object.',
  })
  @ApiBody({
    type: OrganizationRequest,
    description: 'Organization creation parameters.',
  })
  @ApiOkResponse({
    type: OrganizationDto,
    description: 'Organization created successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`name\`: Must be a non-empty string.
      - \`handle\`: Must be a non-empty string.
      - \`description\`: Must be a string.
      - \`website\`: Must be a valid URL.
      - \`currency\`: Must be a valid currency code.
      - \`locale\`: Must be a valid locale.
      - \`includeWatermarks\`: Must be a valid boolean.
      - \`imageQueue\`: Must be a valid queue name.
      - \`modelQueue\`: Must be a valid queue name.
      - \`systemVersion\`: Must be a valid system version.
      - Invalid or unavailable parameters.
      - Failed to analyze request body.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. An unexpected error occurred.',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict. Organization with the same name already exists.',
  })
  @Post()
  async create(
    @Body() organizationRequest: OrganizationRequest,
    @Request() req,
  ): Promise<OrganizationDto> {
    const owner = await this.userProvider.get(req.user.id);
    const organization = await this.organizationRequestManager.create(
      organizationRequest,
      owner,
    );
    return this.organizationMapper.map(organization);
  }
}
