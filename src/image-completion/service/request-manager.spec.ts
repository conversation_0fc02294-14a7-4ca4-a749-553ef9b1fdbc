import { BadRequestException, UnauthorizedException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from 'nestjs-pino';
import {
  ModelEntity,
  PrivacyEnum as ModelPrivacyEnum,
} from '../../model/entity/model.entity';
import { StatusEnum as ModelStatusEnum } from '../../model/enum/status.enum';

import { OrganizationUserProvider } from '../../organization/service/organization-user.provider';
import { PaymentRequiredException } from '../../subscription/exception/payment-required.exception';
import { UserEntity } from '../../user/entity/user.entity';
import { UserProvider } from '../../user/service/provider';
import { ImageCompletionPrivacyRequest } from '../dto/image-completion-privacy.request';
import { ImageCompletionInternalRequest } from '../dto/image-completion.internal-request';
import { ImageCompletionRequest } from '../dto/image-completion.request';
import {
  ImageCompletionEntity,
  PrivacyEnum as ImageCompletionPrivacyEnum,
  StatusEnum,
} from '../entity/image-completion.entity';
import { ImageCompletionManager } from './manager';
import { ImageCompletionProvider } from './provider';
import { ImageCompletionRequestManager } from './request-manager';

describe('ImageCompletionRequestManager', () => {
  let requestManager: ImageCompletionRequestManager;
  let imageCompletionManager: ImageCompletionManager;
  let imageCompletionProvider: ImageCompletionProvider;

  let logger: Logger;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ImageCompletionRequestManager,
        {
          provide: ImageCompletionManager,
          useValue: {
            create: jest.fn(),
            update: jest.fn(),
            save: jest.fn(),
            generateImageCompletionModels: jest.fn(),
          },
        },
        {
          provide: ImageCompletionProvider,
          useValue: {
            get: jest.fn(),
          },
        },

        {
          provide: UserProvider,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: OrganizationUserProvider,
          useValue: {
            isMember: jest.fn().mockResolvedValue(false),
          },
        },
        {
          provide: Logger,
          useValue: {
            error: jest.fn(),
            log: jest.fn(),
            warn: jest.fn(),
            debug: jest.fn(),
            verbose: jest.fn(),
            setContext: jest.fn(),
          },
        },
      ],
    }).compile();

    requestManager = module.get<ImageCompletionRequestManager>(
      ImageCompletionRequestManager,
    );
    imageCompletionManager = module.get<ImageCompletionManager>(
      ImageCompletionManager,
    );
    imageCompletionProvider = module.get<ImageCompletionProvider>(
      ImageCompletionProvider,
    );

    logger = module.get<Logger>(Logger);
  });

  const user: UserEntity = {
    id: 'user-id',
    includeWatermarks: false,
    isActive: true,
    isVerified: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  } as UserEntity;

  describe('create', () => {
    it('should throw BadRequestException if neither prompt nor imageCompletionId is provided', async () => {
      const request: ImageCompletionRequest = {
        prompt: null,
        imageCompletionId: null,
        hideFromUserProfile: false,
      };

      await expect(requestManager.create(request, user)).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should create a new ImageCompletionEntity with prompt', async () => {
      const request: ImageCompletionRequest = {
        prompt: 'Test prompt',
        imageCompletionId: null,
        hideFromUserProfile: false,
      };

      jest.spyOn(imageCompletionManager, 'create').mockResolvedValue(undefined);

      const result = await requestManager.create(request, user);

      expect(imageCompletionManager.create).toHaveBeenCalled();
      expect(result).toBeInstanceOf(ImageCompletionEntity);
    });

    it('should handle imageCompletionId and regenerate', async () => {
      const request: ImageCompletionRequest = {
        prompt: null,
        imageCompletionId: 'completion-id',
        hideFromUserProfile: false,
      };

      const existingCompletion = new ImageCompletionEntity();
      existingCompletion.prompt = 'Existing prompt';
      existingCompletion.regenerations = 0;

      jest
        .spyOn(imageCompletionProvider, 'get')
        .mockResolvedValue(existingCompletion);
      jest.spyOn(imageCompletionManager, 'create').mockResolvedValue(undefined);

      const result = await requestManager.create(request, user);

      expect(imageCompletionProvider.get).toHaveBeenCalledWith('completion-id');
      expect(imageCompletionManager.create).toHaveBeenCalled();
      expect(existingCompletion.regenerations).toBe(1);
      expect(result.regeneratedFrom).toBe(existingCompletion);
    });

    it('should throw UnauthorizedException if user does not have permission to view a model', async () => {
      const request: ImageCompletionRequest = {
        prompt: 'Test prompt with @privateModel',
        imageCompletionId: null,
        hideFromUserProfile: false,
      };

      jest
        .spyOn(imageCompletionManager, 'generateImageCompletionModels')
        .mockRejectedValue(new UnauthorizedException('You do not have permission to view the model @privateModel.'));

      await expect(requestManager.create(request, user)).rejects.toThrow(
        UnauthorizedException,
      );
    });

    it('should throw BadRequestException if model is not available', async () => {
      const request: ImageCompletionRequest = {
        prompt: 'Test prompt with @unavailableModel',
        imageCompletionId: null,
        hideFromUserProfile: false,
      };

      jest
        .spyOn(imageCompletionManager, 'generateImageCompletionModels')
        .mockRejectedValue(new BadRequestException('Model @unavailableModel is not available'));

      await expect(requestManager.create(request, user)).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should handle models in prompt and set hasPrivateModel', async () => {
      const request: ImageCompletionRequest = {
        prompt: 'Test prompt with @model1 and @model2',
        imageCompletionId: null,
        hideFromUserProfile: false,
      };

      jest.spyOn(imageCompletionManager, 'generateImageCompletionModels').mockImplementation(async (entity) => {
        entity.hasPrivateModel = true;
        entity.models = [
          { modelId: 'model1-id' },
          { modelId: 'model2-id' }
        ] as any;
      });
      jest.spyOn(imageCompletionManager, 'create').mockResolvedValue(undefined);

      const result = await requestManager.create(request, user);

      expect(result.hasPrivateModel).toBe(true);
      expect(result.models).toHaveLength(2);
    });

    it('should throw PaymentRequiredException on user_credit_balance.not_found error', async () => {
      const request: ImageCompletionRequest = {
        prompt: 'Test prompt',
        imageCompletionId: null,
        hideFromUserProfile: false,
      };

      jest
        .spyOn(imageCompletionManager, 'create')
        .mockRejectedValue(new Error('user_credit_balance.not_found'));

      await expect(requestManager.create(request, user)).rejects.toThrow(
        PaymentRequiredException,
      );
    });

    it('should log error and throw BadRequestException on other errors', async () => {
      const request: ImageCompletionRequest = {
        prompt: 'Test prompt',
        imageCompletionId: null,
        hideFromUserProfile: false,
      };

      const error = new Error('Some error');

      jest.spyOn(imageCompletionManager, 'create').mockRejectedValue(error);

      await expect(requestManager.create(request, user)).rejects.toThrow(
        BadRequestException,
      );
      expect(logger.error).toHaveBeenCalledWith(
        'Error creating image completion',
        {
          imageCompletion: expect.any(ImageCompletionEntity),
          error: error.message,
        },
      );
    });
  });

  describe('interrupt', () => {
    it('should throw BadRequestException if image is not being generated', async () => {
      const entity = new ImageCompletionEntity();
      entity.status = StatusEnum.READY;

      await expect(requestManager.interrupt(entity)).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should allow interrupting NEW status', async () => {
      const entity = new ImageCompletionEntity();
      entity.status = StatusEnum.NEW;

      jest.spyOn(imageCompletionManager, 'save').mockResolvedValue(undefined);

      await requestManager.interrupt(entity);

      expect(entity.status).toBe(StatusEnum.INTERRUPTED);
      expect(imageCompletionManager.save).toHaveBeenCalledWith(entity);
    });

    it('should update entity status to INTERRUPTED', async () => {
      const entity = new ImageCompletionEntity();
      entity.status = StatusEnum.GENERATING;

      jest.spyOn(imageCompletionManager, 'save').mockResolvedValue(undefined);

      await requestManager.interrupt(entity);

      expect(entity.status).toBe(StatusEnum.INTERRUPTED);
      expect(imageCompletionManager.save).toHaveBeenCalledWith(entity);
    });

    it('should throw PaymentRequiredException on user_credit_balance.not_found error', async () => {
      const entity = new ImageCompletionEntity();
      entity.status = StatusEnum.GENERATING;

      jest
        .spyOn(imageCompletionManager, 'save')
        .mockRejectedValue(new Error('user_credit_balance.not_found'));

      await expect(requestManager.interrupt(entity)).rejects.toThrow(
        PaymentRequiredException,
      );
    });

    it('should log error and throw BadRequestException on other errors', async () => {
      const entity = new ImageCompletionEntity();
      entity.status = StatusEnum.GENERATING;

      const error = new Error('Some error');

      jest.spyOn(imageCompletionManager, 'save').mockRejectedValue(error);

      await expect(requestManager.interrupt(entity)).rejects.toThrow(
        BadRequestException,
      );
      expect(logger.error).toHaveBeenCalledWith(
        'Error interrupting image generation',
        {
          imageCompletion: entity,
          error: error.message,
        },
      );
    });
  });

  describe('updatePrivacy', () => {
    it('should throw BadRequestException if image is not ready', async () => {
      const entity = new ImageCompletionEntity();
      entity.status = StatusEnum.GENERATING;

      const request: ImageCompletionPrivacyRequest = {
        privacy: ImageCompletionPrivacyEnum.PUBLIC,
      };

      await expect(
        requestManager.updatePrivacy(entity, request),
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException if image is NSFW or has private model', async () => {
      const entity = new ImageCompletionEntity();
      entity.status = StatusEnum.READY;
      entity.isNsfw = true;

      const request: ImageCompletionPrivacyRequest = {
        privacy: ImageCompletionPrivacyEnum.PUBLIC,
      };

      await expect(
        requestManager.updatePrivacy(entity, request),
      ).rejects.toThrow(BadRequestException);
    });

    it('should update privacy and return updated entity', async () => {
      const entity = new ImageCompletionEntity();
      entity.status = StatusEnum.READY;
      entity.isNsfw = false;
      entity.hasPrivateModel = false;

      const request: ImageCompletionPrivacyRequest = {
        privacy: ImageCompletionPrivacyEnum.PUBLIC,
      };

      jest.spyOn(imageCompletionManager, 'update').mockResolvedValue(entity);

      const result = await requestManager.updatePrivacy(entity, request);

      expect(entity.privacy).toBe(ImageCompletionPrivacyEnum.PUBLIC);
      expect(imageCompletionManager.update).toHaveBeenCalledWith(entity);
      expect(result).toBe(entity);
    });
  });

  describe('updateInternal', () => {
    it('should map internal request data and update entity', async () => {
      const entity = new ImageCompletionEntity();
      entity.status = StatusEnum.NEW;

      const request: ImageCompletionInternalRequest = {
        prompt: 'Updated prompt',
        status: StatusEnum.GENERATING,
        hideFromUserProfile: false,
      };

      jest.spyOn(imageCompletionManager, 'update').mockResolvedValue(entity);

      const result = await requestManager.updateInternal(entity, request);

      expect(entity.prompt).toBe('Updated prompt');
      expect(entity.status).toBe(StatusEnum.GENERATING);
      expect(imageCompletionManager.update).toHaveBeenCalledWith(entity);
      expect(result).toBe(entity);
    });

    it('should set previewImage to null if status is not GENERATING', async () => {
      const entity = new ImageCompletionEntity();
      entity.status = StatusEnum.NEW;
      entity.previewImage = 'some_image';

      const request: ImageCompletionInternalRequest = {
        prompt: 'Test prompt',
        status: StatusEnum.READY,
        hideFromUserProfile: false,
      };

      jest.spyOn(imageCompletionManager, 'update').mockResolvedValue(entity);

      await requestManager.updateInternal(entity, request);

      expect(entity.previewImage).toBeNull();
    });
  });
});
