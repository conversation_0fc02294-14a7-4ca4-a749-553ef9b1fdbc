import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1699303763431 implements MigrationInterface {
    name = 'Api1699303763431'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "credit_package"
            ADD "stripe_price_id" character varying
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "credit_package" DROP COLUMN "stripe_price_id"
        `);
    }

}
