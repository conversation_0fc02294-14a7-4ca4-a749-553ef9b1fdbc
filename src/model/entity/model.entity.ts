import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { UserEntity } from '../../user/entity/user.entity';
import { StatusEnum } from '../enum/status.enum';
import { ModelOrganizationEntity } from './model-organization.entity';

export enum PrivacyEnum {
  PUBLIC = 'public',
  PRIVATE = 'private',
  LICENSED = 'licensed',
}

export enum ClassEnum {
  PERSON = 'person',
  STYLE = 'style',
  OBJECT = 'object',
}

export enum SettingsEnum {
  NO_VIOLENCE = 'NO_VIOLENCE',
  NO_HATE_SPEECH = 'NO_HATE_SPEECH',
  NO_ILLEGAL_CONTENT = 'NO_ILLEGAL_CONTENT',
  NO_COPYRIGHTED_CONTENT = 'NO_COPYRIGHTED_CONTENT',
  NO_ADULT_CONTENT = 'NO_ADULT_CONTENT',
  NO_RELIGIOUS_CONTENT = 'NO_RELIGIOUS_CONTENT',
  NO_POLITICAL_CONTENT = 'NO_POLITICAL_CONTENT',
  NO_BRANDED_CONTENT = 'NO_BRANDED_CONTENT',
  NO_OTHER = 'NO_OTHER',
}

@Entity('model')
export class ModelEntity {
  static defaultSystemVersion = 3;
  static activeSystemVersions = [3];

  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => UserEntity, { eager: true })
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @Column({ type: 'uuid', nullable: true })
  @Index()
  userId: string;

  @Column({ nullable: false })
  name: string;

  @Column({ nullable: true })
  trainingMode?: string;

  @Column({ nullable: true })
  type?: string;

  @Column({ nullable: true })
  class?: string;

  @Column({
    type: 'varchar',
    enum: StatusEnum,
    default: StatusEnum.NEW,
    nullable: false,
  })
  status: StatusEnum;

  @Column({ type: 'text', nullable: true })
  statusDetail?: string;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ type: 'text', nullable: true })
  website?: string;

  @Column({ nullable: false, default: 0 })
  progress: number;

  @Column({ type: 'text', nullable: true })
  prompt_v1?: string;

  @Column({ type: 'text', nullable: true })
  prompt?: string;

  @Column({
    type: 'enum',
    enum: PrivacyEnum,
    default: PrivacyEnum.PUBLIC,
    nullable: false,
  })
  privacy: PrivacyEnum;

  @Column({ nullable: false, default: 0 })
  usages: number;

  @Column({ nullable: false, default: 0 })
  imagesAvailable: number;

  @Column({ nullable: false, default: 0 })
  likes: number;

  @Column({ nullable: false, default: 0 })
  reports: number;

  @Column({ nullable: false, default: 1 })
  version: number;

  @Column({ type: 'json', nullable: false, default: '[]' })
  systemVersions: number[];

  @Column({ type: 'text', nullable: true })
  storageBucket?: string;

  @Column({ type: 'text', nullable: true })
  storagePath?: string;

  @Column({ type: 'text', nullable: true })
  thumbnail?: string;

  @Column({ type: 'json', nullable: true })
  thumbnailOptions?: any;

  @Column({ type: 'text', nullable: true })
  generationData?: string;

  @Column({ name: 'settings', type: 'json', nullable: true })
  private _settings?: any;

  @Column({ default: false })
  isFeatured: boolean;

  @Column({ default: false })
  isSponsored: boolean;

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: 'private' })
  origin: string;

  @Column({ type: 'timestamp', nullable: true })
  publishedAt?: Date;

  @Column({ type: 'timestamp', nullable: true })
  blockedAt?: Date;

  @Column({ type: 'timestamp', nullable: true })
  finishedEmailSentAt?: Date;

  @Column({ nullable: false, default: 0 })
  comments: number;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt?: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ type: 'text', nullable: true })
  webhookUrl?: string;

  @OneToMany(
    () => ModelOrganizationEntity,
    (modelOrganization) => modelOrganization.model,
    { cascade: false, eager: false },
  )
  modelOrganizations: ModelOrganizationEntity[];

  get settings(): SettingsEnum[] | undefined {
    return this._settings ? JSON.parse(this._settings) : undefined;
  }

  set settings(value: SettingsEnum[] | undefined) {
    if (value) {
      this._settings = JSON.stringify(value);
    } else {
      this._settings = undefined;
    }
  }
}
