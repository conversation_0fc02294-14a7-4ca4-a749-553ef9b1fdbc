import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1722974088564 implements MigrationInterface {
  name = 'Api1722974088564';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE "upscale" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "user_id" uuid NOT NULL,
                "image_completion_id" uuid,
                "image_url" text,
                "prompt" text,
                "prompt_system" text,
                "status" character varying NOT NULL DEFAULT 'new',
                "status_detail" text,
                "progress" integer NOT NULL DEFAULT '0',
                "storage_bucket" text,
                "storage_path" text,
                "image_path" text,
                "generation_settings" json DEFAULT '{}',
                "generation_data" text,
                "generated_by_unit" character varying,
                "generation_seconds" integer NOT NULL DEFAULT '0',
                "is_active" boolean NOT NULL DEFAULT false,
                "blocked_at" TIMESTAMP,
                "deleted_at" TIMESTAMP,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_befd3174adbff81e463019385e0" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            CREATE INDEX "idx_upscale_user_id" ON "upscale" ("user_id")
        `);
    await queryRunner.query(`
            CREATE INDEX "idx_upscale_image_completion_id" ON "upscale" ("image_completion_id")
        `);
    await queryRunner.query(`
            ALTER TABLE "upscale"
            ADD CONSTRAINT "FK_4bb259005f8061f20e8fe2e5693" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "upscale"
            ADD CONSTRAINT "FK_98c96d644175010978baf94c5d7" FOREIGN KEY ("image_completion_id") REFERENCES "image_completion"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "upscale" DROP CONSTRAINT "FK_98c96d644175010978baf94c5d7"
        `);
    await queryRunner.query(`
            ALTER TABLE "upscale" DROP CONSTRAINT "FK_4bb259005f8061f20e8fe2e5693"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."idx_upscale_image_completion_id"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."idx_upscale_user_id"
        `);
    await queryRunner.query(`
            DROP TABLE "upscale"
        `);
  }
}
