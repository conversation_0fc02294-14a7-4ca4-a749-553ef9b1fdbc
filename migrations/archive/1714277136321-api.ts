import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIsBotToUser1612345678901 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_account" ADD "is_bot" boolean NOT NULL DEFAULT false;`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_account" DROP COLUMN "is_bot";`);
  }
}
