import { Provider } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Stripe from 'stripe';

export const stripeFactory: Provider = {
  provide: 'Stripe',
  useFactory: (configService: ConfigService) => {
    const stripeApiKey = configService.get<string>('STRIPE_API_KEY');

    const stripe = new Stripe(stripeApiKey, {
      apiVersion: '2024-09-30.acacia',
      maxNetworkRetries: 1,
    });

    return stripe;
  },
  inject: [ConfigService],
};
