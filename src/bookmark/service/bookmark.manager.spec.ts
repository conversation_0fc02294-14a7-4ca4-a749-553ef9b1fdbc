import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BookmarkManager } from './bookmark.manager';
import { BookmarkEntity } from '../entity/bookmark.entity';

describe('BookmarkManager', () => {
  let manager: BookmarkManager;
  let repository: Repository<BookmarkEntity>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BookmarkManager,
        {
          provide: getRepositoryToken(BookmarkEntity),
          useClass: Repository,
        },
      ],
    }).compile();

    manager = module.get<BookmarkManager>(BookmarkManager);
    repository = module.get<Repository<BookmarkEntity>>(getRepositoryToken(BookmarkEntity));
  });

  it('should be defined', () => {
    expect(manager).toBeDefined();
  });

  // Add more tests for methods like create, update, delete
});
