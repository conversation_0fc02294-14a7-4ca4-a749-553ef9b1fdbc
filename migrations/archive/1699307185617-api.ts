import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1699307185617 implements MigrationInterface {
  name = 'Api1699307185617';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "subscription"
            ADD "stripe_checkout_url" text
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "subscription" DROP COLUMN "stripe_checkout_url"
        `);
  }
}
