import {
  BadRequestException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { IdentityProvider, ServiceProvider } from 'saml2-js';
import { UserEntity } from 'src/user/entity/user.entity';
import { UserManager } from 'src/user/service/manager';
import { UserProvider } from 'src/user/service/provider';
import { Repository } from 'typeorm';
import * as xml2js from 'xml2js';
import { UserAccessTokenDto } from '../dto/user-access-token.dto';
import { IdpConfigEntity } from '../entity/idp-config.entity';
import { SamlConfigService } from './saml-config.service';
import { UserAuthService } from './user-auth.service';

@Injectable()
export class SamlService {
  constructor(
    @InjectRepository(IdpConfigEntity)
    private readonly idpConfigRepository: Repository<IdpConfigEntity>,
    private readonly userProvider: UserProvider,
    private readonly userManager: UserManager,
    private readonly userAuthService: UserAuthService,
    private readonly samlConfigService: SamlConfigService,
    private readonly logger: Logger,
  ) {}

  async getIdPConfig(name: string): Promise<IdpConfigEntity> {
    const config = await this.idpConfigRepository.findOne({ where: { name } });
    if (!config) throw new BadRequestException(`IdP "${name}" not found`);
    return config;
  }

  async createLoginRequestUrl(idpConfig: IdpConfigEntity): Promise<string> {
    this.logger.log('Starting URL generation for IdP config:', idpConfig);

    const sp = this.createServiceProvider(idpConfig);
    this.logger.log('ServiceProvider configuration:', sp);

    const idp = new IdentityProvider({
      sso_login_url: idpConfig.singleSignOnUrl,
      certificates: [idpConfig.certificate],
    });
    this.logger.log('IdentityProvider configuration:', idp);

    return new Promise((resolve, reject) => {
      sp.create_login_request_url(idp, {}, (err, loginUrl) => {
        if (err) {
          this.logger.error('Error generating login URL:', err);
          return reject(err);
        }
        this.logger.log('Generated login URL:', loginUrl);
        resolve(loginUrl);
      });
    });
  }

  async processSamlResponse(samlResponse: string): Promise<UserAccessTokenDto> {
    const decodedResponse = Buffer.from(samlResponse, 'base64').toString(
      'utf-8',
    );

    // Extract the Issuer
    const issuer = await this.extractIssuer(decodedResponse);

    // Retrieve the IdP configuration using the Issuer
    const idpConfig = await this.samlConfigService.getIdPConfigByIssuer(issuer);

    if (!idpConfig) {
      throw new Error(`IdP configuration for issuer "${issuer}" not found.`);
    }

    const sp = this.createServiceProvider(idpConfig);

    const idp = new IdentityProvider({
      sso_login_url: idpConfig.singleSignOnUrl,
      sso_logout_url: idpConfig.slsUrl,
      certificates: [idpConfig.certificate],
    });

    return new Promise((resolve, reject) => {
      sp.post_assert(
        idp,
        { request_body: { SAMLResponse: samlResponse } },
        async (err, samlAssertion) => {
          if (err) {
            console.log(err);
            this.logger.error('saml.post_assert.error', {
              error: err,
            });

            return reject(new UnauthorizedException('Invalid SAML response'));
          }

          const userAttributes = this.mapResponseToUser(
            samlAssertion.user,
            idpConfig.responseMapping,
          );

          this.logger.log('saml.post_assert.success', {
            samlUser: samlAssertion.user,
            mappedAttributs: userAttributes,
          });

          if (!userAttributes.email) {
            throw new BadRequestException('Email not found in SAML response');
          }

          let user = await this.userProvider.findOneBy({
            email: userAttributes.email,
          });

          if (!user) {
            user = new UserEntity();
            user.email = userAttributes.email;
            user.username = userAttributes.username;
            user.name = userAttributes.name;
            user.emailValidatedAt = new Date();

            await this.userManager.create(user);
          }

          const tokens = await this.userAuthService.generateTokens(user);
          resolve(tokens);
        },
      );
    });
  }

  private async extractIssuer(decodedResponse: string): Promise<string> {
    return new Promise((resolve, reject) => {
      xml2js.parseString(decodedResponse, (err, result) => {
        if (err) {
          return reject(err);
        }
        try {
          const issuer = result['samlp:Response']['saml:Issuer'][0];
          resolve(issuer);
        } catch (e) {
          reject(new Error('Issuer not found in SAML response'));
        }
      });
    });
  }

  private createServiceProvider(idpConfig: IdpConfigEntity): ServiceProvider {
    return new ServiceProvider({
      entity_id: idpConfig.entityID,
      // private_key: idpConfig.privateKey,
      certificate: idpConfig.certificate,
      assert_endpoint: idpConfig.acsUrl,
      allow_unencrypted_assertion: true,
    });
  }

  private mapResponseToUser(
    parsedResponse: any,
    responseMapping: Record<string, string>,
  ): any {
    const user = {};

    Object.keys(responseMapping).forEach((key) => {
      const samlAttribute = responseMapping[key];
      const attributeValue = parsedResponse.attributes[samlAttribute];

      if (Array.isArray(attributeValue)) {
        // If it's an array, use the first element
        user[key] = attributeValue[0] || null;
      } else {
        // Otherwise, use the value directly
        user[key] = attributeValue || null;
      }
    });

    return user;
  }
}
