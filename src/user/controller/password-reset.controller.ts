import { <PERSON>, HttpCode, Post } from '@nestjs/common';
import { Body } from '@nestjs/common/decorators/http/route-params.decorator';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { Public } from 'src/core/security/public-routes';
import { UserForgotPasswordRequest } from '../dto/user-forgot-password.request';
import { UserPasswordResetRequest } from '../dto/user-password-reset.request';
import { UserDto } from '../dto/user.dto';
import { UserRequestManager } from '../service/request-manager';

@ApiTags('user')
@Controller('users')
export class PasswordResetController {
  constructor(private userRequestManager: UserRequestManager) {}

  @ApiOperation({
    operationId: 'user_request_password_reset',
    summary: 'Request password reset',
    description:
      'Initiates the password reset process for a user by sending a reset email.\n\n' +
      'Requirements:\n' +
      '- Must provide a valid email address' +
      'Required Parameters:\n' +
      '- email: User email address\n',
  })
  @ApiBody({
    type: UserForgotPasswordRequest,
    description: 'Password reset request parameters.',
  })
  @ApiNoContentResponse({ description: 'Password reset email sent.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`email\`: Must be a valid email.
      - Invalid or unavailable parameters.
  `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Post('request-password-reset')
  @Public()
  @HttpCode(204)
  async requestPasswordReset(@Body() request: UserForgotPasswordRequest) {
    await this.userRequestManager.initiatePasswordReset(request);
  }

  @ApiOperation({
    operationId: 'user_password_reset',
    summary: 'Reset user password',
    description:
      'Resets the password for a user using a valid reset token.\n\n' +
      'Requirements:\n' +
      '- Must provide a valid reset token and new password\n\n' +
      'Required Parameters:\n' +
      '- userId: UUID of the user\n' +
      '- token: Reset token\n' +
      '- password: New password\n',
  })
  @ApiBody({
    type: UserPasswordResetRequest,
    description: 'Password reset parameters.',
  })
  @ApiOkResponse({ type: UserDto, description: 'Password reset successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`userId\`: Must be a valid UUID.
      - \`token\`: Must be a valid reset token.
      - \`password\`: Must be a valid string.
      - Invalid or unavailable parameters.
  `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Post('password-reset')
  @Public()
  @HttpCode(204)
  async resetPassword(@Body() request: UserPasswordResetRequest) {
    await this.userRequestManager.resetPassword(request);
  }
}
