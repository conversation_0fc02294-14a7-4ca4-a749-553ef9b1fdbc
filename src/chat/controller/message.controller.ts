import {
  Body,
  Controller,
  Get,
  Param,
  ParseUUIDPipe,
  Post,
  Query,
  Request,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiBody,
  ApiOkResponse,
  ApiQuery,
  ApiTags,
  ApiBadRequestResponse,
  ApiTooManyRequestsResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiParam,
  ApiOperation,
} from '@nestjs/swagger';
import { Response } from 'express';
import { ChatMessageDto } from '../dto/chat-message.dto';
import { ChatMessageRequest } from '../dto/chat-message.request';
import { ChatMessageSearchRequest } from '../dto/chat-message.search-request';
import { ChatMessageRequestManager } from '../service/chat-message.request-manager';
import { ChatResponseMapper } from '../service/chat.response-mapper';
import { ChatProvider } from '../service/chat.provider';
import { setPaginationHeaders } from '../../core/utils/pagination';

@ApiTags('chat')
@Controller('chat')
export class MessageController {
  constructor(
    private readonly requestManager: ChatMessageRequestManager,
    private readonly responseMapper: ChatResponseMapper,
    private readonly chatProvider: ChatProvider,
  ) {}

  @Post(':id/messages')
  @ApiOperation({
    operationId: 'chat_add_message',
    summary: 'Add a message to a chat conversation',
    description:
      'Adds a new message to the specified chat conversation for the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the chat conversation\n\n' +
      ' Required Body Parameters:\n' +
      '- content: Content of the message\n' +
      '- role: Role of the message sender\n\n' +
      'Optional Body Parameters:\n' +
      '- attachedFiles: Attached files associated with the message\n\n' +
      'Returns the created message object.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the chat conversation to add a message to.',
    type: String,
  })
  @ApiBody({
    type: ChatMessageRequest,
    description:
      'Message request parameters including role, content, and attachments.',
  })
  @ApiOkResponse({
    type: ChatMessageDto,
    description: 'Message added successfully to the conversation.',
  })
  @ApiBadRequestResponse({
    description: `
      Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - \`content\`: Must be a non-empty string.
      - \`role\`: Must be a valid value from the \`MessageRole\` enum.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiTooManyRequestsResponse({
    description: 'Rate limit exceeded. Try again later.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User does not have access to this conversation.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async addMessage(
    @Body() requestBody: ChatMessageRequest,
    @Request() request,
    @Param('id', new ParseUUIDPipe()) conversationId: string,
  ): Promise<ChatMessageDto> {
    const conversation = await this.chatProvider.getConversation(
      conversationId,
      request.user.id,
    );

    const message = await this.requestManager.addMessage(
      conversation,
      request.user.id,
      requestBody,
    );

    return this.responseMapper.mapMessage(message);
  }

  @Get(':id/messages')
  @ApiOperation({
    operationId: 'chat_get_messages',
    summary: 'Get messages from a chat conversation',
    description:
      'Retrieves a paginated list of messages from the specified chat conversation for the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the chat conversation\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number for pagination\n' +
      '- limit: Number of messages per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- role: Role of the message sender\n\n' +
      'Returns an array of message objects.\n\n' +
      'Permission:\n' +
      '- User must have access to the conversation.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the chat conversation to retrieve messages from.',
    type: String,
  })
  @ApiQuery({
    type: ChatMessageSearchRequest,
    description: 'Query parameters for searching and paginating chat messages.',
  })
  @ApiOkResponse({
    type: ChatMessageDto,
    isArray: true,
    description:
      'Returns a paginated list of messages for the specified conversation.',
  })
  @ApiBadRequestResponse({
    description: `
      Bad Request. Possible reasons:\n
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - \`role\`: Must be a valid value from the \`MessageRole\` enum.
      - \`id\`: Must be a valid UUID.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User does not have access to this conversation.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @UsePipes(new ValidationPipe())
  async getMessages(
    @Query() query: ChatMessageSearchRequest,
    @Res() res: Response,
    @Param('id', new ParseUUIDPipe()) conversationId: string,
    @Request() request,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder } = query;

    const filters = {
      conversationId,
      userId: request.user.id,
    };

    const messages = await this.chatProvider.findMessages(
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    const totalCount = await this.chatProvider.countMessages(filters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultipleMessages(messages));
  }
}
