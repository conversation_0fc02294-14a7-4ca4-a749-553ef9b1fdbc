import { Injectable } from '@nestjs/common';
import { AbstractProvider } from 'src/core/service/abstract.provider';
import { BoardCommentEntity } from '../entity/board-comment.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, Repository } from 'typeorm';
import { Logger } from 'nestjs-pino';

@Injectable()
export class BoardCommentProvider extends AbstractProvider<BoardCommentEntity> {
  constructor(
    @InjectRepository(BoardCommentEntity)
    repository: Repository<BoardCommentEntity>,
    logger: Logger,
  ) {
    super(repository, logger);
  }

  prepareFindManyOptions(
    criteria: any,
    page: number,
    limit: number,
    sortBy?: string,
    sortOrder?: string,
  ): FindManyOptions<BoardCommentEntity> {
    if (criteria.username) {
      criteria.user = {
        username: criteria.username,
      };

      delete criteria.username;
    }

    const parentOptions = super.prepareFindManyOptions(
      criteria,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    return {
      ...parentOptions,
      relations: {
        user: true,
      },
    };
  }

  async findCommentsByBoardId(boardId: string): Promise<BoardCommentEntity[]> {
    const customQuery = await this.repository
      .createQueryBuilder('boardComments')
      .where('boardComments.boardId = :boardId', {
        boardId: boardId,
      })
      .distinctOn(['boardComments.user_id'])
      .getMany();

    return customQuery;
  }
}
