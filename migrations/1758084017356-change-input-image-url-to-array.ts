import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeInputImageUrlToArray1758084017356
  implements MigrationInterface
{
  name = 'ChangeInputImageUrlToArray1758084017356';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // First, we need to convert existing text values to JSON arrays
    // For existing records with inputImageUrl, wrap them in an array
    await queryRunner.query(`
      UPDATE "image_edit" 
      SET "input_image_url" = CASE 
        WHEN "input_image_url" IS NOT NULL AND "input_image_url" != '' 
        THEN json_build_array("input_image_url"::text)
        ELSE NULL 
      END
    `);

    // Now change the column type from text to json
    await queryRunner.query(`
      ALTER TABLE "image_edit" 
      ALTER COLUMN "input_image_url" TYPE json USING "input_image_url"::json
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Convert JSON arrays back to single text values (take first element)
    await queryRunner.query(`
      UPDATE "image_edit" 
      SET "input_image_url" = CASE 
        WHEN "input_image_url" IS NOT NULL 
        THEN ("input_image_url"->0)::text
        ELSE NULL 
      END
    `);

    // Change the column type back from json to text
    await queryRunner.query(`
      ALTER TABLE "image_edit" 
      ALTER COLUMN "input_image_url" TYPE text USING "input_image_url"::text
    `);
  }
}
