import { BadRequestException, Injectable } from '@nestjs/common';
import { Logger } from 'nestjs-pino';
import { UserEntity } from '../../user/entity/user.entity';
import { UserCreditBalanceInternalRequest } from '../dto/user-credit-balance.internal-request';
import { UserCreditBalanceEntity } from '../entity/user-credit-balance.entity';
import { PaymentRequiredException } from '../exception/payment-required.exception';
import { UserCreditBalanceManager } from './user-credit-balance.manager';

@Injectable()
export class UserCreditBalanceRequestManager {
  constructor(
    private manager: UserCreditBalanceManager,
    private logger: Logger,
  ) {}

  async handleInternalTransaction(
    request: UserCreditBalanceInternalRequest,
    user?: UserEntity,
    organizationId?: string,
  ): Promise<UserCreditBalanceEntity> {
    try {
      if (request.amount < 0) {
        if (organizationId) {
          return await this.manager.deduct(
            request.creditType,
            Math.abs(request.amount),
            null,
            organizationId,
          );
        }

        return await this.manager.deduct(
          request.creditType,
          Math.abs(request.amount),
          user.id,
        );
      }

      if (organizationId) {
        return await this.manager.increase(
          request.creditType,
          request.amount,
          null,
          organizationId,
        );
      }

      return await this.manager.increase(
        request.creditType,
        request.amount,
        user.id,
      );
    } catch (e) {
      if ('user_credit_balance.not_found' === e.message) {
        throw new PaymentRequiredException();
      }

      this.logger.error('Failed to handle internal transaction', {
        user: user.id,
        error: e.message,
      });

      throw new BadRequestException(e.message);
    }
  }
}
