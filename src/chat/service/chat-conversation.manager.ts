import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ChatConversationEntity } from '../entity/chat-conversation.entity';
import { ChatMessageManager } from './chat-message.manager';

@Injectable()
export class ChatConversationManager {
  constructor(
    @InjectRepository(ChatConversationEntity)
    private readonly conversationRepository: Repository<ChatConversationEntity>,
    private readonly messageManager: ChatMessageManager,
  ) {}

  async create(
    conversation: ChatConversationEntity,
  ): Promise<ChatConversationEntity> {
    return await this.conversationRepository.save(conversation);
  }

  async update(
    conversation: ChatConversationEntity,
  ): Promise<ChatConversationEntity> {
    return await this.conversationRepository.save(conversation);
  }

  async delete(conversation: ChatConversationEntity): Promise<void> {
    await this.messageManager.deleteByConversationId(conversation.id);
    await this.conversationRepository.delete({ id: conversation.id });
  }
}
