import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { ModelUnpublishedEvent } from 'src/model/event/model-unpublished.event';
import { UserProvider } from '../service/provider';
import { UserManager } from '../service/manager';
import { ModelProvider } from 'src/model/service/provider';
import { PrivacyEnum } from 'src/model/entity/model.entity';
import { StatusEnum } from 'src/model/enum/status.enum';

@Injectable()
export class ModelUnpublishedListener {
  constructor(
    private userManager: UserManager,
    private userProvider: UserProvider,
    private modelProvider: ModelProvider,
  ) {}

  @OnEvent('model.unpublished', { async: true })
  async handleModelUnpublishedEvent(event: ModelUnpublishedEvent) {
    const user = await this.userProvider.get(event.userId);

    const userModelsAvailable = await this.modelProvider.countBy({
      userId: user.id,
      privacy: PrivacyEnum.PUBLIC,
      status: StatusEnum.AVAILABLE,
    });

    user.modelsAvailable = userModelsAvailable;

    this.userManager.update(user);
  }
}
