import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { OrganizationUserEntity } from '../entity/organization-user.entity';
import { OrganizationManager } from './manager';
import { OrganizationEntity } from '../entity/organization.entity';

@Injectable()
export class OrganizationUserManager {
  constructor(
    @InjectRepository(OrganizationUserEntity)
    private repository: Repository<OrganizationUserEntity>,
    private organizationManager: OrganizationManager,
  ) {}

  async create(
    organizationUser: OrganizationUserEntity,
  ): Promise<OrganizationUserEntity> {
    return await this.repository.save(organizationUser);
  }

  async update(
    organizationUser: OrganizationUserEntity,
  ): Promise<OrganizationUserEntity> {
    return await this.repository.save(organizationUser);
  }

  async delete(
    organization: OrganizationEntity,
    organizationUser: OrganizationUserEntity,
  ): Promise<void> {
    await this.repository.delete(organizationUser.id);
    organization.members--;

    await this.organizationManager.update(organization);
  }
}
