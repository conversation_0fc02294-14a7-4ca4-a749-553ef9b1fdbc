import { ApiProperty } from '@nestjs/swagger';
import { ModelDto } from 'src/model/dto/model.dto';
import { ImageEditDto } from 'src/public-api/image-edit/dto/image-edit.dto';
import { PublicUserDto } from 'src/user/dto/public.user.dto';

export class ImageDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  user: PublicUserDto;

  @ApiProperty()
  imageEdit?: ImageEditDto;

  @ApiProperty()
  prompt: string;

  @ApiProperty()
  status: string;

  @ApiProperty()
  progressMessage: string;

  @ApiProperty()
  progress: number;

  @ApiProperty()
  previewImage: string;

  @ApiProperty()
  isHot: boolean;

  @ApiProperty()
  privacy: string;

  @ApiProperty()
  likes: number;

  @ApiProperty()
  regenerations: number;

  @ApiProperty()
  imageVersions: { [key: string]: any };

  @ApiProperty()
  systemVersion: number;

  @ApiProperty()
  hasWatermark?: boolean;

  @ApiProperty()
  generationSettings?: any;

  @ApiProperty()
  isNsfw?: boolean;

  @ApiProperty()
  webhookUrl?: string;

  @ApiProperty()
  hidePrompt?: boolean;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty({ type: () => [ModelDto] })
  models?: ModelDto[];
}
