import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1715********* implements MigrationInterface {
    name = 'Api1715*********'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE INDEX "IDX_afe3fdfb98cd47cd28108fa484" ON "user_follow" ("follower_id")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_3759ce2cf656e35680e1be8154" ON "user_follow" ("following_id")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_56a0e4bcec2b5411beafa47ffa" ON "user_account" ("email")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_3c4d4fae641bf9048ad324ee0d" ON "user_account" ("username")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_f83da055ad1a8a6f01603d0a11" ON "user_signup_code" ("user_id")
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DROP INDEX "public"."IDX_f83da055ad1a8a6f01603d0a11"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_3c4d4fae641bf9048ad324ee0d"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_56a0e4bcec2b5411beafa47ffa"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_3759ce2cf656e35680e1be8154"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_afe3fdfb98cd47cd28108fa484"
        `);
    }

}
