import {
  <PERSON>,
  Get,
  Param,
  ParseUUIDPipe,
  Query,
  Request,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiOkResponse,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiOperation,
} from '@nestjs/swagger';
import { Response } from 'express';
import { ChatConversationDto } from '../dto/chat-conversation.dto';
import { ChatConversationSearchRequest } from '../dto/chat-conversation.search-request';
import { ChatProvider } from '../service/chat.provider';
import { ChatResponseMapper } from '../service/chat.response-mapper';
import { setPaginationHeaders } from '../../core/utils/pagination';
import { ChatRateLimitDto } from '../dto/chat-rate-limit.dto';
import { ChatMessageRequestManager } from '../service/chat-message.request-manager';

@ApiTags('chat')
@Controller('chat')
export class ReadController {
  constructor(
    private readonly chatProvider: ChatProvider,
    private readonly responseMapper: ChatResponseMapper,
    private readonly requestManager: ChatMessageRequestManager,
  ) {}

  @Get('limit')
  @ApiOkResponse({
    type: ChatRateLimitDto,
    description:
      'Returns the current chat rate limit usage for the authenticated user.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiOperation({
    operationId: 'chat_get_rate_limit',
    summary: 'Get chat rate limit usage',
    description:
      'Retrieves the current chat rate limit usage for the authenticated user.',
  })
  async getRateLimit(@Request() request): Promise<ChatRateLimitDto> {
    return this.requestManager.getUserMessageUsage(request.user.id);
  }

  @Get(':id')
  @ApiParam({
    name: 'id',
    description: 'UUID of the chat conversation to retrieve.',
    type: String,
  })
  @ApiOkResponse({
    type: ChatConversationDto,
    description: 'Returns the chat conversation with the specified ID.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiBadRequestResponse({
    description: `
      Bad Request. Possible reasons:\n
      - Invalid conversation ID format
      - Conversation does not exist
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User does not have access to this conversation.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiOperation({
    operationId: 'chat_get_conversation',
    summary: 'Get a chat conversation by ID',
    description:
      'Retrieves the chat conversation with the specified UUID for the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the chat conversation\n',
  })
  async get(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<ChatConversationDto> {
    const conversation = await this.chatProvider.getConversation(
      id,
      request.user.id,
    );
    return this.responseMapper.map(conversation);
  }

  @Get()
  @ApiQuery({
    type: ChatConversationSearchRequest,
    description:
      'List the chat conversations for the authenticated user.\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of conversations per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- title: Title of the conversation\n' +
      '- id: UUID of the conversation\n',
  })
  @ApiOkResponse({
    type: ChatConversationDto,
    isArray: true,
    description:
      'Returns a paginated list of chat conversations for the authenticated user.',
  })
  @ApiBadRequestResponse({
    description: `
      Bad Request. Possible reasons:\n
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - \`title\`: Must be a valid string.
      - \`id\`: Must be a valid UUID.
      - Invalid or unavailable parameters.      
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiOperation({
    operationId: 'chat_list_conversations',
    summary: 'List chat conversations',
    description:
      'Lists chat conversations for the authenticated user with optional pagination and filtering.\n\n' +
      'Supports filtering by title and ID, and sorting by various fields.\n\n' +
      'List the chat conversations for the authenticated user.\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of conversations per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- title: Title of the conversation\n' +
      '- id: UUID of the conversation\n',
  })
  @UsePipes(new ValidationPipe())
  async find(
    @Query() query: ChatConversationSearchRequest,
    @Res() res: Response,
    @Request() request,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder } = query;

    const filters = {
      userId: request.user.id,
    };

    const conversations = await this.chatProvider.findConversations(
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    const totalCount = await this.chatProvider.countConversations(filters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultiple(conversations));
  }
}
