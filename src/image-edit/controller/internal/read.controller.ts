import {
  Controller,
  Get,
  Param,
  Query,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiOkResponse,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiOperation,
  ApiBadRequestResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { ImageEditDto } from '../../dto/image-edit.dto';
import { ImageEditSearchRequest } from '../../dto/image-edit.search-request';
import { ImageEditProvider } from '../../service/provider';
import { ImageEditResponseMapper } from '../../service/response-mapper';

@ApiTags('image-edit / internal')
@Controller('internal/image-edits')
export class ReadController {
  constructor(
    private provider: ImageEditProvider,
    private responseMapper: ImageEditResponseMapper,
  ) {}

  @Get()
  @ApiOperation({
    operationId: 'internal_image_edit_list',
    summary: 'List internal image edits',
    description:
      'Retrieves a paginated list of image edits for internal use.\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of items per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- status: Filter by status\n',
  })
  @ApiOkResponse({
    type: Array<ImageEditDto>,
    description: 'Paginated list of image edits.',
  })
  @ApiQuery({
    type: ImageEditSearchRequest,
    description: 'Query parameters for searching and paginating image edits.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - Filter parameters must be valid values.
      - \`status\`: Must be a valid value from the \`StatusEnum\`.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @UsePipes(new ValidationPipe())
  async find(
    @Query() query: ImageEditSearchRequest,
    @Res() res: Response,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;

    const entities = await this.provider.findBy(
      inputFilters,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    const totalCount = await this.provider.countBy(inputFilters);
    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultipleInternal(entities));
  }

  @Get(':id')
  @ApiOperation({
    operationId: 'internal_image_edit_get',
    summary: 'Get internal image edit by ID',
    description:
      'Retrieves a specific image edit by its UUID for internal use.\n\n' +
      'Required Parameters:\n' +
      '- id: ID of the image edit\n',
  })
  @ApiOkResponse({
    type: ImageEditDto,
    description: 'Returns the image edit with the specified ID.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the image edit to retrieve.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - Image edit does not exist.
    `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async get(@Param() params): Promise<ImageEditDto> {
    return this.provider
      .get(params.id)
      .then((entity) => this.responseMapper.mapInternal(entity));
  }
}
