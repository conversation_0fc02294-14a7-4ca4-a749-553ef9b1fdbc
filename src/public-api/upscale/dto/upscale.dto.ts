import { ApiProperty } from '@nestjs/swagger';
import { PublicUserDto } from 'src/user/dto/public.user.dto';

export class UpscaleDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  user: PublicUserDto;

  @ApiProperty()
  userId?: string;

  @ApiProperty()
  imageCompletionId?: string;

  @ApiProperty()
  imageUrl?: string;

  @ApiProperty()
  originalImageThumbnail?: string;

  @ApiProperty()
  status: string;

  @ApiProperty()
  statusDetail: string;

  @ApiProperty()
  progress: number;

  @ApiProperty()
  username: string;

  @ApiProperty()
  createdByUsername: string;

  @ApiProperty()
  createdByThumbnail: string;

  @ApiProperty()
  isUserVerified: boolean;

  @ApiProperty()
  imageVersions: { [key: string]: string };

  @ApiProperty()
  webhookUrl?: string;

  @ApiProperty()
  selected?: boolean;

  @ApiProperty()
  blockedAt?: Date;

  @ApiProperty()
  createdAt: Date;
}
