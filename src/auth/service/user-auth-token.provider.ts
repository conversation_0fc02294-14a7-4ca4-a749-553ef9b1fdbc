import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { Repository } from 'typeorm';
import { AbstractProvider } from '../../core/service/abstract.provider';
import { UserAuthTokenEntity } from '../entity/user-auth-token.entity';

@Injectable()
export class UserAuthTokenProvider extends AbstractProvider<UserAuthTokenEntity> {
  constructor(
    @InjectRepository(UserAuthTokenEntity)
    repository: Repository<UserAuthTokenEntity>,
    logger: Logger,
  ) {
    super(repository, logger);
  }
}
