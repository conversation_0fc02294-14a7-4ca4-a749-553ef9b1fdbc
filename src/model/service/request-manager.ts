import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
  UnauthorizedException,
  forwardRef,
} from '@nestjs/common';
import {
  PrivacyEnum as ImageCompletionPrivacyEnum,
  StatusEnum as ImageCompletionStatusEnum,
} from '../../image-completion/entity/image-completion.entity';
import { AssetManager as ImageCompletionAssetManager } from '../../image-completion/service/asset.manager';
import { ImageCompletionProvider } from '../../image-completion/service/provider';
import { UserEntity } from '../../user/entity/user.entity';
import { ModelActivationRequest } from '../dto/model-activation.dto';
import { ModelInternalRequest } from '../dto/model.internal-request';
import { ModelRequest } from '../dto/model.request';
import { ModelEntity } from '../entity/model.entity';
import { StatusEnum } from '../enum/status.enum';
import { AssetManager } from './asset.manager';
import { ModelManager } from './manager';
import { ModelVersionProvider } from './model-version.provider';

@Injectable()
export class ModelRequestManager {
  constructor(
    private manager: ModelManager,
    private assetManager: AssetManager,
    @Inject(forwardRef(() => ImageCompletionAssetManager))
    private imageCompletionAssetManager: ImageCompletionAssetManager,
    @Inject(forwardRef(() => ImageCompletionProvider))
    private imageCompletionProvider: ImageCompletionProvider,
    private modelVersionProvider: ModelVersionProvider,
  ) {}

  async create(request: ModelRequest, user: UserEntity): Promise<ModelEntity> {
    const entity = new ModelEntity();

    entity.user = user;

    this.mapRequestData(entity, request);

    return await this.manager.create(entity);
  }

  async update(
    entity: ModelEntity,
    request: ModelRequest,
  ): Promise<ModelEntity> {
    this.mapRequestData(entity, request);

    return await this.manager.update(entity);
  }

  async activate(entity: ModelEntity, request: ModelActivationRequest) {
    if (entity.status !== StatusEnum.FINISHED) {
      throw new BadRequestException('Model is not available for activation');
    }

    entity.type = 'type' in request ? request.type : entity.type;
    entity.website = 'website' in request ? request.website : entity.website;
    entity.description =
      'description' in request ? request.description : entity.description;

    await await this.manager.activate(entity);
  }

  async trainModel(entity: ModelEntity) {
    const validStatuses = [
      StatusEnum.NEW,
      StatusEnum.FINISHED,
      StatusEnum.AVAILABLE,
      StatusEnum.FAILED,
    ];

    if (!validStatuses.includes(entity.status)) {
      throw new BadRequestException('Invalid status');
    }

    await this.manager.trainModel(entity, ModelEntity.activeSystemVersions);
  }

  async updateInternal(
    entity: ModelEntity,
    request: ModelInternalRequest,
  ): Promise<ModelEntity> {
    this.mapInternalRequestData(entity, request);

    return await this.manager.update(entity);
  }

  async startTraining(
    model: ModelEntity,
    systemVersion: number,
  ): Promise<ModelEntity> {
    // todo: check if model status is pending or training
    if (
      model.status !== StatusEnum.PENDING &&
      model.status !== StatusEnum.TRAINING
    ) {
      throw new BadRequestException(
        'Model is not in pending or training state',
      );
    }

    const modelVersion = await this.modelVersionProvider.getModelVersion(
      model.id,
      model.version,
    );

    const systemVersionData = modelVersion.systemVersionsData.find(
      (systemVersionData) => systemVersionData.systemVersion == systemVersion,
    );

    if (systemVersionData?.status !== StatusEnum.PENDING) {
      throw new BadRequestException('System version is not in pending state');
    }

    return await this.manager.startTraining(model, systemVersion);
  }

  async reseTrainingStatus(model: ModelEntity): Promise<ModelEntity> {
    return await this.manager.reseTrainingStatus(model);
  }

  async failTraining(
    entity: ModelEntity,
    systemVersion: number,
  ): Promise<ModelEntity> {
    if (entity.status !== StatusEnum.TRAINING) {
      throw new BadRequestException('Model is not in training state');
    }

    return await this.manager.failTraining(entity, systemVersion);
  }

  async finishTraining(
    entity: ModelEntity,
    systemVersion: number,
    request: ModelInternalRequest,
  ): Promise<ModelEntity> {
    this.mapInternalRequestData(entity, request);

    if (entity.status !== StatusEnum.TRAINING) {
      throw new BadRequestException('Model is not in training state');
    }

    return await this.manager.finishTraining(
      entity,
      systemVersion,
      request.prompt,
    );
  }

  async restoreVersion(
    model: ModelEntity,
    version: number,
  ): Promise<ModelEntity> {
    try {
      return await this.manager.restoreVersion(model, version);
    } catch (error) {
      throw new NotFoundException('Model version not found');
    }
  }

  async updateThumbnail(entity: ModelEntity, imageCompletionId: string) {
    const imageCompletion = await this.imageCompletionProvider.get(
      imageCompletionId,
    );

    if (!imageCompletion.hasModel(entity.id)) {
      throw new NotFoundException('Image completion does use this model');
    }

    // check whether that image completion is ready
    if (imageCompletion.status !== ImageCompletionStatusEnum.READY) {
      throw new BadRequestException('Image completion is not available');
    }

    // check whether that image completion is owned by the user OR is public
    if (
      imageCompletion.user.id !== entity.user.id &&
      imageCompletion.privacy !== ImageCompletionPrivacyEnum.PUBLIC
    ) {
      throw new UnauthorizedException('Image completion is not visible');
    }

    // todo: copy image from image completion s3 bucket to model s3 bucket
    const thumbnailObject = await this.imageCompletionAssetManager.getObject(
      imageCompletion,
      imageCompletion.imagePaths[0],
    );

    const thumbnailBuffer = await this.streamToBuffer(thumbnailObject.Body);

    const thumbnailExtension = `${imageCompletion.imagePaths[0]
      .split('.')
      .pop()}`;

    entity.thumbnail =
      entity.storagePath + '/thumbnails/thumbnail.' + thumbnailExtension;

    await this.assetManager.emptyFolder(
      entity,
      entity.storagePath + '/thumbnails/',
    );

    await this.assetManager.storeObject(
      entity,
      entity.thumbnail,
      thumbnailBuffer,
      thumbnailObject.ContentType,
    );

    this.manager.update(entity);
  }

  async streamToBuffer(stream): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      const chunks = [];
      stream.on('data', (chunk) => chunks.push(chunk));
      stream.on('error', reject);
      stream.on('end', () => resolve(Buffer.concat(chunks)));
    });
  }

  mapRequestData(entity: ModelEntity, request: ModelRequest): void {
    entity.name = 'name' in request ? request.name : entity.name;
    entity.privacy = 'privacy' in request ? request.privacy : entity.privacy;
    entity.type = 'type' in request ? request.type : entity.type;
    entity.class = 'class' in request ? request.class : entity.class;
    entity.description =
      'description' in request ? request.description : entity.description;
    entity.website = 'website' in request ? request.website : entity.website;
    entity.webhookUrl =
      'webhookUrl' in request ? request.webhookUrl : entity.webhookUrl;
    entity.settings =
      'settings' in request ? request.settings : entity.settings;
    entity.isActive =
      'isActive' in request ? request.isActive : entity.isActive;
    entity.trainingMode =
      'trainingMode' in request ? request.trainingMode : entity.trainingMode;
  }

  mapInternalRequestData(
    entity: ModelEntity,
    request: ModelInternalRequest,
  ): void {
    this.mapRequestData(entity, request);

    // entity.prompt_v1 =
    //   'prompt_v1' in request ? request.prompt_v1 : entity.prompt_v1;
    // entity.prompt = 'prompt' in request ? request.prompt : entity.prompt;
    entity.usages = 'usages' in request ? request.usages : entity.usages;
    entity.likes = 'likes' in request ? request.likes : entity.likes;
    entity.reports = 'reports' in request ? request.reports : entity.reports;
    entity.version = 'version' in request ? request.version : entity.version;
    entity.thumbnail =
      'thumbnail' in request ? request.thumbnail : entity.thumbnail;
    entity.trainingMode =
      'trainingMode' in request ? request.trainingMode : entity.trainingMode;

    const systemVersionData = {};
    ['systemVersion', 'status', 'prompt'].forEach((key) => {
      if (key in request) {
        systemVersionData[key] = request[key];
      }
    });
  }
}
