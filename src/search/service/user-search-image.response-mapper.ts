import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { UserSearchImageDto } from '../dto/user-search-image.dto';
import { UserSearchImageEntity } from '../entity/user-search-image.entity';
import { UserSearchImageManager } from './user-search-image.manager';

@Injectable()
export class UserSearchImageResponseMapper {
  private cdnHost: string;

  constructor(
    private configService: ConfigService,
    private manager: UserSearchImageManager,
  ) {
    this.cdnHost = this.configService.get<string>('CDN_HOST');
  }

  async map(
    entity: UserSearchImageEntity,
    generateUploadUrl: boolean,
  ): Promise<UserSearchImageDto> {
    const dto = new UserSearchImageDto();

    dto.id = entity.id;
    dto.imageCompletionId = entity.imageCompletionId;
    dto.userId = entity.userId;
    dto.imageUrl = this.cdnHost + '/' + entity.imagePath;
    dto.createdAt = entity.createdAt;

    if (generateUploadUrl) {
      dto.uploadUrl = await this.manager.generateUploadUrl(entity);
    }

    return dto;
  }

  async mapMultiple(
    entities: UserSearchImageEntity[],
  ): Promise<any> {
    const dtos = [];

    for (const entity of entities) {
      dtos.push(await this.map(entity, false));
    }

    return dtos;
  }
}
