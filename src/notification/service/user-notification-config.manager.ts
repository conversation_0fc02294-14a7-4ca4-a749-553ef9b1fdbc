import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserNotificationConfigEntity } from '../entity/user-notification-config.entity';
import { Injectable } from '@nestjs/common';

@Injectable()
export class UserNotificationConfigManager {
  constructor(
    @InjectRepository(UserNotificationConfigEntity)
    private repository: Repository<UserNotificationConfigEntity>,
  ) {}

  async save(
    entity: UserNotificationConfigEntity,
  ): Promise<UserNotificationConfigEntity> {
    return await this.repository.save(entity);
  }

  async delete(entity: UserNotificationConfigEntity): Promise<void> {
    await this.repository.softDelete(entity.id);
  }
}
