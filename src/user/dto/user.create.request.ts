import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsDate,
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  Matches,
} from 'class-validator';
import { IsEmailDomainAllowed } from '../../core/validation/is-email-domain-allowed.decorator';

export class UserCreateRequest {
  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  name: string;

  @IsEmail()
  @IsEmailDomainAllowed()
  @ApiProperty()
  email: string;

  @IsOptional()
  @ApiProperty()
  @Matches(/^[a-zA-Z0-9_.]+$/, {
    message:
      'Username must contain only alphanumeric characters, underscores, and dots',
  })
  username: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  password: string;

  @IsOptional()
  @ApiProperty()
  description: string;

  @IsOptional()
  @ApiProperty()
  website: string;

  @ApiProperty()
  socialMediaAccounts: any;

  @IsDate()
  @IsOptional()
  @ApiProperty()
  birthday: Date;

  @IsOptional()
  @ApiProperty()
  timezone: string;

  @IsOptional()
  @ApiProperty()
  currency: string;

  @IsOptional()
  @ApiProperty()
  locale: string;

  @IsOptional()
  @ApiProperty()
  signupCode?: string;

  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  isBot?: boolean;

  @IsUUID()
  @IsOptional()
  @ApiProperty({ required: false })
  organizationId?: string;
}
