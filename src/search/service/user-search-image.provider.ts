import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { AbstractProvider } from 'src/core/service/abstract.provider';
import { Repository } from 'typeorm';
import { UserSearchImageEntity } from '../entity/user-search-image.entity';

@Injectable()
export class UserSearchImageProvider extends AbstractProvider<UserSearchImageEntity> {
  constructor(
    @InjectRepository(UserSearchImageEntity)
    repository: Repository<UserSearchImageEntity>,
    logger: Logger,
  ) {
    super(repository, logger);
  }
}
