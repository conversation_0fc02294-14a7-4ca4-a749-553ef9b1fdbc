import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1730065999325 implements MigrationInterface {
  name = 'Api1730065999325';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "board_user"
            ADD "deleted_at" TIMESTAMP
        `);
    await queryRunner.query(`
            ALTER TABLE "board_user"
            ADD "created_at" TIMESTAMP NOT NULL DEFAULT now()
        `);
    await queryRunner.query(`
            ALTER TABLE "board_user"
            ADD "updated_at" TIMESTAMP NOT NULL DEFAULT now()
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "board_user" DROP COLUMN "updated_at"
        `);
    await queryRunner.query(`
            ALTER TABLE "board_user" DROP COLUMN "created_at"
        `);
    await queryRunner.query(`
            ALTER TABLE "board_user" DROP COLUMN "deleted_at"
        `);
  }
}
