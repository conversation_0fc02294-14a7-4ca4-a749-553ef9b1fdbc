import {
  <PERSON>,
  Get,
  Param,
  ParseU<PERSON><PERSON>ip<PERSON>,
  Query,
  Request,
  Res,
  UnauthorizedException,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiOkResponse,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import { BaseFindResponseHeadersDto } from 'src/core/dto/base-find-response-headers.dto';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { ModelDto } from '../dto/model.dto';
import { ModelSearchRequest } from '../dto/model.search-request';
import { PrivacyEnum } from '../entity/model.entity';
import { ModelProvider } from '../service/provider';
import { ModelResponseMapper } from '../service/response-mapper';
import { OrganizationUserProvider } from 'src/organization/service/organization-user.provider';
import { UserEntity } from 'src/user/entity/user.entity';
import { JwtIntegrationAuthGuard } from 'src/auth/service/jwt-integration-auth.guard';

@ApiTags('model')
@Controller('public/models')
export class PublicReadController {
  constructor(
    private provider: ModelProvider,
    private responseMapper: ModelResponseMapper,
    private organizationUserProvider: OrganizationUserProvider,
  ) {}

  @ApiOperation({
    operationId: 'public_model_list',
    summary: 'List public and accessible models',
    description:
      'Retrieves a paginated list of models that are public or accessible to the current user (including private models shared with organizations the user belongs to).\n\n' +
      'Requirements:\n' +
      '- User must be authenticated via integration token\n' +
      '- Only public models or models accessible to the user are returned' +
      '\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of models per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- search: Search term\n' +
      '- name: Filter by model name\n' +
      '- userId: Filter by user ID\n' +
      '- username: Filter by username\n' +
      '- class: Filter by model class\n' +
      '- type: Filter by model type\n' +
      '- description: Filter by model description\n' +
      '- privacy: Filter by privacy level\n' +
      '- systemVersion: Filter by system version\n' +
      '- isActive: Filter by active status\n' +
      '- isFeatured: Filter by featured status\n' +
      '- isSponsored: Filter by sponsored status\n' +
      '- organizationId: Filter by organization ID\n' +
      '- organizationIds: Filter by organization IDs\n',
  })
  @ApiOkResponse({
    type: ModelDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated list of public or accessible models.',
  })
  @ApiQuery({
    type: ModelSearchRequest,
    description: 'Query parameters for searching and paginating models.',
  })
  @ApiUnauthorizedResponse({
    description:
      'Unauthorized. The user is not authenticated or token is invalid.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - \`search\`: Must be a valid string.
      - \`name\`: Must be a valid string.
      - \`userId\`: Must be a valid UUID.
      - \`username\`: Must be a valid username.
      - \`class\`: Must be a valid string.
      - \`type\`: Must be a valid string.
      - \`description\`: Must be a valid string.
      - \`privacy\`: Must be a valid value from the \`PrivacyEnum\`.
      - \`systemVersion\`: Must be a valid number.
      - \`isActive\`: Must be a valid boolean value.
      - \`isFeatured\`: Must be a valid boolean value.
      - \`isSponsored\`: Must be a valid boolean value.
      - \`organizationId\`: Must be a valid UUID.
      - \`organizationIds\`: Must be a valid array of UUIDs.
      - Filter parameters must be valid values.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Get()
  @UsePipes(new ValidationPipe())
  @UseGuards(JwtIntegrationAuthGuard)
  async find(
    @Request() request,
    @Query() query: ModelSearchRequest,
    @Res() res: Response,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;
    const filters = await this.validateSearchFilters(
      inputFilters,
      request.user,
    );

    const entities = await this.provider.findBy(
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    );
    const totalCount = await this.provider.countBy(filters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultiple(entities, true));
  }

  @ApiOperation({
    operationId: 'public_model_get',
    summary: 'Get public or accessible model by ID',
    description:
      'Retrieves the details of a specific model by its unique identifier if it is public or accessible to the current user.\n\n' +
      'Requirements:\n' +
      '- User must be authenticated via integration token\n' +
      '- Model must be public or accessible to the user',
  })
  @ApiOkResponse({ type: ModelDto, description: 'Model details.' })
  @ApiParam({
    name: 'id',
    description: 'The unique identifier of the model',
    type: 'string',
    format: 'uuid',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not allowed to view this model.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
    `,
  })
  @ApiNotFoundResponse({
    description: 'Not Found. The model could not be found.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Get(':id')
  @UseGuards(JwtIntegrationAuthGuard)
  async get(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<ModelDto> {
    const entity = await this.provider.get(id);

    // Check if user can access this model
    const canAccess = await this.provider.isModelAccessibleToUser(
      id,
      request.user,
    );
    if (!canAccess) {
      throw new UnauthorizedException(
        'You are not allowed to view this model.',
      );
    }

    return await this.responseMapper.map(entity);
  }

  async validateSearchFilters(
    filters: ModelSearchRequest,
    currentUser: UserEntity,
  ): Promise<any> {
    // Get user's organization IDs for organization-based access
    const userOrganizations =
      await this.organizationUserProvider.getUserOrganizations(currentUser.id);
    const userOrgIds = userOrganizations.map((org) => org.organizationId);

    // If no filters are provided, set the userId filter to the current user's ID
    if (
      !filters.hasOwnProperty('privacy') &&
      !filters.hasOwnProperty('userId')
    ) {
      const baseFilters = [
        { ...filters, user: { id: currentUser.id } },
        { ...filters, privacy: PrivacyEnum.PUBLIC },
      ];

      // Add organization-based access if user belongs to any organizations
      if (userOrgIds.length > 0) {
        baseFilters.push({
          ...filters,
          privacy: PrivacyEnum.PRIVATE,
          // organizationIds will be handled separately in the provider
        });
      }

      return baseFilters;
    }

    // If the privacy filter is set to private and the user is not the owner, throw an exception
    if (
      filters.hasOwnProperty('privacy') &&
      filters.privacy !== PrivacyEnum.PUBLIC &&
      filters.hasOwnProperty('userId') &&
      filters.userId !== currentUser.id
    ) {
      throw new UnauthorizedException(
        "You are not allowed to view other users' private models.",
      );
    }

    // if an userId is set, and it's different than the current user, then allow for only public models
    if (filters.hasOwnProperty('userId') && filters.userId !== currentUser.id) {
      filters.privacy = PrivacyEnum.PUBLIC;
    }

    // if no userId is set and the desired privacy differs from public, then allow for only the current user's models
    if (
      !filters.hasOwnProperty('userId') &&
      PrivacyEnum.PUBLIC !== filters.privacy
    ) {
      filters.userId = currentUser.id;
    }

    return filters;
  }
}
