import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1691574639346 implements MigrationInterface {
    name = 'Api1691574639346'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "o_auth_token" character varying
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "o_auth_token"
        `);
    }

}
