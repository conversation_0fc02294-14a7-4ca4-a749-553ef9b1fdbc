import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserEntity } from '../../user/entity/user.entity';
import { OrganizationUserEntity } from '../entity/organization-user.entity';
import { OrganizationEntity } from '../entity/organization.entity';
import { OrganizationManager } from './manager';

describe('OrganizationManager', () => {
  let manager: OrganizationManager;
  let organizationRepository: Repository<OrganizationEntity>;
  let organizationUserRepository: Repository<OrganizationUserEntity>;

  beforeEach(async () => {
    const organizationRepoMock = {
      save: jest.fn(),
      softDelete: jest.fn(),
    };

    const organizationUserRepoMock = {
      save: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrganizationManager,
        {
          provide: getRepositoryToken(OrganizationEntity),
          useValue: organizationRepoMock,
        },
        {
          provide: getRepositoryToken(OrganizationUserEntity),
          useValue: organizationUserRepoMock,
        },
      ],
    }).compile();

    manager = module.get<OrganizationManager>(OrganizationManager);
    organizationRepository = module.get<Repository<OrganizationEntity>>(
      getRepositoryToken(OrganizationEntity),
    );
    organizationUserRepository = module.get<Repository<OrganizationUserEntity>>(
      getRepositoryToken(OrganizationUserEntity),
    );
  });

  it('should be defined', () => {
    expect(manager).toBeDefined();
  });

  describe('create', () => {
    it('should create a new organization and link the owner', async () => {
      const organization = new OrganizationEntity();
      const owner = new UserEntity();
      owner.id = 'user-1';

      organizationRepository.save = jest.fn().mockResolvedValue(organization);
      organizationUserRepository.save = jest.fn().mockResolvedValue(undefined);

      const result = await manager.create(organization, owner);

      expect(organizationRepository.save).toHaveBeenCalledWith(organization);
      expect(organizationUserRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: owner.id,
          organizationId: organization.id,
          isOwner: true,
          isAdmin: true,
        }),
      );
      expect(result).toBe(organization);
    });
  });

  describe('update', () => {
    it('should update an existing organization', async () => {
      const organization = new OrganizationEntity();
      organizationRepository.save = jest.fn().mockResolvedValue(organization);

      const result = await manager.update(organization);

      expect(organizationRepository.save).toHaveBeenCalledWith(organization);
      expect(result).toBe(organization);
    });
  });

  describe('delete', () => {
    it('should soft delete an organization', async () => {
      const organization = new OrganizationEntity();
      organization.id = 'org-1';
      organizationRepository.softDelete = jest
        .fn()
        .mockResolvedValue(undefined);

      await manager.delete(organization);

      expect(organizationRepository.softDelete).toHaveBeenCalledWith(
        organization.id,
      );
    });
  });
});
