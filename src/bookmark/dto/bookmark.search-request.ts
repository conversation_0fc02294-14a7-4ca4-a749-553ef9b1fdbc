import { ApiProperty } from '@nestjs/swagger';
import { IsIn, IsOptional, IsString } from 'class-validator';
import { BaseSearchRequest } from 'src/core/dto/base.search-request';

export class BookmarkSearchRequest extends BaseSearchRequest {
  @ApiProperty({
    description: 'Type of the bookmark to search for',
    required: false,
  })
  @IsString()
  @IsOptional()
  type?: 'model' | 'image';

  @IsOptional()
  @ApiProperty({ description: 'Field to sort by', required: false })
  sortBy? = 'createdAt';

  @IsOptional()
  @IsIn(['ASC', 'DESC'])
  @ApiProperty({
    description: 'Sort order',
    enum: ['ASC', 'DESC'],
    required: false,
  })
  sortOrder?: 'ASC' | 'DESC' = 'DESC'; // default sort order is ascending
}
