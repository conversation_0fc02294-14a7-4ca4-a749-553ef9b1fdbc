import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { StatusEnum } from '../enum/status.enum';
import { ModelEntity } from './model.entity';

export interface SystemVersionData {
  systemVersion: number;
  status: StatusEnum;
  prompt?: string;
  trainedAt?: Date;
}

@Entity('model_version')
export class ModelVersionEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => ModelEntity)
  @JoinColumn({ name: 'model_id' })
  model: ModelEntity;

  @Column({ type: 'uuid', nullable: false })
  @Index()
  modelId: string;

  @Column({ nullable: false, default: 1 })
  version: number;

  @Column({ type: 'json', nullable: true })
  systemVersionsData?: SystemVersionData[];

  @Column({ type: 'text', nullable: true })
  filename?: string;

  @Column({ type: 'text', nullable: true })
  prompt?: string;

  @Column({ type: 'timestamp', nullable: true })
  trainedAt?: Date;

  @Column({
    type: 'varchar',
    enum: StatusEnum,
    default: StatusEnum.PENDING,
    nullable: false,
  })
  status: StatusEnum;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt?: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
