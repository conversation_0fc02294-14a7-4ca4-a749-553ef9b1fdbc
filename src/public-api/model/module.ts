import { DynamicModule, Module, forwardRef } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { AuthModule } from 'src/auth/auth.module';
import { jwtConfig } from 'src/auth/config/jwt.config';
import { ImageCompletionModule } from 'src/image-completion/module';
import { ModelModule } from 'src/model/module';
import { UserModule } from 'src/user/user.module';
import { CreateController } from './controller/create.controller';
import { DeleteController } from './controller/delete.controller';
import { ReadController } from './controller/read.controller';
import { ThumbnailController } from './controller/thumbnail.controller';
import { UpdateController } from './controller/update.controller';
import { ModelRequestManager } from './service/request-manager';
import { ModelResponseMapper } from './service/response-mapper';
import { TrainingDataService } from './service/training-data.service';
import { OrganizationModule } from 'src/organization/organization.module';

@Module({})
export class PublicModelModule {
  static register(enableControllers: boolean): DynamicModule {
    return {
      module: PublicModelModule,
      providers: [
        ModelRequestManager,
        ModelResponseMapper,
        TrainingDataService,
      ],
      imports: [
        PassportModule,
        JwtModule.register(jwtConfig),
        forwardRef(() => AuthModule),
        forwardRef(() => ImageCompletionModule),
        forwardRef(() => UserModule),
        forwardRef(() => ModelModule),
        forwardRef(() => OrganizationModule),
      ],
      controllers: enableControllers
        ? [
            ReadController,
            CreateController,
            UpdateController,
            DeleteController,
            ThumbnailController,
          ]
        : [],
    };
  }
}
