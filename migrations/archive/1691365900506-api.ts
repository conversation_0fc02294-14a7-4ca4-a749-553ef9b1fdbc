import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1691365900506 implements MigrationInterface {
  name = 'Api1691365900506';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TYPE "public"."model_status_enum" AS ENUM('new', 'training', 'ready', 'failed')
        `);
    await queryRunner.query(`
            ALTER TABLE "model"
            ADD "status" "public"."model_status_enum" NOT NULL DEFAULT 'new'
        `);
    await queryRunner.query(`
            ALTER TABLE "model"
            ADD "thumbnail_options" json
        `);
    await queryRunner.query(`
            ALTER TABLE "model"
            ADD "is_active" boolean NOT NULL DEFAULT false
        `);
    await queryRunner.query(`
            ALTER TABLE "model" DROP COLUMN "privacy"
        `);
    await queryRunner.query(`
            CREATE TYPE "public"."model_privacy_enum" AS ENUM('public', 'private', 'licensed')
        `);
    await queryRunner.query(`
            ALTER TABLE "model"
            ADD "privacy" "public"."model_privacy_enum" NOT NULL DEFAULT 'public'
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "model" DROP COLUMN "privacy"
        `);
    await queryRunner.query(`
            DROP TYPE "public"."model_privacy_enum"
        `);
    await queryRunner.query(`
            ALTER TABLE "model"
            ADD "privacy" character varying NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "model" DROP COLUMN "is_active"
        `);
    await queryRunner.query(`
            ALTER TABLE "model" DROP COLUMN "thumbnail_options"
        `);
    await queryRunner.query(`
            ALTER TABLE "model" DROP COLUMN "status"
        `);
    await queryRunner.query(`
            DROP TYPE "public"."model_status_enum"
        `);
  }
}
