import { BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { Notifier } from 'src/notification/service/notifier';
import { UserEntity } from 'src/user/entity/user.entity';
import { UserManager } from 'src/user/service/manager';
import { UserProvider } from 'src/user/service/provider';
import Stripe from 'stripe';
import { Repository } from 'typeorm';
import { CreditTypeEnum } from '../entity/credit-type.enum';
import { SubscriptionStatusEnum } from '../entity/subscription-status.enum';
import { SubscriptionEntity } from '../entity/subscription.entity';
import { TransactionTypeEnum } from '../entity/transaction-type.enum';
import { SubscriptionActivatedEvent } from '../event/subscription-activated.event';
import { SubscriptionCanceledEvent } from '../event/subscription-canceled.event';
import { CreditPackageProvider } from './credit-package.provider';
import { EmailManager } from './email.manager';
import { SubscriptionManager } from './manager'; // Corrected import path
import { SubscriptionProvider } from './provider';
import { StripeService } from './stripe.service';
import { TransactionManager } from './transaction.manager';
import { UserCreditBalanceManager } from './user-credit-balance.manager';

describe('SubscriptionManager', function () {
  let subscriptionManager: SubscriptionManager;
  let repository: jest.Mocked<Repository<SubscriptionEntity>>;
  let provider: jest.Mocked<SubscriptionProvider>;
  let stripe: {
    checkout: {
      sessions: {
        create: jest.Mock;
        retrieve: jest.Mock;
        expire: jest.Mock;
      };
    };
    subscriptions: {
      retrieve: jest.Mock;
      cancel: jest.Mock;
    };
    customers: {
      create: jest.Mock;
    };
  };
  let userManager: jest.Mocked<UserManager>;
  let creditPackageProvider: jest.Mocked<CreditPackageProvider>;
  let transactionManager: jest.Mocked<TransactionManager>;
  let configService: jest.Mocked<ConfigService>;
  let userCreditBalanceManager: jest.Mocked<UserCreditBalanceManager>;
  let emailManager: jest.Mocked<EmailManager>;
  let eventEmitter: jest.Mocked<EventEmitter2>;
  let logger: jest.Mocked<Logger>;
  let notifier: jest.Mocked<Notifier>;

  beforeEach(async function () {
    repository = {
      save: jest.fn(),
      softDelete: jest.fn(),
      findOne: jest.fn(),
      find: jest.fn(),
      metadata: {},
      manager: {},
    } as any;

    provider = {
      findPendingSubscriptionsForUser: jest.fn(),
      findPending: jest.fn(),
      getBy: jest.fn(),
      findCurrentlyActiveSubscriptionForUser: jest.fn(),
      findAll: jest.fn(),
      findBy: jest.fn(),
      countBy: jest.fn(),
      findOne: jest.fn(),
      count: jest.fn(),
      prepareFindOneOptions: jest.fn(),
      prepareFindManyOptions: jest.fn(),
    } as any;

    stripe = {
      checkout: {
        sessions: {
          create: jest.fn(),
          retrieve: jest.fn(),
          expire: jest.fn(),
        },
      },
      subscriptions: {
        retrieve: jest.fn(),
        cancel: jest.fn(),
      },
      customers: {
        create: jest.fn(),
      },
    };

    userManager = {
      update: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
      generateEmailValidationCode: jest.fn(),
      validateEmail: jest.fn(),
      sendValidationEmail: jest.fn(),
      validateUserPassword: jest.fn(),
      encryptPassword: jest.fn(),
      initiatePasswordReset: jest.fn(),
    } as any;

    creditPackageProvider = {
      getBy: jest.fn(),
      findAll: jest.fn(),
      findBy: jest.fn(),
      countBy: jest.fn(),
      findOne: jest.fn(),
      count: jest.fn(),
      prepareFindOneOptions: jest.fn(),
      prepareFindManyOptions: jest.fn(),
    } as any;

    transactionManager = {
      register: jest.fn(),
      getOrCreate: jest.fn(),
      create: jest.fn(),
    } as any;

    configService = {
      get: jest.fn(),
      getOrThrow: jest.fn(),
    } as any;

    userCreditBalanceManager = {
      increase: jest.fn(),
      deduct: jest.fn(),
      getNextExpiring: jest.fn(),
      getOrCreate: jest.fn(),
      save: jest.fn(),
    } as any;

    emailManager = {
      sendPaidEmail: jest.fn(),
      sendRenewedEmail: jest.fn(),
      sendCancellationEmail: jest.fn(),
    } as any;

    eventEmitter = {
      emit: jest.fn(),
      emitAsync: jest.fn(),
      addListener: jest.fn(),
      on: jest.fn(),
      once: jest.fn(),
      removeListener: jest.fn(),
      off: jest.fn(),
      removeAllListeners: jest.fn(),
      setMaxListeners: jest.fn(),
      getMaxListeners: jest.fn(),
      listeners: jest.fn(),
      rawListeners: jest.fn(),
      listenerCount: jest.fn(),
      eventNames: jest.fn(),
    } as any;

    logger = {
      debug: jest.fn(),
      error: jest.fn(),
      log: jest.fn(),
      verbose: jest.fn(),
      warn: jest.fn(),
    } as any;

    notifier = {
      dispatch: jest.fn(),
      mapUserChannels: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SubscriptionManager,
        {
          provide: getRepositoryToken(SubscriptionEntity),
          useValue: repository,
        },
        {
          provide: SubscriptionProvider,
          useValue: provider,
        },
        {
          provide: 'Stripe',
          useValue: stripe,
        },
        {
          provide: UserProvider,
          useValue: {
            get: jest.fn().mockResolvedValue({
              id: 'user-1',
              email: '<EMAIL>',
            }),
          },
        },
        {
          provide: UserManager,
          useValue: userManager,
        },
        {
          provide: CreditPackageProvider,
          useValue: creditPackageProvider,
        },
        {
          provide: StripeService,
          useValue: {
            getOrCreateStripePriceId: jest.fn(),
          },
        },
        {
          provide: TransactionManager,
          useValue: transactionManager,
        },
        {
          provide: ConfigService,
          useValue: configService,
        },
        {
          provide: UserCreditBalanceManager,
          useValue: userCreditBalanceManager,
        },
        {
          provide: EmailManager,
          useValue: emailManager,
        },
        {
          provide: EventEmitter2,
          useValue: eventEmitter,
        },
        {
          provide: Logger,
          useValue: logger,
        },
        {
          provide: Notifier,
          useValue: notifier,
        },
      ],
    }).compile();

    subscriptionManager = module.get<SubscriptionManager>(SubscriptionManager);
  });

  describe('create', function () {
    it('should throw BadRequestException if there are pending subscriptions', async function () {
      const entity = new SubscriptionEntity();
      entity.user = new UserEntity();
      entity.user.id = 'user-1';

      provider.findPendingSubscriptionsForUser.mockResolvedValue([
        new SubscriptionEntity(),
      ]);

      await expect(subscriptionManager.create(entity)).rejects.toThrow(
        BadRequestException,
      );
      expect(repository.save).not.toHaveBeenCalled();
    });

    it('should save entity with status NEW and call createStripeCheckout if price > 0', async function () {
      const entity = new SubscriptionEntity();
      entity.user = new UserEntity();
      entity.user.id = 'user-1';
      entity.price = 10;

      provider.findPendingSubscriptionsForUser.mockResolvedValue([]);
      repository.save.mockResolvedValue(entity);
      jest
        .spyOn(subscriptionManager, 'createStripeCheckout')
        .mockResolvedValue(undefined);

      const result = await subscriptionManager.create(entity);

      expect(entity.status).toBe(SubscriptionStatusEnum.NEW);
      expect(repository.save).toHaveBeenCalledWith(entity);
      expect(subscriptionManager.createStripeCheckout).toHaveBeenCalledWith(
        entity,
      );
      expect(result).toBe(entity);
    });

    it('should save entity with status NEW and not call createStripeCheckout if price == 0', async function () {
      const entity = new SubscriptionEntity();
      entity.user = new UserEntity();
      entity.user.id = 'user-1';
      entity.price = 0;

      provider.findPendingSubscriptionsForUser.mockResolvedValue([]);
      repository.save.mockResolvedValue(entity);
      jest
        .spyOn(subscriptionManager, 'createStripeCheckout')
        .mockResolvedValue(undefined);

      const result = await subscriptionManager.create(entity);

      expect(entity.status).toBe(SubscriptionStatusEnum.NEW);
      expect(repository.save).toHaveBeenCalledWith(entity);
      expect(subscriptionManager.createStripeCheckout).not.toHaveBeenCalled();
      expect(result).toBe(entity);
    });
  });

  describe('createStripeCheckout', function () {
    it('should create stripe checkout session and update subscription', async function () {
      const subscription = new SubscriptionEntity();
      subscription.user = new UserEntity();
      subscription.user.id = 'user-1';
      subscription.creditPackage = {
        stripePriceId: 'price_123',
        isRecurring: true,
      } as any;
      subscription.price = 10;

      configService.get.mockReturnValue('http://localhost:3000');
      stripe.checkout.sessions.create.mockResolvedValue({
        id: 'cs_test_123',
        url: 'https://checkout.stripe.com/pay/cs_test_123',
      });
      jest
        .spyOn(subscriptionManager, 'getOrCreateStripeCustomerId')
        .mockResolvedValue('cus_123');

      await subscriptionManager.createStripeCheckout(subscription);

      expect(configService.get).toHaveBeenCalledWith('FRONTEND_URL');
      expect(stripe.checkout.sessions.create).toHaveBeenCalled();
      expect(subscription.status).toBe(SubscriptionStatusEnum.PENDING);
      expect(subscription.stripeCheckoutUrl).toBe(
        'https://checkout.stripe.com/pay/cs_test_123',
      );
      expect(subscription.stripeCheckoutSessionId).toBe('cs_test_123');
      expect(repository.save).toHaveBeenCalledWith(subscription);
    });
  });

  describe('updateStripeCheckoutStatus', function () {
    it('should activate subscription if payment_status is paid and not recurring', async function () {
      const subscription = new SubscriptionEntity();
      subscription.stripeCheckoutSessionId = 'cs_test_123';
      subscription.creditPackage = {
        isRecurring: false,
      } as any;
      subscription.paidAt = null;

      const session = {
        id: 'cs_test_123',
        object: 'checkout.session',
        payment_status: 'paid',
        created: 1633072800,
        cancel_url: '',
        success_url: '',
        mode: 'payment',
      } as unknown as Stripe.Checkout.Session;

      stripe.checkout.sessions.retrieve.mockResolvedValue(session);

      jest
        .spyOn(subscriptionManager, 'activateSubscription')
        .mockResolvedValue(undefined);

      await subscriptionManager.updateStripeCheckoutStatus(subscription);

      expect(subscription.paidAt).toEqual(new Date(1633072800 * 1000));
      expect(subscription.stripeCheckoutUrl).toBeNull();
      expect(repository.save).toHaveBeenCalledWith(subscription);
      expect(subscriptionManager.activateSubscription).toHaveBeenCalledWith(
        subscription,
      );
    });

    it('should update stripe subscription status if payment_status is paid and is recurring', async function () {
      const subscription = new SubscriptionEntity();
      subscription.paidAt = new Date(1633072801 * 1000);
      subscription.stripeCheckoutSessionId = 'cs_test_123';
      subscription.creditPackage = {
        isRecurring: true,
      } as any;

      const session = {
        id: 'cs_test_123',
        object: 'checkout.session',
        payment_status: 'paid',
        created: 1633072800,
        subscription: 'sub_123',
        cancel_url: '',
        success_url: '',
        mode: 'subscription',
        status: 'complete',
      } as Partial<Stripe.Checkout.Session>;

      stripe.checkout.sessions.retrieve.mockResolvedValue(session);

      jest
        .spyOn(subscriptionManager, 'updateStripeSubscriptionStatus')
        .mockResolvedValue(undefined);

      await subscriptionManager.updateStripeCheckoutStatus(subscription);

      expect(stripe.checkout.sessions.retrieve).toHaveBeenCalledWith(
        'cs_test_123',
      );
      expect(subscription.externalReference).toBe('sub_123');
      expect(subscription.paidAt).toEqual(new Date(1633072800 * 1000));
      expect(subscription.stripeCheckoutUrl).toBeNull();
      expect(repository.save).toHaveBeenCalledWith(subscription);
      expect(
        subscriptionManager.updateStripeSubscriptionStatus,
      ).toHaveBeenCalledWith(subscription);
    });
  });

  describe('activateSubscription', function () {
    it('should activate subscription and emit event', async function () {
      const subscription = new SubscriptionEntity();
      subscription.id = 'sub-1';
      subscription.userId = 'user-1';
      subscription.user = new UserEntity();
      subscription.user.id = 'user-1';
      subscription.status = SubscriptionStatusEnum.PENDING;

      jest
        .spyOn(subscriptionManager, 'registerTopUpTransaction')
        .mockResolvedValue(undefined);

      await subscriptionManager.activateSubscription(subscription);

      expect(subscription.status).toBe(SubscriptionStatusEnum.ACTIVE);
      expect(repository.save).toHaveBeenCalledWith(subscription);
      expect(subscriptionManager.registerTopUpTransaction).toHaveBeenCalledWith(
        subscription,
      );
      expect(eventEmitter.emit).toHaveBeenCalledWith(
        'subscription.activated',
        new SubscriptionActivatedEvent({
          id: subscription.id,
          userId: subscription.user.id,
        }),
      );
    });

    describe('cancelSubscription', function () {
      it('should soft delete new or pending subscription and expire checkout session', async function () {
        const subscription = new SubscriptionEntity();
        subscription.id = 'sub-1';
        subscription.userId = 'user-1';
        subscription.user = new UserEntity();
        subscription.user.id = 'user-1';
        subscription.status = SubscriptionStatusEnum.NEW;
        subscription.stripeCheckoutSessionId = 'cs_test_123';

        await subscriptionManager.cancelSubscription(subscription);

        expect(repository.softDelete).toHaveBeenCalledWith(subscription.id);
        expect(stripe.checkout.sessions.expire).toHaveBeenCalledWith(
          'cs_test_123',
        );
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          'subscription.canceled',
          new SubscriptionCanceledEvent({
            id: subscription.id,
            userId: subscription.user.id,
          }),
        );
      });

      it('should cancel active subscription in Stripe and update status', async function () {
        const subscription = new SubscriptionEntity();
        subscription.id = 'sub-1';
        subscription.userId = 'user-1';
        subscription.user = new UserEntity();
        subscription.user.id = 'user-1';
        subscription.status = SubscriptionStatusEnum.ACTIVE;
        subscription.externalReference = 'sub_123';

        stripe.subscriptions.retrieve.mockResolvedValue({
          status: 'active',
          id: 'sub_123',
        });
        stripe.subscriptions.cancel.mockResolvedValue({});

        await subscriptionManager.cancelSubscription(subscription);

        expect(stripe.subscriptions.retrieve).toHaveBeenCalledWith('sub_123');
        expect(stripe.subscriptions.cancel).toHaveBeenCalledWith('sub_123');
        expect(subscription.status).toBe(SubscriptionStatusEnum.INACTIVE);
        expect(repository.save).toHaveBeenCalledWith(subscription);
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          'subscription.canceled',
          new SubscriptionCanceledEvent({
            id: subscription.id,
            userId: subscription.user.id,
          }),
        );
      });
    });

    describe('registerTopUpTransaction', function () {
      it('should register transactions and increase user balance', async function () {
        const subscription = new SubscriptionEntity();
        subscription.id = 'sub-1';
        subscription.userId = 'user-1';
        subscription.user = new UserEntity();
        subscription.user.id = 'user-1';
        subscription.createdAt = new Date();
        subscription.creditPackage = {
          id: 'package-1',
          price: 10,
          name: 'Test Package',
          creditTypes: {
            [CreditTypeEnum.IMAGE]: 100, // Adjusted to use valid enum key
          },
        } as any;
        subscription.stripeLatestInvoice = 'inv_123';

        await subscriptionManager.registerTopUpTransaction(subscription);

        const transactionContext = {
          subscriptionId: subscription.id,
          userId: subscription.userId,
          creditPackageId: subscription.creditPackage.id,
          stripeInvoice: subscription.stripeLatestInvoice,
        };

        expect(transactionManager.register).toHaveBeenCalledWith(
          TransactionTypeEnum.SUBSCRIPTION,
          subscription.creditPackage.price,
          subscription.creditPackage.name,
          expect.stringContaining(subscription.id),
          false,
          subscription.userId,
          null,
          transactionContext,
        );
        expect(transactionManager.register).toHaveBeenCalledWith(
          TransactionTypeEnum.TOP_UP,
          100,
          'image',
          expect.stringContaining(subscription.id),
          false,
          subscription.userId,
          null,
          transactionContext,
        );
        expect(userCreditBalanceManager.increase).toHaveBeenCalledWith(
          'image',
          100,
          subscription.userId,
          null,
          expect.any(Date),
        );
      });
    });

    describe('getOrCreateStripeCustomerId', function () {
      it('should return existing customer ID if available', async function () {
        const user = new UserEntity();
        user.stripeCustomerId = 'cus_123';

        const result = await subscriptionManager.getOrCreateStripeCustomerId(
          user,
        );

        expect(result).toBe('cus_123');
        expect(stripe.customers.create).not.toHaveBeenCalled();
      });

      it('should create new Stripe customer if ID not available', async function () {
        const user = new UserEntity();
        user.email = '<EMAIL>';
        userManager.update.mockResolvedValue(user);

        stripe.customers.create.mockResolvedValue({ id: 'cus_123' });

        const result = await subscriptionManager.getOrCreateStripeCustomerId(
          user,
        );

        expect(stripe.customers.create).toHaveBeenCalledWith({
          email: '<EMAIL>',
        });
        expect(userManager.update).toHaveBeenCalledWith(user);
        expect(result).toBe('cus_123');
      });
    });

    describe('createFreeSubscription', function () {
      it('should create and activate free subscription', async function () {
        const user = new UserEntity();
        const creditPackage = {
          name: 'Free',
          price: 0,
          isVisible: false,
        };

        creditPackageProvider.getBy.mockResolvedValue(creditPackage as any);
        jest
          .spyOn(subscriptionManager, 'create')
          .mockResolvedValue(new SubscriptionEntity());
        jest
          .spyOn(subscriptionManager, 'activateSubscription')
          .mockResolvedValue(undefined);

        await subscriptionManager.createFreeSubscription(user);

        expect(creditPackageProvider.getBy).toHaveBeenCalledWith({
          name: 'Free',
          isVisible: false,
        });
        expect(subscriptionManager.create).toHaveBeenCalled();
        expect(subscriptionManager.activateSubscription).toHaveBeenCalled();
      });
    });

    describe('refreshPendingSubscriptions', function () {
      it('should refresh pending subscriptions', async function () {
        const subscriptions = [
          new SubscriptionEntity(),
          new SubscriptionEntity(),
        ];
        subscriptions[0].status = SubscriptionStatusEnum.PENDING;
        subscriptions[1].status = SubscriptionStatusEnum.ACTIVE;

        provider.findPending.mockResolvedValue(subscriptions);
        jest
          .spyOn(subscriptionManager, 'updateStripeCheckoutStatus')
          .mockResolvedValue(undefined);
        jest
          .spyOn(subscriptionManager, 'updateStripeSubscriptionStatus')
          .mockResolvedValue(undefined);

        await subscriptionManager.refreshPendingSubscriptions();

        expect(provider.findPending).toHaveBeenCalledWith(100);
        expect(
          subscriptionManager.updateStripeCheckoutStatus,
        ).toHaveBeenCalledWith(subscriptions[0]);
        expect(
          subscriptionManager.updateStripeSubscriptionStatus,
        ).toHaveBeenCalledWith(subscriptions[1]);
      });
    });
  });
});
