import { DynamicModule, forwardRef, Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthModule } from '../auth/auth.module';
import { jwtConfig } from '../auth/config/jwt.config';
import { CoreModule } from '../core/core.module';
import { ImageCompletionModule } from '../image-completion/module';
import { ModelModule } from '../model/module';
import { NotificationModule } from '../notification/module';
import { SubscriptionModule } from '../subscription/module';
import { UserModule } from '../user/user.module';
import { CreateController } from './controller/create.controller';
import { DeleteController } from './controller/delete.controller';
import { OrganizationModelController } from './controller/model.controller';
import { ReadController } from './controller/read.controller';
import { SubscriptionController } from './controller/subscription.controller';
import { UpdateController } from './controller/update.controller';
import { UserController } from './controller/user.controller';
import { OrganizationInviteEntity } from './entity/organization-invite.entity';
import { OrganizationSubscriptionEntity } from './entity/organization-subscription.entity';
import { OrganizationUserEntity } from './entity/organization-user.entity';
import { OrganizationEntity } from './entity/organization.entity';
import { OrganizationCreatedListener } from './listener/organization-created.listener';
import { UserCreatedListener } from './listener/user-created.listener';
import { OrganizationManager } from './service/manager';
import { OrganizationInviteManager } from './service/organization-invite.manager';
import { OrganizationInviteProvider } from './service/organization-invite.provider';
import { OrganizationInviteRequestManager } from './service/organization-invite.request-manager';
import { OrganizationInviteResponseMapper } from './service/organization-invite.response-mapper';
import { OrganizationSubscriptionRequestManager } from './service/organization-subscription-request.manager';
import { OrganizationSubscriptionManager } from './service/organization-subscription.manager';
import { OrganizationSubscriptionProvider } from './service/organization-subscription.provider';
import { OrganizationSubscriptionResponseMapper } from './service/organization-subscription.response-mapper';
import { OrganizationUserManager } from './service/organization-user.manager';
import { OrganizationUserProvider } from './service/organization-user.provider';
import { OrganizationUserRequestManager } from './service/organization-user.request.manager';
import { OrganizationUserResponseMapper } from './service/organization-user.response-mapper';
import { OrganizationProvider } from './service/provider';
import { OrganizationRequestManager } from './service/request-manager';
import { OrganizationResponseMapper } from './service/response-mapper';
import { StripeWebhookHandler } from './service/stripe.webhook-handler';
import { OrganizationCreditBalanceResponseMapper } from './service/organization-credit-balance.response-mapper';

@Module({
  imports: [
    CoreModule,
    TypeOrmModule.forFeature([
      OrganizationEntity,
      OrganizationUserEntity,
      OrganizationInviteEntity,
      OrganizationSubscriptionEntity,
    ]),
    JwtModule.register(jwtConfig),
    forwardRef(() => AuthModule),
    forwardRef(() => ImageCompletionModule),
    forwardRef(() => ModelModule),
    forwardRef(() => SubscriptionModule),
    forwardRef(() => UserModule),
    forwardRef(() => NotificationModule),
  ],
  providers: [
    OrganizationManager,
    OrganizationProvider,
    OrganizationUserProvider,
    OrganizationInviteProvider,
    OrganizationInviteRequestManager,
    OrganizationInviteManager,
    OrganizationInviteResponseMapper,
    OrganizationRequestManager,
    OrganizationResponseMapper,
    OrganizationCreatedListener,
    OrganizationUserManager,
    OrganizationUserRequestManager,
    OrganizationUserResponseMapper,
    UserCreatedListener,
    OrganizationSubscriptionManager,
    OrganizationSubscriptionRequestManager,
    OrganizationSubscriptionResponseMapper,
    OrganizationSubscriptionProvider,
    OrganizationCreditBalanceResponseMapper,
    StripeWebhookHandler,
  ],
  exports: [
    OrganizationProvider,
    OrganizationUserProvider,
    OrganizationManager,
    OrganizationUserManager,
    OrganizationSubscriptionManager,
    OrganizationInviteProvider,
    OrganizationInviteManager,
    OrganizationResponseMapper,
    StripeWebhookHandler,
  ],
})
export class OrganizationModule {
  static register(enableControllers: boolean): DynamicModule {
    return {
      module: OrganizationModule,
      controllers: enableControllers
        ? [
            CreateController,
            DeleteController,
            OrganizationModelController,
            ReadController,
            UpdateController,
            SubscriptionController,
            UserController,
          ]
        : [],
    };
  }
}
