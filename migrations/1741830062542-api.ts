import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1741830062542 implements MigrationInterface {
  name = 'Api1741830062542';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            DROP INDEX IF EXISTS "public"."IDX_f29cfb2e32f6d58394bf0ce7e5"
        `);
    await queryRunner.query(`
            CREATE TYPE "public"."organization_subscription_status_enum" AS ENUM(
                'new',
                'pending',
                'active',
                'expired',
                'cancelled'
            )
        `);
    await queryRunner.query(`
            CREATE TABLE "organization_subscription" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "name" character varying NOT NULL,
                "price" numeric(10, 2) NOT NULL,
                "status" "public"."organization_subscription_status_enum" NOT NULL DEFAULT 'new',
                "external_reference" text,
                "stripe_checkout_session_id" character varying,
                "stripe_checkout_url" text,
                "stripe_latest_invoice" character varying,
                "expires_at" TIMESTAMP NOT NULL,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "organization_id" uuid NOT NULL,
                "credit_package_id" uuid NOT NULL,
                CONSTRAINT "PK_7a8b198dd9b0474bb1bdd391aa3" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            ALTER TABLE "organization" DROP COLUMN "followers_count"
        `);
    await queryRunner.query(`
            ALTER TABLE "organization" DROP COLUMN "following_count"
        `);
    await queryRunner.query(`
            ALTER TABLE "organization" DROP COLUMN "blocked_at"
        `);
    await queryRunner.query(`
            ALTER TABLE "organization" DROP COLUMN "is_default"
        `);
    await queryRunner.query(`
            ALTER TABLE "organization" DROP COLUMN "image_queue"
        `);
    await queryRunner.query(`
            ALTER TABLE "organization" DROP COLUMN "locale"
        `);
    await queryRunner.query(`
            ALTER TABLE "organization" DROP COLUMN "model_queue"
        `);
    await queryRunner.query(`
            ALTER TABLE "credit_package"
            ADD "target_entity" character varying NOT NULL DEFAULT 'user'
        `);
    await queryRunner.query(`
            ALTER TABLE "organization"
            ADD "owner_id" uuid
        `);
    await queryRunner.query(`
            ALTER TABLE "organization_user" DROP CONSTRAINT "FK_f29cfb2e32f6d58394bf0ce7e5c"
        `);
    await queryRunner.query(`
            ALTER TABLE "organization_user"
            ALTER COLUMN "user_id"
            SET NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "organization" DROP COLUMN "description"
        `);
    await queryRunner.query(`
            ALTER TABLE "organization"
            ADD "description" text
        `);
    await queryRunner.query(`
            ALTER TABLE "organization" DROP COLUMN "website"
        `);
    await queryRunner.query(`
            ALTER TABLE "organization"
            ADD "website" text
        `);
    await queryRunner.query(`
            ALTER TABLE "organization" DROP COLUMN "profile_picture"
        `);
    await queryRunner.query(`
            ALTER TABLE "organization"
            ADD "profile_picture" text
        `);
    await queryRunner.query(
      `ALTER TABLE "organization" ALTER COLUMN "images_generated" SET DEFAULT '0'`,
    );
    await queryRunner.query(`UPDATE "organization" SET "images_generated" = 0`);
    await queryRunner.query(
      `ALTER TABLE "organization" ALTER COLUMN "images_generated" SET NOT NULL`,
    );

    await queryRunner.query(
      `ALTER TABLE "organization" ALTER COLUMN "images_available" SET DEFAULT '0'`,
    );
    await queryRunner.query(`UPDATE "organization" SET "images_available" = 0`);
    await queryRunner.query(
      `ALTER TABLE "organization" ALTER COLUMN "images_available" SET NOT NULL`,
    );

    await queryRunner.query(
      `ALTER TABLE "organization" ALTER COLUMN "models_available" SET DEFAULT '0'`,
    );
    await queryRunner.query(`UPDATE "organization" SET "models_available" = 0`);
    await queryRunner.query(
      `ALTER TABLE "organization" ALTER COLUMN "models_available" SET NOT NULL`,
    );
    await queryRunner.query(`
            CREATE INDEX "idx_organization_user_organization_id" ON "organization_user" ("organization_id")
        `);
    await queryRunner.query(`
            CREATE INDEX "idx_organization_user_user_id" ON "organization_user" ("user_id")
        `);
    await queryRunner.query(`
            ALTER TABLE "organization_subscription"
            ADD CONSTRAINT "FK_c83d041b37d2ec31ddb9e98e660" FOREIGN KEY ("organization_id") REFERENCES "organization"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "organization_subscription"
            ADD CONSTRAINT "FK_bbcaf4d3428d4c657ac8b9b3f0e" FOREIGN KEY ("credit_package_id") REFERENCES "credit_package"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "organization_user"
            ADD CONSTRAINT "FK_f29cfb2e32f6d58394bf0ce7e5c" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "organization"
            ADD CONSTRAINT "FK_44b825ff54ae787d79fb347c625" FOREIGN KEY ("owner_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "organization" DROP CONSTRAINT "FK_44b825ff54ae787d79fb347c625"
        `);
    await queryRunner.query(`
            ALTER TABLE "organization_user" DROP CONSTRAINT "FK_f29cfb2e32f6d58394bf0ce7e5c"
        `);
    await queryRunner.query(`
            ALTER TABLE "organization_subscription" DROP CONSTRAINT "FK_bbcaf4d3428d4c657ac8b9b3f0e"
        `);
    await queryRunner.query(`
            ALTER TABLE "organization_subscription" DROP CONSTRAINT "FK_c83d041b37d2ec31ddb9e98e660"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."idx_organization_user_user_id"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."idx_organization_user_organization_id"
        `);
    await queryRunner.query(`
            ALTER TABLE "organization"
            ALTER COLUMN "models_available" DROP DEFAULT
        `);
    await queryRunner.query(`
            ALTER TABLE "organization"
            ALTER COLUMN "models_available" DROP NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "organization"
            ALTER COLUMN "images_available" DROP DEFAULT
        `);
    await queryRunner.query(`
            ALTER TABLE "organization"
            ALTER COLUMN "images_available" DROP NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "organization"
            ALTER COLUMN "images_generated" DROP DEFAULT
        `);
    await queryRunner.query(`
            ALTER TABLE "organization"
            ALTER COLUMN "images_generated" DROP NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "organization" DROP COLUMN "profile_picture"
        `);
    await queryRunner.query(`
            ALTER TABLE "organization"
            ADD "profile_picture" character varying
        `);
    await queryRunner.query(`
            ALTER TABLE "organization" DROP COLUMN "website"
        `);
    await queryRunner.query(`
            ALTER TABLE "organization"
            ADD "website" character varying
        `);
    await queryRunner.query(`
            ALTER TABLE "organization" DROP COLUMN "description"
        `);
    await queryRunner.query(`
            ALTER TABLE "organization"
            ADD "description" character varying
        `);
    await queryRunner.query(`
            ALTER TABLE "organization_user"
            ALTER COLUMN "user_id" DROP NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "organization_user"
            ADD CONSTRAINT "FK_f29cfb2e32f6d58394bf0ce7e5c" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "organization" DROP COLUMN "owner_id"
        `);
    await queryRunner.query(`
            ALTER TABLE "organization_user" DROP COLUMN "updated_at"
        `);
    await queryRunner.query(`
            ALTER TABLE "organization_user" DROP COLUMN "created_at"
        `);
    await queryRunner.query(`
            ALTER TABLE "organization_user" DROP COLUMN "deleted_at"
        `);
    await queryRunner.query(`
            ALTER TABLE "credit_package" DROP COLUMN "target_entity"
        `);
    await queryRunner.query(`
            ALTER TABLE "organization"
            ADD "model_queue" character varying
        `);
    await queryRunner.query(`
            ALTER TABLE "organization"
            ADD "locale" character varying
        `);
    await queryRunner.query(`
            ALTER TABLE "organization"
            ADD "image_queue" character varying
        `);
    await queryRunner.query(`
            ALTER TABLE "organization"
            ADD "is_default" boolean NOT NULL DEFAULT false
        `);
    await queryRunner.query(`
            ALTER TABLE "organization"
            ADD "blocked_at" TIMESTAMP
        `);
    await queryRunner.query(`
            ALTER TABLE "organization"
            ADD "following_count" integer
        `);
    await queryRunner.query(`
            ALTER TABLE "organization"
            ADD "followers_count" integer
        `);
    await queryRunner.query(`
            DROP TABLE "organization_subscription"
        `);
    await queryRunner.query(`
            DROP TYPE "public"."organization_subscription_status_enum"
        `);
    await queryRunner.query(`
            CREATE INDEX "IDX_f29cfb2e32f6d58394bf0ce7e5" ON "organization_user" ("user_id")
        `);
  }
}
