import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1742387117676 implements MigrationInterface {
  name = 'Api1742387117676';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        INSERT INTO credit_package (name, price, expires_after_months, credit_types, target_entity, seats)
        SELECT 'Enterprise Basic - Monthly', 50000, 1, '{"IMAGE": 100000, "MODEL": 1}', 'organization', 10
        WHERE NOT EXISTS (
            SELECT 1 FROM credit_package WHERE name = 'Enterprise Basic - Monthly'
        );
    `);

    await queryRunner.query(`
        INSERT INTO credit_package (name, price, expires_after_months, credit_types, target_entity, seats)
        SELECT 'Enterprise Basic - Plus', 150000, 1, '{"IMAGE": 500000, "MODEL": 1}', 'organization', 30
        WHERE NOT EXISTS (
            SELECT 1 FROM credit_package WHERE name = 'Enterprise Basic - Plus'
        );
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        DELETE FROM credit_package WHERE name = 'Enterprise Basic - Monthly';
    `);

    await queryRunner.query(`
        DELETE FROM credit_package WHERE name = 'Enterprise Basic - Plus';
    `);
  }
}
