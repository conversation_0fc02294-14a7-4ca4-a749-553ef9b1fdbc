import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1691287327346 implements MigrationInterface {
    name = 'Api1691287327346'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "model" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "name" character varying NOT NULL,
                "prompt" text,
                "privacy" character varying NOT NULL,
                "usages" integer NOT NULL DEFAULT '0',
                "likes" integer NOT NULL DEFAULT '0',
                "reports" integer NOT NULL DEFAULT '0',
                "storage_path" text,
                "thumbnail" text,
                "blocked_at" TIMESTAMP,
                "deleted_at" TIMESTAMP,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "user_id" uuid,
                CONSTRAINT "PK_d6df271bba301d5cc79462912a4" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            ALTER TABLE "model"
            ADD CONSTRAINT "FK_71526a7bd80ed755ce8f78d4da2" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "model" DROP CONSTRAINT "FK_71526a7bd80ed755ce8f78d4da2"
        `);
        await queryRunner.query(`
            DROP TABLE "model"
        `);
    }

}
