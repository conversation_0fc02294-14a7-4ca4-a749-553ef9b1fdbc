import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1710972489060 implements MigrationInterface {
  name = 'Api1710972489060';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "images_available" integer DEFAULT '0'
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "models_available" integer DEFAULT '0'
        `);

    await queryRunner.query(`
            UPDATE "user_account"
            SET "images_available" = ( 
                SELECT COUNT(*) 
                FROM "image_completion" 
                WHERE "image_completion"."user_id" = "user_account"."id" 
                AND "image_completion"."privacy" = 'public' 
                AND "image_completion"."status" = 'ready'
            ),
            "models_available" = (
                SELECT COUNT(*)
                FROM "model"
                WHERE "model"."user_id" = "user_account"."id"
                AND "model"."privacy" = 'public'
                AND "model"."status" = 'available'
            )
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "models_available"
        `);
    await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "images_available"
        `);
  }
}
