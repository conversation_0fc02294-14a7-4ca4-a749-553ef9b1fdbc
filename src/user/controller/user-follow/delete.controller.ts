import {
  Controller,
  Delete,
  HttpCode,
  Param,
  ParseUUIDPipe,
  Request,
} from '@nestjs/common';
import {
  ApiNoContentResponse,
  ApiParam,
  ApiTags,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiNotFoundResponse,
  ApiBadRequestResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { UserFollowManager } from '../../service/user-follow.manager';
import { UserProvider } from 'src/user/service/provider';

@ApiTags('user-follow')
@Controller('/users/current')
export class DeleteController {
  constructor(
    private userProvider: UserProvider,
    private manager: UserFollowManager,
  ) {}

  @ApiOperation({
    operationId: 'user_follow_unfollow',
    summary: 'Unfollow a user',
    description:
      'Removes the follow relationship between the current user and the specified user.\n\n' +
      'Requirements:\n' +
      '- User must be authenticated\n' +
      '- The user to unfollow must exist\n\n' +
      'Required Parameters:\n' +
      '- followingId: The unique identifier of the user to unfollow',
  })
  @ApiNoContentResponse({ description: 'Unfollowed successfully.' })
  @ApiParam({
    name: 'followingId',
    description: 'The unique identifier of the user to unfollow',
    type: 'string',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiNotFoundResponse({
    description: 'Not Found. The user to unfollow could not be found.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`followingId\`: Must be a valid UUID.
  `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Delete('follows/:followingId')
  @Delete('following/:followingId')
  @HttpCode(204)
  async unfollow(
    @Request() request,
    @Param('followingId', new ParseUUIDPipe()) followingId: string,
  ): Promise<void> {
    const following = await this.userProvider.get(followingId);

    await this.manager.unfollow(request.user, following);
  }

  @ApiOperation({
    operationId: 'user_follow_remove_follower',
    summary: 'Remove a follower',
    description:
      "Removes a follower from the current user's followers list.\n\n" +
      'Requirements:\n' +
      '- User must be authenticated\n' +
      '- The follower must exist\n\n' +
      'Required Parameters:\n' +
      '- followerId: The unique identifier of the follower to remove',
  })
  @ApiNoContentResponse({ description: 'Follower removed successfully.' })
  @ApiParam({
    name: 'followerId',
    description: 'The unique identifier of the follower to remove',
    type: 'string',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiNotFoundResponse({
    description: 'Not Found. The follower could not be found.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`followerId\`: Must be a valid UUID.
  `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Delete('followers/:followerId')
  @HttpCode(204)
  async deleteFollower(
    @Request() request,
    @Param('followerId', new ParseUUIDPipe()) followerId: string,
  ): Promise<void> {
    const follower = await this.userProvider.get(followerId);

    await this.manager.unfollow(follower, request.user);
  }
}
