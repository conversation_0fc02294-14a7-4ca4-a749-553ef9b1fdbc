import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { BoardCommentEntity } from '../entity/board-comment.entity';
import { Repository } from 'typeorm';
import { BoardManager } from './manager';
import { Notifier } from 'src/notification/service/notifier';
import { UserResponseMapper } from 'src/user/service/response-mapper';
import { BoardResponseMapper } from './response-mapper';
import { UserProvider } from 'src/user/service/provider';
import { BoardCommentProvider } from './board-comment.provider';
import { BoardEntity } from '../entity/board.entity';
import { BoardCommentedNotification } from '../notification/board-commented.notification';
import { BoardCommentNotification } from '../notification/board-comment.notification';

@Injectable()
export class BoardCommentManager {
  constructor(
    @InjectRepository(BoardCommentEntity)
    private repository: Repository<BoardCommentEntity>,
    private boardManager: BoardManager,
    private notifier: Notifier,
    private userResponseMapper: UserResponseMapper,
    private boardResponseMapper: BoardResponseMapper,
    private userProvider: UserProvider,
    private boardCommentProvider: BoardCommentProvider,
  ) {}

  async comment(
    board: BoardEntity,
    userId: string,
    comment: string,
  ): Promise<void> {
    const boardComment = new BoardCommentEntity();
    boardComment.board = board;
    boardComment.userId = userId;
    boardComment.comment = comment;

    await this.repository.save(boardComment);

    board.comments++;

    await this.boardManager.save(board);

    const user = await this.userProvider.get(userId);

    const entities = await this.boardCommentProvider.findCommentsByBoardId(
      board.id,
    );

    entities.forEach(async (entity) => {
      if (
        entity.userId != boardComment.userId &&
        board.ownerId != entity.userId
      ) {
        await this.notifier.dispatch(
          new BoardCommentNotification(entity.userId, {
            id: board.id,
            thumbnail: this.boardResponseMapper.getThumbnailUrls(board),
            ownerId: board.ownerId,
            commentedBy: boardComment.userId,
            commentedByUsername: user.username,
            commentedByThumbnail:
              this.userResponseMapper.mapProfilePicture(user),
            commentedAt: boardComment.createdAt,
          }),
        );
      }
    });

    if (board.ownerId != boardComment.userId) {
      await this.notifier.dispatch(
        new BoardCommentedNotification(board.ownerId, {
          id: board.id,
          thumbnail: this.boardResponseMapper.getThumbnailUrls(board),
          ownerId: board.ownerId,
          commentedBy: boardComment.userId,
          commentedByUsername: user.username,
          commentedByThumbnail: this.userResponseMapper.mapProfilePicture(user),
          commentedAt: boardComment.createdAt,
        }),
      );
    }
  }

  async delete(
    boardComment: BoardCommentEntity,
    board: BoardEntity,
  ): Promise<void> {
    await this.repository.softRemove(boardComment);

    board.comments--;
    await this.boardManager.save(board);
  }

  async save(entity: BoardCommentEntity) {
    await this.repository.save(entity);
  }
}
