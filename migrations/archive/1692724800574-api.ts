import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1692724800574 implements MigrationInterface {
    name = 'Api1692724800574'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "email_validation_code" character varying
        `);
        await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "email_validation_code_expires_at" TIMESTAMP
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "email_validation_code_expires_at"
        `);
        await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "email_validation_code"
        `);
    }

}
