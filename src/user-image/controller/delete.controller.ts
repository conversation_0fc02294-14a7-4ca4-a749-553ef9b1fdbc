import {
  Controller,
  Delete,
  HttpCode,
  Param,
  ParseUUIDPipe,
  Request,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiNoContentResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
  ApiNotFoundResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/service/jwt-auth.guard';
import { UserImageManager } from '../service/user-image.manager';
import { UserImageProvider } from '../service/user-image.provider';

@ApiTags('user-images')
@Controller('user-images')
@UseGuards(JwtAuthGuard)
export class DeleteController {
  constructor(
    private readonly userImageProvider: UserImageProvider,
    private readonly userImageManager: UserImageManager,
  ) {}

  @Delete(':id')
  @ApiOperation({
    operationId: 'user_image_delete',
    summary: 'Delete a user image',
    description:
      'Deletes a user image by its ID for the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the image\n',
  })
  @ApiParam({
    name: 'id',
    description: 'Image ID (UUID)',
    type: 'string',
    format: 'uuid',
  })
  @ApiNoContentResponse({ description: 'User image deleted successfully.' })
  @ApiBadRequestResponse({ description: 'Bad Request. Invalid image ID.' })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiNotFoundResponse({
    description: 'Not Found. The image could not be found.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @HttpCode(204)
  async deleteUserImage(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<void> {
    const image = await this.userImageProvider.getBy({
      id,
      userId: request.user.id,
    });

    await this.userImageManager.delete(image);
  }
}
