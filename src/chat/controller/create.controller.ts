import { Body, Controller, Post, Request } from '@nestjs/common';
import {
  ApiBody,
  ApiOperation,
  ApiTags,
  ApiOkResponse,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { ChatConversationDto } from '../dto/chat-conversation.dto';
import { ChatConversationRequest } from '../dto/chat-conversation.request';
import { ChatConversationRequestManager } from '../service/chat-conversation.request-manager';
import { ChatResponseMapper } from '../service/chat.response-mapper';

@ApiTags('chat')
@Controller('chat')
export class CreateController {
  constructor(
    private readonly requestManager: ChatConversationRequestManager,
    private readonly responseMapper: ChatResponseMapper,
  ) {}

  @Post()
  @ApiOperation({
    operationId: 'chat_create',
    summary: 'Create a new chat conversation',
    description:
      'Creates a new chat conversation history for the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- title: Title of the conversation\n',
  })
  @ApiBody({
    type: ChatConversationRequest,
    description:
      'Chat conversation request parameters including title, system prompt, model, and initial messages.',
  })
  @ApiOkResponse({
    type: ChatConversationDto,
    description: 'The chat conversation was successfully created.',
  })
  @ApiBadRequestResponse({
    description: `
      Bad Request. Possible reasons:\n
      - \`title\`: Must be a non-empty string.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description:
      'Forbidden. User does not have permission to create chat conversations.',
  })
  @ApiInternalServerErrorResponse({
    description:
      'Internal Server Error. An unexpected error occurred during chat creation.',
  })
  async create(
    @Body() requestBody: ChatConversationRequest,
    @Request() request,
  ): Promise<ChatConversationDto> {
    const user = request.user;
    const conversation = await this.requestManager.create(requestBody, user);

    return this.responseMapper.map(conversation);
  }
}
