import {
  <PERSON>umn,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { UserEntity } from '../../user/entity/user.entity';
import { OrganizationEntity } from '../../organization/entity/organization.entity';
import { ModelEntity } from './model.entity';

@Entity('model_organization')
export class ModelOrganizationEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  @Index('idx_model_organization_model_id')
  modelId: string;

  @Column({ type: 'uuid' })
  @Index('idx_model_organization_organization_id')
  organizationId: string;

  @Column({ type: 'uuid', nullable: true })
  @Index('idx_model_organization_shared_by_user_id')
  sharedByUserId: string;

  @ManyToOne(() => UserEntity, { nullable: true })
  @JoinColumn({ name: 'shared_by_user_id' })
  sharedByUser: UserEntity;

  @ManyToOne(() => ModelEntity, { nullable: false })
  @JoinColumn({ name: 'model_id' })
  model: ModelEntity;

  @ManyToOne(() => OrganizationEntity, { nullable: false })
  @JoinColumn({ name: 'organization_id' })
  organization: OrganizationEntity;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt?: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
