import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1691172375240 implements MigrationInterface {
    name = 'Api1691172375240'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "user_auth_token" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "type" character varying NOT NULL,
                "token" text,
                "expires_at" TIMESTAMP NOT NULL,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "user_id" uuid,
                CONSTRAINT "UQ_1c4e838c33c560c0e2f75bd65f6" UNIQUE ("token"),
                CONSTRAINT "PK_64d4188100cc6efae886afd8181" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            ALTER TABLE "user_auth_token"
            ADD CONSTRAINT "FK_e4dbc40e1c38f1497e0de9d1670" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_auth_token" DROP CONSTRAINT "FK_e4dbc40e1c38f1497e0de9d1670"
        `);
        await queryRunner.query(`
            DROP TABLE "user_auth_token"
        `);
    }

}
