import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1711091342432 implements MigrationInterface {
    name = 'Api1711091342432'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "model"
            ADD "published_at" TIMESTAMP
        `);

        await queryRunner.query(`
            UPDATE model SET published_at = NOW() WHERE published_at IS NULL AND privacy = 'public'
        `);

        await queryRunner.query(`
            UPDATE user_account 
            SET models_available = ( 
                SELECT COUNT(*) 
                FROM model m 
                WHERE m.user_id = user_account.id 
                AND m.privacy = 'public' 
                AND m.deleted_at IS NULL
            )
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "model" DROP COLUMN "published_at"
        `);
    }

}
