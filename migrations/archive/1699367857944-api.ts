import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1699367857944 implements MigrationInterface {
    name = 'Api1699367857944'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "transaction"
            ADD "description" text
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "transaction" DROP COLUMN "description"
        `);
    }

}
