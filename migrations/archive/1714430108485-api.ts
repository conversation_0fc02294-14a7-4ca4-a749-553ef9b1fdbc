import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1714430108485 implements MigrationInterface {
    name = 'Api1714430108485'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_follow"
            ADD CONSTRAINT "UQ_6ead635e1a8378c275c536bd0dc" UNIQUE ("follower_id", "following_id")
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_follow" DROP CONSTRAINT "UQ_6ead635e1a8378c275c536bd0dc"
        `);
    }

}
