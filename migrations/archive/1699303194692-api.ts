import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1699303194692 implements MigrationInterface {
    name = 'Api1699303194692'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "credit_package"
            ADD "stripe_product_id" character varying
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription"
            ADD "status" character varying NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription"
            ADD "external_reference" text
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription"
            ADD "created_at" TIMESTAMP NOT NULL DEFAULT now()
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription"
            ADD "updated_at" TIMESTAMP NOT NULL DEFAULT now()
        `);
        await queryRunner.query(`
            ALTER TABLE "user_credit_balance"
            ADD "created_at" TIMESTAMP NOT NULL DEFAULT now()
        `);
        await queryRunner.query(`
            ALTER TABLE "user_credit_balance"
            ADD "updated_at" TIMESTAMP NOT NULL DEFAULT now()
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_credit_balance" DROP COLUMN "updated_at"
        `);
        await queryRunner.query(`
            ALTER TABLE "user_credit_balance" DROP COLUMN "created_at"
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription" DROP COLUMN "updated_at"
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription" DROP COLUMN "created_at"
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription" DROP COLUMN "external_reference"
        `);
        await queryRunner.query(`
            ALTER TABLE "subscription" DROP COLUMN "status"
        `);
        await queryRunner.query(`
            ALTER TABLE "credit_package" DROP COLUMN "stripe_product_id"
        `);
    }

}
