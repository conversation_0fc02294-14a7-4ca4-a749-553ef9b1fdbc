import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { CreditTypeEnum } from '../entity/credit-type.enum';

export class UserCreditBalanceInternalRequest {
  @ApiProperty()
  @IsString()
  @IsOptional()
  username?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  organizationId?: string;

  @ApiProperty()
  @IsEnum(CreditTypeEnum, {
    message: 'Credit Type must be a valid value',
  })
  @ApiProperty({ enum: CreditTypeEnum })
  creditType: CreditTypeEnum = CreditTypeEnum.IMAGE;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  amount: number;

  @ApiProperty()
  @IsOptional()
  expiresAt?: Date;
}
