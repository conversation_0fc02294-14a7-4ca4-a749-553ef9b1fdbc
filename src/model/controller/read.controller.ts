import {
  <PERSON>,
  Get,
  Param,
  Parse<PERSON><PERSON><PERSON>ip<PERSON>,
  Query,
  Request,
  Res,
  UnauthorizedException,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiOkResponse,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiOperation,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiInternalServerErrorResponse,
  ApiForbiddenResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import { BaseFindResponseHeadersDto } from 'src/core/dto/base-find-response-headers.dto';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { UserEntity } from 'src/user/entity/user.entity';
import { ModelDto } from '../dto/model.dto';
import { ModelSearchRequest } from '../dto/model.search-request';
import { PrivacyEnum } from '../entity/model.entity';
import { StatusEnum } from '../enum/status.enum';
import { ModelProvider } from '../service/provider';
import { ModelResponseMapper } from '../service/response-mapper';
import { OrganizationUserProvider } from 'src/organization/service/organization-user.provider';

@ApiTags('model')
@Controller('models')
export class ReadController {
  constructor(
    private provider: ModelProvider,
    private responseMapper: ModelResponseMapper,
    private organizationUserProvider: OrganizationUserProvider,
  ) {}

  @Get()
  @ApiOperation({
    operationId: 'model_list',
    summary: 'List models',
    description:
      'Retrieves a paginated list of models for the authenticated user.\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of models per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- search: Search term\n' +
      '- name: Filter by model name\n' +
      '- userId: Filter by user ID\n' +
      '- username: Filter by username\n' +
      '- class: Filter by model class\n' +
      '- type: Filter by model type\n' +
      '- description: Filter by model description\n' +
      '- privacy: Filter by privacy level\n' +
      '- systemVersion: Filter by system version\n' +
      '- isActive: Filter by active status\n' +
      '- isFeatured: Filter by featured status\n' +
      '- isSponsored: Filter by sponsored status\n' +
      '- organizationId: Filter by organization ID\n' +
      '- organizationIds: Filter by organization IDs\n',
  })
  @ApiOkResponse({
    type: ModelDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated list of models.',
  })
  @ApiQuery({
    type: ModelSearchRequest,
    description: 'Query parameters for searching and paginating models.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - \`search\`: Must be a valid string.
      - \`name\`: Must be a valid string.
      - \`userId\`: Must be a valid UUID.
      - \`username\`: Must be a valid username.
      - \`class\`: Must be a valid string.
      - \`type\`: Must be a valid string.
      - \`description\`: Must be a valid string.
      - \`privacy\`: Must be a valid value from the \`PrivacyEnum\`.
      - \`systemVersion\`: Must be a valid number.
      - \`isActive\`: Must be a valid boolean value.
      - \`isFeatured\`: Must be a valid boolean value.
      - \`isSponsored\`: Must be a valid boolean value.
      - \`organizationId\`: Must be a valid UUID.
      - \`organizationIds\`: Must be a valid array of UUIDs.
      - Filter parameters must be valid values.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @UsePipes(new ValidationPipe())
  async find(
    @Request() request,
    @Query() query: ModelSearchRequest,
    @Res() res: Response,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;
    const filters = await this.validateSearchFilters(
      inputFilters,
      request.user,
    );

    const entities = await this.provider.findBy(
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    );
    const totalCount = await this.provider.countBy(filters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(
      await this.responseMapper.mapMultiple(
        entities,
        true,
        false,
        request.user?.id,
      ),
    );
  }

  @Get(':id')
  @ApiOperation({
    operationId: 'model_get',
    summary: 'Get model by ID',
    description:
      'Retrieves a specific model by its UUID for the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the model\n',
  })
  @ApiOkResponse({
    type: ModelDto,
    description: 'Returns the model with the specified ID.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the model to retrieve.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - Model does not exist.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User does not have access to this model.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async get(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<ModelDto> {
    const entity = await this.provider.get(id);

    // Check if user can access this model
    const canAccess = await this.provider.isModelAccessibleToUser(
      id,
      request.user,
    );
    if (!canAccess) {
      throw new UnauthorizedException(
        'You are not allowed to view this model.',
      );
    }

    return await this.responseMapper.map(entity);
  }

  async validateSearchFilters(
    filters: ModelSearchRequest,
    currentUser: UserEntity,
  ): Promise<any> {
    const hasUserFilter =
      (filters.hasOwnProperty('userId') && filters.userId !== currentUser.id) ||
      (filters.hasOwnProperty('username') &&
        filters.username !== currentUser.username);

    if (hasUserFilter) {
      // If the privacy filter is set to private and the user is not the owner, throw an exception
      if (
        filters.hasOwnProperty('privacy') &&
        filters.privacy !== PrivacyEnum.PUBLIC
      ) {
        throw new UnauthorizedException(
          "You are not allowed to view other users' private models.",
        );
      }

      if (filters.hasOwnProperty('username')) {
        delete filters.userId;
      }

      filters.privacy = PrivacyEnum.PUBLIC;
      filters.status = StatusEnum.AVAILABLE;
      return filters;
    }

    // Get user's organization IDs for organization-based access
    const userOrganizations =
      await this.organizationUserProvider.getUserOrganizations(currentUser.id);
    const userOrgIds = userOrganizations.map((org) => org.organizationId);

    // Show: user's own models + public available models + organization-shared models
    const baseFilters = [
      // User's own models (all statuses and privacy levels)
      { ...filters, userId: currentUser.id },
      // Public available models from other users
      {
        ...filters,
        privacy: PrivacyEnum.PUBLIC,
        status: StatusEnum.AVAILABLE,
      },
    ];

    // Add organization-shared private models if user belongs to any organizations
    if (userOrgIds.length > 0) {
      baseFilters.push({
        ...filters,
        privacy: PrivacyEnum.PRIVATE,
        status: StatusEnum.AVAILABLE,
        organizationIds: userOrgIds,
      });
    }

    return baseFilters;
  }
}
