import { Controller, Post, Request } from '@nestjs/common';
import { Body } from '@nestjs/common/decorators/http/route-params.decorator';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { UserProvider } from 'src/user/service/provider';
import { UpscaleDto } from '../dto/upscale.dto';
import { UpscaleRequest } from '../dto/upscale.request';
import { UpscaleRequestManager } from '../service/request-manager';
import { UpscaleResponseMapper } from '../service/response-mapper';

@ApiTags('upscale')
@Controller('upscales')
export class CreateController {
  constructor(
    private requestManager: UpscaleRequestManager,
    private responseMapper: UpscaleResponseMapper,
    private userProvider: UserProvider,
  ) {}

  @ApiOperation({
    operationId: 'upscale_create',
    summary: 'Create an upscale',
    description:
      'Creates a new upscale for the current user.\n\n' +
      'Requirements:\n' +
      '- User must be authenticated\n' +
      '- Request body must contain valid upscale parameters' +
      '\n\n' +
      'Optional Parameters:\n' +
      '- imageCompletionId: UUID of the source image completion\n' +
      '- imageUrl: URL of the source image\n' +
      '- prompt: Optional text prompt to guide the upscale\n' +
      '- strength: Upscale strength factor from 1 to 5 (default: 1)\n' +
      '- webhookUrl: Optional URL to receive a POST notification when upscale is complete\n' +
      '- organizationId: Optional organization ID to deduct credits from\n',
  })
  @ApiBody({
    type: UpscaleRequest,
    description: 'Upscale creation parameters.',
  })
  @ApiOkResponse({
    type: UpscaleDto,
    description: 'Upscale created successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`imageCompletionId\`: Must be a valid UUID.
      - \`imageUrl\`: Must be a valid URL.
      - \`prompt\`: Must be a valid string.
      - \`strength\`: Must be a valid number between 1 and 5.
      - \`webhookUrl\`: Must be a valid URL.
      - \`organizationId\`: Must be a valid UUID.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Post()
  async create(
    @Body() requestBody: UpscaleRequest,
    @Request() request,
  ): Promise<UpscaleDto> {
    const user = await this.userProvider.get(request.user.id);

    const upscale = await this.requestManager.create(requestBody, user);

    return this.responseMapper.map(upscale);
  }
}
