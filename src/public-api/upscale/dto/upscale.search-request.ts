import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsBoolean,
  IsEnum,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { BaseSearchRequest } from 'src/core/dto/base.search-request';
import { StatusEnum } from 'src/upscale/entity/upscale.entity';

export class UpscaleSearchRequest extends BaseSearchRequest {
  @IsOptional()
  @IsUUID()
  @ApiProperty({ required: false })
  userId: string;

  @IsOptional()
  @IsString()
  @ApiProperty({ required: false })
  username: string;

  @IsOptional()
  @IsUUID()
  @ApiProperty({ required: false })
  imageId: string;

  @IsOptional()
  @IsEnum(StatusEnum)
  @Transform(({ value }) => value.toLowerCase())
  @ApiProperty({ required: false, enum: StatusEnum })
  status?: StatusEnum;

  @IsOptional()
  @IsBoolean()
  @ApiProperty({ required: false })
  onlyFollowing: boolean;
}
