import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1691165851158 implements MigrationInterface {
    name = 'Api1691165851158'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD CONSTRAINT "UQ_56a0e4bcec2b5411beafa47ffa5" UNIQUE ("email")
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_account" DROP CONSTRAINT "UQ_56a0e4bcec2b5411beafa47ffa5"
        `);
    }
}
