import { Injectable } from '@nestjs/common';
import { DateTime } from 'luxon';
import { Logger } from 'nestjs-pino';
import { CreditTypeEnum } from '../entity/credit-type.enum';
import { SubscriptionStatusEnum } from '../entity/subscription-status.enum';
import { TransactionTypeEnum } from '../entity/transaction-type.enum';
import { SubscriptionProvider } from './provider';
import { TransactionManager } from './transaction.manager';
import { UserCreditBalanceManager } from './user-credit-balance.manager';

@Injectable()
export class CreditRenewalService {
  constructor(
    private readonly subscriptionProvider: SubscriptionProvider,
    private readonly userCreditBalanceManager: UserCreditBalanceManager,
    private readonly transactionManager: TransactionManager,
    private readonly logger: Logger,
  ) {}

  /**
   * Runs daily at 2 AM to process monthly credit renewals for yearly subscriptions
   * TODO: Add @Cron(CronExpression.EVERY_DAY_AT_2AM) when @nestjs/schedule is installed
   */
  async processMonthlyRenewals(): Promise<void> {
    this.logger.log('credit_renewal.process_monthly_renewals_started');

    try {
      const yearlySubscriptions =
        await this.findYearlySubscriptionsDueForRenewal();

      this.logger.log('credit_renewal.yearly_subscriptions_found', {
        count: yearlySubscriptions.length,
      });

      for (const subscription of yearlySubscriptions) {
        try {
          await this.processSubscriptionRenewal(subscription);
        } catch (error) {
          this.logger.error('credit_renewal.subscription_renewal_failed', {
            subscriptionId: subscription.id,
            error,
          });
        }
      }

      this.logger.log('credit_renewal.process_monthly_renewals_completed', {
        processedCount: yearlySubscriptions.length,
      });
    } catch (error) {
      this.logger.error('credit_renewal.process_monthly_renewals_error', {
        error,
      });
    }
  }

  /**
   * Find yearly subscriptions that are due for monthly credit renewal
   */
  private async findYearlySubscriptionsDueForRenewal() {
    const now = DateTime.now();
    const startOfDay = now.startOf('day').toJSDate();
    const endOfDay = now.endOf('day').toJSDate();

    // Find active yearly subscriptions where the next monthly renewal is due
    const subscriptions = await this.subscriptionProvider.findBy(
      {
        status: SubscriptionStatusEnum.ACTIVE,
        creditPackage: {
          expiresAfterMonths: 12,
          isRecurring: true,
        },
      },
      1,
      1000, // Process up to 1000 subscriptions per run
    );

    // Filter subscriptions that are due for monthly renewal
    return subscriptions.filter((subscription) => {
      const lastRenewal = subscription.renewedAt || subscription.paidAt;
      if (!lastRenewal) return false;

      const nextMonthlyRenewal = DateTime.fromJSDate(lastRenewal).plus({
        months: 1,
      });
      const renewalDate = nextMonthlyRenewal.startOf('day');

      return (
        renewalDate >= DateTime.fromJSDate(startOfDay) &&
        renewalDate <= DateTime.fromJSDate(endOfDay)
      );
    });
  }

  /**
   * Process monthly credit renewal for a yearly subscription
   */
  private async processSubscriptionRenewal(subscription: any): Promise<void> {
    this.logger.log('credit_renewal.processing_subscription', {
      subscriptionId: subscription.id,
      userId: subscription.userId,
      creditPackageId: subscription.creditPackageId,
    });

    const creditPackage = subscription.creditPackage;
    const now = new Date();

    // Create transaction reference for this monthly renewal
    const transactionReference = `${
      subscription.id
    }|monthly|${now.toISOString()}`;

    const transactionContext = {
      subscriptionId: subscription.id,
      userId: subscription.userId,
      creditPackageId: creditPackage.id,
      renewalType: 'monthly',
      renewalDate: now,
    };

    // Register the monthly renewal transaction
    await this.transactionManager.register(
      TransactionTypeEnum.SUBSCRIPTION,
      0, // No charge for monthly renewal of yearly subscription
      `${creditPackage.name} - Monthly Renewal`,
      transactionReference,
      false,
      subscription.userId,
      null,
      transactionContext,
    );

    // Allocate credits for each credit type
    for (const creditType in creditPackage.creditTypes) {
      const amount = creditPackage.creditTypes[creditType];

      await this.transactionManager.register(
        TransactionTypeEnum.TOP_UP,
        amount,
        creditType,
        transactionReference,
        false,
        subscription.userId,
        null,
        transactionContext,
      );

      // Calculate expiration date (1 month from now)
      const creditExpirationDate = DateTime.fromJSDate(now)
        .plus({ months: 1 })
        .toJSDate();

      await this.userCreditBalanceManager.increase(
        creditType.toLowerCase() as CreditTypeEnum,
        amount,
        subscription.userId,
        null,
        creditExpirationDate,
      );

      this.logger.log('credit_renewal.credits_allocated', {
        subscriptionId: subscription.id,
        userId: subscription.userId,
        creditType,
        amount,
        expiresAt: creditExpirationDate,
      });
    }

    this.logger.log('credit_renewal.subscription_processed', {
      subscriptionId: subscription.id,
      userId: subscription.userId,
    });
  }

  /**
   * Manual method to process renewals for a specific subscription
   * Useful for testing or manual intervention
   */
  async processSubscriptionRenewalManual(
    subscriptionId: string,
  ): Promise<void> {
    const subscription = await this.subscriptionProvider.get(subscriptionId);

    if (!subscription) {
      throw new Error(`Subscription not found: ${subscriptionId}`);
    }

    if (subscription.status !== SubscriptionStatusEnum.ACTIVE) {
      throw new Error(`Subscription is not active: ${subscriptionId}`);
    }

    if (subscription.creditPackage.expiresAfterMonths !== 12) {
      throw new Error(`Subscription is not yearly: ${subscriptionId}`);
    }

    await this.processSubscriptionRenewal(subscription);
  }

  /**
   * Get statistics about yearly subscriptions and their renewal status
   */
  async getRenewalStatistics(): Promise<{
    totalYearlySubscriptions: number;
    subscriptionsDueToday: number;
    subscriptionsOverdue: number;
  }> {
    const now = DateTime.now();
    const startOfDay = now.startOf('day').toJSDate();

    // Get all active yearly subscriptions
    const yearlySubscriptions = await this.subscriptionProvider.findBy(
      {
        status: SubscriptionStatusEnum.ACTIVE,
        creditPackage: {
          expiresAfterMonths: 12,
          isRecurring: true,
        },
      },
      1,
      10000,
    );

    const subscriptionsDueToday = yearlySubscriptions.filter((subscription) => {
      const lastRenewal = subscription.renewedAt || subscription.paidAt;
      if (!lastRenewal) return false;

      const nextMonthlyRenewal = DateTime.fromJSDate(lastRenewal).plus({
        months: 1,
      });
      const renewalDate = nextMonthlyRenewal.startOf('day');

      return (
        renewalDate.toJSDate().getTime() ===
        DateTime.fromJSDate(startOfDay).toJSDate().getTime()
      );
    });

    const subscriptionsOverdue = yearlySubscriptions.filter((subscription) => {
      const lastRenewal = subscription.renewedAt || subscription.paidAt;
      if (!lastRenewal) return false;

      const nextMonthlyRenewal = DateTime.fromJSDate(lastRenewal).plus({
        months: 1,
      });
      const renewalDate = nextMonthlyRenewal.startOf('day');

      return renewalDate.toJSDate() < startOfDay;
    });

    return {
      totalYearlySubscriptions: yearlySubscriptions.length,
      subscriptionsDueToday: subscriptionsDueToday.length,
      subscriptionsOverdue: subscriptionsOverdue.length,
    };
  }
}
