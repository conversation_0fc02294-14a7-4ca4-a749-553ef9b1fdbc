import { SQSClient, SQSClientConfig } from '@aws-sdk/client-sqs';
import { fromEnv } from '@aws-sdk/credential-provider-env';
import { defaultProvider } from '@aws-sdk/credential-provider-node';
import { Provider } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export const sqsFactory: Provider = {
  provide: 'SQS',
  useFactory: (configService: ConfigService) => {
    const awsAccessKeyId = configService.get<string>('AWS_ACCESS_KEY_ID');
    const awsSecretAccessKey = configService.get<string>(
      'AWS_SECRET_ACCESS_KEY',
    );
    const localstackUrl = configService.get<string>('LOCALSTACK_SQS_URL');

    let sqsConfig: SQSClientConfig = {
      region: configService.get<string>('AWS_REGION'),
      credentials: defaultProvider(),
    };

    if (awsAccessKeyId && awsSecretAccessKey) {
      sqsConfig.credentials = fromEnv();
    } else if (localstackUrl) {
      sqsConfig = {
        ...sqsConfig,
        endpoint: localstackUrl,
        tls: false,
        credentials: {
          accessKeyId: 'dummy',
          secretAccessKey: 'dummy',
        },
      };
    }

    return new SQSClient(sqsConfig);
  },
  inject: [ConfigService],
};
