import { BadRequestException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from 'nestjs-pino';
import { UserEntity } from 'src/user/entity/user.entity';
import { SubscriptionUpgradeRequest } from '../dto/subscription-upgrade.request';
import { CreditPackageEntity } from '../entity/credit-package.entity';
import { SubscriptionEntity } from '../entity/subscription.entity';
import { CreditPackageProvider } from './credit-package.provider';
import { SubscriptionManager } from './manager';
import { SubscriptionProvider } from './provider';
import { StripeService } from './stripe.service';
import { UpgradeManager } from './upgrade.manager';
import { VoucherService } from './voucher.service';

describe('UpgradeManager', () => {
  let manager: UpgradeManager;
  let subscriptionProvider: SubscriptionProvider;
  let creditPackageProvider: CreditPackageProvider;
  let subscriptionManager: SubscriptionManager;
  let stripeService: StripeService;
  let logger: Logger;

  const mockSubscriptionProvider = {
    findCurrentlyActiveSubscriptionForUser: jest.fn(),
  };

  const mockCreditPackageProvider = {
    get: jest.fn(),
    findBy: jest.fn(),
  };

  const mockSubscriptionManager = {
    create: jest.fn(),
  };

  const mockStripeService = {
    getStripeSubscription: jest.fn(),
    getOrCreateStripePriceId: jest.fn(),
    calculateProration: jest.fn(),
  };

  const mockVoucherService = {
    validateVoucher: jest.fn(),
    calculateDiscountAmount: jest.fn(),
    applyVoucherToSubscriptionUpdate: jest.fn(),
  };

  const mockLogger = {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UpgradeManager,
        {
          provide: SubscriptionProvider,
          useValue: mockSubscriptionProvider,
        },
        {
          provide: CreditPackageProvider,
          useValue: mockCreditPackageProvider,
        },
        {
          provide: SubscriptionManager,
          useValue: mockSubscriptionManager,
        },
        {
          provide: StripeService,
          useValue: mockStripeService,
        },
        {
          provide: VoucherService,
          useValue: mockVoucherService,
        },
        {
          provide: Logger,
          useValue: mockLogger,
        },
      ],
    }).compile();

    manager = module.get<UpgradeManager>(UpgradeManager);
    subscriptionProvider = module.get<SubscriptionProvider>(SubscriptionProvider);
    creditPackageProvider = module.get<CreditPackageProvider>(CreditPackageProvider);
    subscriptionManager = module.get<SubscriptionManager>(SubscriptionManager);
    stripeService = module.get<StripeService>(StripeService);
    logger = module.get<Logger>(Logger);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('previewUpgrade', () => {
    const mockUser = { id: 'user_123' } as UserEntity;
    const mockCurrentSubscription = {
      id: 'sub_123',
      creditPackageId: 'pkg_monthly',
      creditPackage: {
        id: 'pkg_monthly',
        name: 'Pro',
        price: 749,
        expiresAfterMonths: 1,
        targetEntity: 'user',
      },
      externalReference: 'stripe_sub_123',
    } as SubscriptionEntity;

    const mockYearlyPackage = {
      id: 'pkg_yearly',
      name: 'Pro - Yearly',
      price: 7490,
      expiresAfterMonths: 12,
      targetEntity: 'user',
      isRecurring: true,
    } as CreditPackageEntity;

    it('should preview yearly upgrade with proration', async () => {
      const request: SubscriptionUpgradeRequest = {
        targetCreditPackageId: 'pkg_yearly',
        confirmProration: false,
      };

      mockSubscriptionProvider.findCurrentlyActiveSubscriptionForUser.mockResolvedValue(
        mockCurrentSubscription,
      );
      mockCreditPackageProvider.get.mockResolvedValue(mockYearlyPackage);

      const mockStripeSubscription = {
        current_period_start: **********, // 2022-01-01
        current_period_end: **********, // 2022-02-01
      };
      const mockProratedInvoice = { amount_due: 6741 };

      mockStripeService.getStripeSubscription.mockResolvedValue(mockStripeSubscription);
      mockStripeService.getOrCreateStripePriceId.mockResolvedValue('price_yearly');
      mockStripeService.calculateProration.mockResolvedValue(mockProratedInvoice);

      const result = await manager.previewUpgrade(mockUser, request);

      expect(result.currentSubscriptionId).toBe('sub_123');
      expect(result.currentPackageName).toBe('Pro');
      expect(result.targetPackageName).toBe('Pro - Yearly');
      expect(result.isYearlyUpgrade).toBe(true);
      expect(result.upgradeAllowed).toBe(true);
      expect(result.proratedAmount).toBe(6741);
      expect(result.yearlySavings).toBe(1498); // 749 * 12 - 7490 = 8988 - 7490 = 1498
    });

    it('should reject upgrade if no active subscription', async () => {
      const request: SubscriptionUpgradeRequest = {
        targetCreditPackageId: 'pkg_yearly',
      };

      mockSubscriptionProvider.findCurrentlyActiveSubscriptionForUser.mockResolvedValue(null);

      await expect(manager.previewUpgrade(mockUser, request)).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should reject downgrade attempts', async () => {
      const request: SubscriptionUpgradeRequest = {
        targetCreditPackageId: 'pkg_downgrade',
      };

      const mockDowngradePackage = {
        id: 'pkg_downgrade',
        name: 'Beginner',
        price: 99, // Lower than current 749
        targetEntity: 'user',
        isRecurring: true,
      };

      mockSubscriptionProvider.findCurrentlyActiveSubscriptionForUser.mockResolvedValue(
        mockCurrentSubscription,
      );
      mockCreditPackageProvider.get.mockResolvedValue(mockDowngradePackage);

      const result = await manager.previewUpgrade(mockUser, request);

      expect(result.upgradeAllowed).toBe(false);
      expect(result.upgradeBlockedReason).toBe('upgrade.downgrade_not_allowed');
    });

    it('should reject upgrade to same package', async () => {
      const request: SubscriptionUpgradeRequest = {
        targetCreditPackageId: 'pkg_monthly',
      };

      const mockSamePackage = {
        id: 'pkg_monthly',
        name: 'Pro',
        price: 749,
        targetEntity: 'user',
        isRecurring: true,
      };

      mockSubscriptionProvider.findCurrentlyActiveSubscriptionForUser.mockResolvedValue(
        mockCurrentSubscription,
      );
      mockCreditPackageProvider.get.mockResolvedValue(mockSamePackage);

      const result = await manager.previewUpgrade(mockUser, request);

      expect(result.upgradeAllowed).toBe(false);
      expect(result.upgradeBlockedReason).toBe('upgrade.same_package');
    });
  });

  describe('executeUpgrade', () => {
    const mockUser = { id: 'user_123' } as UserEntity;
    const mockCurrentSubscription = {
      id: 'sub_123',
      creditPackageId: 'pkg_monthly',
      creditPackage: {
        id: 'pkg_monthly',
        name: 'Pro',
        price: 749,
        expiresAfterMonths: 1,
        targetEntity: 'user',
      },
      externalReference: 'stripe_sub_123',
    } as SubscriptionEntity;

    const mockYearlyPackage = {
      id: 'pkg_yearly',
      name: 'Pro - Yearly',
      price: 7490,
      expiresAfterMonths: 12,
      targetEntity: 'user',
      isRecurring: true,
    } as CreditPackageEntity;

    it('should execute upgrade successfully', async () => {
      const request: SubscriptionUpgradeRequest = {
        targetCreditPackageId: 'pkg_yearly',
        confirmProration: true,
      };

      const mockNewSubscription = {
        id: 'sub_new',
        user: mockUser,
        name: 'Pro - Yearly',
        price: 7490,
        creditPackage: mockYearlyPackage,
      } as SubscriptionEntity;

      mockSubscriptionProvider.findCurrentlyActiveSubscriptionForUser.mockResolvedValue(
        mockCurrentSubscription,
      );
      mockCreditPackageProvider.get.mockResolvedValue(mockYearlyPackage);
      mockSubscriptionManager.create.mockResolvedValue(mockNewSubscription);

      const result = await manager.executeUpgrade(mockUser, request);

      expect(mockSubscriptionManager.create).toHaveBeenCalledWith(
        expect.objectContaining({
          user: mockUser,
          name: 'Pro - Yearly',
          price: 7490,
          creditPackage: mockYearlyPackage,
        }),
      );
      expect(result).toEqual(mockNewSubscription);
    });

    it('should require proration confirmation for Stripe subscriptions', async () => {
      const request: SubscriptionUpgradeRequest = {
        targetCreditPackageId: 'pkg_yearly',
        confirmProration: false, // Not confirmed
      };

      mockSubscriptionProvider.findCurrentlyActiveSubscriptionForUser.mockResolvedValue(
        mockCurrentSubscription,
      );
      mockCreditPackageProvider.get.mockResolvedValue(mockYearlyPackage);

      await expect(manager.executeUpgrade(mockUser, request)).rejects.toThrow(
        'upgrade.proration_confirmation_required',
      );
    });

    it('should reject invalid upgrade attempts', async () => {
      const request: SubscriptionUpgradeRequest = {
        targetCreditPackageId: 'pkg_invalid',
        confirmProration: true,
      };

      const mockInvalidPackage = {
        id: 'pkg_invalid',
        name: 'Beginner',
        price: 99, // Lower price = downgrade
        targetEntity: 'user',
        isRecurring: true,
      };

      mockSubscriptionProvider.findCurrentlyActiveSubscriptionForUser.mockResolvedValue(
        mockCurrentSubscription,
      );
      mockCreditPackageProvider.get.mockResolvedValue(mockInvalidPackage);

      await expect(manager.executeUpgrade(mockUser, request)).rejects.toThrow(
        'upgrade.downgrade_not_allowed',
      );
    });
  });

  describe('getUpgradeOptions', () => {
    it('should return valid upgrade options', async () => {
      const mockUser = { id: 'user_123' } as UserEntity;
      const mockCurrentSubscription = {
        creditPackage: {
          targetEntity: 'user',
          price: 749,
          id: 'pkg_current',
        },
      } as SubscriptionEntity;

      const mockPackages = [
        {
          id: 'pkg_upgrade1',
          name: 'Pro - Yearly',
          price: 7490,
          targetEntity: 'user',
          isRecurring: true,
        },
        {
          id: 'pkg_upgrade2',
          name: 'Enterprise',
          price: 1499,
          targetEntity: 'user',
          isRecurring: true,
        },
        {
          id: 'pkg_downgrade',
          name: 'Beginner',
          price: 99, // This should be filtered out
          targetEntity: 'user',
          isRecurring: true,
        },
      ];

      mockSubscriptionProvider.findCurrentlyActiveSubscriptionForUser.mockResolvedValue(
        mockCurrentSubscription,
      );
      mockCreditPackageProvider.findBy.mockResolvedValue(mockPackages);

      const result = await manager.getUpgradeOptions(mockUser);

      expect(result).toHaveLength(2); // Only valid upgrades
      expect(result.map((pkg) => pkg.id)).toEqual(['pkg_upgrade1', 'pkg_upgrade2']);
    });

    it('should return empty array if no active subscription', async () => {
      const mockUser = { id: 'user_123' } as UserEntity;

      mockSubscriptionProvider.findCurrentlyActiveSubscriptionForUser.mockResolvedValue(null);

      const result = await manager.getUpgradeOptions(mockUser);

      expect(result).toEqual([]);
    });
  });
});
