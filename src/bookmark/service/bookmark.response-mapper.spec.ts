import { Test, TestingModule } from '@nestjs/testing';
import { BookmarkResponseMapper } from './bookmark.response-mapper';
import { BookmarkEntity } from '../entity/bookmark.entity';
import { ModelProvider } from 'src/model/service/provider';
import { ImageCompletionProvider } from 'src/image-completion/service/provider';
import { ModelResponseMapper } from 'src/model/service/response-mapper';
import { ImageCompletionResponseMapper } from 'src/image-completion/service/response-mapper';
import { ModelEntity } from 'src/model/entity/model.entity';
import { ImageCompletionEntity } from 'src/image-completion/entity/image-completion.entity';
import { ModelDto } from 'src/model/dto/model.dto';
import { ImageCompletionDto } from 'src/image-completion/dto/image-completion.dto';

describe('BookmarkResponseMapper', () => {
  let responseMapper: BookmarkResponseMapper;
  let modelProvider: ModelProvider;
  let imageCompletionProvider: ImageCompletionProvider;
  let modelResponseMapper: ModelResponseMapper;
  let imageCompletionResponseMapper: ImageCompletionResponseMapper;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BookmarkResponseMapper,
        {
          provide: ModelProvider,
          useValue: { get: jest.fn() },
        },
        {
          provide: ImageCompletionProvider,
          useValue: { get: jest.fn() },
        },
        {
          provide: ModelResponseMapper,
          useValue: { map: jest.fn() },
        },
        {
          provide: ImageCompletionResponseMapper,
          useValue: { map: jest.fn() },
        },
      ],
    }).compile();

    responseMapper = module.get<BookmarkResponseMapper>(BookmarkResponseMapper);
    modelProvider = module.get<ModelProvider>(ModelProvider);
    imageCompletionProvider = module.get<ImageCompletionProvider>(
      ImageCompletionProvider,
    );
    modelResponseMapper = module.get<ModelResponseMapper>(ModelResponseMapper);
    imageCompletionResponseMapper = module.get<ImageCompletionResponseMapper>(
      ImageCompletionResponseMapper,
    );
  });

  it('should be defined', () => {
    expect(responseMapper).toBeDefined();
  });

  it('should map entity to DTO with model details', async () => {
    const entity: BookmarkEntity = {
      id: '1',
      userId: 'user1',
      type: 'model',
      referenceId: 'ref1',
      createdAt: new Date(),
    };

    const model: Partial<ModelEntity> = { id: 'ref1', name: 'Test Model' };
    const modelDto: Partial<ModelDto> = {
      id: 'ref1',
      name: 'Test Model',
    };

    jest.spyOn(modelProvider, 'get').mockResolvedValue(model as ModelEntity);
    jest
      .spyOn(modelResponseMapper, 'map')
      .mockResolvedValue(modelDto as ModelDto);

    const dto = await responseMapper.map(entity);
    expect(dto.bookmarkDetails).toEqual({ id: 'ref1', name: 'Test Model' });
  });

  it('should map entity to DTO with image completion details', async () => {
    const entity: BookmarkEntity = {
      id: '2',
      userId: 'user2',
      type: 'image',
      referenceId: 'ref2',
      createdAt: new Date(),
    };

    const imageCompletion: Partial<ImageCompletionEntity> = {
      id: 'ref2',
      prompt: 'Test Image',
    };
    jest
      .spyOn(imageCompletionProvider, 'get')
      .mockResolvedValue(imageCompletion as ImageCompletionEntity);
    jest.spyOn(imageCompletionResponseMapper, 'map').mockResolvedValue({
      id: 'ref2',
      prompt: 'Test Image',
    } as ImageCompletionDto);

    const dto = await responseMapper.map(entity);
    expect(dto.bookmarkDetails).toEqual({ id: 'ref2', prompt: 'Test Image' });
  });
});
