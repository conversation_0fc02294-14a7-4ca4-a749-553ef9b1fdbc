import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { AbstractProvider } from 'src/core/service/abstract.provider';
import { FindManyOptions, FindOneOptions, Repository } from 'typeorm';
import { UpscaleEntity } from '../entity/upscale.entity';

@Injectable()
export class UpscaleProvider extends AbstractProvider<UpscaleEntity> {
  constructor(
    @InjectRepository(UpscaleEntity)
    repository: Repository<UpscaleEntity>,
    logger: Logger,
  ) {
    super(repository, logger);
  }

  prepareFindOneOptions(criteria: any): FindOneOptions {
    return {
      where: criteria,
      relations: {
        user: true,
      },
    };
  }

  prepareFindManyOptions(
    criteria: any,
    page: number,
    limit: number,
    sortBy?: string,
    sortOrder = 'ASC',
  ): FindManyOptions<UpscaleEntity> {
    const relations = {
      imageCompletion: true,
      user: null,
    };

    if (criteria.userId) {
      criteria.user = { id: criteria.userId };
      delete criteria.userId;
    }

    if (criteria.imageCompletionId) {
      criteria.imageCompletion = { id: criteria.imageCompletionId };
      delete criteria.imageCompletionId;
    }

    if (criteria.username) {
      criteria.user = { username: criteria.username };
      delete criteria.username;
    }

    if (criteria.onlyFollowing && criteria.followerId) {
      relations.user = {
        followers: true,
      };

      if (!criteria.user) {
        criteria.user = {};
      }

      criteria.user.followers = { followerId: criteria.followerId };
    } else {
      relations.user = true;
    }

    delete criteria.followerId;
    delete criteria.onlyFollowing;

    const parentOptions = super.prepareFindManyOptions(
      criteria,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    return {
      ...parentOptions,
      relations: relations,
    };
  }
}
