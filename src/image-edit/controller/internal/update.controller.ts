import {
  Body,
  Controller,
  HttpCode,
  Param,
  ParseUUI<PERSON><PERSON><PERSON>,
  Put,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiNoContentResponse,
  ApiParam,
  ApiTags,
  ApiOperation,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { ImageEditRequestManager } from '../../service/request-manager';
import { ImageEditInternalRequest } from 'src/image-edit/dto/image-edit.internal-request';
import { ImageEditResponseMapper } from 'src/image-edit/service/response-mapper';
import { ImageEditDto } from 'src/image-edit/dto/image-edit.dto';

@ApiTags('image-edit / internal')
@Controller('internal/image-edits')
export class UpdateController {
  constructor(
    private requestManager: ImageEditRequestManager,
    private responseMapper: ImageEditResponseMapper,
  ) {}

  @Put(':id/generation')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'internal_image_edit_start_generation',
    summary: 'Start generation for image edit',
    description:
      'Marks the specified image edit as started for generation (internal use).\n\n' +
      'Required Parameters:\n' +
      '- id: ID of the image edit to update\n',
  })
  @ApiParam({ name: 'id', description: 'ID of the image edit to update' })
  @ApiNoContentResponse({ description: 'Image edit marked as started.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - Image edit does not exist.
    `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async startGeneration(
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<void> {
    await this.requestManager.startGeneration(id);
  }

  @Put(':id')
  @ApiOperation({
    operationId: 'internal_image_edit_update',
    summary: 'Update internal image edit',
    description:
      'Updates the specified image edit for internal use.\n\n' +
      'Required Query Parameters:\n' +
      '- id: ID of the image edit to update\n' +
      'Required Body Parameters:\n' +
      '- status: New status of the image edit\n' +
      '- generationSeconds: New generation seconds of the image edit\n' +
      '- width: New width of the image edit\n' +
      '- height: New height of the image edit\n',
  })
  @ApiParam({ name: 'id', description: 'ID of the image edit to update' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - \`status\`: Must be a valid value from the \`StatusEnum\`.
      - \`generationSeconds\`: Must be a valid number.
      - \`width\`: Must be a valid number between 480 and 2160.
      - \`height\`: Must be a valid number between 480 and 2160.
      - Image edit does not exist.
      - Invalid or unavailable parameters.
      - Failed to analyze request body.
   `,
  })
  @ApiBody({
    type: ImageEditInternalRequest,
    description: 'Image edit update parameters.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async update(
    @Param('id', new ParseUUIDPipe()) id: string,
    @Body() requestBody: ImageEditInternalRequest,
  ): Promise<ImageEditDto> {
    const entity = await this.requestManager.updateInternal(id, requestBody);
    return await this.responseMapper.map(entity);
  }

  @Put(':id/conclusion')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'internal_image_edit_finish_generation',
    summary: 'Finish generation for image edit',
    description:
      'Marks the specified image edit as finished for generation (internal use).\n\n' +
      'Required Query Parameters:\n' +
      '- id: ID of the image edit to update\n' +
      'Required Body Parameters:\n' +
      '- status: New status of the image edit\n',
  })
  @ApiParam({ name: 'id', description: 'ID of the image edit to update' })
  @ApiNoContentResponse({ description: 'Image edit marked as finished.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - \`status\`: Must be a valid value from the \`StatusEnum\`.
      - Image edit does not exist.
      - Invalid or unavailable parameters.
      - Failed to analyze request body.
  `,
  })
  @ApiBody({
    type: ImageEditInternalRequest,
    description: 'Image edit update parameters.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async finishGeneration(
    @Param('id', new ParseUUIDPipe()) id: string,
    @Body() requestBody: ImageEditInternalRequest,
  ): Promise<void> {
    await this.requestManager.finishGeneration(id, requestBody);
  }

  @Put(':id/failure')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'internal_image_edit_fail_generation',
    summary: 'Fail generation for image edit',
    description:
      'Marks the specified image edit as failed for generation (internal use).\n\n' +
      'Required Parameters:\n' +
      '- id: ID of the image edit to update\n',
  })
  @ApiParam({ name: 'id', description: 'ID of the image edit to update' })
  @ApiNoContentResponse({ description: 'Image edit marked as failed.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - Image edit does not exist.
      - Invalid or unavailable parameters.
  `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async failGeneration(
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<void> {
    await this.requestManager.failGeneration(id);
  }
}
