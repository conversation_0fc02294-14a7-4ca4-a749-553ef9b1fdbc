import { ApiProperty } from '@nestjs/swagger';

export class BaseFindResponseHeadersDto {
  @ApiProperty({ description: 'Total count of items' })
  'X-Total-Count': number;

  @ApiProperty({ description: 'Current page of pagination' })
  'X-Current-Page': number;

  @ApiProperty({ description: 'Results per page' })
  'X-Per-Page': number;

  @ApiProperty({ description: 'Total count of pages' })
  'X-Total-Pages': number;

  static toSwaggerHeaders(): Record<string, any> {
    return {
      'X-Total-Count': { description: 'Total count of items', type: 'number' },
      'X-Current-Page': {
        description: 'Current page of pagination',
        type: 'number',
      },
      'X-Per-Page': { description: 'Results per page', type: 'number' },
      'X-Total-Pages': { description: 'Total count of pages', type: 'number' },
    };
  }
}
