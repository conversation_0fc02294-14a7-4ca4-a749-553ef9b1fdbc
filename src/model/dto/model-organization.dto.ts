import { ApiProperty } from '@nestjs/swagger';
import { PublicOrganizationDto } from 'src/organization/dto/public.organization.dto';
import { PublicUserDto } from 'src/user/dto/public.user.dto';

export class ModelOrganizationDto {
  @ApiProperty({
    description: 'Unique identifier of the model-organization relationship',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Organization that has access to the model',
    type: PublicOrganizationDto,
  })
  organization: PublicOrganizationDto;

  @ApiProperty({
    description: 'User who shared the model with the organization',
    type: PublicUserDto,
    required: false,
  })
  sharedByUser?: PublicUserDto;

  @ApiProperty({
    description: 'Date when the model was shared',
    example: '2024-01-15T10:30:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Date when the sharing was last updated',
    example: '2024-01-15T10:30:00Z',
  })
  updatedAt: Date;
}

export class ModelOrganizationListDto {
  @ApiProperty({
    description: 'Model ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  modelId: string;

  @ApiProperty({
    description: 'List of organizations that have access to the model',
    type: [ModelOrganizationDto],
  })
  organizations: ModelOrganizationDto[];
}
