import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1712049504746 implements MigrationInterface {
    name = 'Api1712049504746'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "model"
            ADD "version" integer NOT NULL DEFAULT '1'
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "model" DROP COLUMN "version"
        `);
    }

}
