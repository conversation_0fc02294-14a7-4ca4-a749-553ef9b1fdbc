import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1704033655530 implements MigrationInterface {
    name = 'Api1704033655530'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "image_completion"
            ADD "generation_settings" json DEFAULT '{}'
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "image_completion" DROP COLUMN "generation_settings"
        `);
    }

}
