import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ImageCompletionCommentEntity } from '../entity/image-completion-comment.entity';
import { ImageCompletionEntity } from '../entity/image-completion.entity';
import { ImageCommentedEvent } from '../event/image-commented.event';
import { ImageCompletionManager } from './manager';
import { Notifier } from 'src/notification/service/notifier';
import { ImageCommentedNotification } from '../notification/image-commented.notification';
import { UserResponseMapper } from 'src/user/service/response-mapper';
import { ImageCompletionResponseMapper } from './response-mapper';
import { UserProvider } from 'src/user/service/provider';
import { ImageCompletionCommentProvider } from './comment.provider';
import { ImageCommentNotification } from '../notification/image-comment.notification';

@Injectable()
export class ImageCompletionCommentManager {
  constructor(
    @InjectRepository(ImageCompletionCommentEntity)
    private repository: Repository<ImageCompletionCommentEntity>,
    private imageCompletionManager: ImageCompletionManager,
    private eventEmitter: EventEmitter2,
    private notifier: Notifier,
    private userResponseMapper: UserResponseMapper,
    private imageResponseMapper: ImageCompletionResponseMapper,
    private imageCompletionCommentProvider: ImageCompletionCommentProvider,
    private userProvider: UserProvider,
  ) {}

  async comment(
    imageCompletion: ImageCompletionEntity,
    userId: string,
    comment: string,
  ): Promise<void> {
    const imageCompletionComment = new ImageCompletionCommentEntity();
    imageCompletionComment.imageCompletion = imageCompletion;
    imageCompletionComment.userId = userId;
    imageCompletionComment.comment = comment;
    await this.repository.save(imageCompletionComment);

    imageCompletion.comments++;
    await this.imageCompletionManager.save(imageCompletion);

    const user = await this.userProvider.get(userId);

    const entities =
      await this.imageCompletionCommentProvider.findCommentsByImageId(
        imageCompletion.id,
      );

    entities.forEach(async (entity) => {
      if (
        entity.userId != imageCompletionComment.userId &&
        imageCompletion.userId != entity.userId
      ) {
        await this.notifier.dispatch(
          new ImageCommentNotification(entity.userId, {
            id: imageCompletion.id,
            thumbnail:
              this.imageResponseMapper.generateThumbnailUrl(imageCompletion),
            userId: imageCompletion.userId,
            commentedById: imageCompletionComment.userId,
            commentedByUsername: user.username,
            commentedByThumbnail:
              this.userResponseMapper.mapProfilePicture(user),
            commentedAt: imageCompletionComment.createdAt,
          }),
        );
      }
    });

    if (imageCompletion.userId != imageCompletionComment.userId) {
      await this.notifier.dispatch(
        new ImageCommentedNotification(imageCompletion.userId, {
          id: imageCompletion.id,
          thumbnail:
            this.imageResponseMapper.generateThumbnailUrl(imageCompletion),
          userId: imageCompletion.userId,
          commentedById: imageCompletionComment.userId,
          commentedByUsername: user.username,
          commentedByThumbnail: this.userResponseMapper.mapProfilePicture(user),
          commentedAt: imageCompletionComment.createdAt,
        }),
      );
    }

    await this.eventEmitter.emit(
      'image.commented',
      new ImageCommentedEvent({
        id: imageCompletionComment.id,
        imageCompletionId: imageCompletion.id,
        userId: userId,
        ownerId: imageCompletion.userId,
      }),
    );
  }

  async delete(
    imageCompletionComment: ImageCompletionCommentEntity,
  ): Promise<void> {
    const imageCompletion = imageCompletionComment.imageCompletion;

    await this.repository.softRemove(imageCompletionComment);

    imageCompletion.comments--;
    await this.imageCompletionManager.save(imageCompletion);
  }

  async save(entity: ImageCompletionCommentEntity) {
    await this.repository.save(entity);
  }
}
