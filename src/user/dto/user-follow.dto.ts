import { ApiProperty } from '@nestjs/swagger';
import { PublicUserDto } from './public.user.dto';

export class UserFollowDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  followerId: string;

  @ApiProperty()
  follower: PublicUserDto;

  @ApiProperty()
  followingId: string;

  @ApiProperty()
  following: PublicUserDto;

  @ApiProperty()
  isApproved?: boolean;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}
