import { Injectable } from '@nestjs/common';
import { UserProvider } from 'src/user/service/provider';
import { ModelCommentLikeEntity } from '../entity/model-comment-like.entity';
import { ModelCommentLikeDto } from '../dto/model-comment-like.dto';
import { ModelCommentResponseMapper } from './model-comment.response-mapper';
import { ModelCommentProvider } from './model-comment.provider';

@Injectable()
export class ModelCommentLikeResponseMapper {
  constructor(
    private userProvider: UserProvider,
    private modelCommentResponseMapper: ModelCommentResponseMapper,
    private modelCommentProvider: ModelCommentProvider,
  ) {}

  async mapMultiple(
    entities: ModelCommentLikeEntity[],
    mapImage: boolean = false,
  ): Promise<any> {
    const dtos = [];

    for (const entity of entities) {
      dtos.push(await this.map(entity, mapImage));
    }

    return dtos;
  }

  async map(entity: ModelCommentLikeEntity, mapImage: boolean = false) {
    const dto = new ModelCommentLikeDto();

    const user = await this.userProvider.get(entity.userId);

    dto.id = entity.id;
    dto.userId = entity.userId;
    dto.username = user.username;

    if (mapImage) {
      const modelEntity = await this.modelCommentProvider.get(
        entity.modelCommentId,
      );
      dto.modelComment = await this.modelCommentResponseMapper.map(modelEntity);
    }

    return dto;
  }
}
