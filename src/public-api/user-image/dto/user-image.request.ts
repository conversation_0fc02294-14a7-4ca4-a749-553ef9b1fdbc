import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsIn, IsInt, IsOptional, IsString, Min } from 'class-validator';

export class UserImageRequest {
  @IsString()
  @Transform((obj) => obj.value.toLowerCase())
  @IsIn(['jpg', 'jpeg', 'png', 'gif', 'webp'])
  @ApiProperty({
    description: 'File extension of the image. This determines the file format that will be uploaded.',
    required: true,
    enum: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
    example: 'jpg',
  })
  extension: string;

  @IsOptional()
  @IsInt()
  @Min(1)
  @Transform((obj) => Number(obj.value))
  @ApiProperty({
    description: 'Number of images to generate upload URLs for. If you need to upload multiple images at once, increase this value.',
    required: false,
    default: 1,
    example: 1,
  })
  numberOfImages: number = 1;

  @IsOptional()
  @IsString()
  @ApiProperty({
    description: 'Original filename of the image. This is stored for reference but does not affect the actual filename used in storage.',
    required: false,
    example: 'my_photo.jpg',
  })
  originalFilename?: string;

  @IsOptional()
  @ApiProperty({
    description: 'Additional metadata for the image. This can be any JSON object with custom properties you want to associate with the image.',
    required: false,
    type: 'object',
    example: { title: 'My vacation photo', tags: ['vacation', 'beach'] },
  })
  metadata?: Record<string, any>;
}
