import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1702587301339 implements MigrationInterface {
    name = 'Api1702587301339'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "user_signup_code_id" uuid
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "user_signup_code_id"
        `);
    }

}
