import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { ChatConversationEntity } from '../entity/chat-conversation.entity';
import { ChatMessageEntity } from '../entity/chat-message.entity';

@Injectable()
export class ChatProvider {
  constructor(
    @InjectRepository(ChatConversationEntity)
    private readonly conversationRepository: Repository<ChatConversationEntity>,
    @InjectRepository(ChatMessageEntity)
    private readonly messageRepository: Repository<ChatMessageEntity>,
  ) {}

  async getConversation(
    id: string,
    userId: string,
  ): Promise<ChatConversationEntity> {
    const conversation = await this.conversationRepository.findOne({
      where: { id, userId },
      relations: { messages: true },
    });

    if (!conversation) {
      throw new NotFoundException(`Conversation with ID ${id} not found`);
    }

    return conversation;
  }

  async findConversations(
    criteria: any,
    page: number,
    limit: number,
    sortBy = 'updatedAt',
    sortOrder: 'ASC' | 'DESC' = 'DESC',
  ): Promise<ChatConversationEntity[]> {
    const queryBuilder = this.prepareConversationQueryBuilder(criteria);

    queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .orderBy(`conversation.${sortBy}`, sortOrder);

    return await queryBuilder.getMany();
  }

  async countConversations(criteria: any): Promise<number> {
    const queryBuilder = this.prepareConversationQueryBuilder(criteria);
    return await queryBuilder.getCount();
  }

  prepareConversationQueryBuilder(
    criteria: any,
  ): SelectQueryBuilder<ChatConversationEntity> {
    const queryBuilder =
      this.conversationRepository.createQueryBuilder('conversation');

    if (criteria.userId) {
      queryBuilder.andWhere('conversation.userId = :userId', {
        userId: criteria.userId,
      });
    }

    if (criteria.id) {
      queryBuilder.andWhere('conversation.id = :id', {
        id: criteria.id,
      });
    }

    if (criteria.title) {
      queryBuilder.andWhere('conversation.title ILIKE :title', {
        title: `%${criteria.title}%`,
      });
    }

    return queryBuilder;
  }

  async findMessages(
    criteria: any,
    page: number,
    limit: number,
    sortBy = 'createdAt',
    sortOrder: 'ASC' | 'DESC' = 'ASC',
  ): Promise<ChatMessageEntity[]> {
    const queryBuilder = this.prepareMessageQueryBuilder(criteria);

    queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .orderBy(`message.${sortBy}`, sortOrder);

    return await queryBuilder.getMany();
  }

  async countMessages(criteria: any): Promise<number> {
    const queryBuilder = this.prepareMessageQueryBuilder(criteria);
    return await queryBuilder.getCount();
  }

  prepareMessageQueryBuilder(
    criteria: any,
  ): SelectQueryBuilder<ChatMessageEntity> {
    const queryBuilder = this.messageRepository
      .createQueryBuilder('message')
      .leftJoinAndSelect('message.conversation', 'conversation')
      .leftJoinAndSelect('message.attachedFiles', 'attachedFiles');

    if (criteria.conversationId) {
      queryBuilder.andWhere('message.conversationId = :conversationId', {
        conversationId: criteria.conversationId,
      });
    }

    if (criteria.id) {
      queryBuilder.andWhere('message.id = :id', {
        id: criteria.id,
      });
    }

    if (criteria.role) {
      queryBuilder.andWhere('message.role = :role', {
        role: criteria.role,
      });
    }

    if (criteria.userId) {
      queryBuilder.andWhere('conversation.userId = :userId', {
        userId: criteria.userId,
      });
    }

    return queryBuilder;
  }

  async getMessage(id: string): Promise<ChatMessageEntity> {
    const message = await this.messageRepository.findOne({
      where: { id },
      relations: { attachedFiles: true },
    });

    if (!message) {
      throw new NotFoundException(`Message with ID ${id} not found`);
    }

    return message;
  }
}
