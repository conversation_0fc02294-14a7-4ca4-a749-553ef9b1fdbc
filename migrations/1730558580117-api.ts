import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1730558580117 implements MigrationInterface {
    name = 'Api1730558580117'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "board"
            ADD "is_default" boolean NOT NULL DEFAULT false
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "board" DROP COLUMN "is_default"
        `);
    }

}
