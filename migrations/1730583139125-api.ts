import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1730583139125 implements MigrationInterface {
    name = 'Api1730583139125'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "idp_config"
            ADD "private_key" text
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "idp_config" DROP COLUMN "private_key"
        `);
    }

}
