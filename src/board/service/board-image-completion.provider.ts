import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { ImageCompletionProvider } from 'src/image-completion/service/provider';
import { Repository } from 'typeorm';
import { AbstractProvider } from '../../core/service/abstract.provider';
import { BoardImageCompletionEntity } from '../entity/board-image-completion.entity';
import { BoardEntity } from '../entity/board.entity';

@Injectable()
export class BoardImageCompletionProvider extends AbstractProvider<BoardImageCompletionEntity> {
  constructor(
    @InjectRepository(BoardImageCompletionEntity)
    protected readonly repository: Repository<BoardImageCompletionEntity>,
    protected readonly imageCompletionProvider: ImageCompletionProvider,
    protected readonly logger: Logger,
  ) {
    super(repository, logger);
  }

  async findBoardsByImage(imageCompletionId: string): Promise<BoardEntity[]> {
    const entities = await this.repository
      .createQueryBuilder('boardImageCompletion')
      .innerJoinAndSelect('boardImageCompletion.board', 'board')
      .innerJoinAndSelect('board.owner', 'owner')
      .where('boardImageCompletion.imageCompletionId = :imageCompletionId', {
        imageCompletionId,
      })
      .getMany();

    return entities.map((entity) => entity.board);
  }
}
