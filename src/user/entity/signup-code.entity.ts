import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { SignupCodeFeatureEnum } from './signup-code-feature.enum';
import { UserEntity } from './user.entity';

@Entity('user_signup_code')
export class SignupCodeEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', nullable: true })
  @Index('idx_user_signup_code_user_id')
  userId: string;

  @ManyToOne(() => UserEntity)
  user: UserEntity;

  @Column({ nullable: true, unique: true })
  code?: string;

  @Column({ type: 'text', nullable: true })
  notes?: string;

  @Column({ default: 0 })
  usages: number;

  @Column({ default: 10 })
  limit: number;

  @Column({ default: true })
  isActive: boolean;

  @Column({ type: 'json', nullable: true, default: {} })
  features?: { [key in SignupCodeFeatureEnum]: any };

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt?: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
