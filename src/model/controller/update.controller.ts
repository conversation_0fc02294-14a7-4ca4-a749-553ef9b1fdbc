import {
  <PERSON>,
  HttpCode,
  Param,
  ParseUUIDPipe,
  Patch,
  Put,
  Request,
} from '@nestjs/common';
import { Body } from '@nestjs/common/decorators/http/route-params.decorator';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { ModelActivationRequest } from '../dto/model-activation.dto';
import { ModelDto } from '../dto/model.dto';
import { ModelRequest } from '../dto/model.request';
import { ModelProvider } from '../service/provider';
import { ModelRequestManager } from '../service/request-manager';
import { ModelResponseMapper } from '../service/response-mapper';

@ApiTags('model')
@Controller('models')
export class UpdateController {
  constructor(
    private provider: ModelProvider,
    private requestManager: ModelRequestManager,
    private responseMapper: ModelResponseMapper,
  ) {}

  @Patch(':id')
  @ApiOperation({
    operationId: 'model_update',
    summary: 'Update model',
    description:
      'Updates the specified model for the authenticated user.\n\n' +
      'Required Query Parameters:\n' +
      '- id: UUID of the model to update\n\n' +
      'Optional Body Parameters:\n' +
      '- name: New name of the model\n' +
      '- privacy: New privacy of the model\n' +
      '- description: New description of the model\n' +
      '- type: New type of the model\n' +
      '- class: New class of the model\n' +
      '- webhookUrl: New webhook URL for the model\n' +
      '- website: New website of the model\n' +
      '- settings: New settings of the model\n' +
      '- isActive: New active status of the model\n',
  })
  @ApiBody({ type: ModelRequest, description: 'Model update parameters.' })
  @ApiOkResponse({ type: ModelDto, description: 'Model updated successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - \`name\`: Must be a valid string.
      - \`privacy\`: Must be a valid value from the \`PrivacyEnum\`.
      - \`description\`: Must be a valid string.
      - \`type\`: Must be a valid string.
      - \`class\`: Must be a valid string.
      - \`webhookUrl\`: Must be a valid URL.
      - \`website\`: Must be a valid URL.
      - \`settings\`: Must be a valid array of settings.
      - \`isActive\`: Must be a valid boolean.
      - Invalid or unavailable parameters.
      - Failed to analyze request body.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description:
      'Forbidden. User does not have permission to update this model.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async update(
    @Body() requestBody: ModelRequest,
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<ModelDto> {
    const entity = await this.provider.getBy({
      user: { id: request.user.id },
      id: id,
    });

    await this.requestManager.update(entity, requestBody);

    return await this.responseMapper.map(entity);
  }

  @Put(':id/training')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'model_train',
    summary: 'Start model training',
    description:
      'Starts training for the specified model for the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the model to train\n',
  })
  @ApiNoContentResponse({ description: 'Model training started successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - Model does not exist.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description:
      'Forbidden. User does not have permission to train this model.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async trainModel(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ) {
    const entity = await this.provider.getBy({
      user: { id: request.user.id },
      id: id,
    });

    await this.requestManager.trainModel(entity);
  }

  @Put(':id/activation')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'model_activate',
    summary: 'Activate model',
    description:
      'Activates the specified model for the authenticated user.\n\n' +
      'Required Query Parameters:\n' +
      '- id: UUID of the model to activate\n\n' +
      'Required Body Parameters:\n' +
      '- type: New type of the model\n' +
      '- description: New description of the model\n' +
      '- website: New website of the model\n',
  })
  @ApiNoContentResponse({ description: 'Model activated successfully.' })
  @ApiBody({
    type: ModelActivationRequest,
    description: 'Model activation parameters.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - \`type\`: Must be a valid string.
      - \`description\`: Must be a valid string.
      - \`website\`: Must be a valid URL.
      - Model does not exist.
      - Invalid or unavailable parameters.
      - Failed to analyze request body.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description:
      'Forbidden. User does not have permission to activate this model.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async modelActivation(
    @Body() requestBody: ModelActivationRequest,
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ) {
    const entity = await this.provider.getBy({
      user: { id: request.user.id },
      id: id,
    });

    await this.requestManager.activate(entity, requestBody);
  }

  @Patch(':id/version/:version')
  @ApiOperation({
    operationId: 'model_set_version',
    summary: 'Restore model version',
    description:
      'Restores the specified version of the model for the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the model\n' +
      '- version: Version number to restore\n',
  })
  @ApiOkResponse({
    type: ModelDto,
    description: 'Model version restored successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - \`version\`: Must be a valid number.
      - Model does not exist.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description:
      'Forbidden. User does not have permission to restore this version.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async setVersion(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
    @Param('version') version: number,
  ): Promise<ModelDto> {
    return this.provider
      .getBy({
        user: { id: request.user.id },
        id: id,
      })
      .then((entity) => this.requestManager.restoreVersion(entity, version))
      .then((entity) => this.responseMapper.map(entity));
  }
}
