import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1744825902187 implements MigrationInterface {
  name = 'Api1744825902187';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      INSERT INTO global_notification (id, title, message, text_color, background_color, is_active)
      VALUES (
        'global',
        'Scheduled Maintenance',
        'We''ll be undergoing maintenance at 2 AM UTC.',
        '#FFFFFF',
        '#FF5733',
        false
      );
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DELETE FROM global_notification WHERE id = 'global';
    `);
  }
}
