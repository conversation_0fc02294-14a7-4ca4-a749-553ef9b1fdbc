import {
  <PERSON>,
  Get,
  Param,
  ParseUUIDPipe,
  Request,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiOkResponse,
  ApiParam,
  ApiTags,
  ApiOperation,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { IntegrationTokenDto } from '../dto/integration-token.dto';
import { UserAuthTokenResponseMapper } from '../service/response-mapper';
import { UserAuthTokenProvider } from '../service/user-auth-token.provider';

@ApiTags('user')
@Controller('auth/integration-tokens')
export class ReadController {
  constructor(
    private provider: UserAuthTokenProvider,
    private responseMapper: UserAuthTokenResponseMapper,
  ) {}

  @Get()
  @ApiOperation({
    operationId: 'integration_token_list',
    summary: 'List integration tokens',
    description: 'Retrieves all integration tokens for the authenticated user.',
  })
  @ApiOkResponse({
    type: IntegrationTokenDto,
    isArray: true,
    description: 'Array of integration tokens for the user.',
  })
  @ApiBadRequestResponse({
    description:
      'Bad Request. Possible Reasons: \n\n' +
      '- Invalid request parameters\n' +
      '- Missing required parameters\n',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @UsePipes(new ValidationPipe())
  async findAuth(@Request() request): Promise<IntegrationTokenDto[]> {
    const filters = {
      user: { id: request.user.id },
      type: 'integration',
    };

    const entities = await this.provider.findBy(filters, 1, 999999);

    return this.responseMapper.mapMultiple(entities);
  }

  @Get(':id')
  @ApiOperation({
    operationId: 'integration_token_get',
    summary: 'Get integration token by ID',
    description:
      'Retrieves a specific integration token by its UUID for the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the integration token',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the integration token to retrieve.',
    type: String,
  })
  @ApiOkResponse({
    type: IntegrationTokenDto,
    description: 'Returns the integration token with the specified ID.',
  })
  @ApiBadRequestResponse({
    description:
      'Bad Request. Possible reasons:\n\n' + '- Invalid integration token ID.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. User does not have access to this token.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async getAuth(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<IntegrationTokenDto> {
    return this.provider
      .getBy({ id: id, userId: request.user.id })
      .then((entity) => this.responseMapper.map(entity));
  }
}
