import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1706671810175 implements MigrationInterface {
    name = 'Api1706671810175'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "system_version" integer NOT NULL DEFAULT '1'
        `);
        await queryRunner.query(`
            ALTER TABLE "image_completion"
            ADD "system_version" integer NOT NULL DEFAULT '1'
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "image_completion" DROP COLUMN "system_version"
        `);
        await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "system_version"
        `);
    }

}
