import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { addYears, formatISO } from 'date-fns';
import * as fs from 'fs';
import * as Handlebars from 'handlebars';
import * as path from 'path';
import { AppConfigurationService } from 'src/core/service/app-configuration.service';
import { Repository } from 'typeorm';
import { IdpConfigEntity } from '../entity/idp-config.entity';

@Injectable()
export class SamlConfigService {
  constructor(
    @InjectRepository(IdpConfigEntity)
    private readonly idpConfigRepository: Repository<IdpConfigEntity>,
    private readonly appConfig: AppConfigurationService,
  ) {}

  async getIdPConfig(name: string): Promise<IdpConfigEntity> {
    return this.idpConfigRepository.findOne({ where: { name } });
  }

  async getIdPConfigByIssuer(issuer: string): Promise<IdpConfigEntity> {
    return this.idpConfigRepository.findOne({ where: { issuer } });
  }

  generateMetadataXML(idpConfig: IdpConfigEntity): string {
    const templatePath = path.join(
      this.appConfig.rootDir,
      '/resources/saml/metadata-template.xml',
    );
    const templateContent = fs.readFileSync(templatePath, 'utf8');
    const template = Handlebars.compile(templateContent);

    // Calculate validUntil as one year from now
    const validUntil = formatISO(addYears(new Date(), 1));

    // Set cacheDuration to one day
    const cacheDuration = 'PT86400S';

    const metadata = template({
      entityID: idpConfig.entityID,
      acsUrl: idpConfig.acsUrl,
      slsUrl: idpConfig.slsUrl,
      certificate: idpConfig.certificate,
      organizationName: idpConfig.organizationName,
      organizationDisplayName: idpConfig.organizationDisplayName,
      organizationURL: idpConfig.organizationURL,
      contactGivenName: idpConfig.contactGivenName,
      contactEmail: idpConfig.contactEmail,
      validUntil,
      cacheDuration,
    });

    return metadata;
  }
}
