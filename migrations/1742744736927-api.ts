import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1742744736927 implements MigrationInterface {
  name = 'Api1742744736927';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DO $$ 
      BEGIN 
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name='organization_user' AND column_name='deleted_at') THEN
          ALTER TABLE "organization_user" ADD "deleted_at" TIMESTAMP;
        END IF;

        IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name='organization_user' AND column_name='created_at') THEN
          ALTER TABLE "organization_user" ADD "created_at" TIMESTAMP NOT NULL DEFAULT now();
        END IF;

        IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name='organization_user' AND column_name='updated_at') THEN
          ALTER TABLE "organization_user" ADD "updated_at" TIMESTAMP NOT NULL DEFAULT now();
        END IF;
      END $$;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DO $$ 
      BEGIN 
        IF EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name='organization_user' AND column_name='updated_at') THEN
          ALTER TABLE "organization_user" DROP COLUMN "updated_at";
        END IF;

        IF EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name='organization_user' AND column_name='created_at') THEN
          ALTER TABLE "organization_user" DROP COLUMN "created_at";
        END IF;

        IF EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name='organization_user' AND column_name='deleted_at') THEN
          ALTER TABLE "organization_user" DROP COLUMN "deleted_at";
        END IF;
      END $$;
    `);
  }
}
