import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1733674850048 implements MigrationInterface {
  name = 'Api1733674850048';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE INDEX "idx_image_completion_privacy" ON "image_completion" ("privacy")
        `);
    await queryRunner.query(`
            CREATE INDEX "idx_image_completion_is_nsfw" ON "image_completion" ("is_nsfw")
        `);
    await queryRunner.query(`
            CREATE INDEX "idx_image_completion_deleted_at" ON "image_completion" ("deleted_at")
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            DROP INDEX "public"."idx_image_completion_deleted_at"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."idx_image_completion_is_nsfw"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."idx_image_completion_privacy"
        `);
  }
}
