import {
  <PERSON>,
  Get,
  Param,
  ParseUUIDPipe,
  Query,
  Request,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiOkResponse,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiOperation,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import { BaseFindResponseHeadersDto } from 'src/core/dto/base-find-response-headers.dto';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { UserSignupCodeSearchRequest } from 'src/user/dto/user-signup-code.search-request';
import { UserDto } from 'src/user/dto/user.dto';
import { UserProvider } from 'src/user/service/provider';
import { UserResponseMapper } from 'src/user/service/response-mapper';
import { SignupCodeDto } from '../../dto/signup-code.dto';
import { SignupCodeSearchRequest } from '../../dto/signup-code.search-request';
import { SignupCodeProvider } from '../../service/signup-code.provider';
import { SignupCodeResponseMapper } from '../../service/signup-code.response-mapper';

@ApiTags('signup-code')
@Controller('signup_codes')
export class ReadController {
  constructor(
    private provider: SignupCodeProvider,
    private responseMapper: SignupCodeResponseMapper,
    private userProvider: UserProvider,
    private userResponseMapper: UserResponseMapper,
  ) {}

  @ApiOperation({
    operationId: 'signup_code_list',
    summary: 'List signup codes',
    description:
      'Retrieves a paginated list of signup codes for the current user.' +
      'Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of signup codes per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- userId: The unique identifier of the user to list signup codes for\n' +
      '- code: Filter by signup code\n\n' +
      'Returns the list of signup codes.',
  })
  @ApiOkResponse({
    type: SignupCodeDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated list of signup codes.',
  })
  @ApiQuery({
    type: SignupCodeSearchRequest,
    description: 'Query parameters for searching and paginating signup codes.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - \`userId\`: Must be a valid UUID.
      - \`code\`: Must be a valid string.
      - Filter parameters must be valid values.
      - Invalid or unavailable parameters.
  `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Get()
  @UsePipes(new ValidationPipe())
  async find(
    @Request() request,
    @Query() query: SignupCodeSearchRequest,
    @Res() res: Response,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;

    inputFilters.userId = request.user.id;

    const entities = await this.provider.findBy(
      inputFilters,
      page,
      limit,
      sortBy,
      sortOrder,
    );
    const totalCount = await this.provider.countBy(inputFilters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultiple(entities));
  }

  @ApiOperation({
    operationId: 'signup_code_usages',
    summary: 'List users who used signup codes',
    description:
      'Retrieves a paginated list of users who have used signup codes created by the current user.\n\n' +
      'Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of users per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- userSignupCodeId[]: The unique identifier of the user signup code to list users for\n\n' +
      'Returns the list of users who used signup codes.',
  })
  @ApiOkResponse({
    type: UserDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated list of users who used signup codes.',
  })
  @ApiQuery({
    type: UserSignupCodeSearchRequest,
    description: 'Query parameters for searching and paginating users.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - \`userSignupCodeId\`: Must be a valid UUID array.
      - Filter parameters must be valid values.
      - Invalid or unavailable parameters.
  `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Get('usages')
  async usages(
    @Request() request,
    @Query() query: UserSignupCodeSearchRequest,
    @Res() res: Response,
  ): Promise<void> {
    const signupCodes = await this.provider.findBy(
      { userId: request.user.id },
      1,
      9999,
    );
    query.userSignupCodeIds = signupCodes.map((signupCode) => signupCode.id);

    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;

    const entities = await this.userProvider.findBy(
      inputFilters,
      page,
      limit,
      sortBy,
      sortOrder,
    );
    const totalCount = await this.userProvider.countBy(inputFilters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.userResponseMapper.mapMultipleUserSignup(entities));
  }

  @ApiOperation({
    operationId: 'signup_code_get',
    summary: 'Get signup code by ID',
    description:
      'Retrieves the details of a specific signup code by its unique identifier.' +
      'Requirements:\n' +
      '- User must be authenticated\n' +
      '- Signup code must exist and belong to the user \n\n' +
      'Required Parameters:\n' +
      '- id: The unique identifier of the signup code\n\n' +
      'Returns the signup code details.',
  })
  @ApiOkResponse({ type: SignupCodeDto, description: 'Signup code details.' })
  @ApiParam({
    name: 'id',
    description: 'The unique identifier of the signup code',
    type: 'string',
    format: 'uuid',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - Signup code does not exist.
  `,
  })
  @ApiNotFoundResponse({
    description: 'Not Found. The signup code could not be found.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Get(':id')
  async get(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<SignupCodeDto> {
    return this.provider
      .get(id)
      .then((entity) => this.responseMapper.map(entity));
  }
}
