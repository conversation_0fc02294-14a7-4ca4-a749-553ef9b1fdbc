import {
  Controller,
  Get,
  Param,
  Query,
  Request,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiOkResponse,
  ApiParam,
  ApiTags,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiInternalServerErrorResponse,
  ApiQuery,
} from '@nestjs/swagger';
import { Response } from 'express';
import { BaseFindResponseHeadersDto } from 'src/core/dto/base-find-response-headers.dto';
import { AuthOptional, Public } from 'src/core/security/public-routes';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { PublicUserDto } from '../dto/public.user.dto';
import { UserOAuthDto } from '../dto/user-oauth.dto';
import { UserDto } from '../dto/user.dto';
import { UserSearchRequest } from '../dto/user.search-request';
import { UserProvider } from '../service/provider';
import { UserResponseMapper } from '../service/response-mapper';
import { UserLeaderboardSearchRequest } from '../dto/user-leaderboard.search.request';
import { UserLeaderboardDTO } from '../dto/user-leaderboard.dto';

@ApiTags('user')
@Controller('users')
export class ReadController {
  constructor(
    private userProvider: UserProvider,
    private userMapper: UserResponseMapper,
  ) {}

  @ApiOperation({
    operationId: 'user_get_current',
    summary: 'Get current user',
    description:
      'Retrieves the profile information of the current authenticated user.',
  })
  @ApiOkResponse({ type: UserDto, description: 'Current user profile.' })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - Invalid or unavailable parameters.
  `,
  })
  @Get('current')
  async getCurrent(@Request() req): Promise<UserDto> {
    return this.userProvider
      .get(req.user.id)
      .then((user) => this.userMapper.map(user));
  }

  @ApiOperation({
    operationId: 'user_leaderboard',
    summary: 'Get user leaderboard',
    description:
      'Retrieves a paginated leaderboard of users based on activity.\n\n' +
      'Optional Query Parameters:\n' +
      '- day, week, month, year: Filter by time period\n' +
      '- username: Filter by username',
  })
  @ApiOkResponse({
    type: UserLeaderboardDTO,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated leaderboard of users.',
  })
  @ApiQuery({ type: UserLeaderboardSearchRequest })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - \`username\`: Must be a valid username.
      - \`day\`, \`week\`, \`month\`, \`year\`: Must be a valid boolean value.
      - Filter parameters must be valid values.
      - Invalid or unavailable parameters.
  `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @AuthOptional()
  @Public()
  @Get('rank')
  async findLeaderboard(
    @Res() res: Response,
    @Query() query: UserLeaderboardSearchRequest,
  ) {
    let start;

    const { page, limit, ...filters } = query;
    if (filters.day) {
      start = new Date(new Date().setDate(-1)).toISOString();
    } else if (filters.week) {
      start = new Date(new Date().setDate(-7)).toISOString();
    } else if (filters.month) {
      start = new Date(new Date().setMonth(-1)).toISOString();
    } else {
      const d = new Date();
      d.setFullYear(d.getFullYear() - 1);
      start = d.toISOString();
    }

    if (filters.username) {
      const entitiy = await this.userProvider.findLeaderboard(
        start,
        new Date().toISOString(),
        page,
        limit,
        filters.username ?? null,
      );
      res.send(await this.userMapper.mapLeaderboard(entitiy[0]));
    } else {
      const offset = (page - 1) * limit;
      const entities = await this.userProvider.findLeaderboard(
        start,
        new Date().toISOString(),
        page,
        limit,
      );
      res.send(await this.userMapper.mapLeaderboardMultiple(entities, offset));
    }
  }

  @ApiOperation({
    operationId: 'user_get_by_username',
    summary: 'Get user by username',
    description:
      'Retrieves the public profile of a user by their username.' +
      '\n\n' +
      'Required Parameters:\n' +
      '- username: Username of the user\n',
  })
  @ApiOkResponse({ type: PublicUserDto, description: 'Public user profile.' })
  @ApiParam({ name: 'username', description: 'Username of the user.' })
  @ApiNotFoundResponse({
    description: 'Not Found. The user could not be found.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Get(':username')
  async getByUsername(
    @Request() req,
    @Param('username') username: string,
  ): Promise<PublicUserDto> {
    return this.userProvider
      .getBy({ username: username })
      .then((user) => this.userMapper.mapPublic(user));
  }

  @ApiOperation({
    operationId: 'user_get_by_email',
    summary: 'Get user by email',
    description:
      'Retrieves the public profile of a user by their email address.' +
      '\n\n' +
      'Required Parameters:\n' +
      '- email: Email address of the user\n',
  })
  @ApiOkResponse({ type: PublicUserDto, description: 'Public user profile.' })
  @ApiParam({ name: 'email', description: 'Email address of the user.' })
  @ApiNotFoundResponse({
    description: 'Not Found. The user could not be found.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Get('/email/:email')
  async getByEmail(
    @Request() req,
    @Param('email') email: string,
  ): Promise<PublicUserDto> {
    return this.userProvider
      .getBy({ email: email })
      .then((user) => this.userMapper.mapPublic(user));
  }

  @ApiOperation({
    operationId: 'user_get_oauth_info',
    summary: 'Get current user OAuth info',
    description:
      'Retrieves OAuth information for the current authenticated user.',
  })
  @ApiOkResponse({ type: UserOAuthDto, description: 'OAuth information.' })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @Get('current/oauth')
  async getOAuthInfo(@Request() req): Promise<UserOAuthDto> {
    return this.userProvider
      .get(req.user.id)
      .then((user) => this.userMapper.mapOAuthInfo(user));
  }

  @ApiOperation({
    operationId: 'user_list',
    summary: 'List users',
    description:
      'Retrieves a paginated list of public user profiles.\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of users per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- username: Filter by username\n' +
      '- isVerified: Filter by verification status\n' +
      '- followersCountMin: Filter by minimum number of followers\n' +
      '- followersCountMax: Filter by maximum number of followers\n' +
      '- followingCountMin: Filter by minimum number of following\n' +
      '- followingCountMax: Filter by maximum number of following\n' +
      '- imagesGeneratedMin: Filter by minimum number of images generated\n' +
      '- imagesGeneratedMax: Filter by maximum number of images generated\n' +
      '- imagesAvailableMin: Filter by minimum number of images available\n' +
      '- imagesAvailableMax: Filter by maximum number of images available\n' +
      '- modelsAvailableMin: Filter by minimum number of models available\n' +
      '- modelsAvailableMax: Filter by maximum number of models available\n',
  })
  @ApiOkResponse({
    type: PublicUserDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated list of public users.',
  })
  @ApiQuery({ type: UserSearchRequest })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - \`username\`: Must be a valid username.
      - \`isVerified\`: Must be a valid boolean value.
      - \`followersCountMin\`: Must be a valid number.
      - \`followersCountMax\`: Must be a valid number.
      - \`followingCountMin\`: Must be a valid number.
      - \`followingCountMax\`: Must be a valid number.
      - \`imagesGeneratedMin\`: Must be a valid number.
      - \`imagesGeneratedMax\`: Must be a valid number.
      - \`imagesAvailableMin\`: Must be a valid number.
      - \`imagesAvailableMax\`: Must be a valid number.
      - \`modelsAvailableMin\`: Must be a valid number.
      - \`modelsAvailableMax\`: Must be a valid number.
      - Filter parameters must be valid values.
      - Invalid or unavailable parameters.
  `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Get()
  @UsePipes(new ValidationPipe())
  async find(
    @Request() request,
    @Query() query: UserSearchRequest,
    @Res() res: Response,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;

    const filters = this.prepareSearchFilters(inputFilters);

    const entities = await this.userProvider.findBy(
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    );
    const totalCount = await this.userProvider.countBy(filters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.userMapper.mapMultiplePublic(entities));
  }

  prepareSearchFilters(filters: UserSearchRequest): any {
    const criteria = {
      isBot: false,
    };

    return {
      ...filters,
      ...criteria,
    };
  }
}
