import { Injectable } from '@nestjs/common';
import { ModelCommentManager } from './model-comment.manager';
import { ModelEntity } from '../entity/model.entity';
import { ModelCommentRequest } from '../dto/model-comment.request';
import { ModelCommentEntity } from '../entity/model-comment.entity';

@Injectable()
export class ModelCommentRequestManager {
  constructor(private manager: ModelCommentManager) {}

  async comment(
    model: ModelEntity,
    userId: string,
    request: ModelCommentRequest,
  ): Promise<void> {
    await this.manager.comment(model, userId, request.comment);
  }

  async delete(
    modelComment: ModelCommentEntity,
    model: ModelEntity,
  ): Promise<void> {
    await this.manager.delete(modelComment, model);
  }
}
