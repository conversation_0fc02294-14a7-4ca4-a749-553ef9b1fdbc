import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { Repository } from 'typeorm';
import { UserEntity } from '../../user/entity/user.entity';
import { CreditTypeEnum } from '../entity/credit-type.enum';
import { TransactionTypeEnum } from '../entity/transaction-type.enum';
import { TransactionEntity } from '../entity/transaction.entity';
import { TransactionManager } from './transaction.manager';
import { UserCreditBalanceManager } from './user-credit-balance.manager';

describe('TransactionManager', () => {
  let service: TransactionManager;
  let repository: Repository<TransactionEntity>;
  let userCreditBalanceManager: UserCreditBalanceManager;
  let logger: Logger;

  const mockRepository = {
    findOne: jest.fn(),
    save: jest.fn(),
  };

  const mockUserCreditBalanceManager = {
    deduct: jest.fn(),
    increase: jest.fn(),
  };

  const mockLogger = {
    log: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TransactionManager,
        {
          provide: getRepositoryToken(TransactionEntity),
          useValue: mockRepository,
        },
        {
          provide: UserCreditBalanceManager,
          useValue: mockUserCreditBalanceManager,
        },
        { provide: Logger, useValue: mockLogger },
      ],
    }).compile();

    service = module.get<TransactionManager>(TransactionManager);
    repository = module.get<Repository<TransactionEntity>>(
      getRepositoryToken(TransactionEntity),
    );
    userCreditBalanceManager = module.get<UserCreditBalanceManager>(
      UserCreditBalanceManager,
    );
    logger = module.get<Logger>(Logger);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('register', () => {
    it('should create a new transaction and increase balance for a top-up', async () => {
      const user = { id: 'user1' } as UserEntity;
      const transactionType = TransactionTypeEnum.TOP_UP;
      const price = 100;
      const description = 'model';
      const reference = 'ref123';
      const transaction = {
        amount: 0,
        type: transactionType,
        user,
      } as TransactionEntity;

      jest.spyOn(service, 'getOrCreate').mockResolvedValue(transaction);
      mockRepository.save.mockResolvedValue(transaction);

      const result = await service.register(
        transactionType,
        price,
        description,
        reference,
        true,
        user.id,
      );

      expect(service.getOrCreate).toHaveBeenCalledWith(
        transactionType,
        description,
        reference,
        user.id,
        undefined,
        undefined,
      );
      expect(mockLogger.log).toHaveBeenCalledWith('transaction.register', {
        type: transactionType,
        price,
        description,
        reference,
        updateBalance: true,
        userId: user.id,
        organizationId: undefined,
      });
      expect(mockUserCreditBalanceManager.increase).toHaveBeenCalledWith(
        CreditTypeEnum.MODEL,
        price,
        user.id,
        undefined,
      );
      expect(mockRepository.save).toHaveBeenCalledWith(transaction);
      expect(result).toBe(transaction);
    });

    it('should create a new transaction and deduct balance for a spending', async () => {
      const user = { id: 'user1' } as UserEntity;
      const transactionType = TransactionTypeEnum.SPENDING;
      const price = 50;
      const description = 'image';
      const reference = 'ref123';
      const transaction = {
        amount: 0,
        type: transactionType,
        user,
      } as TransactionEntity;

      jest.spyOn(service, 'getOrCreate').mockResolvedValue(transaction);
      mockRepository.save.mockResolvedValue(transaction);

      const result = await service.register(
        transactionType,
        price,
        description,
        reference,
        true,
        user.id,
      );

      expect(service.getOrCreate).toHaveBeenCalledWith(
        transactionType,
        description,
        reference,
        user.id,
        undefined,
        undefined,
      );
      expect(mockLogger.log).toHaveBeenCalledWith('transaction.register', {
        type: transactionType,
        price,
        description,
        reference,
        updateBalance: true,
        userId: user.id,
        organizationId: undefined,
      });
      expect(mockUserCreditBalanceManager.deduct).toHaveBeenCalledWith(
        CreditTypeEnum.IMAGE,
        price,
        user.id,
        undefined,
      );
      expect(mockRepository.save).toHaveBeenCalledWith(transaction);
      expect(result).toBe(transaction);
    });

    it('should not update balance if updateBalance is false', async () => {
      const user = { id: 'user1' } as UserEntity;
      const transactionType = TransactionTypeEnum.SUBSCRIPTION;
      const price = 200;
      const description = 'subscription';
      const reference = 'ref456';
      const transaction = {
        amount: 0,
        type: transactionType,
        user,
      } as TransactionEntity;

      jest.spyOn(service, 'getOrCreate').mockResolvedValue(transaction);
      mockRepository.save.mockResolvedValue(transaction);

      const result = await service.register(
        transactionType,
        price,
        description,
        reference,
        false,
        user.id,
      );

      expect(service.getOrCreate).toHaveBeenCalledWith(
        transactionType,
        description,
        reference,
        user.id,
        undefined,
        undefined,
      );
      expect(mockLogger.log).toHaveBeenCalledWith('transaction.register', {
        type: transactionType,
        price,
        description,
        reference,
        updateBalance: false,
        userId: user.id,
        organizationId: undefined,
      });
      expect(mockUserCreditBalanceManager.increase).not.toHaveBeenCalled();
      expect(mockUserCreditBalanceManager.deduct).not.toHaveBeenCalled();
      expect(mockRepository.save).toHaveBeenCalledWith(transaction);
      expect(result).toBe(transaction);
    });
  });

  describe('getOrCreate', () => {
    it('should create a new transaction if not found', async () => {
      const user = { id: 'user1' } as UserEntity;
      const transactionType = TransactionTypeEnum.TOP_UP;
      const description = 'model';
      const reference = 'ref123';
      const newTransaction = {
        type: transactionType,
        userId: user.id,
        organizationId: undefined,
        description,
        reference,
        context: undefined,
      } as TransactionEntity;

      mockRepository.findOne.mockResolvedValue(null);
      mockRepository.save.mockResolvedValue(newTransaction);

      const result = await service.getOrCreate(
        transactionType,
        description,
        reference,
        user.id,
      );

      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: {
          type: transactionType,
          description,
          reference,
          userId: user.id,
          organizationId: undefined,
        },
      });
      expect(result).toEqual(newTransaction);
    });

    it('should return an existing transaction if found', async () => {
      const user = { id: 'user1' } as UserEntity;
      const transactionType = TransactionTypeEnum.TOP_UP;
      const description = 'model';
      const reference = 'ref123';
      const existingTransaction = {
        type: transactionType,
        user,
        description,
        reference,
      } as TransactionEntity;

      mockRepository.findOne.mockResolvedValue(existingTransaction);

      const result = await service.getOrCreate(
        transactionType,
        description,
        reference,
        user.id,
      );

      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: {
          type: transactionType,
          description,
          reference,
          userId: user.id,
          organizationId: undefined,
        },
      });
      expect(result).toEqual(existingTransaction);
    });
  });
});
