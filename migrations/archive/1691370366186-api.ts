import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1691370366186 implements MigrationInterface {
  name = 'Api1691370366186';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE "image_completion" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "prompt" text,
                "prompt_system" text,
                "status" character varying NOT NULL DEFAULT 'new',
                "privacy" character varying NOT NULL,
                "likes" integer NOT NULL DEFAULT '0',
                "reports" integer NOT NULL DEFAULT '0',
                "image_paths" json,
                "is_active" boolean NOT NULL DEFAULT false,
                "blocked_at" TIMESTAMP,
                "deleted_at" TIMESTAMP,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "user_id" uuid,
                CONSTRAINT "PK_2465137ca78fc29e301e6320276" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            CREATE TABLE "image_completion_model" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "image_completion_id" uuid,
                "model_id" uuid,
                CONSTRAINT "PK_526032da0c56cf50a4d04e2a865" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion"
            ADD CONSTRAINT "FK_cb24a4b667e103bc038f6c72927" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion_model"
            ADD CONSTRAINT "FK_8c85fbe5ccf3f8c84e514682c9d" FOREIGN KEY ("image_completion_id") REFERENCES "image_completion"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion_model"
            ADD CONSTRAINT "FK_b647c482570bb6aaf5d5a39f84e" FOREIGN KEY ("model_id") REFERENCES "model"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "image_completion_model" DROP CONSTRAINT "FK_b647c482570bb6aaf5d5a39f84e"
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion_model" DROP CONSTRAINT "FK_8c85fbe5ccf3f8c84e514682c9d"
        `);
    await queryRunner.query(`
            ALTER TABLE "image_completion" DROP CONSTRAINT "FK_cb24a4b667e103bc038f6c72927"
        `);
    await queryRunner.query(`
            DROP TABLE "image_completion_model"
        `);
    await queryRunner.query(`
            DROP TABLE "image_completion"
        `);
  }
}
