import { DynamicModule, Module, forwardRef } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { AuthModule } from 'src/auth/auth.module';
import { jwtConfig } from 'src/auth/config/jwt.config';
import { ImageCompletionModule } from 'src/image-completion/module';
import { ImageEditModule } from 'src/image-edit/image-edit.module';
import { ModelModule } from 'src/model/module';
import { UpscaleModule } from 'src/upscale/module';
import { UserModule } from 'src/user/user.module';
import { CreateController } from './controller/create.controller';
import { ReadController } from './controller/read.controller';
import { UpdateController } from './controller/update.controller';
import { ImageRequestManager } from './service/request-manager';
import { ImageResponseMapper } from './service/response-mapper';
import { OrganizationModule } from 'src/organization/organization.module';

@Module({})
export class PublicImageModule {
  static register(enableControllers: boolean): DynamicModule {
    return {
      module: PublicImageModule,
      providers: [ImageRequestManager, ImageResponseMapper],
      imports: [
        PassportModule,
        JwtModule.register(jwtConfig),
        forwardRef(() => AuthModule),
        forwardRef(() => UserModule),
        forwardRef(() => ModelModule),
        forwardRef(() => ImageCompletionModule),
        forwardRef(() => UpscaleModule),
        forwardRef(() => ImageEditModule),
        forwardRef(() => OrganizationModule),
      ],
      controllers: enableControllers
        ? [CreateController, ReadController, UpdateController]
        : [],
    };
  }
}
