import { ApiProperty } from '@nestjs/swagger';
import { OrganizationDto } from './organization.dto';
import { StatusEnum } from '../enum/organization-user.enum';
import { PublicUserDto } from 'src/user/dto/public.user.dto';

export class OrganizationInviteDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  organization: OrganizationDto;

  @ApiProperty()
  isAdmin: boolean;

  @ApiProperty()
  status: StatusEnum;

  @ApiProperty()
  invitedBy?: PublicUserDto;

  @ApiProperty()
  invitedAt?: Date;

  @ApiProperty()
  acceptedAt?: Date;

  @ApiProperty()
  user: PublicUserDto;

  @ApiProperty()
  createdAt?: Date;
}
