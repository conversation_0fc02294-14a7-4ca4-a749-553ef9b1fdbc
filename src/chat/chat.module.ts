import { DynamicModule, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CreateController } from './controller/create.controller';
import { ReadController } from './controller/read.controller';
import { DeleteController } from './controller/delete.controller';
import { MessageController } from './controller/message.controller';
import { UpdateController } from './controller/update.controller';
import { ChatConversationEntity } from './entity/chat-conversation.entity';
import { ChatMessageEntity } from './entity/chat-message.entity';
import { ChatProvider } from './service/chat.provider';
import { ChatConversationManager } from './service/chat-conversation.manager';
import { ChatConversationRequestManager } from './service/chat-conversation.request-manager';
import { ChatMessageManager } from './service/chat-message.manager';
import { ChatMessageRequestManager } from './service/chat-message.request-manager';
import { ChatResponseMapper } from './service/chat.response-mapper';
import { ChatMessageRelationshipEntity } from './entity/chat-message-relationship.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ChatConversationEntity,
      ChatMessageEntity,
      ChatMessageRelationshipEntity,
    ]),
  ],
  providers: [
    ChatProvider,
    ChatConversationManager,
    ChatConversationRequestManager,
    ChatMessageManager,
    ChatMessageRequestManager,
    ChatResponseMapper,
  ],
  exports: [
    ChatProvider,
    ChatConversationManager,
    ChatConversationRequestManager,
    ChatMessageManager,
    ChatMessageRequestManager,
    ChatResponseMapper,
  ],
})
export class ChatModule {
  static register(enableControllers: boolean): DynamicModule {
    return {
      module: ChatModule,
      controllers: enableControllers
        ? [
            CreateController,
            ReadController,
            DeleteController,
            MessageController,
            UpdateController,
          ]
        : [],
    };
  }
}
