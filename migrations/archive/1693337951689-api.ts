import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1693337951689 implements MigrationInterface {
    name = 'Api1693337951689'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "image_completion_model"
            ADD "marked_as_used" boolean NOT NULL DEFAULT false
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "image_completion_model" DROP COLUMN "marked_as_used"
        `);
    }

}
