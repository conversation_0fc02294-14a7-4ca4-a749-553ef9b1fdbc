import {
  Controller,
  Delete,
  HttpC<PERSON>,
  Param,
  ParseU<PERSON><PERSON>ip<PERSON>,
  Request,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiNoContentResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiBearerAuth,
  ApiResponse,
} from '@nestjs/swagger';
import { JwtIntegrationAuthGuard } from '../../../auth/service/jwt-integration-auth.guard';
import { UserImageManager } from '../../../user-image/service/user-image.manager';
import { UserImageProvider } from '../../../user-image/service/user-image.provider';

@ApiTags('user-images')
@Controller('user-images')
@ApiBearerAuth()
export class DeleteController {
  constructor(
    private readonly userImageProvider: UserImageProvider,
    private readonly userImageManager: UserImageManager,
  ) {}

  @ApiOperation({
    operationId: 'user_image_delete',
    summary: 'Delete a user image',
    description:
      'Deletes a user image from your library.\n\n' +
      'Path Parameters:\n' +
      '- id: UUID of the image to delete',
  })
  @ApiParam({
    name: 'id',
    description: 'Image ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiNoContentResponse({
    description: 'The image was successfully deleted',
  })
  @ApiBadRequestResponse({
    description: 'Invalid image ID format',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. The user is not authenticated',
  })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden. User does not have permission to delete this image',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found. The image to delete could not be found',
  })
  @ApiResponse({
    status: 422,
    description: 'Unprocessable Entity. Invalid request parameters',
  })
  @ApiResponse({
    status: 500,
    description:
      'Internal Server Error. An unexpected error occurred while deleting the image',
  })
  @Delete(':id')
  @HttpCode(204)
  @UseGuards(JwtIntegrationAuthGuard)
  async delete(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<void> {
    const image = await this.userImageProvider.getBy({
      id,
      userId: request.user.id,
    });

    await this.userImageManager.delete(image);
  }
}
