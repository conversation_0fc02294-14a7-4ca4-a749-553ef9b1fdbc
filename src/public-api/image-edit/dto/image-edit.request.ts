import { ApiProperty } from '@nestjs/swagger';
import {
    IsBoolean,
    IsEnum,
    IsInt,
    IsOptional,
    IsString,
    IsUrl,
    IsU<PERSON><PERSON>,
    <PERSON>,
    <PERSON>,
} from 'class-validator';
import { EditMode } from '../../../image-edit/entity/image-edit.entity';

export class ImageEditRequest {
  @ApiProperty({ description: 'Original image completion ID', required: false })
  @IsOptional()
  @IsUUID()
  originalImageCompletionId: string;

  @ApiProperty({ description: 'Edit mode, either "in", "out", "skin", or "context"' })
  @IsEnum(EditMode)
  mode: EditMode;

  @ApiProperty({ description: 'Width of the edited image', required: false })
  @IsInt()
  @IsOptional()
  width?: number;

  @ApiProperty({ description: 'Height of the edited image', required: false })
  @IsInt()
  @IsOptional()
  height?: number;

  @ApiProperty({
    type: 'number',
    description: 'Number of image completions to generate',
    required: false,
  })
  @IsInt()
  @IsOptional()
  @Min(1)
  @Max(5)
  imageCompletionsCount? = 3;

  @ApiProperty({ description: 'Prompt', required: false })
  @IsString()
  @IsOptional()
  prompt?: string;

  @ApiProperty({ description: 'Inpainting mask', required: false })
  @IsString()
  @IsOptional()
  mask?: string;

  @ApiProperty({ description: 'Input image URL', required: true })
  @IsOptional()
  @IsString()
  imageUrl?: string;

  @ApiProperty({ type: 'object', required: false })
  @IsOptional()
  settings?: any;

  @ApiProperty()
  @IsOptional()
  @IsUrl()
  webhookUrl?: string;

  @ApiProperty({ default: false })
  @IsBoolean()
  hidePrompt = false;

  @ApiProperty({
    description: 'Organization ID to deduct credits from. If provided, credits will be deducted from the organization account instead of the user account. User must be a member of the organization.',
    required: false,
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID(4, { message: 'organizationId must be a valid UUID' })
  organizationId?: string;
}
