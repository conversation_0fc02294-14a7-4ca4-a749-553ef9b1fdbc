import {
  <PERSON>,
  Get,
  Param,
  Query,
  Res,
  Request,
  ParseUUIDPipe,
  UsePipes,
  ValidationPipe,
  UnauthorizedException,
  NotFoundException,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Response } from 'express';
import { ImageEditDto } from '../dto/image-edit.dto';
import { ImageEditSearchRequest } from '../dto/image-edit.search-request';
import { ImageEditProvider } from '../../../image-edit/service/provider';
import { ImageEditResponseMapper } from '../service/response-mapper';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { BaseFindResponseHeadersDto } from 'src/core/dto/base-find-response-headers.dto';
import { ImageEditMaskDto } from 'src/public-api/upscale/dto/image-edit-mask.dto';
import { ImageEditImageCompletionProvider } from 'src/image-edit/service/image-edit-image-completion.provider';

@ApiTags('image-edit')
@Controller('image-edits')
@ApiBearerAuth()
export class ReadController {
  constructor(
    private readonly provider: ImageEditProvider,
    private readonly responseMapper: ImageEditResponseMapper,
    private imageEditImageCompletionProvider: ImageEditImageCompletionProvider,
  ) {}

  @Get()
  @UsePipes(new ValidationPipe())
  @ApiOperation({
    operationId: 'image_edit_find',
    summary: 'Find image edits',
    description:
      'Retrieves a paginated list of image edits.\n\n' +
      'Query Parameters:\n' +
      '- page: Page number (default: 1)\n' +
      '- limit: Items per page (default: 20)\n' +
      '- sortBy: Field to sort by (createdAt, status)\n' +
      '- sortOrder: "ASC" or "DESC"\n' +
      '- status: Filter by status (see Status Values)\n\n' +
      'Status Values:\n' +
      '- new: Edit task created but not started\n' +
      '- generating: Currently processing the edit\n' +
      '- ready: Edit completed successfully\n' +
      '- saved: Edit has been saved by user\n' +
      '- failed: Edit failed to process\n' +
      '- interrupted: Edit was stopped by user\n\n' +
      'Response Headers:\n' +
      '- X-Total-Count: Total count of items\n' +
      '- X-Current-Page: Current page of pagination\n' +
      '- X-Per-Page: Results per page\n' +
      '- X-Total-Pages: Total count of pages',
  })
  @ApiQuery({
    type: ImageEditSearchRequest,
    description: 'Query parameters for filtering and pagination',
  })
  @ApiOkResponse({
    type: ImageEditDto,
    isArray: true,
    description: 'List of image edits successfully retrieved.',
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
  })
  @ApiResponse({
    status: 400,
    description: `
      Bad Request. Possible reasons:
      - Invalid pagination parameters
      - Invalid sort parameters
      - Invalid filter values
    `,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiResponse({
    status: 500,
    description:
      'Internal Server Error. An unexpected error occurred while retrieving the image edits.',
  })
  async find(
    @Query() query: ImageEditSearchRequest,
    @Res() res: Response,
    @Request() request,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;

    const filters = {
      ...inputFilters,
      userId: request.user.id,
    };

    const entities = await this.provider.findBy(
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    );
    const totalCount = await this.provider.countBy(filters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultiple(entities));
  }

  @Get(':id')
  @ApiOperation({
    operationId: 'image_edit_get',
    summary: 'Get an image edit',
    description:
      'Retrieves a specific image edit by ID.\n\n' +
      'Path Parameters:\n' +
      '- id: UUID of the image edit\n\n',
  })
  @ApiParam({
    name: 'id',
    description: 'The unique identifier of the image edit to retrieve',
  })
  @ApiOkResponse({
    type: ImageEditDto,
    description: 'The image edit was successfully retrieved.',
  })
  @ApiResponse({
    status: 400,
    description: `
      Bad Request. Possible reasons:
      - Invalid ID format
      - Image edit does not exist
    `,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden. User does not have permission to access this image edit.',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found. The requested image edit could not be found.',
  })
  @ApiResponse({
    status: 500,
    description:
      'Internal Server Error. An unexpected error occurred while retrieving the image edit.',
  })
  async get(
    @Param('id', new ParseUUIDPipe()) id: string,
    @Request() request,
  ): Promise<ImageEditDto> {
    const entity = await this.provider.get(id);

    if (entity.userId != request.user?.id) {
      throw new UnauthorizedException(
        "You are not allowed to view other users' images.",
      );
    }

    return await this.responseMapper.map(entity);
  }

  @Get(':imageCompletionId/mask')
  @ApiOperation({
    operationId: 'image_edit_get_mask',
    summary: 'Get mask for image completion',
    description:
      'Retrieves the mask associated with an image completion.\n\n' +
      'Path Parameters:\n' +
      '- imageCompletionId: UUID of the image completion\n\n' +
      'Returns the mask data that was used for the image edit. ' +
      'This endpoint is useful for retrieving the original mask used in inpainting operations.',
  })
  @ApiOkResponse({ type: ImageEditMaskDto })
  @ApiBadRequestResponse()
  @ApiParam({
    name: 'imageCompletionId',
    description: 'ID of the image completion to retrieve the mask for',
  })
  async getMaskByImageCompletion(
    @Param('imageCompletionId', new ParseUUIDPipe()) imageCompletionId: string,
    @Request() request,
  ): Promise<ImageEditMaskDto> {
    const imageEditImageCompletion =
      await this.imageEditImageCompletionProvider.getBy({
        imageCompletionId: imageCompletionId,
      });

    const imageEdit = imageEditImageCompletion.imageEdit;

    if (!imageEdit) {
      throw new NotFoundException(
        'No image edit found for this image completion',
      );
    }

    if (!imageEdit.mask) {
      throw new NotFoundException('No mask found for this image edit');
    }

    if (imageEdit.userId != request.user?.id) {
      throw new UnauthorizedException(
        "You are not allowed to view other users' image masks.",
      );
    }

    return { mask: imageEdit.mask } as ImageEditMaskDto;
  }
}
