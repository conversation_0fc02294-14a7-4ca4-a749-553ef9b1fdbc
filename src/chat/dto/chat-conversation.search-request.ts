import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsUUID } from 'class-validator';
import { BaseSearchRequest } from 'src/core/dto/base.search-request';

export class ChatConversationSearchRequest extends BaseSearchRequest {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsUUID()
  id?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  title?: string;
}
