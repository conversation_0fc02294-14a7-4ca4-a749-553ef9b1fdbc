import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { AbstractProvider } from 'src/core/service/abstract.provider';
import { Repository } from 'typeorm';
import { ModelOrganizationEntity } from '../entity/model-organization.entity';

@Injectable()
export class ModelOrganizationProvider extends AbstractProvider<ModelOrganizationEntity> {
  constructor(
    @InjectRepository(ModelOrganizationEntity)
    repository: Repository<ModelOrganizationEntity>,
    logger: Logger,
  ) {
    super(repository, logger);
  }

  /**
   * Find all organization shares for a specific model
   */
  async findByModelId(modelId: string): Promise<ModelOrganizationEntity[]> {
    return await this.repository.find({
      where: { modelId },
      relations: {
        organization: true,
        sharedByUser: true,
      },
    });
  }

  /**
   * Find all model shares for a specific organization
   */
  async findByOrganizationId(organizationId: string): Promise<ModelOrganizationEntity[]> {
    return await this.repository.find({
      where: { organizationId },
      relations: {
        model: {
          user: true,
        },
        sharedByUser: true,
      },
    });
  }

  /**
   * Find a specific model-organization relationship
   */
  async findByModelAndOrganization(
    modelId: string,
    organizationId: string,
  ): Promise<ModelOrganizationEntity | null> {
    return await this.repository.findOne({
      where: { modelId, organizationId },
      relations: {
        model: {
          user: true,
        },
        organization: true,
        sharedByUser: true,
      },
    });
  }

  /**
   * Check if a model is shared with a specific organization
   */
  async isModelSharedWithOrganization(
    modelId: string,
    organizationId: string,
  ): Promise<boolean> {
    const count = await this.repository.count({
      where: { modelId, organizationId },
    });
    return count > 0;
  }

  /**
   * Get organization IDs that have access to a specific model
   */
  async getOrganizationIdsForModel(modelId: string): Promise<string[]> {
    const shares = await this.repository.find({
      where: { modelId },
      select: ['organizationId'],
    });
    return shares.map(share => share.organizationId);
  }

  /**
   * Get model IDs that are shared with a specific organization
   */
  async getModelIdsForOrganization(organizationId: string): Promise<string[]> {
    const shares = await this.repository.find({
      where: { organizationId },
      select: ['modelId'],
    });
    return shares.map(share => share.modelId);
  }

  /**
   * Remove a model-organization share relationship
   */
  async removeShare(modelId: string, organizationId: string): Promise<void> {
    await this.repository.softDelete({ modelId, organizationId });
  }

  /**
   * Remove all shares for a specific model
   */
  async removeAllSharesForModel(modelId: string): Promise<void> {
    await this.repository.softDelete({ modelId });
  }

  /**
   * Remove all shares for a specific organization
   */
  async removeAllSharesForOrganization(organizationId: string): Promise<void> {
    await this.repository.softDelete({ organizationId });
  }

  /**
   * Save a model-organization relationship
   */
  async save(entity: ModelOrganizationEntity): Promise<ModelOrganizationEntity> {
    return await this.repository.save(entity);
  }
}
