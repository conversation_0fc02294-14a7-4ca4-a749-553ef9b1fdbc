import { DynamicModule, Module, forwardRef } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { AuthModule } from 'src/auth/auth.module';
import { jwtConfig } from 'src/auth/config/jwt.config';
import { ImageCompletionModule } from 'src/image-completion/module';
import { ImageEditModule } from 'src/image-edit/image-edit.module';
import { UserModule } from 'src/user/user.module';
import { CreateController } from './controller/create.controller';
import { ReadController } from './controller/read.controller';
import { ImageEditRequestManager } from './service/request-manager';
import { ImageEditResponseMapper } from './service/response-mapper';
import { ModelModule } from 'src/model/module';
import { OrganizationModule } from 'src/organization/organization.module';

@Module({})
export class PublicImageEditModule {
  static register(enableControllers: boolean): DynamicModule {
    return {
      module: PublicImageEditModule,
      providers: [ImageEditRequestManager, ImageEditResponseMapper],
      imports: [
        PassportModule,
        JwtModule.register(jwtConfig),
        forwardRef(() => AuthModule),
        forwardRef(() => UserModule),
        forwardRef(() => ImageCompletionModule),
        forwardRef(() => ImageEditModule),
        forwardRef(() => ModelModule),
        forwardRef(() => OrganizationModule),
      ],
      exports: [ImageEditResponseMapper],
      controllers: enableControllers ? [CreateController, ReadController] : [],
    };
  }
}
