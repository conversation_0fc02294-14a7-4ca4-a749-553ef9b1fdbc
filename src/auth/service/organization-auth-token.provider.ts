import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { AbstractProvider } from 'src/core/service/abstract.provider';
import { Repository } from 'typeorm';
import { OrganizationAuthTokenEntity } from '../entity/organization-auth-token.entity';

@Injectable()
export class OrganizationAuthTokenProvider extends AbstractProvider<OrganizationAuthTokenEntity> {
  constructor(
    @InjectRepository(OrganizationAuthTokenEntity)
    repository: Repository<OrganizationAuthTokenEntity>,
    logger: Logger,
  ) {
    super(repository, logger);
  }
}
