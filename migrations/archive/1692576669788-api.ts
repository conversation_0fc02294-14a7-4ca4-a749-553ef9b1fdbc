import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1692576669788 implements MigrationInterface {
    name = 'Api1692576669788'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "model"
            ADD "finished_email_sent_at" TIMESTAMP
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "model" DROP COLUMN "finished_email_sent_at"
        `);
    }

}
