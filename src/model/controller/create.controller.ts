import { Controller, Post, Request } from '@nestjs/common';
import { Body } from '@nestjs/common/decorators/http/route-params.decorator';
import { ModelDto } from '../dto/model.dto';
import { ModelRequest } from '../dto/model.request';
import { ModelRequestManager } from '../service/request-manager';
import { ModelResponseMapper } from '../service/response-mapper';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { UserProvider } from '../../user/service/provider';

@ApiTags('model')
@Controller('models')
export class CreateController {
  constructor(
    private requestManager: ModelRequestManager,
    private responseMapper: ModelResponseMapper,
    private userProvider: UserProvider,
  ) {}

  @Post()
  @ApiOperation({
    operationId: 'model_create',
    summary: 'Create a new model',
    description:
      'Creates a new model for the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- name: Name of the model' +
      '\n\n' +
      'Optional Parameters:\n' +
      '- privacy: Privacy of the model\n' +
      '- trainingMode: Training mode of the model\n' +
      '- description: Description of the model\n' +
      '- type: Type of the model\n' +
      '- class: Class of the model\n' +
      '- trainingDataUrls: Array of training data URLs\n' +
      '- webhookUrl: Webhook URL for the model\n' +
      '- website: Website of the model\n' +
      '- settings: Settings of the model\n' +
      '- isActive: Active status of the model\n',
  })
  @ApiBody({ type: ModelRequest, description: 'Model creation parameters.' })
  @ApiOkResponse({ type: ModelDto, description: 'Model created successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`name\`: Must be a non-empty string.
      - \`privacy\`: Must be a valid value from the \`PrivacyEnum\`.
      - \`trainingMode\`: Must be a valid string.
      - \`description\`: Must be a non-empty string.
      - \`type\`: Must be a non-empty string.
      - \`class\`: Must be a valid value from the \`ClassEnum\`.
      - \`trainingDataUrls\`: Must be a valid array of URLs.
      - \`webhookUrl\`: Must be a valid URL.
      - \`website\`: Must be a valid URL.
      - \`settings\`: Must be a valid array of settings.
      - \`isActive\`: Must be a valid boolean.
      - Invalid or unavailable parameters.
      - Failed to analyze request body.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiInternalServerErrorResponse({
    description:
      'Internal Server Error. Unexpected error occurred during model creation.',
  })
  async create(
    @Body() requestBody: ModelRequest,
    @Request() request,
  ): Promise<ModelDto> {
    const user = await this.userProvider.get(request.user.id);

    return this.requestManager
      .create(requestBody, user)
      .then((entity) => this.responseMapper.map(entity));
  }
}
