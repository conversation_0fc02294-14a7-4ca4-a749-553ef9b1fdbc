import { Injectable } from '@nestjs/common';
import { UserProvider } from 'src/user/service/provider';
import { UserResponseMapper } from 'src/user/service/response-mapper';
import { ModelCommentEntity } from '../entity/model-comment.entity';
import { ModelCommentDto } from '../dto/model-comment.dto';
import { ModelCommentLikeProvider } from './model-comment-like.provider';

@Injectable()
export class ModelCommentResponseMapper {
  constructor(
    private userProvider: UserProvider,
    private userResponseMapper: UserResponseMapper,
    private modelCommentLikeProvider: ModelCommentLikeProvider,
  ) {}

  async mapMultiple(
    entities: ModelCommentEntity[],
    userId: string = null,
  ): Promise<any> {
    const dtos = [];

    for (const entity of entities) {
      dtos.push(await this.map(entity, userId));
    }

    return dtos;
  }

  async map(
    entity: ModelCommentEntity,
    userId: string = null,
  ): Promise<ModelCommentDto> {
    const dto = new ModelCommentDto();
    const user = await this.userProvider.get(entity.userId);

    dto.id = entity.id;
    dto.userId = entity.userId;
    dto.username = user.username;
    dto.userProfilePicture = this.userResponseMapper.mapProfilePicture(user);
    dto.comment = entity.comment;
    dto.createdAt = entity.createdAt;
    dto.likes = entity.likes;

    if (userId) {
      const userHasLiked = await this.modelCommentLikeProvider.countBy({
        modelCommentId: entity.id,
        userId: userId,
      });

      dto.liked = userHasLiked > 0;
    }

    return dto;
  }
}
