import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ModelCommentEntity } from '../entity/model-comment.entity';
import { Repository } from 'typeorm';
import { ModelManager } from './manager';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Notifier } from 'src/notification/service/notifier';
import { UserResponseMapper } from 'src/user/service/response-mapper';
import { UserProvider } from 'src/user/service/provider';
import { ModelEntity } from '../entity/model.entity';
import { ModelCommentedNotification } from '../notification/model-commented.notification';
import { ModelCommentedEvent } from '../event/model-commented.event';
import { ModelResponseMapper } from './response-mapper';
import { ModelCommentProvider } from './model-comment.provider';
import { ModelCommentNotification } from '../notification/model-comment.notification';

@Injectable()
export class ModelCommentManager {
  constructor(
    @InjectRepository(ModelCommentEntity)
    private repository: Repository<ModelCommentEntity>,
    private modelManager: ModelManager,
    private eventEmitter: EventEmitter2,
    private notifier: Notifier,
    private userResponseMapper: UserResponseMapper,
    private modelResponseMapper: ModelResponseMapper,
    private userProvider: UserProvider,
    private modelCommentProvider: ModelCommentProvider,
  ) {}

  async comment(
    model: ModelEntity,
    userId: string,
    comment: string,
  ): Promise<void> {
    const modelComment = new ModelCommentEntity();
    modelComment.model = model;
    modelComment.userId = userId;
    modelComment.comment = comment;

    await this.repository.save(modelComment);

    model.comments++;

    await this.modelManager.save(model);

    const user = await this.userProvider.get(userId);

    const entities = await this.modelCommentProvider.findCommentsByModelId(
      model.id,
    );

    entities.forEach(async (entity) => {
      if (
        entity.userId != modelComment.userId &&
        model.userId != entity.userId
      ) {
        await this.notifier.dispatch(
          new ModelCommentNotification(entity.userId, {
            id: model.id,
            thumbnail: this.modelResponseMapper.generateThumbnailUrl(model),
            userId: model.userId,
            commentedById: modelComment.userId,
            commentedByUsername: user.username,
            commentedByThumbnail:
              this.userResponseMapper.mapProfilePicture(user),
            commentedAt: modelComment.createdAt,
          }),
        );
      }
    });

    if (model.userId != modelComment.userId) {
      await this.notifier.dispatch(
        new ModelCommentedNotification(model.userId, {
          id: model.id,
          thumbnail: this.modelResponseMapper.generateThumbnailUrl(model),
          name: model.name,
          userId: model.userId,
          commentedById: modelComment.userId,
          commentedByUsername: user.username,
          commentedByThumbnail: this.userResponseMapper.mapProfilePicture(user),
          commentedAt: modelComment.createdAt,
        }),
      );
    }

    await this.eventEmitter.emit(
      'model.commented',
      new ModelCommentedEvent({
        id: modelComment.id,
        modelId: model.id,
        userId: userId,
        ownerId: model.userId,
      }),
    );
  }

  async delete(
    modelComment: ModelCommentEntity,
    model: ModelEntity,
  ): Promise<void> {
    await this.repository.softRemove(modelComment);

    model.comments--;
    await this.modelManager.save(model);
  }

  async save(entity: ModelCommentEntity) {
    await this.repository.save(entity);
  }
}
