import {
  Controller,
  Get,
  Query,
  Request,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOkResponse,
  ApiBadRequestResponse,
  ApiQuery,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import { BookmarkDto } from '../dto/bookmark.dto';
import { BookmarkSearchRequest } from '../dto/bookmark.search-request';
import { BookmarkProvider } from '../service/bookmark.provider';
import { BookmarkResponseMapper } from '../service/bookmark.response-mapper';
import { setPaginationHeaders } from 'src/core/utils/pagination';

@ApiTags('bookmark')
@Controller('bookmarks')
export class ReadController {
  constructor(
    private readonly provider: BookmarkProvider,
    private readonly responseMapper: BookmarkResponseMapper,
  ) {}

  @Get()
  @ApiOperation({
    operationId: 'bookmark_list',
    summary: 'List bookmarks',
    description:
      'Retrieves a paginated list of bookmarks for the authenticated user.\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of bookmarks per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- type: Filter by bookmark type (model, image)\n',
  })
  @ApiOkResponse({
    type: BookmarkDto,
    isArray: true,
    description: 'Paginated list of bookmarks.',
  })
  @ApiQuery({
    type: BookmarkSearchRequest,
    description: 'Query parameters for searching and paginating bookmarks.',
  })
  @ApiBadRequestResponse({
    description: `
      Bad Request. Possible reasons:\n
      - Invalid query parameters.
      - \`type\`: If provided, must be a valid value ("image", "model").
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers.
      - Failed to analyze request.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @UsePipes(new ValidationPipe())
  async find(
    @Query() query: BookmarkSearchRequest,
    @Request() request,
    @Res() res: Response,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...filters } = query;
    const userId = request.user.id;
    const entities = await this.provider.findBy(
      { ...filters, userId },
      page,
      limit,
      sortBy,
      sortOrder,
    );
    const totalCount = await this.provider.countBy({ ...filters, userId });

    setPaginationHeaders(res, totalCount, page, limit);
    res.send(await this.responseMapper.mapMultiple(entities, userId));
  }
}
