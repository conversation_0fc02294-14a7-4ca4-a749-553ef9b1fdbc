import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('idp_config')
export class IdpConfigEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  name: string;

  @Column({ nullable: true })
  issuer: string;

  @Column()
  entityID: string;

  @Column()
  acsUrl: string;

  @Column()
  slsUrl: string;

  @Column({ type: 'text', nullable: true })
  certificate: string;

  @Column({ type: 'text', nullable: true })
  privateKey: string;

  @Column({ type: 'text', nullable: true })
  metadataUrl: string;

  @Column({ nullable: true })
  singleSignOnUrl: string;

  @Column({ nullable: true })
  organizationName: string;

  @Column({ nullable: true })
  organizationDisplayName: string;

  @Column({ nullable: true })
  organizationURL: string;

  @Column({ nullable: true })
  contactGivenName: string;

  @Column({ nullable: true })
  contactEmail: string;

  @Column({ type: 'json', nullable: true })
  responseMapping: Record<string, string>; // Map SAML response attributes to user fields

  @Column({ type: 'boolean', default: false })
  isDefault: boolean;
}
