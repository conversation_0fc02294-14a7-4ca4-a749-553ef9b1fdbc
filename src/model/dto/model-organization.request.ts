import { ApiProperty } from '@nestjs/swagger';
import { IsUUID } from 'class-validator';

export class ModelOrganizationShareRequest {
  @ApiProperty({
    description: 'Organization ID to share the model with',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID(4, { message: 'organizationId must be a valid UUID' })
  organizationId: string;
}

export class ModelOrganizationUnshareRequest {
  @ApiProperty({
    description: 'Organization ID to unshare the model from',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID(4, { message: 'organizationId must be a valid UUID' })
  organizationId: string;
}
