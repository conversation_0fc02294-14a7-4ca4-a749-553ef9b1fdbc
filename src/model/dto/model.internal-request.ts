import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';
import { StatusEnum } from '../enum/status.enum';
import { ModelRequest } from './model.request';

export class ModelInternalRequest extends ModelRequest {
  @IsOptional()
  @IsEnum(StatusEnum, {
    message: 'Status must be a valid value',
  })
  @ApiProperty({ enum: StatusEnum })
  status: StatusEnum;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  progress: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  prompt_v1?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  prompt?: string;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  usages: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  likes: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  reports: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  version: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  storagePath?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  generationData?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  thumbnail?: string;
}
