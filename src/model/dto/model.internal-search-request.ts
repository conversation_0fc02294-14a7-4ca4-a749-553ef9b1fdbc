import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt, IsOptional, Max, Min } from 'class-validator';
import { ModelSearchRequest } from './model.search-request';

export class ModelInternalSearchRequest extends ModelSearchRequest {
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(1000)
  @Transform((obj) => Number(obj.value))
  @ApiProperty({
    description: 'Number of items per page',
    required: false,
    default: 10,
  })
  limit?: number = 10;
}
