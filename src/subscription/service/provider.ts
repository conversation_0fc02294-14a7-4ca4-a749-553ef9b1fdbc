import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import {
  EntityNotFoundError,
  FindOneOptions,
  In,
  MoreThan,
  Repository,
} from 'typeorm';
import { AbstractProvider } from '../../core/service/abstract.provider';
import { SubscriptionStatusEnum } from '../entity/subscription-status.enum';
import { SubscriptionEntity } from '../entity/subscription.entity';

@Injectable()
export class SubscriptionProvider extends AbstractProvider<SubscriptionEntity> {
  constructor(
    @InjectRepository(SubscriptionEntity)
    repository: Repository<SubscriptionEntity>,
    logger: Logger,
  ) {
    super(repository, logger);
  }

  async getCurrentlyActiveSubscriptionForUser(
    userId: string,
  ): Promise<SubscriptionEntity | null> {
    const subscription = await this.findCurrentlyActiveSubscriptionForUser(
      userId,
    );

    if (!subscription) {
      throw new EntityNotFoundError(this.repository.target, userId);
    }

    return subscription;
  }

  // async getCurrentlyActiveSubscriptionForUser(
  //   userId: string,
  // ): Promise<SubscriptionEntity> {
  //   return this.getBy({
  //     user: { id: userId },
  //     status: In([
  //       SubscriptionStatusEnum.INACTIVE,
  //       SubscriptionStatusEnum.ACTIVE,
  //     ]),
  //     expiresAt: MoreThan(new Date()),
  //   });
  // }

  async getByExternalReference(externalReference: string) {
    const subscription = await this.repository.findOne({
      where: {
        externalReference,
      },
      order: {
        createdAt: 'DESC',
      },
    });

    if (!subscription) {
      throw new EntityNotFoundError(this.repository.target, {
        externalReference,
      });
    }

    return subscription;
  }

  async findCurrentlyActiveSubscriptionForUser(
    userId: string,
  ): Promise<SubscriptionEntity> {
    return this.repository.findOne({
      where: {
        user: { id: userId },
        status: In([
          SubscriptionStatusEnum.ACTIVE,
          SubscriptionStatusEnum.INACTIVE,
        ]),
        expiresAt: MoreThan(new Date()),
      },
      order: {
        status: 'ASC',
        expiresAt: 'DESC',
      },
    });
  }

  async findPendingSubscriptionsForUser(
    userId: string,
  ): Promise<SubscriptionEntity[]> {
    return this.findBy(
      {
        user: { id: userId },
        status: SubscriptionStatusEnum.PENDING,
      },
      1,
      999,
    );
  }

  async findPending(limit = 20): Promise<SubscriptionEntity[]> {
    return this.findBy(
      {
        status: SubscriptionStatusEnum.PENDING,
      },
      1,
      limit,
    );
  }

  prepareFindOneOptions(criteria: any): FindOneOptions {
    return {
      where: criteria,
      relations: {
        user: true,
        creditPackage: true,
      },
    };
  }
}
