import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1692371758156 implements MigrationInterface {
    name = 'Api1692371758156'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "image_completion" DROP COLUMN "storage_bucket"
        `);
        await queryRunner.query(`
            ALTER TABLE "image_completion"
            ADD "storage_bucket" text
        `);
        await queryRunner.query(`
            ALTER TABLE "image_completion" DROP COLUMN "storage_path"
        `);
        await queryRunner.query(`
            ALTER TABLE "image_completion"
            ADD "storage_path" text
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "image_completion" DROP COLUMN "storage_path"
        `);
        await queryRunner.query(`
            ALTER TABLE "image_completion"
            ADD "storage_path" json
        `);
        await queryRunner.query(`
            ALTER TABLE "image_completion" DROP COLUMN "storage_bucket"
        `);
        await queryRunner.query(`
            ALTER TABLE "image_completion"
            ADD "storage_bucket" json
        `);
    }

}
