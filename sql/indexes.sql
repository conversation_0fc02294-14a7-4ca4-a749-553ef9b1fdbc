CREATE INDEX idx_image_completion_like_user_id ON image_completion_like (user_id);
CREATE INDEX idx_image_completion_like_image_completion_id ON image_completion_like (image_completion_id);

CREATE INDEX idx_image_completion_user_id ON image_completion (user_id);
CREATE INDEX idx_image_completion_regenerated_from_id ON image_completion (regenerated_from_id);

CREATE INDEX idx_user_signup_code_user_id ON user_signup_code (user_id);

CREATE INDEX idx_transaction_user_id ON transaction (user_id);
CREATE INDEX idx_transaction_type ON transaction (type);
