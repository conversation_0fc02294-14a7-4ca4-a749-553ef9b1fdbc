import { <PERSON>, <PERSON><PERSON>, <PERSON> } from '@nestjs/common';
import { Body } from '@nestjs/common/decorators/http/route-params.decorator';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiNotFoundResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { UpscaleDto } from '../../dt../../dto/upscale.dto';
import { UpscaleInternalRequest } from '../../dto/upscale.internal-request';
import { UpscaleProvider } from '../../service/provider';
import { UpscaleRequestManager } from '../../service/request-manager';
import { UpscaleResponseMapper } from '../../service/response-mapper';

@ApiTags('upscale / internal')
@Controller('internal/upscales')
export class UpdateController {
  constructor(
    private provider: UpscaleProvider,
    private requestManager: UpscaleRequestManager,
    private responseMapper: UpscaleResponseMapper,
  ) {}

  @ApiOperation({
    operationId: 'upscale_internal_update',
    summary: 'Update an upscale (internal)',
    description:
      'Updates an upscale with internal parameters. Intended for internal use only.\n\n' +
      'Requirements:\n' +
      '- Upscale must exist' +
      '\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the upscale\n\n' +
      'Required Body Parameters:\n' +
      '- status: New status of the upscale\n' +
      '- generationSeconds: New generation seconds of the upscale\n' +
      '- progress: New progress of the upscale\n' +
      '- promptSystem: New prompt system of the upscale\n' +
      '- statusDetail: New status detail of the upscale\n' +
      '- imagePath: New image path of the upscale\n' +
      '- generationSettings: New generation settings of the upscale\n' +
      '- generationData: New generation data of the upscale\n' +
      '- generatedByUnit: New generated by unit of the upscale\n' +
      '- isActive: New active status of the upscale\n',
  })
  @ApiBody({
    type: UpscaleInternalRequest,
    description: 'Internal upscale update parameters.',
  })
  @ApiOkResponse({
    type: UpscaleDto,
    description: 'Upscale updated successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - \`status\`: Must be a valid value from the \`StatusEnum\`.
      - \`generationSeconds\`: Must be a valid number.
      - \`progress\`: Must be a valid number between 0 and 100.
      - \`promptSystem\`: Must be a valid string.
      - \`statusDetail\`: Must be a valid string.
      - \`imagePath\`: Must be a valid string.
      - \`generationSettings\`: Must be a valid object.
      - \`generationData\`: Must be a valid string.
      - \`generatedByUnit\`: Must be a valid string.
      - \`isActive\`: Must be a valid boolean.
      - Upscale does not exist.
      - Invalid or unavailable parameters.
      - Failed to analyze request body.
    `,
  })
  @ApiNotFoundResponse({
    description: 'Not Found. The upscale could not be found.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @ApiParam({
    name: 'id',
    description: 'The unique identifier of the upscale',
    type: 'string',
    format: 'uuid',
  })
  @Patch(':id')
  async update(
    @Body() requestBody: UpscaleInternalRequest,
    @Param() params,
  ): Promise<UpscaleDto> {
    const entity = await this.provider.get(params.id);

    await this.requestManager.updateInternal(entity, requestBody);

    return await this.responseMapper.mapInternal(entity);
  }
}
