import { BadRequestException, Injectable } from '@nestjs/common';
import axios, { AxiosError } from 'axios';
import { Logger } from 'nestjs-pino';
import { ModelEntity } from 'src/model/entity/model.entity';
import { AssetManager } from 'src/model/service/asset.manager';
import { v4 as uuidv4 } from 'uuid';
import * as sharp from 'sharp';

@Injectable()
export class TrainingDataService {
  // 10MB in bytes
  private readonly MAX_IMAGE_SIZE = 16 * 1024 * 1024;
  private readonly MAX_DIMENSION = 1024;

  constructor(private assetManager: AssetManager, private logger: Logger) {}

  private async resizeImage(
    buffer: Buffer,
    contentType: string,
  ): Promise<Buffer> {
    try {
      const image = sharp(buffer);
      const metadata = await image.metadata();

      if (!metadata.width || !metadata.height) {
        throw new BadRequestException('Invalid image format');
      }

      // If image is already smaller than max dimensions, return original
      if (
        metadata.width <= this.MAX_DIMENSION &&
        metadata.height <= this.MAX_DIMENSION
      ) {
        return buffer;
      }

      // Resize maintaining aspect ratio
      return await image
        .resize(this.MAX_DIMENSION, this.MAX_DIMENSION, {
          fit: 'inside',
          withoutEnlargement: true,
        })
        .toFormat(this.getSharpFormat(contentType))
        .toBuffer();
    } catch (error) {
      this.logger.error('Failed to resize image', {
        error: error.message,
        stack: error.stack,
        contentType,
        originalError: error,
      });
      throw new BadRequestException(
        `Failed to process image: ${error.message}`,
      );
    }
  }

  private getSharpFormat(mimeType: string): keyof sharp.FormatEnum {
    switch (mimeType.toLowerCase()) {
      case 'image/jpeg':
      case 'image/jpg':
        return 'jpeg';
      case 'image/png':
        return 'png';
      case 'image/webp':
        return 'webp';
      default:
        return 'jpeg';
    }
  }

  private async checkImageMetadata(url: string): Promise<void> {
    try {
      const headResponse = await axios.head(url, { timeout: 5000 });

      // Check size
      const contentLength = parseInt(
        headResponse.headers['content-length'],
        10,
      );
      if (!isNaN(contentLength) && contentLength > this.MAX_IMAGE_SIZE) {
        throw new BadRequestException(
          `Image size (${Math.round(
            contentLength / 1024 / 1024,
          )}MB) exceeds maximum allowed size of ${
            this.MAX_IMAGE_SIZE / 1024 / 1024
          }MB`,
        );
      }

      // Check content type
      const contentType = headResponse.headers['content-type'];
      if (contentType && !this.isValidImageType(contentType)) {
        throw new BadRequestException(
          `Invalid image type: ${contentType}. Supported types are: JPEG, PNG, and WebP`,
        );
      }

      if (!contentType) {
        this.logger.warn('Content-Type header not found', { url });
      }
      if (isNaN(contentLength)) {
        this.logger.warn('Content-Length header not found', { url });
      }
    } catch (error) {
      if (axios.isAxiosError(error)) {
        // If it's not a HEAD error, we'll proceed with the download
        this.logger.warn('Failed to check image metadata', {
          url,
          error: error.message,
        });
        return;
      }
      throw error;
    }
  }

  async validateImage(url: string): Promise<void> {
    try {
      await this.checkImageMetadata(url);
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const axiosError = error as AxiosError;
        if (axiosError.response?.status === 404) {
          throw new BadRequestException(`Image not found at ${url}`);
        }
        if (axiosError.code === 'ECONNABORTED') {
          throw new BadRequestException(`Timeout checking image at ${url}`);
        }
      }
      throw error;
    }
  }

  async downloadAndStoreImages(
    entity: ModelEntity,
    urls: string[],
  ): Promise<void> {
    for (let i = 0; i < urls.length; i++) {
      this.logger.log('Processing training image', {
        modelId: entity.id,
        imageIndex: i + 1,
        url: urls[i],
      });

      try {
        const response = await axios.get(urls[i], {
          responseType: 'arraybuffer',
          timeout: 30000,
          maxContentLength: this.MAX_IMAGE_SIZE,
          maxBodyLength: this.MAX_IMAGE_SIZE,
        });

        const contentType = response.headers['content-type'];
        if (!this.isValidImageType(contentType)) {
          this.logger.warn('Invalid image type detected', {
            modelId: entity.id,
            imageIndex: i + 1,
            contentType,
            url: urls[i],
          });
          throw new BadRequestException(
            `Invalid image type: ${contentType} for image ${i + 1}`,
          );
        }

        // Resize image before storing
        const resizedImageBuffer = await this.resizeImage(
          response.data,
          contentType,
        );

        const fileName = `${uuidv4()}.${this.getExtensionFromMimeType(
          contentType,
        )}`;
        const key = `${entity.storagePath}/images/${fileName}`;

        this.logger.log('Storing training image', {
          modelId: entity.id,
          imageIndex: i + 1,
          key,
          contentType,
          originalSize: response.data.length,
          resizedSize: resizedImageBuffer.length,
        });

        await this.assetManager.storeObject(
          entity,
          key,
          resizedImageBuffer,
          contentType,
        );
      } catch (error) {
        this.logger.error('Failed to download or store training image', {
          modelId: entity.id,
          modelStorageBucket: entity.storageBucket,
          modelStoragePath: entity.storagePath,
          imageIndex: i + 1,
          url: urls[i],
          error: error.message,
          stack: error.stack,
          isAxiosError: axios.isAxiosError(error),
          statusCode: axios.isAxiosError(error)
            ? error.response?.status
            : undefined,
        });

        if (axios.isAxiosError(error)) {
          const axiosError = error as AxiosError;
          if (axiosError.response?.status === 404) {
            throw new BadRequestException(
              `Image ${i + 1} not found at ${urls[i]}`,
            );
          }
          if (axiosError.code === 'ECONNABORTED') {
            throw new BadRequestException(`Timeout downloading image ${i + 1}`);
          }
        }

        throw new BadRequestException(
          `Failed to download training image ${i + 1}: ${error.message}`,
        );
      }
    }
  }

  private isValidImageType(mimeType: string): boolean {
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    return validTypes.includes(mimeType.toLowerCase());
  }

  private getExtensionFromMimeType(mimeType: string): string {
    const map = {
      'image/jpeg': 'jpg',
      'image/jpg': 'jpg',
      'image/png': 'png',
      'image/webp': 'webp',
    };
    return map[mimeType.toLowerCase()] || 'jpg';
  }
}
