import { ApiProperty } from '@nestjs/swagger';

export class OrganizationDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  handle: string;

  @ApiProperty()
  isAdmin: boolean;

  @ApiProperty()
  email: string;

  @ApiProperty()
  profilePicture: string;

  @ApiProperty()
  description: string;

  @ApiProperty()
  website: string;

  @ApiProperty()
  imagesGenerated: number;

  @ApiProperty()
  imagesAvailable: number;

  @ApiProperty()
  modelsAvailable: number;

  @ApiProperty()
  followersCount: number;

  @ApiProperty()
  followingCount: number;

  @ApiProperty()
  members: number;

  @ApiProperty()
  seatsPurchased: number;

  @ApiProperty()
  isActive: boolean;

  @ApiProperty()
  includeWatermarks: boolean;
}
