import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { OrganizationUserProvider } from 'src/organization/service/organization-user.provider';
import { IsNull, Repository } from 'typeorm';
import { CreditTypeEnum } from '../entity/credit-type.enum';
import { UserCreditBalanceEntity } from '../entity/user-credit-balance.entity';
import { UserCreditBalanceProvider } from './user-credit-balance.provider';

@Injectable()
export class UserCreditBalanceManager {
  constructor(
    @InjectRepository(UserCreditBalanceEntity)
    private repository: Repository<UserCreditBalanceEntity>,
    private provider: UserCreditBalanceProvider,
    private organizationUserProvider: OrganizationUserProvider,
    private logger: Logger,
  ) {}

  async save(
    entity: UserCreditBalanceEntity,
  ): Promise<UserCreditBalanceEntity> {
    await this.repository.save(entity);

    return entity;
  }

  async deduct(
    creditType: CreditTypeEnum,
    amount: number,
    userId?: string,
    organizationId?: string,
  ): Promise<UserCreditBalanceEntity> {
    this.logger.log('user_credit_balance.deduct', {
      creditType,
      amount,
      userId,
      organizationId,
    });

    const balance = await this.getNextExpiring(
      creditType,
      userId,
      organizationId,
    );

    balance.balance -= amount;
    await this.save(balance);

    return balance;
  }

  async getNextExpiring(
    creditType: CreditTypeEnum,
    userId?: string,
    organizationId?: string,
  ): Promise<UserCreditBalanceEntity> {
    if (organizationId) {
      return this.provider.getNextExpiring(creditType, null, organizationId);
    }

    return this.provider.getNextExpiring(creditType, userId);
  }

  async increase(
    creditType: CreditTypeEnum,
    amount: number,
    userId?: string,
    organizationId?: string,
    expiresAt?: Date,
  ): Promise<UserCreditBalanceEntity> {
    this.logger.log('user_credit_balance.increase', {
      creditType,
      amount,
      expiresAt,
      userId,
      organizationId,
    });

    const balance = await this.getOrCreate(
      creditType,
      expiresAt,
      userId,
      organizationId,
    );

    balance.balance += amount;

    await this.save(balance);

    return balance;
  }

  async getOrCreate(
    creditType: CreditTypeEnum,
    expiresAt?: Date,
    userId?: string,
    organizationId?: string,
  ) {
    const whereClause = organizationId
      ? {
          creditType,
          expiresAt: expiresAt ?? IsNull(),
          userId: IsNull(),
          organizationId,
        }
      : {
          creditType,
          expiresAt: expiresAt ?? IsNull(),
          userId,
          organizationId: IsNull(),
        };

    let balance = await this.repository.findOne({
      where: whereClause,
    });

    if (!balance) {
      balance = new UserCreditBalanceEntity();
      balance.creditType = creditType;
      balance.balance = 0;
      balance.expiresAt = expiresAt;
      balance.userId = organizationId ? null : userId;
      balance.organizationId = organizationId;

      await this.save(balance);
    }

    return balance;
  }
}
