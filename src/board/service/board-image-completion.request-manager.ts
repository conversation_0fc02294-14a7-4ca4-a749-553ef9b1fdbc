import { Injectable } from '@nestjs/common';
import { ImageCompletionSearchRequest } from 'src/image-completion/dto/image-completion.search-request';
import { ImageCompletionRequestManager } from 'src/image-completion/service/request-manager';
import { UserEntity } from 'src/user/entity/user.entity';
import { BoardEntity } from '../entity/board.entity';
import { SafetyEnum } from '../enum/board-safety.enum';
import { VisibilityEnum } from '../enum/board-visibility.enum';

@Injectable()
export class BoardImageCompletionRequestManager {
  constructor(
    private imageCompletionRequestManager: ImageCompletionRequestManager,
  ) {}

  async sanitizeSearchFilters(
    filters: ImageCompletionSearchRequest,
    currentUser: UserEntity,
    board: BoardEntity,
  ): Promise<any> {
    const criteria =
      await this.imageCompletionRequestManager.sanitizeSearchFilters(filters);

    criteria.includeNsfw = true;

    if (
      board.visibility === VisibilityEnum.PUBLIC ||
      board.safety === SafetyEnum.SFW
    ) {
      criteria.includeNsfw = false;
    }

    criteria.boardId = board.id;

    return criteria;
  }
}
