import {
  Controller,
  Delete,
  ForbiddenException,
  HttpCode,
  Param,
  ParseUUIDPipe,
  Request,
} from '@nestjs/common';
import {
  ApiNoContentResponse,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiForbiddenResponse,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { BoardUserProvider } from '../service/board-user.provider';
import { BoardManager } from '../service/manager';
import { BoardProvider } from '../service/provider';

@ApiTags('board')
@Controller('boards')
export class DeleteController {
  constructor(
    private provider: BoardProvider,
    private manager: BoardManager,
    private boardUserProvider: BoardUserProvider,
  ) {}

  @Delete(':id')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'board_delete',
    summary: 'Delete a board',
    description:
      'Deletes the specified board if the authenticated user is the owner.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the board to delete',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the board to delete.',
    type: String,
  })
  @ApiNoContentResponse({
    description: 'Board deleted successfully.',
  })
  @ApiBadRequestResponse({
    description: `
      Bad Request. Possible reasons:\n
      - \`id\`: Must be a valid UUID.
      - Board does not exist.
      - Invalid or unavailable parameters.
      - Failed to analyze request.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden. Only the owner can delete the board.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async delete(
    @Request() req,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<void> {
    const board = await this.provider.get(id);

    if (!(await this.boardUserProvider.isOwner(req.user.id, board.id))) {
      throw new ForbiddenException();
    }

    await this.manager.delete(board);
  }
}
