import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1701019620107 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
                INSERT INTO "credit_package"
                ("id", "name", "price", "is_recurring", "is_visible", "credit_types")
                VALUES
                (uuid_generate_v4(), 'Free', 0, false, false, '{"IMAGE": 120}')
            `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
