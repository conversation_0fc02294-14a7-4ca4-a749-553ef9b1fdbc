import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1733367111449 implements MigrationInterface {
    name = 'Api1733367111449'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "idp_config"
            ADD "issuer" character varying
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "idp_config" DROP COLUMN "issuer"
        `);
    }

}
