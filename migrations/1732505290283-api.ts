import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1732505290283 implements MigrationInterface {
  name = 'Api1732505290283';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE "bookmarks" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "user_id" character varying NOT NULL,
                "type" character varying NOT NULL,
                "reference_id" character varying NOT NULL,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_7f976ef6cecd37a53bd11685f32" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            CREATE INDEX "idx_bookmark_user_id" ON "bookmarks" ("user_id")
        `);
    await queryRunner.query(`
            CREATE INDEX "idx_bookmark_reference_id" ON "bookmarks" ("reference_id")
        `);
    await queryRunner.query(`
            CREATE UNIQUE INDEX "idx_bookmark_user_type_ref" ON "bookmarks" ("user_id", "type", "reference_id")
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            DROP INDEX "public"."idx_bookmark_user_type_ref"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."idx_bookmark_reference_id"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."idx_bookmark_user_id"
        `);
    await queryRunner.query(`
            DROP TABLE "bookmarks"
        `);
  }
}
