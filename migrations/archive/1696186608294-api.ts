import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1696186608294 implements MigrationInterface {
    name = 'Api1696186608294'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "image_completion_like" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "user_id" uuid NOT NULL,
                "deleted_at" TIMESTAMP,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "image_completion_id" uuid,
                CONSTRAINT "PK_3664255684d9f9bdd7dae90ed48" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            ALTER TABLE "image_completion_like"
            ADD CONSTRAINT "FK_98469faf3de8c60756a3cfb62a5" FOREIGN KEY ("image_completion_id") REFERENCES "image_completion"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "image_completion_like" DROP CONSTRAINT "FK_98469faf3de8c60756a3cfb62a5"
        `);
        await queryRunner.query(`
            DROP TABLE "image_completion_like"
        `);
    }

}
