import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1693692134850 implements MigrationInterface {
    name = 'Api1693692134850'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "reset_password_token" character varying
        `);
        await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "reset_password_expires" TIMESTAMP
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "reset_password_expires"
        `);
        await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "reset_password_token"
        `);
    }

}
