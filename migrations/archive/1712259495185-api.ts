import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1712259495185 implements MigrationInterface {
  name = 'Api1712259495185';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE "model_version" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "version" integer NOT NULL DEFAULT '1',
                "filename" text,
                "trained_at" TIMESTAMP,
                "deleted_at" TIMESTAMP,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "model_id" uuid,
                CONSTRAINT "PK_1213bca4e8ee7ac1323cc4bf454" PRIMARY KEY ("id")
            )
        `);

    await queryRunner.query(`
        ALTER TABLE "model_version"
        ADD CONSTRAINT "FK_959afe36ff1a069b7cfbdb65ece" FOREIGN KEY ("model_id") REFERENCES "model"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);

    await queryRunner.query(`
        INSERT INTO model_version
        (id, model_id, version, filename, created_at, trained_at, updated_at)
        SELECT uuid_generate_v4(), m.id, m.version, m.storage_path, m.created_at, finished_email_sent_at, COALESCE(m.finished_email_sent_at, m.created_at)
        FROM model m
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "model_version" DROP CONSTRAINT "FK_959afe36ff1a069b7cfbdb65ece"
        `);
    await queryRunner.query(`
            DROP TABLE "model_version"
        `);
  }
}
