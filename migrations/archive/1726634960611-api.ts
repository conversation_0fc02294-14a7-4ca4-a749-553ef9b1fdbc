import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1726634960611 implements MigrationInterface {
  name = 'Api1726634960611';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "subscription"
            ADD "previous_subscription_id" uuid
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "subscription" DROP COLUMN "previous_subscription_id"
        `);
  }
}
