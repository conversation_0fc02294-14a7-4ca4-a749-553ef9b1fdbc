import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1739380514342 implements MigrationInterface {
  name = 'Api1739380514342';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "image_edit"
            ADD "input_image_url" text
        `);
    await queryRunner.query(`
            ALTER TABLE "image_edit" DROP CONSTRAINT "FK_b45b48fc973cbea3f3805ccee86"
        `);
    await queryRunner.query(`
            ALTER TABLE "image_edit"
            ALTER COLUMN "original_image_completion_id" DROP NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "image_edit"
            ADD CONSTRAINT "FK_b45b48fc973cbea3f3805ccee86" FOREIGN KEY ("original_image_completion_id") REFERENCES "image_completion"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "image_edit" DROP CONSTRAINT "FK_b45b48fc973cbea3f3805ccee86"
        `);
    await queryRunner.query(`
            ALTER TABLE "image_edit"
            ALTER COLUMN "original_image_completion_id"
            SET NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "image_edit"
            ADD CONSTRAINT "FK_b45b48fc973cbea3f3805ccee86" FOREIGN KEY ("original_image_completion_id") REFERENCES "image_completion"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "image_edit" DROP COLUMN "input_image_url"
        `);
  }
}
