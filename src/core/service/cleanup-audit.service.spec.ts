import { Test, TestingModule } from '@nestjs/testing';
import { CleanupAuditService } from './cleanup-audit.service';
import { CleanupStatusEnum, CleanupOperationTypeEnum } from '../enum/cleanup-status.enum';

describe('CleanupAuditService', () => {
  let service: CleanupAuditService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CleanupAuditService,
      ],
    }).compile();

    service = module.get<CleanupAuditService>(CleanupAuditService);
    
    // Mock the logger to avoid console output during tests
    jest.spyOn(service['logger'], 'log').mockImplementation();
    jest.spyOn(service['logger'], 'warn').mockImplementation();
    jest.spyOn(service['logger'], 'error').mockImplementation();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('startOperation', () => {
    it('should start a new cleanup operation', () => {
      const operationId = service.startOperation(
        CleanupOperationTypeEnum.USER_IMAGES,
        'user123',
        '<EMAIL>',
        { username: 'testuser' }
      );

      expect(operationId).toBeDefined();
      expect(operationId).toMatch(/user_images_user123_\d+/);
      expect(service['logger'].log).toHaveBeenCalledWith(
        'Cleanup operation started',
        expect.objectContaining({
          operationId,
          operationType: CleanupOperationTypeEnum.USER_IMAGES,
          userId: 'user123',
          userEmail: '<EMAIL>',
          metadata: { username: 'testuser' },
        })
      );
    });

    it('should return a unique operation ID for each operation', () => {
      const operationId1 = service.startOperation(
        CleanupOperationTypeEnum.USER_IMAGES,
        'user123',
        '<EMAIL>'
      );

      const operationId2 = service.startOperation(
        CleanupOperationTypeEnum.IMAGE_COMPLETIONS,
        'user123',
        '<EMAIL>'
      );

      expect(operationId1).not.toBe(operationId2);
    });
  });

  describe('updateProgress', () => {
    it('should update operation progress', () => {
      const operationId = service.startOperation(
        CleanupOperationTypeEnum.USER_IMAGES,
        'user123',
        '<EMAIL>'
      );

      service.updateProgress(operationId, 10, 8, 2);

      const operation = service.getOperation(operationId);
      expect(operation?.itemsProcessed).toBe(10);
      expect(operation?.itemsDeleted).toBe(8);
      expect(operation?.itemsFailed).toBe(2);
      expect(service['logger'].log).toHaveBeenCalledWith(
        'Cleanup operation progress',
        expect.objectContaining({
          operationId,
          itemsProcessed: 10,
          itemsDeleted: 8,
          itemsFailed: 2,
        })
      );
    });

    it('should warn when operation is not found', () => {
      service.updateProgress('nonexistent', 10, 8, 2);
      expect(service['logger'].warn).toHaveBeenCalledWith(
        'Cleanup operation not found for progress update',
        { operationId: 'nonexistent' }
      );
    });
  });

  describe('completeOperation', () => {
    it('should complete operation successfully without failures', () => {
      const operationId = service.startOperation(
        CleanupOperationTypeEnum.USER_IMAGES,
        'user123',
        '<EMAIL>'
      );

      service.completeOperation(operationId, 10, 10, 0);

      const operation = service.getOperation(operationId);
      expect(operation?.status).toBe(CleanupStatusEnum.COMPLETED);
      expect(operation?.endTime).toBeDefined();
      expect(operation?.itemsProcessed).toBe(10);
      expect(operation?.itemsDeleted).toBe(10);
      expect(operation?.itemsFailed).toBe(0);
    });

    it('should mark operation as partially completed when there are failures', () => {
      const operationId = service.startOperation(
        CleanupOperationTypeEnum.USER_IMAGES,
        'user123',
        '<EMAIL>'
      );

      service.completeOperation(operationId, 10, 8, 2);

      const operation = service.getOperation(operationId);
      expect(operation?.status).toBe(CleanupStatusEnum.PARTIALLY_COMPLETED);
      expect(operation?.itemsFailed).toBe(2);
    });
  });

  describe('failOperation', () => {
    it('should mark operation as failed', () => {
      const operationId = service.startOperation(
        CleanupOperationTypeEnum.USER_IMAGES,
        'user123',
        '<EMAIL>'
      );

      service.failOperation(operationId, 'S3 connection failed');

      const operation = service.getOperation(operationId);
      expect(operation?.status).toBe(CleanupStatusEnum.FAILED);
      expect(operation?.error).toBe('S3 connection failed');
      expect(operation?.endTime).toBeDefined();
    });

    it('should mark operation as retrying when retrying is true', () => {
      const operationId = service.startOperation(
        CleanupOperationTypeEnum.USER_IMAGES,
        'user123',
        '<EMAIL>'
      );

      service.failOperation(operationId, 'Temporary error', true);

      const operation = service.getOperation(operationId);
      expect(operation?.status).toBe(CleanupStatusEnum.RETRYING);
      expect(operation?.retryCount).toBe(1);
      expect(operation?.endTime).toBeUndefined();
    });
  });

  describe('generateUserCleanupSummary', () => {
    it('should generate accurate summary for user operations', () => {
      const userId = 'user123';
      const userEmail = '<EMAIL>';

      // Create multiple operations for the same user
      const op1 = service.startOperation(CleanupOperationTypeEnum.USER_IMAGES, userId, userEmail);
      service.completeOperation(op1, 5, 5, 0);

      const op2 = service.startOperation(CleanupOperationTypeEnum.IMAGE_COMPLETIONS, userId, userEmail);
      service.completeOperation(op2, 10, 8, 2);

      const op3 = service.startOperation(CleanupOperationTypeEnum.MODELS, userId, userEmail);
      service.failOperation(op3, 'Failed to delete models');

      const summary = service.generateUserCleanupSummary(userId);

      expect(summary.totalOperations).toBe(3);
      expect(summary.completedOperations).toBe(1);
      expect(summary.partiallyCompletedOperations).toBe(1);
      expect(summary.failedOperations).toBe(1);
      expect(summary.totalItemsDeleted).toBe(13); // 5 + 8
      expect(summary.totalItemsFailed).toBe(2);
    });

    it('should return empty summary for user with no operations', () => {
      const summary = service.generateUserCleanupSummary('nonexistent-user');

      expect(summary.totalOperations).toBe(0);
      expect(summary.completedOperations).toBe(0);
      expect(summary.failedOperations).toBe(0);
      expect(summary.totalItemsDeleted).toBe(0);
      expect(summary.totalItemsFailed).toBe(0);
    });
  });

  describe('cleanupOldOperations', () => {
    it('should remove old completed operations', async () => {
      const operationId = service.startOperation(
        CleanupOperationTypeEnum.USER_IMAGES,
        'user123',
        '<EMAIL>'
      );

      service.completeOperation(operationId, 10, 10, 0);

      // Mock old operation by directly manipulating the endTime
      const operation = service.getOperation(operationId);
      if (operation) {
        operation.endTime = new Date(Date.now() - 25 * 60 * 60 * 1000); // 25 hours ago
      }

      service.cleanupOldOperations(24); // Remove operations older than 24 hours

      expect(service.getOperation(operationId)).toBeUndefined();
    });

    it('should not remove operations without endTime', () => {
      const operationId = service.startOperation(
        CleanupOperationTypeEnum.USER_IMAGES,
        'user123',
        '<EMAIL>'
      );

      service.cleanupOldOperations(24);

      expect(service.getOperation(operationId)).toBeDefined();
    });
  });
});
