import { BadRequestException, Injectable } from '@nestjs/common';
import { ModelCommentLikeManager } from './model-comment-like.manager';
import { ModelCommentLikeProvider } from './model-comment-like.provider';
import { ModelCommentEntity } from '../entity/model-comment.entity';

@Injectable()
export class ModelCommentLikeRequestManager {
  constructor(
    private manager: ModelCommentLikeManager,
    private provider: ModelCommentLikeProvider,
  ) {}

  async like(modelComment: ModelCommentEntity, userId: string): Promise<void> {
    const existingLikes = await this.provider.countBy({
      modelCommentId: modelComment.id,
      userId: userId,
    });

    if (existingLikes > 0) {
      throw new BadRequestException('Already liked');
    }

    await this.manager.like(modelComment, userId);
  }

  async unlike(
    modelComment: ModelCommentEntity,
    userId: string,
  ): Promise<void> {
    const modelCommentLike = await this.provider.getBy({
      modelCommentId: modelComment.id,
      userId: userId,
    });

    await this.manager.unlike(modelCommentLike);
  }
}
