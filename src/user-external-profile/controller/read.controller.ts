import { Controller, Get, Param, Query, Request } from '@nestjs/common';
import { UserExternalProfileProvider } from '../service/provider';
import { UserExternalProfileResponseMapper } from '../service/response-mapper';
import { UserExternalProfileDto } from '../dto/user-external-profile.dto';
import {
  ApiOkResponse,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiBadRequestResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
} from '@nestjs/swagger';
import { UserExternalProfileSearchRequest } from '../dto/model.search-request';

@ApiTags('user')
@Controller('users/current/external-profiles')
export class ReadController {
  constructor(
    private provider: UserExternalProfileProvider,
    private responseMapper: UserExternalProfileResponseMapper,
  ) {}

  @Get()
  @ApiOperation({
    operationId: 'user_external_profile_list',
    summary: 'List external profiles for current user',
    description:
      'Retrieves a paginated list of external profiles for the current authenticated user.\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of profiles per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n',
  })
  @ApiOkResponse({
    type: Array<UserExternalProfileDto>,
    description: 'Paginated list of external profiles.',
  })
  @ApiQuery({ type: UserExternalProfileSearchRequest })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - Invalid or unavailable parameters.
  `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async findCurrentExternal(
    @Request() request,
    @Query() query: UserExternalProfileSearchRequest,
  ): Promise<UserExternalProfileDto[]> {
    return this.provider
      .findBy({ user: { id: request.user.id } }, query.page, query.limit)
      .then((entities) => this.responseMapper.mapMultiple(entities));
  }

  @Get(':id')
  @ApiOperation({
    operationId: 'user_external_profile_get',
    summary: 'Get external profile by ID for current user',
    description:
      'Retrieves a specific external profile by its ID for the current authenticated user.\n\n' +
      'Required Parameter:\n' +
      '- id: External Profile ID\n',
  })
  @ApiOkResponse({
    type: UserExternalProfileDto,
    description: 'External profile details.',
  })
  @ApiParam({ name: 'id', description: 'External Profile ID' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - External profile does not exist.
  `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. The user is not authenticated.',
  })
  @ApiNotFoundResponse({
    description: 'Not Found. The external profile could not be found.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async getCurrentExternal(
    @Request() request,
    @Param() params,
  ): Promise<UserExternalProfileDto> {
    return this.provider
      .getBy({ user: { id: request.user.id }, id: params.id })
      .then((entity) => this.responseMapper.map(entity));
  }
}
