import { BadRequestException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from 'nestjs-pino';
import Stripe from 'stripe';
import { VoucherService, VoucherValidationResult } from './voucher.service';

describe('VoucherService', () => {
  let service: VoucherService;
  let stripe: Stripe;
  let logger: Logger;

  const mockStripe = {
    promotionCodes: {
      list: jest.fn(),
    },
    invoices: {
      list: jest.fn(),
    },
    subscriptions: {
      list: jest.fn(),
    },
  };

  const mockLogger = {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        VoucherService,
        {
          provide: 'Stripe',
          useValue: mockStripe,
        },
        {
          provide: Logger,
          useValue: mockLogger,
        },
      ],
    }).compile();

    service = module.get<VoucherService>(VoucherService);
    stripe = module.get<Stripe>('Stripe');
    logger = module.get<Logger>(Logger);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('validateVoucher', () => {
    it('should validate a valid fixed amount voucher code', async () => {
      const mockPromotionCode = {
        id: 'promo_123',
        active: true,
        expires_at: null,
        max_redemptions: null,
        times_redeemed: 0,
        code: 'SAVE20',
        coupon: {
          id: 'coupon_123',
          amount_off: 2000, // $20.00
          percent_off: null,
        },
        restrictions: null,
      } as unknown as Stripe.PromotionCode;

      mockStripe.promotionCodes.list.mockResolvedValue({
        data: [mockPromotionCode],
      });

      const result = await service.validateVoucher('SAVE20');

      expect(result.isValid).toBe(true);
      expect(result.promotionCode).toEqual(mockPromotionCode);
      expect(result.discountAmount).toBe(2000);
      expect(result.discountPercent).toBeUndefined();
      expect(result.errorMessage).toBeUndefined();

      expect(mockStripe.promotionCodes.list).toHaveBeenCalledWith({
        code: 'SAVE20',
        active: true,
        limit: 1,
      });
    });

    it('should validate a valid percentage voucher code', async () => {
      const mockPromotionCode = {
        id: 'promo_456',
        active: true,
        expires_at: null,
        max_redemptions: null,
        times_redeemed: 0,
        code: 'SAVE25PERCENT',
        coupon: {
          id: 'coupon_456',
          amount_off: null,
          percent_off: 25,
        },
        restrictions: null,
      } as unknown as Stripe.PromotionCode;

      mockStripe.promotionCodes.list.mockResolvedValue({
        data: [mockPromotionCode],
      });

      const result = await service.validateVoucher('SAVE25PERCENT');

      expect(result.isValid).toBe(true);
      expect(result.promotionCode).toEqual(mockPromotionCode);
      expect(result.discountAmount).toBeUndefined();
      expect(result.discountPercent).toBe(25);
    });

    it('should return invalid for non-existent voucher code', async () => {
      mockStripe.promotionCodes.list.mockResolvedValue({
        data: [],
      });

      const result = await service.validateVoucher('INVALID_CODE');

      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toBe('voucher.invalid_code');
    });

    it('should return invalid for expired voucher code', async () => {
      const expiredTime = Math.floor(Date.now() / 1000) - 3600; // 1 hour ago
      const mockPromotionCode = {
        id: 'promo_expired',
        active: true,
        expires_at: expiredTime,
        code: 'EXPIRED',
        coupon: {
          id: 'coupon_expired',
          amount_off: 1000,
        },
      } as Stripe.PromotionCode;

      mockStripe.promotionCodes.list.mockResolvedValue({
        data: [mockPromotionCode],
      });

      const result = await service.validateVoucher('EXPIRED');

      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toBe('voucher.expired_code');
    });

    it('should return invalid for voucher with usage limit reached', async () => {
      const mockPromotionCode = {
        id: 'promo_limit_reached',
        active: true,
        expires_at: null,
        max_redemptions: 100,
        times_redeemed: 100,
        code: 'LIMIT_REACHED',
        coupon: {
          id: 'coupon_limit',
          amount_off: 1000,
        },
      } as Stripe.PromotionCode;

      mockStripe.promotionCodes.list.mockResolvedValue({
        data: [mockPromotionCode],
      });

      const result = await service.validateVoucher('LIMIT_REACHED');

      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toBe('voucher.usage_limit_reached');
    });

    it('should check customer eligibility when customer ID is provided', async () => {
      const mockPromotionCode = {
        id: 'promo_first_time',
        active: true,
        expires_at: null,
        max_redemptions: null,
        times_redeemed: 0,
        code: 'FIRST_TIME',
        coupon: {
          id: 'coupon_first_time',
          amount_off: 1000,
        },
        restrictions: {
          first_time_transaction: true,
        },
      } as Stripe.PromotionCode;

      mockStripe.promotionCodes.list.mockResolvedValue({
        data: [mockPromotionCode],
      });

      // Mock that customer has no previous paid invoices
      mockStripe.invoices.list.mockResolvedValue({
        data: [],
      });

      // Mock that customer has no subscriptions with this promotion code
      mockStripe.subscriptions.list.mockResolvedValue({
        data: [],
      });

      const result = await service.validateVoucher('FIRST_TIME', 'cus_123');

      expect(result.isValid).toBe(true);
      expect(mockStripe.invoices.list).toHaveBeenCalledWith({
        customer: 'cus_123',
        status: 'paid',
        limit: 1,
      });
      expect(mockStripe.subscriptions.list).toHaveBeenCalledWith({
        customer: 'cus_123',
        limit: 100,
      });
    });

    it('should reject first-time promotion for existing customers', async () => {
      const mockPromotionCode = {
        id: 'promo_first_time',
        active: true,
        expires_at: null,
        max_redemptions: null,
        times_redeemed: 0,
        code: 'FIRST_TIME',
        coupon: {
          id: 'coupon_first_time',
          amount_off: 1000,
        },
        restrictions: {
          first_time_transaction: true,
        },
      } as Stripe.PromotionCode;

      mockStripe.promotionCodes.list.mockResolvedValue({
        data: [mockPromotionCode],
      });

      // Mock that customer has previous paid invoices
      mockStripe.invoices.list.mockResolvedValue({
        data: [{ id: 'inv_123' }],
      });

      const result = await service.validateVoucher('FIRST_TIME', 'cus_123');

      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toBe('voucher.not_first_time_customer');
    });

    it('should handle Stripe API errors gracefully', async () => {
      mockStripe.promotionCodes.list.mockRejectedValue(new Error('Stripe API error'));

      const result = await service.validateVoucher('ERROR_CODE');

      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toBe('voucher.validation_failed');
      expect(mockLogger.error).toHaveBeenCalledWith('voucher.validation_error', {
        error: expect.any(Error),
        voucherCode: 'ERROR_CODE',
      });
    });
  });

  describe('calculateDiscountAmount', () => {
    it('should calculate fixed amount discount', () => {
      const validationResult: VoucherValidationResult = {
        isValid: true,
        promotionCode: {
          coupon: {
            amount_off: 2000,
            percent_off: null,
          },
        } as Stripe.PromotionCode,
      };

      const discount = service.calculateDiscountAmount(5000, validationResult);

      expect(discount).toBe(2000);
    });

    it('should cap fixed amount discount at original amount', () => {
      const validationResult: VoucherValidationResult = {
        isValid: true,
        promotionCode: {
          coupon: {
            amount_off: 8000,
            percent_off: null,
          },
        } as Stripe.PromotionCode,
      };

      const discount = service.calculateDiscountAmount(5000, validationResult);

      expect(discount).toBe(5000);
    });

    it('should calculate percentage discount', () => {
      const validationResult: VoucherValidationResult = {
        isValid: true,
        promotionCode: {
          coupon: {
            amount_off: null,
            percent_off: 25,
          },
        } as unknown as Stripe.PromotionCode,
      };

      const discount = service.calculateDiscountAmount(10000, validationResult);

      expect(discount).toBe(2500); // 25% of 10000
    });

    it('should return 0 for invalid validation result', () => {
      const validationResult: VoucherValidationResult = {
        isValid: false,
        errorMessage: 'voucher.invalid',
      };

      const discount = service.calculateDiscountAmount(5000, validationResult);

      expect(discount).toBe(0);
    });
  });

  describe('applyVoucherToSubscriptionUpdate', () => {
    it('should apply valid voucher to subscription update parameters', async () => {
      const mockPromotionCode = {
        id: 'promo_123',
        active: true,
        code: 'SAVE20',
        coupon: {
          amount_off: 2000,
        },
      } as Stripe.PromotionCode;

      mockStripe.promotionCodes.list.mockResolvedValue({
        data: [mockPromotionCode],
      });

      mockStripe.subscriptions.list.mockResolvedValue({
        data: [],
      });

      const originalParams = {
        items: [{ id: 'si_123', price: 'price_456' }],
      };

      const result = await service.applyVoucherToSubscriptionUpdate(
        originalParams,
        'SAVE20',
        'cus_123',
      );

      expect(result).toEqual({
        ...originalParams,
        promotion_code: 'promo_123',
      });
    });

    it('should throw BadRequestException for invalid voucher', async () => {
      mockStripe.promotionCodes.list.mockResolvedValue({
        data: [],
      });

      const originalParams = {
        items: [{ id: 'si_123', price: 'price_456' }],
      };

      await expect(
        service.applyVoucherToSubscriptionUpdate(originalParams, 'INVALID', 'cus_123'),
      ).rejects.toThrow(BadRequestException);
    });
  });
});
