import {
  Body,
  Controller,
  Post,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiNoContentResponse,
  ApiTags,
  ApiOperation,
} from '@nestjs/swagger';
import { AppConfigurationService } from 'src/core/service/app-configuration.service';
import { Mailer } from 'src/core/service/mailer.service';
import { InternalEmailRequest } from '../dto/internal-email.request';

@ApiTags('internal / mailer')
@Controller('internal/email')
export class MailerController {
  constructor(
    private appConfig: AppConfigurationService,
    private mailer: Mailer,
  ) {}

  @ApiOperation({
    operationId: 'internal_email_send',
    summary: 'Send internal email',
    description:
      'Sends an internal email using the configured mailer service.\n\n' +
      'Required Body Parameters:\n' +
      '- subject: Email subject\n' +
      '- body: Email body\n',
  })
  @ApiBody({
    type: InternalEmailRequest,
    description: 'Email sending parameters.',
  })
  @ApiNoContentResponse({ description: 'Email sent successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`subject\`: Must be a non-empty string.
      - \`body\`: Must be a non-empty string.
      - Invalid or unavailable parameters.
      - Failed to analyze request body.
  `,
  })
  @Post()
  @UsePipes(new ValidationPipe())
  async send(@Body() requestBody: InternalEmailRequest): Promise<void> {
    await this.mailer.sendRaw(
      requestBody.body,
      requestBody.subject,
      this.appConfig.asString('INTERNAL_MAILER_TO') ?? '<EMAIL>',
    );
  }
}
