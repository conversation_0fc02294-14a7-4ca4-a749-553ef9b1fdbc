import { <PERSON>, Post } from '@nestjs/common';
import { Body } from '@nestjs/common/decorators/http/route-params.decorator';
import { UserDto } from '../dto/user.dto';
import { UserResponseMapper } from '../service/response-mapper';
import { UserCreateRequest } from '../dto/user.create.request';
import { UserRequestManager } from '../service/request-manager';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { Public } from '../../core/security/public-routes';

@ApiTags('user')
@Controller('users')
export class CreateController {
  constructor(
    private userMapper: UserResponseMapper,
    private userRequestManager: UserRequestManager,
  ) {}

  @ApiOperation({
    operationId: 'user_create',
    summary: 'Create a new user',
    description:
      'Creates a new user account.\n\n' +
      'Requirements:\n' +
      '- Email must be unique\n' +
      '- Username must be unique\n' +
      'Required Parameters:\n' +
      '- email: User email address\n' +
      '- password: User password\n' +
      '- username: User username\n' +
      '- name: User name\n\n' +
      'Optional Parameters:\n' +
      '- description: User description\n' +
      '- website: User website\n' +
      '- socialMediaAccounts: User social media accounts\n' +
      '- birthday: User birthday\n' +
      '- timezone: User timezone\n' +
      '- currency: User currency\n' +
      '- locale: User locale\n\n' +
      'Returns the created user object.',
  })
  @ApiBody({
    type: UserCreateRequest,
    description: 'User creation parameters.',
  })
  @ApiOkResponse({ type: UserDto, description: 'User created successfully.' })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`email\`: Must be a valid email.
      - \`password\`: Must be a valid string.
      - \`username\`: Must be a valid string.
      - \`name\`: Must be a valid string.
      - \`description\`: Must be a valid string.
      - \`website\`: Must be a valid string.
      - \`socialMediaAccounts\`: Must be a valid object.
      - \`birthday\`: Must be a valid date.
      - \`timezone\`: Must be a valid string.
      - \`currency\`: Must be a valid string.
      - \`locale\`: Must be a valid string.
      - Invalid or unavailable parameters.
  `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @Public()
  @Post()
  async create(@Body() userCreateRequest: UserCreateRequest): Promise<UserDto> {
    const user = await this.userRequestManager.create(userCreateRequest);

    return this.userMapper.map(user);
  }
}
