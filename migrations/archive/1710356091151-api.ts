import { MigrationInterface, QueryRunner } from "typeorm";

export class Api1710356091151 implements MigrationInterface {
    name = 'Api1710356091151'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "description" text
        `);
        await queryRunner.query(`
            ALTER TABLE "user_account"
            ADD "website" text
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "website"
        `);
        await queryRunner.query(`
            ALTER TABLE "user_account" DROP COLUMN "description"
        `);
    }

}
