import { BadRequestException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from 'nestjs-pino';
import { UserEntity } from '../../user/entity/user.entity';
import { UserCreditBalanceInternalRequest } from '../dto/user-credit-balance.internal-request';
import { CreditTypeEnum } from '../entity/credit-type.enum';
import { UserCreditBalanceEntity } from '../entity/user-credit-balance.entity';
import { PaymentRequiredException } from '../exception/payment-required.exception';
import { UserCreditBalanceManager } from './user-credit-balance.manager';
import { UserCreditBalanceRequestManager } from './user-credit-balance.request-manager';

describe('UserCreditBalanceRequestManager', () => {
  let requestManager: UserCreditBalanceRequestManager;
  let creditBalanceManager: UserCreditBalanceManager;
  let logger: Logger;

  const user: UserEntity = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    includeWatermarks: false,
    isActive: true,
    isVerified: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  } as UserEntity;

  const request: UserCreditBalanceInternalRequest = {
    creditType: CreditTypeEnum.IMAGE,
    amount: 100,
  };

  const mockCreditBalanceEntity: UserCreditBalanceEntity = {
    id: '123e4567-e89b-12d3-a456-426614174001',
    balance: 100,
    creditType: CreditTypeEnum.IMAGE,
    user: user,
    userId: user.id,
    createdAt: new Date(),
    updatedAt: new Date(),
  } as UserCreditBalanceEntity;

  beforeEach(async () => {
    const mockLogger = {
      error: jest.fn(),
      log: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
      verbose: jest.fn(),
      setContext: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserCreditBalanceRequestManager,
        {
          provide: UserCreditBalanceManager,
          useValue: {
            deduct: jest.fn(),
            increase: jest.fn(),
          },
        },
        {
          provide: Logger,
          useValue: mockLogger,
        },
      ],
    }).compile();

    requestManager = module.get<UserCreditBalanceRequestManager>(
      UserCreditBalanceRequestManager,
    );
    creditBalanceManager = module.get<UserCreditBalanceManager>(
      UserCreditBalanceManager,
    );
    logger = module.get<Logger>(Logger);
  });

  it('should increase credit balance when amount is positive', async () => {
    jest
      .spyOn(creditBalanceManager, 'increase')
      .mockResolvedValue(mockCreditBalanceEntity);

    const result = await requestManager.handleInternalTransaction(
      request,
      user,
    );

    expect(creditBalanceManager.increase).toHaveBeenCalledWith(
      request.creditType,
      request.amount,
      user.id,
    );
    expect(result).toEqual(mockCreditBalanceEntity);
  });

  it('should deduct credit balance when amount is negative', async () => {
    const deductRequest = { ...request, amount: -50 };
    jest
      .spyOn(creditBalanceManager, 'deduct')
      .mockResolvedValue(mockCreditBalanceEntity);

    const result = await requestManager.handleInternalTransaction(
      deductRequest,
      user,
    );

    expect(creditBalanceManager.deduct).toHaveBeenCalledWith(
      deductRequest.creditType,
      50,
      user.id,
    );
    expect(result).toEqual(mockCreditBalanceEntity);
  });

  it('should throw PaymentRequiredException when user credit balance is not found', async () => {
    jest
      .spyOn(creditBalanceManager, 'increase')
      .mockRejectedValue(new Error('user_credit_balance.not_found'));

    await expect(
      requestManager.handleInternalTransaction(request, user),
    ).rejects.toThrow(PaymentRequiredException);
  });

  it('should log error and throw BadRequestException for other errors', async () => {
    const errorMessage = 'some_other_error';
    jest
      .spyOn(creditBalanceManager, 'increase')
      .mockRejectedValue(new Error(errorMessage));

    await expect(
      requestManager.handleInternalTransaction(request, user),
    ).rejects.toThrow(BadRequestException);
    expect(logger.error).toHaveBeenCalledWith(
      'Failed to handle internal transaction',
      {
        user: user.id,
        error: errorMessage,
      },
    );
  });
});
