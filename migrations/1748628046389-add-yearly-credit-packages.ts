import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddYearlyCreditPackages1748628046389 implements MigrationInterface {
  name = 'AddYearlyCreditPackages1748628046389';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        INSERT INTO credit_package (name, price, expires_after_months, credit_types, target_entity, is_recurring, is_visible)
        SELECT 'Beginner - Yearly', 990, 12, '{"IMAGE": 3600, "MODEL": 5}', 'user', true, true
        WHERE NOT EXISTS (
            SELECT 1 FROM credit_package WHERE name = 'Beginner - Yearly'
        );
    `);

    await queryRunner.query(`
        INSERT INTO credit_package (name, price, expires_after_months, credit_types, target_entity, is_recurring, is_visible)
        SELECT 'Fun - Yearly', 2490, 12, '{"IMAGE": 10800, "MODEL": 10}', 'user', true, true
        WHERE NOT EXISTS (
            SELECT 1 FROM credit_package WHERE name = 'Fun - Yearly'
        );
    `);

    await queryRunner.query(`
        INSERT INTO credit_package (name, price, expires_after_months, credit_types, target_entity, is_recurring, is_visible)
        SELECT 'Pro - Yearly', 7490, 12, '{"IMAGE": 36000, "MODEL": 20}', 'user', true, true
        WHERE NOT EXISTS (
            SELECT 1 FROM credit_package WHERE name = 'Pro - Yearly'
        );
    `);

    // Add yearly variants for organization plans
    await queryRunner.query(`
        INSERT INTO credit_package (name, price, expires_after_months, credit_types, target_entity, is_recurring, is_visible, seats)
        SELECT 'Enterprise Basic - Yearly', 500000, 12, '{"IMAGE": 100000, "MODEL": 1}', 'organization', true, true, 10
        WHERE NOT EXISTS (
            SELECT 1 FROM credit_package WHERE name = 'Enterprise Basic - Yearly'
        );
    `);

    await queryRunner.query(`
        INSERT INTO credit_package (name, price, expires_after_months, credit_types, target_entity, is_recurring, is_visible, seats)
        SELECT 'Enterprise Plus - Yearly', 1500000, 12, '{"IMAGE": 500000, "MODEL": 1}', 'organization', true, true, 30
        WHERE NOT EXISTS (
            SELECT 1 FROM credit_package WHERE name = 'Enterprise Plus - Yearly'
        );
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        DELETE FROM credit_package WHERE name = 'Beginner - Yearly';
    `);

    await queryRunner.query(`
        DELETE FROM credit_package WHERE name = 'Fun - Yearly';
    `);

    await queryRunner.query(`
        DELETE FROM credit_package WHERE name = 'Pro - Yearly';
    `);

    await queryRunner.query(`
        DELETE FROM credit_package WHERE name = 'Enterprise Basic - Yearly';
    `);

    await queryRunner.query(`
        DELETE FROM credit_package WHERE name = 'Enterprise Basic - Plus Yearly';
    `);
  }
}
