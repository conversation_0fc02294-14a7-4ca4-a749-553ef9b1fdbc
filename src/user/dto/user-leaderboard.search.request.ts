import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsOptional, IsString } from 'class-validator';
import { BaseSearchRequest } from '../../core/dto/base.search-request';

export class UserLeaderboardSearchRequest extends BaseSearchRequest {
  @IsOptional()
  @IsString()
  @ApiProperty({ required: false })
  username?: string;

  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  day: boolean;

  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  week: boolean;

  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  month: boolean;

  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  year: boolean;
}
