import {
  BadRequestException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import {
  ModelEntity,
  PrivacyEnum as ModelPrivacyEnum,
} from 'src/model/entity/model.entity';
import { ModelProvider } from 'src/model/service/provider';
import { UserEntity } from 'src/user/entity/user.entity';
import { StatusEnum as ModelStatusEnum } from '../../model/enum/status.enum';
import { ModelVersionProvider } from 'src/model/service/model-version.provider';

@Injectable()
export class PromptManager {
  constructor(
    private modelProvider: ModelProvider,
    private modelVersionProvider: ModelVersionProvider,
  ) {}

  async extractModelsFromPrompt(
    prompt: string,
    user: UserEntity,
  ): Promise<ModelEntity[]> {
    const modelNames = this.extractModelNamesFromPrompt(prompt);

    if (modelNames.length > 0) {
      const models = await this.modelProvider.findByNames(modelNames, user);

      // Check if all requested models were found (findByNames already handles access control)
      if (models.length < modelNames.length) {
        const foundModelNames = models.map((m) => m.name);
        const missingModels = modelNames.filter(
          (name) => !foundModelNames.includes(name),
        );
        throw new UnauthorizedException(
          `You do not have permission to view the model @${missingModels[0]}.`,
        );
      }

      models.forEach((model) => {
        if (model.status !== ModelStatusEnum.AVAILABLE) {
          throw new BadRequestException(`Model not available`);
        }
      });

      return models;
    }

    return [];
  }

  async generatePromptSystem(
    prompt: string,
    models: ModelEntity[],
    systemVersion: number,
  ): Promise<string> {
    if (models?.length) {
      for (const model of models) {
        // Replace '@+<model name>' with model.prompt and model.type (optional)
        const placeholder = '@+' + model.name;
        const modelVersion = await this.modelVersionProvider.getModelVersion(
          model.id,
          model.version,
        );

        const modelSystemVersion = modelVersion.systemVersionsData.find(
          (modelSystemVersion) =>
            modelSystemVersion.systemVersion == systemVersion,
        );

        const replacement =
          `${modelSystemVersion.prompt}, ` +
          (model.type ? `${model.type}, ` : '');

        prompt = prompt.replace(new RegExp(placeholder, 'ig'), replacement);
      }
    }

    prompt += prompt.endsWith(', ') ? '' : ', ';

    return prompt;
  }

  extractModelNamesFromPrompt(prompt: string): string[] {
    return (prompt.match(/@(\w+)/g) || []).map((name) => name.slice(1));
  }

  containsPrivateModel(models: ModelEntity[]): boolean {
    const privacyLevels = models.map((model) => model.privacy);

    return (
      privacyLevels.includes(ModelPrivacyEnum.LICENSED) ||
      privacyLevels.includes(ModelPrivacyEnum.PRIVATE)
    );
  }
}
